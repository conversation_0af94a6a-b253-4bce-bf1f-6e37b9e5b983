# IRT calculation and output helpers

irt_uploadData_func <- function(irtUserData) {
  ext <- tools::file_ext(irtUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(irtUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(irtUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(irtUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(irtUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

irt_results_func <- function(data, item_vars, model = 1) {
  tryCatch({
    if (!requireNamespace("mirt", quietly = TRUE)) {
      stop("Package 'mirt' is required for IRT analysis.")
    }
    
    fit <- mirt::mirt(data[, item_vars], model)
    
    list(
      fit = fit,
      data = data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during IRT calculation:", e$message))
  })
}

irt_ht_html <- function(results) {
  # No single hypothesis test, summary is more informative
  tagList(
    h4("Item Response Theory (IRT) Model"),
    p("See summary table for model fit and item parameters.")
  )
}

irt_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  item_fit <- mirt::itemfit(results$fit)
  out <- list(
    h4("Model Summary"),
    renderPrint(summary(results$fit)),
    h4("Item Fit Statistics"),
    renderDataTable({
      DT::datatable(as.data.frame(item_fit), options = list(pageLength = 10, searching = FALSE))
    })
  )
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

irt_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  # Item characteristic curves
  plot(results$fit, type = 'trace', main = 'Item Characteristic Curves')
  # Test information curve
  plot(results$fit, type = 'info', main = 'Test Information Curve')
}