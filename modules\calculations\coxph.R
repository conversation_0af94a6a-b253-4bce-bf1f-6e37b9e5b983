# Cox Proportional Hazards calculation and output helpers

coxph_uploadData_func <- function(coxphUserData) {
  ext <- tools::file_ext(coxphUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(coxphUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(coxphUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(coxphUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(coxphUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

coxph_results_func <- function(data, time_var, event_var, covariates) {
  tryCatch({
    if (!requireNamespace("survival", quietly = TRUE)) {
      stop("Package 'survival' required for Cox PH model.")
    }
    
    formula <- as.formula(paste0("survival::Surv(", time_var, ", ", event_var, ") ~ ", paste(covariates, collapse = " + ")))
    fit <- survival::coxph(formula, data = data)
    
    list(
      fit = fit,
      data = data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Cox PH calculation:", e$message))
  })
}

coxph_ht_html <- function(results) {
  # No single hypothesis test, summary is more informative
  tagList(
    h4("Cox Proportional Hazards Model"),
    p("See summary table for model coefficients and diagnostics.")
  )
}

coxph_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  out <- list(
    h4("Model Summary"),
    renderPrint(summary(results$fit))
  )
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

coxph_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  if (requireNamespace("survminer", quietly = TRUE)) {
    survminer::ggsurvplot(survival::survfit(results$fit), data = results$data)
  } else {
    plot(survival::survfit(results$fit), main = 'Survival Curve')
  }
  # Residuals
  resids <- residuals(results$fit)
  plot(resids, type = 'h', main = 'CoxPH Residuals', ylab = 'Residuals')
}