# Cochran-Mantel-Haenszel Test Calculations
# Stratified contingency tables

perform_cochran_mantel_haenszel <- function(data, odds_ratio = TRUE, homogeneity_test = TRUE, 
                                           effect_size = TRUE, conf_level = 0.95) {
  
  # Validate inputs
  required_cols <- c("Stratum", "Var1", "Var2", "Count")
  missing_cols <- required_cols[!required_cols %in% names(data)]
  if (length(missing_cols) > 0) {
    stop("Missing required columns: ", paste(missing_cols, collapse = ", "))
  }
  
  # Data summary
  n_observations <- sum(data$Count)
  n_strata <- length(unique(data$Stratum))
  n_missing <- sum(is.na(data$Count))
  
  # Remove missing values
  data <- data[complete.cases(data), ]
  
  # Check minimum requirements
  if (n_strata < 2) {
    stop("At least 2 strata are required")
  }
  
  # Create contingency tables for each stratum
  contingency_tables <- list()
  stratum_results <- data.frame(
    Stratum = character(),
    Odds_Ratio = numeric(),
    SE = numeric(),
    CI_Lower = numeric(),
    CI_Upper = numeric(),
    p_value = numeric(),
    stringsAsFactors = FALSE
  )
  
  # Process each stratum
  for (stratum in unique(data$Stratum)) {
    stratum_data <- data[data$Stratum == stratum, ]
    
    # Create 2x2 contingency table
    table_2x2 <- xtabs(Count ~ Var1 + Var2, data = stratum_data)
    
    # Ensure 2x2 structure
    if (nrow(table_2x2) < 2 || ncol(table_2x2) < 2) {
      warning(paste("Stratum", stratum, "does not have sufficient levels for 2x2 table"))
      next
    }
    
    contingency_tables[[as.character(stratum)]] <- table_2x2
    
    # Calculate odds ratio for this stratum
    if (odds_ratio) {
      or_result <- calculate_stratum_odds_ratio(table_2x2, conf_level)
      
      stratum_results <- rbind(stratum_results, data.frame(
        Stratum = as.character(stratum),
        Odds_Ratio = or_result$odds_ratio,
        SE = or_result$se,
        CI_Lower = or_result$ci_lower,
        CI_Upper = or_result$ci_upper,
        p_value = or_result$p_value,
        stringsAsFactors = FALSE
      ))
    }
  }
  
  # Perform Cochran-Mantel-Haenszel test
  cmh_result <- perform_cmh_test(contingency_tables)
  
  # Calculate overall odds ratio
  overall_odds_ratio <- NULL
  if (odds_ratio && nrow(stratum_results) > 0) {
    overall_odds_ratio <- calculate_overall_odds_ratio(stratum_results, conf_level)
  }
  
  # Perform homogeneity test
  homogeneity_result <- NULL
  if (homogeneity_test && nrow(stratum_results) > 1) {
    homogeneity_result <- perform_homogeneity_test(stratum_results)
  }
  
  # Calculate effect sizes
  effect_sizes <- NULL
  if (effect_size) {
    effect_sizes <- calculate_cmh_effect_sizes(cmh_result, n_observations)
  }
  
  # Create test results table
  test_results <- data.frame(
    Test = c("Cochran-Mantel-Haenszel", "Mantel-Haenszel"),
    Statistic = c(cmh_result$cmh_statistic, cmh_result$mh_statistic),
    df = c(cmh_result$df, cmh_result$df),
    p_value = c(cmh_result$cmh_p_value, cmh_result$mh_p_value),
    stringsAsFactors = FALSE
  )
  
  if (effect_size) {
    test_results$Effect_Size <- c(effect_sizes$cmh_effect, effect_sizes$mh_effect)
  }
  
  # Create contingency tables summary
  contingency_summary <- data.frame(
    Stratum = character(),
    Var1_Level = character(),
    Var2_Level = character(),
    Count = numeric(),
    stringsAsFactors = FALSE
  )
  
  for (stratum_name in names(contingency_tables)) {
    table_2x2 <- contingency_tables[[stratum_name]]
    for (i in 1:nrow(table_2x2)) {
      for (j in 1:ncol(table_2x2)) {
        contingency_summary <- rbind(contingency_summary, data.frame(
          Stratum = stratum_name,
          Var1_Level = rownames(table_2x2)[i],
          Var2_Level = colnames(table_2x2)[j],
          Count = table_2x2[i, j],
          stringsAsFactors = FALSE
        ))
      }
    }
  }
  
  # Prepare association plot data
  association_plot_data <- contingency_summary
  
  # Create results list
  results <- list(
    data = data,
    n_observations = n_observations,
    n_strata = n_strata,
    n_missing = n_missing,
    test_results = test_results,
    stratum_results = stratum_results,
    overall_odds_ratio = overall_odds_ratio,
    homogeneity_test = homogeneity_result,
    contingency_tables = contingency_summary,
    association_plot_data = association_plot_data,
    conf_level = conf_level
  )
  
  return(results)
}

# Calculate odds ratio for a single stratum
calculate_stratum_odds_ratio <- function(table_2x2, conf_level) {
  
  # Extract cell counts
  a <- table_2x2[1, 1]  # First row, first column
  b <- table_2x2[1, 2]  # First row, second column
  c <- table_2x2[2, 1]  # Second row, first column
  d <- table_2x2[2, 2]  # Second row, second column
  
  # Calculate odds ratio
  odds_ratio <- (a * d) / (b * c)
  
  # Calculate standard error
  se <- sqrt(1/a + 1/b + 1/c + 1/d)
  
  # Calculate confidence interval
  alpha <- 1 - conf_level
  log_or <- log(odds_ratio)
  log_se <- se
  
  ci_lower <- exp(log_or - qnorm(1 - alpha/2) * log_se)
  ci_upper <- exp(log_or + qnorm(1 - alpha/2) * log_se)
  
  # Calculate p-value (chi-square test)
  expected <- outer(rowSums(table_2x2), colSums(table_2x2)) / sum(table_2x2)
  chi_square <- sum((table_2x2 - expected)^2 / expected)
  p_value <- pchisq(chi_square, 1, lower.tail = FALSE)
  
  return(list(
    odds_ratio = odds_ratio,
    se = se,
    ci_lower = ci_lower,
    ci_upper = ci_upper,
    p_value = p_value
  ))
}

# Perform Cochran-Mantel-Haenszel test
perform_cmh_test <- function(contingency_tables) {
  
  # Initialize sums
  sum_observed <- 0
  sum_expected <- 0
  sum_variance <- 0
  sum_mh_numerator <- 0
  sum_mh_denominator <- 0
  
  # Process each stratum
  for (stratum_name in names(contingency_tables)) {
    table_2x2 <- contingency_tables[[stratum_name]]
    
    # Extract cell counts
    a <- table_2x2[1, 1]
    b <- table_2x2[1, 2]
    c <- table_2x2[2, 1]
    d <- table_2x2[2, 2]
    
    # Calculate expected value for cell (1,1)
    n <- a + b + c + d
    expected_a <- (a + b) * (a + c) / n
    
    # Calculate variance
    variance <- (a + b) * (c + d) * (a + c) * (b + d) / (n^2 * (n - 1))
    
    # Update sums
    sum_observed <- sum_observed + a
    sum_expected <- sum_expected + expected_a
    sum_variance <- sum_variance + variance
    
    # Mantel-Haenszel components
    sum_mh_numerator <- sum_mh_numerator + (a * d) / n
    sum_mh_denominator <- sum_mh_denominator + (b * c) / n
  }
  
  # Calculate CMH statistic
  cmh_statistic <- (sum_observed - sum_expected)^2 / sum_variance
  cmh_p_value <- pchisq(cmh_statistic, 1, lower.tail = FALSE)
  
  # Calculate Mantel-Haenszel statistic
  mh_odds_ratio <- sum_mh_numerator / sum_mh_denominator
  mh_statistic <- (log(mh_odds_ratio))^2 / sum_variance
  mh_p_value <- pchisq(mh_statistic, 1, lower.tail = FALSE)
  
  return(list(
    cmh_statistic = cmh_statistic,
    cmh_p_value = cmh_p_value,
    mh_statistic = mh_statistic,
    mh_p_value = mh_p_value,
    mh_odds_ratio = mh_odds_ratio,
    df = 1
  ))
}

# Calculate overall odds ratio
calculate_overall_odds_ratio <- function(stratum_results, conf_level) {
  
  # Use Mantel-Haenszel method for overall odds ratio
  # This is a simplified version - in practice, you'd use the full contingency tables
  
  # Calculate weighted average
  weights <- 1 / stratum_results$SE^2
  weighted_or <- sum(stratum_results$Odds_Ratio * weights) / sum(weights)
  
  # Calculate standard error
  se <- sqrt(1 / sum(weights))
  
  # Calculate confidence interval
  alpha <- 1 - conf_level
  log_or <- log(weighted_or)
  ci_lower <- exp(log_or - qnorm(1 - alpha/2) * se)
  ci_upper <- exp(log_or + qnorm(1 - alpha/2) * se)
  
  return(data.frame(
    Method = "Mantel-Haenszel",
    Odds_Ratio = weighted_or,
    SE = se,
    CI_Lower = ci_lower,
    CI_Upper = ci_upper,
    stringsAsFactors = FALSE
  ))
}

# Perform homogeneity test
perform_homogeneity_test <- function(stratum_results) {
  
  # Breslow-Day test for homogeneity
  # This is a simplified version - in practice, you'd use the full contingency tables
  
  # Calculate chi-square statistic for homogeneity
  overall_or <- exp(mean(log(stratum_results$Odds_Ratio)))
  
  chi_square <- sum((log(stratum_results$Odds_Ratio) - log(overall_or))^2 / stratum_results$SE^2)
  df <- nrow(stratum_results) - 1
  p_value <- pchisq(chi_square, df, lower.tail = FALSE)
  
  return(data.frame(
    Test = "Breslow-Day Test",
    Statistic = chi_square,
    df = df,
    p_value = p_value,
    stringsAsFactors = FALSE
  ))
}

# Calculate effect sizes
calculate_cmh_effect_sizes <- function(cmh_result, n_observations) {
  
  # Cramer's V equivalent for CMH test
  cmh_effect <- sqrt(cmh_result$cmh_statistic / n_observations)
  
  # Mantel-Haenszel effect size
  mh_effect <- sqrt(cmh_result$mh_statistic / n_observations)
  
  return(list(
    cmh_effect = cmh_effect,
    mh_effect = mh_effect
  ))
}

# Generate CMH diagnostic plots
generate_cmh_plots <- function(results, plot_types = c("odds_ratios", "association", "homogeneity")) {
  
  plots <- list()
  
  # Odds ratios by stratum
  if ("odds_ratios" %in% plot_types && !is.null(results$stratum_results)) {
    p1 <- ggplot(results$stratum_results, aes(x = Stratum, y = Odds_Ratio)) +
      geom_point(size = 3) +
      geom_errorbar(aes(ymin = CI_Lower, ymax = CI_Upper), width = 0.2) +
      geom_hline(yintercept = 1, color = "red", linetype = "dashed") +
      labs(title = "Odds Ratios by Stratum",
           x = "Stratum", y = "Odds Ratio") +
      theme_minimal() +
      theme(axis.text.x = element_text(angle = 45, hjust = 1))
    
    plots$odds_ratios <- p1
  }
  
  # Association plot
  if ("association" %in% plot_types && !is.null(results$association_plot_data)) {
    p2 <- ggplot(results$association_plot_data, aes(x = Var1_Level, y = Count, fill = Var2_Level)) +
      geom_bar(stat = "identity", position = "dodge") +
      facet_wrap(~Stratum) +
      labs(title = "Association Plot by Stratum",
           x = "Variable 1", y = "Count", fill = "Variable 2") +
      theme_minimal()
    
    plots$association <- p2
  }
  
  # Homogeneity plot
  if ("homogeneity" %in% plot_types && !is.null(results$stratum_results)) {
    p3 <- ggplot(results$stratum_results, aes(x = Stratum, y = log(Odds_Ratio))) +
      geom_point(size = 3) +
      geom_errorbar(aes(ymin = log(CI_Lower), ymax = log(CI_Upper)), width = 0.2) +
      geom_hline(yintercept = 0, color = "red", linetype = "dashed") +
      labs(title = "Log Odds Ratios by Stratum (Homogeneity)",
           x = "Stratum", y = "Log Odds Ratio") +
      theme_minimal() +
      theme(axis.text.x = element_text(angle = 45, hjust = 1))
    
    plots$homogeneity <- p3
  }
  
  return(plots)
} 