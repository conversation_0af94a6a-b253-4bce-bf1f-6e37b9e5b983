MDSUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("mdsUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("mdsVars"), "Variables for MDS", choices = NULL, multiple = TRUE),
        selectInput(ns("mdsDist"), "Distance Metric", choices = c("euclidean", "manhattan", "maximum", "canberra", "binary", "minkowski")),
        br(),
        actionButton(ns("goMDS"), label = "Run MDS", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("mdsError")),
        plotOutput(ns("mdsPlot")),
        verbatimTextOutput(ns("mdsStress"))
      )
    )
  )
} 