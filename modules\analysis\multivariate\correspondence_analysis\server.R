correspondenceAnalysisServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactives
    caUploadData <- eventReactive(input$caUserData, {
      handle_file_upload(input$caUserData)
    })
    
    caResults <- reactive({
      data <- caUploadData()
      if (is.null(data) || is.null(input$caRowVar) || is.null(input$caColVar)) {
        return(NULL)
      }
      caResults_func(
        TRUE,
        data,
        input$caRowVar,
        input$caColVar,
        input$caNDim
      )
    })
    
    # Validation errors
    caValidationErrors <- reactive({
      errors <- c()
      data <- caUploadData()
      
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$caRowVar) || input$caRowVar == "") {
        errors <- c(errors, "Please select a row variable.")
      }
      
      if (is.null(input$caColVar) || input$caColVar == "") {
        errors <- c(errors, "Please select a column variable.")
      }
      
      if (!is.null(input$caRowVar) && !is.null(input$caColVar) && 
          input$caRowVar == input$caColVar) {
        errors <- c(errors, "Row and column variables must be different.")
      }
      
      if (!is.null(input$caRowVar) && input$caRowVar %in% names(data)) {
        if (length(unique(data[[input$caRowVar]])) < 2) {
          errors <- c(errors, sprintf("Row variable '%s' must have at least two unique values.", input$caRowVar))
        }
      }
      
      if (!is.null(input$caColVar) && input$caColVar %in% names(data)) {
        if (length(unique(data[[input$caColVar]])) < 2) {
          errors <- c(errors, sprintf("Column variable '%s' must have at least two unique values.", input$caColVar))
        }
      }
      
      errors
    })
    
    # Outputs
    output$caResults <- renderUI({
      results <- caResults()
      if (is.null(results)) return(NULL)
      correspondenceAnalysisResults(caResults, reactive({input$caSigLvl}))
    })
    
    output$correspondenceAnalysisPlot <- renderPlot({
      results <- caResults()
      if (is.null(results)) return(NULL)
      correspondenceAnalysisPlot(caResults, reactive({input$caSigLvl}))
    })
    
    output$caInterpretation <- renderUI({
      results <- caResults()
      if (is.null(results)) return(NULL)
      correspondenceAnalysisInterpretation(caResults, reactive({input$caSigLvl}))
    })
    
    output$caCoordinatesOutput <- renderUI({
      results <- caResults()
      if (is.null(results)) return(NULL)
      correspondenceAnalysisCoordinates(caResults, reactive({input$caSigLvl}))
    })
    
    output$caCoordinatesTable <- DT::renderDT({
      results <- caResults()
      if (is.null(results) || is.null(results$coordinates)) return(NULL)
      
      coords_df <- results$coordinates
      DT::datatable(coords_df,
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(coords_df)))))
    })
    
    output$caContributionsOutput <- renderUI({
      results <- caResults()
      if (is.null(results)) return(NULL)
      correspondenceAnalysisContributions(caResults, reactive({input$caSigLvl}))
    })
    
    output$caContributionsTable <- DT::renderDT({
      results <- caResults()
      if (is.null(results) || is.null(results$contributions)) return(NULL)
      
      contrib_df <- results$contributions
      DT::datatable(contrib_df,
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(contrib_df)))))
    })
    
    output$renderCAData <- renderUI({
      req(caUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("caInitialUploadTable")), style = "width: 75%"),
        br(),
        br()
      )
    })
    
    output$caInitialUploadTable <- DT::renderDT({
      req(caUploadData())
      DT::datatable(caUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(caUploadData())))))
    })
    
    # Update selectizeInput choices after file upload
    observeEvent(caUploadData(), {
      data <- caUploadData()
      
      # Clear selectizeInputs and main panel
      updateSelectizeInput(session, 'caRowVar', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'caColVar', choices = character(0), selected = NULL, server = TRUE)
      output$correspondenceAnalysisResults <- renderUI({ NULL })
      
      # If data is valid, update choices and show preview
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'caRowVar', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'caColVar', choices = names(data), server = TRUE)
        
        output$correspondenceAnalysisResults <- renderUI({
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('caPreviewTable'))
          )
        })
        
        output$caPreviewTable <- DT::renderDT({
          head(data, 20)
        })
      }
    })
    
    # Show main results or error screen when Calculate is pressed
    observeEvent(input$goCorrespondenceAnalysis, {
      output$correspondenceAnalysisResults <- renderUI({
        errors <- caValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Correspondence Analysis", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("caTabset"),
              selected = "Analysis",
              tabPanel(
                id    = ns("ca"),
                title = "Analysis",
                titlePanel("Correspondence Analysis Results"),
                br(),
                uiOutput(ns('caResults')),
                br(),
                plotOutput(ns('correspondenceAnalysisPlot'), width = "100%", height = "500px"),
                br(),
                uiOutput(ns('caInterpretation'))
              ),
              tabPanel(
                id    = ns("caCoords"),
                title = "Coordinates",
                DTOutput(ns("caCoordinatesTable")),
                uiOutput(ns("caCoordinatesOutput"))
              ),
              tabPanel(
                id    = ns("caContrib"),
                title = "Contributions",
                DTOutput(ns("caContributionsTable")),
                uiOutput(ns("caContributionsOutput"))
              ),
              tabPanel(
                id    = ns("caData"),
                title = "Uploaded Data",
                uiOutput(ns("renderCAData"))
              )
            )
          )
        }
      })
    })
  })
} 