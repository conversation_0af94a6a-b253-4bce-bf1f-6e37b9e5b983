PairwisePlotMatrixServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Source the calculations file
    source("modules/calculations/pairwise_plot_matrix.R")
    
    # File upload reactive
    ppmData <- eventReactive(input$ppmUserData, {
      handle_pairwise_plot_matrix_data_upload(input$ppmUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(ppmData(), {
      data <- ppmData()
      updateSelectizeInput(session, 'ppmVars', choices = character(0), selected = NULL, server = TRUE)
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'ppmVars', choices = names(data), selected = NULL, server = TRUE)
      }
    })
    
    # Validation errors reactive
    ppmValidationErrors <- reactive({
      validate_pairwise_plot_matrix_inputs(ppmData(), input$ppmVars)
    })
    
    # Pairwise plot matrix analysis reactive
    ppmResult <- eventReactive(input$goPPM, {
      perform_pairwise_plot_matrix_analysis(ppmData(), input$ppmVars)
    })
    
    # Error handling
    output$ppmError <- renderUI({
      errors <- ppmValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          ppmResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "Pairwise Plot Matrix Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$ppmModelSummary <- renderUI({
      req(ppmResult())
      render_pairwise_plot_matrix_summary_ui(ppmResult())
    })
    
    output$ppmPlot <- renderPlot({
      req(ppmResult())
      render_pairwise_plot_matrix_plots(ppmResult())
    })
    
    output$ppmDiagnostics <- renderUI({
      req(ppmResult())
      render_pairwise_plot_matrix_diagnostics(ppmResult())
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$ppmDataSummary <- renderUI({
      req(ppmResult())
      render_pairwise_plot_matrix_data_summary(ppmResult(), ppmData())
    })
    
    output$ppmAssumptions <- renderUI({
      req(ppmResult())
      render_pairwise_plot_matrix_assumptions_check(ppmResult())
    })
    
    output$ppmDiagnosticPlots <- renderPlot({
      req(ppmResult())
      render_pairwise_plot_matrix_diagnostic_plots(ppmResult(), ppmData())
    })
    
    # Uploaded Data Tab Outputs
    output$ppmDataTable <- DT::renderDT({
      req(ppmData())
      render_pairwise_plot_matrix_data_table(ppmData())
    })
    
    output$ppmDataInfo <- renderUI({
      req(ppmData())
      render_pairwise_plot_matrix_data_info(ppmData(), input$ppmUserData)
    })
  })
} 