# Advanced Visualization Server Logic

advancedVisualizationServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    # Reactive values for storing results
    adv_viz_results <- reactiveVal(NULL)
    adv_viz_plot <- reactiveVal(NULL)
    
    # Data upload reactive
    advVizData <- eventReactive(input$advVizUserData, {
      handle_file_upload(input$advVizUserData)
    })

    # Update variable choices when data changes
    observeEvent(advVizData(), {
      data <- advVizData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectInput(session, "adv_viz_x", choices = names(data))
        updateSelectInput(session, "adv_viz_y", choices = names(data))
        updateSelectInput(session, "adv_viz_z", choices = names(data))
        updateSelectInput(session, "adv_viz_color", choices = c("None" = "none", names(data)), selected = "none")
        updateSelectInput(session, "adv_viz_size", choices = c("None" = "none", names(data)), selected = "none")
        updateSelectInput(session, "adv_viz_group", choices = c("None" = "none", names(data)), selected = "none")
        
        updateSelectInput(session, "adv_viz_lat", choices = names(data))
        updateSelectInput(session, "adv_viz_lon", choices = names(data))
        updateSelectInput(session, "adv_viz_map_color", choices = names(data))
        updateSelectInput(session, "adv_viz_map_size", choices = names(data))
        
        updateSelectInput(session, "adv_viz_from", choices = names(data))
        updateSelectInput(session, "adv_viz_to", choices = names(data))
        updateSelectInput(session, "adv_viz_edge_weight", choices = c("None" = "none", names(data)), selected = "none")
        updateSelectInput(session, "adv_viz_node_group", choices = c("None" = "none", names(data)), selected = "none")
        
        updateSelectInput(session, "adv_viz_matrix_vars", choices = names(data), selected = names(data)[1:min(5, ncol(data))])
        updateSelectInput(session, "adv_viz_par_vars", choices = names(data), selected = names(data)[1:min(5, ncol(data))])
        updateSelectInput(session, "adv_viz_radar_vars", choices = names(data), selected = names(data)[1:min(5, ncol(data))])
        updateSelectInput(session, "adv_viz_radar_group", choices = names(data))
        
        updateSelectInput(session, "adv_viz_tree_group", choices = names(data))
        updateSelectInput(session, "adv_viz_tree_size", choices = names(data))
        updateSelectInput(session, "adv_viz_tree_color", choices = c("None" = "none", names(data)), selected = "none")
        
        updateSelectInput(session, "adv_viz_sankey_from", choices = names(data))
        updateSelectInput(session, "adv_viz_sankey_to", choices = names(data))
        updateSelectInput(session, "adv_viz_sankey_value", choices = names(data))
      }
    })

    # Generate visualization
    observeEvent(input$run_adv_viz, {
      req(advVizData(), input$adv_viz_type)
      
      tryCatch({
        plot_result <- NULL
        
        if (input$adv_viz_type == "3d_scatter") {
          plot_result <- create_3d_scatter_plot()
        } else if (input$adv_viz_type == "3d_surface") {
          plot_result <- create_3d_surface_plot()
        } else if (input$adv_viz_type == "geo_map") {
          plot_result <- create_geographic_map()
        } else if (input$adv_viz_type == "network") {
          plot_result <- create_network_plot()
        } else if (input$adv_viz_type == "heatmap_matrix") {
          plot_result <- create_heatmap_matrix()
        } else if (input$adv_viz_type == "parallel_coords") {
          plot_result <- create_parallel_coordinates()
        } else if (input$adv_viz_type == "radar") {
          plot_result <- create_radar_chart()
        } else if (input$adv_viz_type == "treemap") {
          plot_result <- create_treemap()
        } else if (input$adv_viz_type == "sankey") {
          plot_result <- create_sankey_diagram()
        }
        
        if (!is.null(plot_result)) {
          adv_viz_results(plot_result)
          adv_viz_plot(plot_result$plot)
        }
        
      }, error = function(e) {
        showNotification(paste("Error in visualization:", e$message), type = "error")
      })
    })

    # 3D Scatter Plot
    create_3d_scatter_plot <- function() {
      req(input$adv_viz_x, input$adv_viz_y, input$adv_viz_z)
      
      data <- advVizData()
      plot_data <- data.frame(
        x = data[[input$adv_viz_x]],
        y = data[[input$adv_viz_y]],
        z = data[[input$adv_viz_z]]
      )
      
      if (input$adv_viz_color != "none") {
        plot_data$color <- data[[input$adv_viz_color]]
      }
      if (input$adv_viz_size != "none") {
        plot_data$size <- data[[input$adv_viz_size]]
      }
      if (input$adv_viz_group != "none") {
        plot_data$group <- data[[input$adv_viz_group]]
      }
      
      if (input$adv_viz_interactive) {
        p <- plot_ly(plot_data, x = ~x, y = ~y, z = ~z, type = "scatter3d", mode = "markers")
        
        if (input$adv_viz_color != "none") {
          p <- p %>% add_markers(color = ~color)
        }
        if (input$adv_viz_size != "none") {
          p <- p %>% add_markers(size = ~size)
        }
        
        p <- p %>% layout(
          title = input$adv_viz_title,
          scene = list(
            xaxis = list(title = input$adv_viz_xlab),
            yaxis = list(title = input$adv_viz_ylab),
            zaxis = list(title = "Z")
          )
        )
      } else {
        # Static 3D plot using scatterplot3d
        p <- scatterplot3d(plot_data$x, plot_data$y, plot_data$z,
                           main = input$adv_viz_title,
                           xlab = input$adv_viz_xlab,
                           ylab = input$adv_viz_ylab,
                           zlab = "Z")
      }
      
      return(list(plot = p, type = "3d_scatter"))
    }

    # Geographic Map
    create_geographic_map <- function() {
      req(input$adv_viz_lat, input$adv_viz_lon)
      
      data <- advVizData()
      plot_data <- data.frame(
        lat = data[[input$adv_viz_lat]],
        lon = data[[input$adv_viz_lon]]
      )
      
      if (input$adv_viz_map_color != "none") {
        plot_data$color <- data[[input$adv_viz_map_color]]
      }
      if (input$adv_viz_map_size != "none") {
        plot_data$size <- data[[input$adv_viz_map_size]]
      }
      
      # Create leaflet map
      m <- leaflet(plot_data) %>%
        addTiles() %>%
        addCircleMarkers(
          lng = ~lon, lat = ~lat,
          radius = if(input$adv_viz_map_size != "none") ~size else 5,
          color = if(input$adv_viz_map_color != "none") ~color else "red",
          popup = ~paste("Lat:", lat, "Lon:", lon)
        )
      
      return(list(plot = m, type = "geo_map"))
    }

    # Network Plot
    create_network_plot <- function() {
      req(input$adv_viz_from, input$adv_viz_to)
      
      data <- advVizData()
      edges <- data.frame(
        from = data[[input$adv_viz_from]],
        to = data[[input$adv_viz_to]]
      )
      
      if (input$adv_viz_edge_weight != "none") {
        edges$weight <- data[[input$adv_viz_edge_weight]]
      }
      
      # Create network object
      net <- graph_from_data_frame(edges, directed = FALSE)
      
      if (input$adv_viz_interactive) {
        # Interactive network using visNetwork
        nodes <- data.frame(id = unique(c(edges$from, edges$to)))
        if (input$adv_viz_node_group != "none") {
          nodes$group <- data[[input$adv_viz_node_group]][match(nodes$id, data[[input$adv_viz_from]])]
        }
        
        p <- visNetwork(nodes, edges) %>%
          visPhysics(stabilization = FALSE) %>%
          visLayout(randomSeed = 123)
      } else {
        # Static network plot
        p <- plot(net, 
                  vertex.size = 5,
                  vertex.label.cex = 0.8,
                  edge.width = if(input$adv_viz_edge_weight != "none") edges$weight else 1)
      }
      
      return(list(plot = p, type = "network"))
    }

    # Heatmap Matrix
    create_heatmap_matrix <- function() {
      req(input$adv_viz_matrix_vars)
      
      data <- advVizData()
      # Select numeric variables only
      numeric_vars <- input$adv_viz_matrix_vars[sapply(data[input$adv_viz_matrix_vars], is.numeric)]
      
      if (length(numeric_vars) < 2) {
        stop("Need at least 2 numeric variables for correlation matrix")
      }
      
      # Calculate correlation matrix
      cor_matrix <- cor(data[numeric_vars], method = input$adv_viz_cor_method, use = "complete.obs")
      
      if (input$adv_viz_interactive) {
        p <- plot_ly(z = cor_matrix, 
                     x = colnames(cor_matrix), 
                     y = rownames(cor_matrix),
                     type = "heatmap",
                     colorscale = "Viridis") %>%
          layout(title = input$adv_viz_title)
      } else {
        p <- heatmap(cor_matrix, 
                     main = input$adv_viz_title,
                     col = viridis::viridis(100))
      }
      
      return(list(plot = p, type = "heatmap_matrix"))
    }

    # Placeholder functions for other visualization types
    create_3d_surface_plot <- function() {
      # Placeholder implementation
      return(list(plot = ggplot() + ggtitle("3D Surface Plot - Coming Soon"), type = "3d_surface"))
    }
    
    create_parallel_coordinates <- function() {
      # Placeholder implementation
      return(list(plot = ggplot() + ggtitle("Parallel Coordinates - Coming Soon"), type = "parallel_coords"))
    }
    
    create_radar_chart <- function() {
      # Placeholder implementation
      return(list(plot = ggplot() + ggtitle("Radar Chart - Coming Soon"), type = "radar"))
    }
    
    create_treemap <- function() {
      # Placeholder implementation
      return(list(plot = ggplot() + ggtitle("Treemap - Coming Soon"), type = "treemap"))
    }
    
    create_sankey_diagram <- function() {
      # Placeholder implementation
      return(list(plot = ggplot() + ggtitle("Sankey Diagram - Coming Soon"), type = "sankey"))
    }

    # Output plot
    output$adv_viz_plot <- renderPlot({
      req(adv_viz_plot())
      adv_viz_plot()
    })

    # Output for interactive plots
    output$adv_viz_interactive_plot <- renderUI({
      req(adv_viz_results())
      results <- adv_viz_results()
      
      if (results$type %in% c("geo_map", "network") && input$adv_viz_interactive) {
        # For leaflet and visNetwork plots
        if (results$type == "geo_map") {
          leafletOutput(ns("map_plot"))
        } else {
          visNetworkOutput(ns("network_plot"))
        }
      } else {
        # For plotly plots
        # plotlyOutput(ns("plotly_plot"))
        plotOutput(ns("adv_viz_plot"), height = "600px")
      }
    })

    # Render plotly plot
    # output$plotly_plot <- renderPlotly({
    #   req(adv_viz_results())
    #   results <- adv_viz_results()
    #   
    #   if (results$type %in% c("3d_scatter", "heatmap_matrix") && input$adv_viz_interactive) {
    #     results$plot
    #   } else {
    #     # Fallback to empty plot
    #     plot_ly() %>% layout(title = "No interactive plot available")
    #   }
    # })

    # Render adv_viz_plotly (main plotly output)
    # output$adv_viz_plotly <- renderPlotly({
    #   req(adv_viz_results())
    #   results <- adv_viz_results()
    #   
    #   if (results$type %in% c("3d_scatter", "heatmap_matrix") && input$adv_viz_interactive) {
    #     results$plot
    #   } else {
    #     # Fallback to empty plot
    #     plot_ly() %>% layout(title = "No interactive plot available")
    #   }
    # })

    # Download plot
    output$download_adv_viz <- downloadHandler(
      filename = function() {
        paste("advanced_visualization_", format(Sys.time(), "%Y%m%d_%H%M%S"), ".png", sep = "")
      },
      content = function(file) {
        req(adv_viz_plot())
        ggsave(file, plot = adv_viz_plot(), width = 10, height = 8, dpi = 300)
      }
    )

    # Reset functionality
    observeEvent(input$reset_adv_viz, {
      adv_viz_results(NULL)
      adv_viz_plot(NULL)
    })
  })
} 