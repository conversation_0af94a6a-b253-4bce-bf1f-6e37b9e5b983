# Mann-Whitney U test calculation and output helpers

mann_whit<PERSON>_uploadData_func <- function(mannWhitneyUserData) {
  ext <- tools::file_ext(mannWhitneyUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(mannWhitneyUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(mannWhitneyUserData$datapath),
         xlsx = readxl::read_xlsx(mannWhitneyUserData$datapath),
         txt = readr::read_tsv(mannWhitneyUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

mann_whitney_results_func <- function(data, num_var, group_var, conf_level, alternative) {
  tryCatch({
    x <- data[[num_var]]
    g <- as.factor(data[[group_var]])
    
    # Core test
    test <- wilcox.test(x ~ g, conf.int = TRUE, conf.level = conf_level, alternative = alternative, exact = FALSE)
    
    # Effect size (r = Z/sqrt(N))
    n_total <- length(x)
    z_score <- qnorm(test$p.value / 2, lower.tail = FALSE)
    effect_size <- abs(z_score) / sqrt(n_total)
    
    # Descriptive statistics
    desc_stats <- data.frame(
      Group = levels(g),
      N = tapply(x, g, length),
      Mean = tapply(x, g, mean),
      Median = tapply(x, g, median),
      SD = tapply(x, g, sd),
      Min = tapply(x, g, min),
      Max = tapply(x, g, max)
    )
    
    # Rank summary
    ranks <- rank(x)
    rank_stats <- data.frame(
      Group = levels(g),
      N = tapply(ranks, g, length),
      Mean_Rank = tapply(ranks, g, mean),
      Sum_Ranks = tapply(ranks, g, sum)
    )
    
    # Return all results in a list
    list(
      test = test,
      effect_size = effect_size,
      desc_stats = desc_stats,
      rank_stats = rank_stats,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Mann-Whitney U test calculation:", e$message))
  })
}

mann_whitney_ht_html <- function(results, conf_level) {
  test <- results$test
  effect_size <- results$effect_size
  
  # Effect size interpretation
  effect_interp <- if (effect_size < 0.1) {
    "Negligible effect size"
  } else if (effect_size < 0.3) {
    "Small effect size"
  } else if (effect_size < 0.5) {
    "Medium effect size"
  } else {
    "Large effect size"
  }
  
  # Conclusion
  conclusion <- if (test$p.value < (1 - conf_level)) {
    sprintf("Since P (%.4f) < %.2f, reject H0. There is a significant difference between the groups.", test$p.value, (1 - conf_level))
  } else {
    sprintf("Since P (%.4f) >= %.2f, do not reject H0. There is not a significant difference between the groups.", test$p.value, (1 - conf_level))
  }
  
  withMathJax(tagList(
    h4('Hypotheses'),
    p('$H_0$: The distributions of the two groups are identical'),
    p('$H_A$: The distributions of the two groups are not identical'),
    br(),
    h4('Test Statistic'),
    p(sprintf('$U = %.4f$', test$statistic)),
    br(),
    h4('P-value'),
    p(sprintf('$P = %.4f$', test$p.value)),
    br(),
    h4('Confidence Interval'),
    if (!is.null(test$conf.int)) {
      p(sprintf('CI = (%.4f, %.4f)', test$conf.int[1], test$conf.int[2]))
    },
    br(),
    h4("Effect Size"),
    p(sprintf("Effect size (r): %.4f", effect_size)),
    p(effect_interp),
    br(),
    h4("Assumptions Check"),
    p("1. Independent observations"),
    p("2. Ordinal or continuous data"),
    p("3. Similar shape distributions"),
    br(),
    h4('Conclusion'),
    p(conclusion)
  ))
}

mann_whitney_summary_html <- function(results) {
  tagList(
    h4("Descriptive Statistics by Group"),
    renderTable(results$desc_stats, digits = 4),
    br(),
    h4("Rank Summary"),
    renderTable(results$rank_stats, digits = 4)
  )
}

mann_whitney_plot <- function(data, num_var, group_var) {
  ggplot(data, aes(x = as.factor(.data[[group_var]]), y = .data[[num_var]], fill = as.factor(.data[[group_var]]))) +
    geom_boxplot(alpha = 0.7) +
    labs(title = "Boxplot of Response by Group",
         x = group_var,
         y = num_var) +
    theme_minimal() +
    theme(legend.position = "none")
}