server <- function(session, input, output) {
  darkmode_toggle(inputid = "togglemode")

  descStatsServer(id = "ds")
  probDistServer(id = "pd")
  sampSizeEstServer(id = "sse")
  statisticalInferenceServer(id = "si")
  regressionAndCorrelationServer(id = "rc")
  SurvivalAnalysisServer(id = "surv")
  BayesianTestServer(id = "bayes")
  CustomTestServer(id = "custom")
  # Advanced Tab Modules
  PCAServer("pca")
  ClusterAnalysisServer("clust")
  ROCAnalysisServer("roc")
  PowerAnalysisServer("power")
  EffectSizeServer("es")
  CustomPlotServer("plot")
  MetaAnalysisServer("meta")
  MixedEffectsServer("me")
  SurveyPsychometricsServer("survey")
  TimeSeriesServer("ts")
  STLDecompositionServer("stl")
  StateSpaceServer("ss")
  ChangePointServer("cp")
  SpectralAnalysisServer("spec")
  NetworkAnalysisServer("net")
  SEMServer("sem")
  LatentClassServer("lc")
  IRTServer("irt")
  DataSummarizationServer("dsum")
  MissingnessVizServer("miss")
  # Advanced Stats Modules
  AdvancedSurvivalServer("asurv")
  GAMServer("gam")
  MANOVAServer("manova")
  NonlinearRegressionServer("nlr")
  PropensityScoreServer("ps")
  MediationModerationServer("mm")
  BayesianHierarchicalServer("bh")
  # Machine Learning Modules
  SupervisedMLServer("sml")
  UnsupervisedMLServer("uml")
  ModelComparisonServer("mcomp")
  FeatureSelectionServer("fsel")
  # Additional Methods Modules
  BootstrapServer("boot")
  PermutationTestsServer("perm")
  SimulationServer("sim")
  DiscriminantServer("disc")
  CCAServer("cca")
  MDSServer("mds")
  TSNEUMAPServer("tsne")
  CompetingRisksServer("comp")
  StratifiedKMServer("skm")
  CoxPHServer("cox")
  # Emerging Methods Modules
  DeepLearningServer("dl")
  NaturalLanguageProcessingServer("nlp")
  SpatialAnalysisServer("spatial")
  RealTimeAnalyticsServer("rta")
  # New Advanced Modules
  multilevelModelingServer("mlm")
  advancedVisualizationServer("adv_viz")
  ensembleMethodsServer("ensemble")
  longitudinalAnalysisServer("long")
  
  # --- Newly Implemented Modules ---
  
  # Sign Test
  SignTestServer("sign_test")
  
  # Jonckheere-Terpstra Test
  JonckheereTerpstraServer("jonckheere_terpstra")
  
  # Three-Way ANOVA
  ThreeWayAnovaServer("three_way")
  
  # ANCOVA
  AncovaServer("ancova")
  
  # Polynomial Regression
  PolynomialRegressionServer("poly")
  
  # Stepwise Regression
  StepwiseRegressionServer("stepwise")
  
  # Ridge Regression
  RidgeRegressionServer("ridge")
  
  # --- Newly Implemented High Priority Modules ---
  
  # Lasso Regression
  LassoRegressionServer("lasso")
  
  # Elastic Net Regression
  ElasticNetRegressionServer("elastic_net")
  
  # Mixed ANOVA
  MixedAnovaServer("mixed_anova")
  
  # Non-parametric ANCOVA
  NonparametricAncovaServer("nonparametric_ancova")
  
  # Cochran-Mantel-Haenszel Test
  CochranMantelHaenszelServer("cochran_mantel_haenszel")
  
  # --- Newly Implemented Medium Priority Modules ---
  
  # Reliability Analysis
  ReliabilityAnalysisServer("reliability")
  
  # Partial and Semi-partial Correlations
  PartialCorrelationsServer("partial_corr")
  
  # Log-linear Models
  LogLinearModelsServer("log_linear")
  
  # --- Newly Implemented Low Priority Modules ---
  
  # Quality Control Charts
  QualityControlChartsServer("qc_charts")
  
  # --- Phase 1 and Phase 2 Modules ---
  
  # Phase 1 Modules (High Priority)
  
  # Mann-Kendall Trend Test
  mannKendallServer("mann_kendall")
  
  # Anderson-Darling Test
  andersonDarlingServer("anderson_darling")
  
  # Bartlett's Test
  bartlettsTestServer("bartletts_test")
  
  # Phase 2 Modules (Medium Priority)
  
  # Runs Test (Wald-Wolfowitz)
  runsTestServer("runs_test")
  
  # Durbin-Watson Test
  durbinWatsonServer("durbin_watson")
  
  # Breusch-Pagan Test
  breuschPaganServer("breusch_pagan")
  
  # Shapiro-Wilk Test
  shapiroWilkServer("shapiro_wilk")
  
  # Jarque-Bera Test
  jarqueBeraServer("jarque_bera")
  
  # Ljung-Box Test
  ljungBoxServer("ljung_box")
  
  # Augmented Dickey-Fuller Test
  augmentedDickeyFullerServer("augmented_dickey_fuller")

  # --- Additional High Priority Modules ---

  # Quantile Regression
  quantileRegressionServer("quantile_regression")

  # Robust ANOVA
  robustAnovaServer("robust_anova")

  # Factor Analysis
  factorAnalysisServer("factor_analysis")

  # Discriminant Analysis
  discriminantAnalysisServer("discriminant_analysis")

  # Correspondence Analysis
  correspondenceAnalysisServer("correspondence_analysis")

  # --- Additional Medium Priority Modules ---

  # Experimental Design Tools
  experimentalDesignServer("experimental_design")
}
