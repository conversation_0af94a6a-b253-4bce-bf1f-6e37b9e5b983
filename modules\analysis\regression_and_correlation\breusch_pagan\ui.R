breuschPaganSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(
      inputId = ns("bpUserData"),
      label   = strong("Upload your Data (.csv or .xls or .xlsx or .txt)"),
      accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
    ),
    div(
      id = ns("bpUploadInputs"),
      selectizeInput(
        inputId = ns("bpResponse"),
        label = strong("Response Variable (Y)"),
        choices = c(""),
        selected = NULL,
        options = list(placeholder = 'Select response variable', onInitialize = I('function() { this.setValue(\"\"); }'))
      ),
      selectizeInput(
        inputId = ns("bpPredictors"),
        label = strong("Predictor Variables (X)"),
        choices = c(""),
        multiple = TRUE,
        selected = NULL,
        options = list(hideSelected = FALSE, placeholder = 'Select predictor variables', onInitialize = I('function() { this.setValue(\"\"); }'))
      ),
      radioButtons(
        inputId = ns("bpSigLvl"),
        label = strong("Significance Level (alpha)"),
        choices = c("10%", "5%", "1%"),
        selected = "5%",
        inline = TRUE
      )
    ),
    br(),
    actionButton(ns("goInference"), label = "Calculate", class = "act-btn")
  )
}

breuschPaganMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('breuschPaganResults'))
  )
}

breuschPaganUI <- function(id) {
  ns <- NS(id)
  tagList(
    breuschPaganSidebarUI(id),
    breuschPaganMainUI(id)
  )
} 