BootstrapUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("bootUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("bootVars"), "Variables", choices = NULL, multiple = TRUE),
        numericInput(ns("bootSamples"), "Number of Bootstrap Samples", value = 1000, min = 100, max = 10000),
        br(),
        actionButton(ns("goBoot"), label = "Run Bootstrap", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("bootError")),
        tableOutput(ns("bootResults")),
        plotOutput(ns("bootPlot"))
      )
    )
  )
} 