#!/bin/bash

echo "============================================================"
echo "CougarStats Package Installation Script"
echo "============================================================"
echo ""
echo "This script will install all necessary R packages for CougarStats"
echo ""
echo "Make sure you have R installed and accessible from command line"
echo ""

# Check if R is available
if ! command -v R &> /dev/null; then
    echo "ERROR: R is not found in your PATH"
    echo "Please install R from https://cran.r-project.org/"
    echo "Make sure to add R to your system PATH"
    echo ""
    exit 1
fi

echo "R found! Starting package installation..."
echo ""

# Run the R installation script
Rscript install_all_packages.R

echo ""
echo "============================================================"
echo "Installation completed!"
echo "============================================================"
echo ""
echo "If you encountered any errors, please check the output above"
echo "and refer to the troubleshooting guides:"
echo "- CMPRSK_INSTALLATION.md (for cmprsk issues)"
echo "- ERROR_HANDLING_README.md (for general troubleshooting)"
echo "" 