# CougarStats Test Suite

This directory contains comprehensive test scripts for all 81 modules in the CougarStats application.

## Overview

The test suite is designed to verify the functionality of all statistical analysis modules in CougarStats, ensuring that:

- All calculation functions work correctly
- Data validation is properly implemented
- Error handling functions as expected
- Output formats are consistent
- The standard tab structure is properly implemented

## Test Structure

### Test Files

1. **`test_modules.R`** - Main test script that tests all 81 modules
2. **`testthat/`** - Directory containing individual test suites:
   - `test_basic_statistics.R` - Tests for basic statistical modules
   - `test_inference_tests.R` - Tests for inference test modules
   - `test_regression_correlation.R` - Tests for regression and correlation modules
   - `test_advanced_analysis.R` - Tests for advanced analysis modules
   - `test_machine_learning.R` - Tests for machine learning modules
   - `test_advanced_stats.R` - Tests for advanced statistics modules

### Test Categories

The tests are organized into the following categories:

#### Basic Statistics (9 modules)
- Descriptive Statistics
- Probability Distributions
- Sample Size Estimation
- Data Summarization
- Missingness Visualization
- Outlier Detection
- Variable Transformation
- Pairwise Plot Matrix
- Simulation

#### Inference Tests (18 modules)
- One Sample Inference
- Two Sample Inference
- Paired T-Test
- ANOVA (One-way, Two-way, Repeated Measures)
- Chi-Square Test
- Kruskal-Wallis Test
- Mann-Whitney U Test
- Wilcoxon Signed-Rank Test
- Friedman Test
- Levene's/Bartlett's Test
- Post Hoc Tests
- Proportion Tests
- Bayesian Tests
- Bayesian Model Comparison
- Custom Test
- McNemar Test
- Cochran's Q Test
- Power Analysis

#### Regression & Correlation (16 modules)
- Simple Linear Regression
- Multiple Linear Regression
- Logistic Regression
- Correlation Analysis
- Robust Regression
- Poisson/Negative Binomial Regression
- Quasi-binomial/Quasi-Poisson Regression
- Zero-inflated Models
- Bayesian Regression
- Survival Analysis
- Cox Proportional Hazards
- Nonlinear Regression
- GAM (Generalized Additive Models)
- MANOVA
- Mediation/Moderation Analysis
- Propensity Score Analysis
- Bayesian Hierarchical Models

#### Advanced Analysis (20 modules)
- PCA (Principal Component Analysis)
- Cluster Analysis
- ROC Analysis
- Meta-Analysis
- Mixed Effects Models
- Survey Psychometrics
- Time Series Analysis
- STL Decomposition
- State Space Models
- Change Point Detection
- Spectral Analysis
- Network Analysis
- SEM (Structural Equation Modeling)
- Latent Class Analysis
- IRT (Item Response Theory)
- Multiple Imputation
- TSNE/UMAP
- Discriminant Analysis
- CCA (Canonical Correlation Analysis)
- MDS (Multidimensional Scaling)
- Competing Risks
- Correlation Heatmap
- Stratified Kaplan-Meier
- Advanced Survival Analysis

#### Machine Learning (4 modules)
- Supervised Machine Learning
- Unsupervised Machine Learning
- Model Comparison
- Feature Selection

#### Bootstrap and Permutation Tests (2 modules)
- Bootstrap Analysis
- Permutation Tests

## Running the Tests

### Prerequisites

1. Ensure all required R packages are installed
2. Make sure all calculation modules are properly sourced
3. Verify that sample data files are available in the `sample_data/` directory

### Running All Tests

```r
# From the project root directory
source("tests/test_modules.R")
```

### Running Individual Test Suites

```r
# Test basic statistics modules
source("tests/testthat/test_basic_statistics.R")

# Test inference modules
source("tests/testthat/test_inference_tests.R")

# Test regression and correlation modules
source("tests/testthat/test_regression_correlation.R")

# Test advanced analysis modules
source("tests/testthat/test_advanced_analysis.R")

# Test machine learning modules
source("tests/testthat/test_machine_learning.R")

# Test advanced statistics modules
source("tests/testthat/test_advanced_stats.R")
```

### Using testthat Framework

```r
# Run all tests using testthat
library(testthat)
test_dir("tests/testthat")
```

## Test Data

The tests use sample data files located in the `sample_data/` directory. Each module has a corresponding sample data file:

- `desc_stats.csv` - General purpose data for most tests
- `ttest_independent.csv` - Data for t-tests
- `anova_oneway.csv` - Data for ANOVA tests
- `regression_simple.csv` - Data for simple regression
- `regression_multiple.csv` - Data for multiple regression
- And many more...

If a specific sample data file is not available, the test will use a default dataset or create synthetic data.

## Test Results

### Output Files

After running the tests, the following files are generated:

- `test_results.rds` - Detailed results for each module
- `test_summary.rds` - Summary statistics of the test run

### Reading Results

```r
# Load test results
results <- readRDS("tests/test_results.rds")
summary <- readRDS("tests/test_summary.rds")

# View summary
print(summary)

# Check specific module results
print(results$"Descriptive Statistics")
```

### Test Output

The test runner provides real-time feedback:

```
Testing: Descriptive Statistics
  ✓ PASSED
Testing: Probability Distributions
  ✓ PASSED
Testing: Sample Size Estimation
  ✗ FAILED: Function not found
...
```

## What the Tests Verify

### Functionality Tests
- **Calculation Accuracy**: Verifies that statistical calculations produce correct results
- **Data Validation**: Ensures proper validation of input data
- **Error Handling**: Tests error handling for invalid inputs
- **Output Format**: Verifies consistent output structure

### Integration Tests
- **Module Loading**: Ensures all modules can be loaded without errors
- **Data Flow**: Tests data flow through the analysis pipeline
- **Output Consistency**: Verifies consistent output formats across modules

### Tab Structure Tests
- **Standard Tabs**: Verifies the presence of "Analysis", "Diagnostics", and "Uploaded Data" tabs
- **Tab Content**: Ensures each tab contains appropriate content
- **Navigation**: Tests tab navigation functionality

## Troubleshooting

### Common Issues

1. **"Function not found" errors**
   - Ensure all calculation modules are properly sourced
   - Check that function names match between server and calculation modules

2. **"File not found" errors**
   - Verify that sample data files exist in the `sample_data/` directory
   - Check file paths and permissions

3. **Package dependency errors**
   - Install missing R packages
   - Update package versions if needed

4. **Memory issues**
   - Reduce the number of bootstrap iterations or simulation runs
   - Use smaller sample datasets for testing

### Debugging

To debug specific module tests:

```r
# Test a single module
data <- load_test_data()
result <- test_calculation("desc_stats_analysis", data, names(data)[1])
print(result)
```

## Contributing

When adding new modules to CougarStats:

1. Create corresponding test functions in the appropriate test file
2. Add sample data if needed
3. Update the test data mapping
4. Run the full test suite to ensure compatibility

## Test Coverage

The test suite aims to achieve comprehensive coverage of:

- ✅ All 81 modules in CougarStats
- ✅ Standard tab structure implementation
- ✅ Data validation and error handling
- ✅ Calculation accuracy
- ✅ Output format consistency
- ✅ Integration between modules

## Performance

- **Test Duration**: Approximately 5-10 minutes for full test suite
- **Memory Usage**: Varies based on data size and analysis complexity
- **Parallel Execution**: Tests can be run in parallel for faster execution

## Continuous Integration

The test suite is designed to be integrated into CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
- name: Run CougarStats Tests
  run: |
    Rscript tests/test_modules.R
```

## Support

For issues with the test suite:

1. Check the troubleshooting section above
2. Review the test output for specific error messages
3. Verify that all dependencies are properly installed
4. Ensure sample data files are available and accessible

---

**Note**: This test suite is designed to work with the enhanced CougarStats application that includes the standard tab structure across all 81 modules. 