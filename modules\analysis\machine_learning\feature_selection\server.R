FeatureSelectionServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    fsData <- eventReactive(input$fsUserData, {
      handle_file_upload(input$fsUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(fsData(), {
      data <- fsData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, "fsResponse", choices = names(data), server = TRUE)
        updateSelectizeInput(session, "fsPredictors", choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    fsValidationErrors <- reactive({
      errors <- c()
      data <- fsData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$fsResponse) || input$fsResponse == "") {
        errors <- c(errors, "Please select a response variable.")
      }
      
      if (is.null(input$fsPredictors) || length(input$fsPredictors) < 2) {
        errors <- c(errors, "Please select at least 2 predictor variables for feature selection.")
      }
      
      if (!is.null(input$fsResponse) && !is.null(input$fsPredictors) && 
          input$fsResponse != "" && length(input$fsPredictors) >= 2) {
        
        # Check if response variable is in predictors
        if (input$fsResponse %in% input$fsPredictors) {
          errors <- c(errors, "Response variable cannot be included in predictors.")
        }
        
        # Check for sufficient observations
        n_obs <- nrow(data)
        n_vars <- length(input$fsPredictors) + 1  # +1 for response
        
        if (n_obs <= n_vars) {
          errors <- c(errors, sprintf("Number of observations (%d) must be greater than number of variables (%d).", n_obs, n_vars))
        }
        
        # Check for variance in response variable
        response_var <- data[[input$fsResponse]]
        if (sd(response_var, na.rm = TRUE) == 0) {
          errors <- c(errors, "Response variable must have variance (not all the same).")
        }
        
        # Check for variance in predictor variables
        for (var in input$fsPredictors) {
          if (sd(data[[var]], na.rm = TRUE) == 0) {
            errors <- c(errors, sprintf("Predictor variable '%s' must have variance (not all the same).", var))
          }
        }
      }
      
      # Check cross-validation folds
      if (!is.null(input$fsCVFolds) && (input$fsCVFolds < 2 || input$fsCVFolds > 20)) {
        errors <- c(errors, "Cross-validation folds must be between 2 and 20.")
      }
      
      errors
    })
    
    # Feature selection analysis reactive
    fsResult <- eventReactive(input$goFS, {
      data <- fsData()
      req(data, input$fsResponse, input$fsPredictors)
      
      # Remove rows with missing values
      complete_data <- data[complete.cases(data[, c(input$fsResponse, input$fsPredictors)]), ]
      
      if (nrow(complete_data) < 10) {
        stop("Insufficient complete cases for feature selection.")
      }
      
      # Prepare data
      x <- complete_data[, input$fsPredictors, drop = FALSE]
      y <- complete_data[[input$fsResponse]]
      
      # Set up cross-validation
      cv_folds <- ifelse(is.null(input$fsCVFolds), 5, input$fsCVFolds)
      
      # Perform feature selection
      tryCatch({
        if (requireNamespace("caret", quietly = TRUE)) {
          ctrl <- caret::trainControl(method = "cv", number = cv_folds)
          
          # Correlation-based feature selection
          cor_matrix <- cor(x, y)
          cor_importance <- abs(cor_matrix)
          cor_ranked <- data.frame(
            Variable = rownames(cor_matrix),
            Correlation = cor_matrix[, 1],
            Abs_Correlation = cor_importance[, 1],
            stringsAsFactors = FALSE
          )
          cor_ranked <- cor_ranked[order(cor_ranked$Abs_Correlation, decreasing = TRUE), ]
          
          # Stepwise selection using linear regression
          stepwise_result <- NULL
          tryCatch({
            full_model <- lm(y ~ ., data = data.frame(y = y, x))
            stepwise_result <- step(full_model, direction = "both", trace = 0)
            stepwise_vars <- names(coef(stepwise_result))[-1]  # Remove intercept
          }, error = function(e) {
            stepwise_vars <- character(0)
          })
          
          # Recursive Feature Elimination (RFE) if caret is available
          rfe_result <- NULL
          rfe_importance <- NULL
          tryCatch({
            if (requireNamespace("randomForest", quietly = TRUE)) {
              rfe_ctrl <- caret::rfeControl(functions = caret::rfFuncs, method = "cv", number = cv_folds)
              rfe_result <- caret::rfe(x, y, sizes = c(1:min(10, ncol(x))), rfeControl = rfe_ctrl)
              rfe_importance <- varImp(rfe_result)
            }
          }, error = function(e) {
            # Skip RFE if it fails
          })
          
          # Lasso regression for feature selection
          lasso_result <- NULL
          lasso_importance <- NULL
          tryCatch({
            if (requireNamespace("glmnet", quietly = TRUE)) {
              # Standardize predictors
              x_scaled <- scale(x)
              
              # Fit lasso regression
              lasso_fit <- glmnet::cv.glmnet(as.matrix(x_scaled), y, alpha = 1)
              
              # Get coefficients at optimal lambda
              lasso_coef <- coef(lasso_fit, s = "lambda.min")
              lasso_importance <- data.frame(
                Variable = rownames(lasso_coef),
                Coefficient = as.vector(lasso_coef),
                Abs_Coefficient = abs(as.vector(lasso_coef)),
                stringsAsFactors = FALSE
              )
              lasso_importance <- lasso_importance[lasso_importance$Abs_Coefficient > 0, ]
              lasso_importance <- lasso_importance[order(lasso_importance$Abs_Coefficient, decreasing = TRUE), ]
            }
          }, error = function(e) {
            # Skip lasso if it fails
          })
          
          # Combine results
          feature_importance <- list(
            correlation = cor_ranked,
            stepwise = stepwise_vars,
            rfe = rfe_importance,
            lasso = lasso_importance
          )
          
          # Select top features based on correlation
          n_top_features <- min(5, ncol(x))
          top_features <- cor_ranked$Variable[1:n_top_features]
          
          list(
            feature_importance = feature_importance,
            top_features = top_features,
            correlation_ranking = cor_ranked,
            stepwise_selected = stepwise_vars,
            rfe_selected = ifelse(!is.null(rfe_result), rfe_result$optVariables, character(0)),
            lasso_selected = ifelse(!is.null(lasso_importance), lasso_importance$Variable[lasso_importance$Abs_Coefficient > 0], character(0)),
            cv_folds = cv_folds,
            n_observations = nrow(complete_data),
            n_predictors = length(input$fsPredictors),
            response_variable = input$fsResponse,
            predictor_variables = input$fsPredictors
          )
          
        } else {
          stop("Package 'caret' is required for feature selection.")
        }
        
      }, error = function(e) {
        stop(e$message)
      })
    })
    
    # Error handling
    output$fsError <- renderUI({
      errors <- fsValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          fsResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "Feature Selection Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$fsModelSummary <- renderUI({
      req(fsResult())
      res <- fsResult()
      
      tagList(
        h4("Feature Selection Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Metric = c("Response Variable", "Number of Predictors", "Number of Observations", "Cross-Validation Folds"),
            Value = c(
              res$response_variable,
              res$n_predictors,
              res$n_observations,
              res$cv_folds
            )
          )
        }),
        h4("Top Features by Correlation"),
        renderTable({
          head(res$correlation_ranking, 10)
        }),
        h4("Feature Selection Results"),
        renderTable({
          data.frame(
            Method = c("Correlation", "Stepwise", "RFE", "Lasso"),
            Selected_Features = c(
              length(res$top_features),
              length(res$stepwise_selected),
              length(res$rfe_selected),
              length(res$lasso_selected)
            ),
            stringsAsFactors = FALSE
          )
        })
      )
    })
    
    output$fsPlot <- renderPlot({
      req(fsResult())
      res <- fsResult()
      
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # Correlation importance plot
      top_cor <- head(res$correlation_ranking, 10)
      barplot(top_cor$Abs_Correlation, names.arg = top_cor$Variable,
              main = "Feature Importance by Correlation", ylab = "|Correlation|",
              col = "lightblue", las = 2)
      
      # Correlation vs response
      plot(res$correlation_ranking$Correlation, res$correlation_ranking$Abs_Correlation,
           main = "Correlation vs Absolute Correlation",
           xlab = "Correlation", ylab = "|Correlation|", pch = 19, col = "blue")
      abline(h = 0, col = "red", lty = 2)
      abline(v = 0, col = "red", lty = 2)
      
      # Feature selection overlap
      methods <- c("Correlation", "Stepwise", "RFE", "Lasso")
      selected_counts <- c(
        length(res$top_features),
        length(res$stepwise_selected),
        length(res$rfe_selected),
        length(res$lasso_selected)
      )
      barplot(selected_counts, names.arg = methods,
              main = "Number of Selected Features by Method",
              ylab = "Number of Features", col = "lightgreen")
      
      # Correlation heatmap of top features
      if (length(res$top_features) >= 2) {
        top_data <- res$correlation_ranking[res$correlation_ranking$Variable %in% res$top_features, ]
        if (nrow(top_data) >= 2) {
          cor_matrix <- cor(res$data[, top_data$Variable, drop = FALSE])
          heatmap(cor_matrix, main = "Correlation Matrix of Top Features",
                  Rowv = NA, Colv = NA, scale = "none")
        } else {
          plot.new()
          title("Insufficient Features for Heatmap")
        }
      } else {
        plot.new()
        title("Insufficient Features for Heatmap")
      }
      
      par(mfrow = c(1, 1))
    })
    
    output$fsDiagnostics <- renderUI({
      req(fsResult())
      res <- fsResult()
      
      tagList(
        h4("Feature Selection Diagnostics"),
        renderTable({
          data.frame(
            Metric = c("Total Features", "Top Features Selected", "Selection Methods Used", "Average Correlation"),
            Value = c(
              res$n_predictors,
              length(res$top_features),
              sum(c(length(res$stepwise_selected) > 0, length(res$rfe_selected) > 0, length(res$lasso_selected) > 0)),
              round(mean(abs(res$correlation_ranking$Correlation)), 4)
            )
          )
        }),
        h4("Feature Selection Agreement"),
        renderTable({
          # Calculate agreement between methods
          all_selected <- unique(c(res$top_features, res$stepwise_selected, res$rfe_selected, res$lasso_selected))
          agreement_matrix <- data.frame(
            Method = c("Correlation", "Stepwise", "RFE", "Lasso"),
            Selected_Count = c(
              length(res$top_features),
              length(res$stepwise_selected),
              length(res$rfe_selected),
              length(res$lasso_selected)
            ),
            stringsAsFactors = FALSE
          )
          agreement_matrix
        })
      )
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$fsDataSummary <- renderUI({
      req(fsData(), input$fsResponse, input$fsPredictors)
      data <- fsData()
      response <- input$fsResponse
      predictors <- input$fsPredictors
      
      tagList(
        h4("Dataset Summary"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "Response Variable", "Number of Predictors", "Complete Cases"),
            Value = c(
              nrow(data),
              ncol(data),
              response,
              length(predictors),
              sum(complete.cases(data[, c(response, predictors)]))
            )
          )
        }),
        h4("Response Variable Summary"),
        renderTable({
          if (!is.null(response) && response != "") {
            response_data <- data[[response]]
            data.frame(
              Metric = c("Mean", "Median", "SD", "Min", "Max", "Missing"),
              Value = c(
                round(mean(response_data, na.rm = TRUE), 4),
                round(median(response_data, na.rm = TRUE), 4),
                round(sd(response_data, na.rm = TRUE), 4),
                round(min(response_data, na.rm = TRUE), 4),
                round(max(response_data, na.rm = TRUE), 4),
                sum(is.na(response_data))
              )
            )
          } else {
            data.frame(Metric = "N/A", Value = "N/A")
          }
        })
      )
    })
    
    output$fsAssumptions <- renderUI({
      req(fsResult())
      res <- fsResult()
      
      tagList(
        h4("Feature Selection Assumptions Check"),
        renderTable({
          data.frame(
            Assumption = c("Adequate Sample Size", "Variable Selection", "Cross-Validation", "Feature Diversity"),
            Status = c(
              ifelse(res$n_observations >= 30, "Pass", "Fail"),
              "Pass",
              "Pass",
              ifelse(res$n_predictors >= 3, "Pass", "Fail")
            ),
            Description = c(
              "Sufficient observations for reliable feature selection",
              "Appropriate variables selected for analysis",
              "Cross-validation used to prevent overfitting",
              "Multiple features available for selection"
            )
          )
        }),
        h4("Feature Selection Guidelines"),
        renderTable({
          data.frame(
            Method = c("Correlation", "Stepwise", "RFE", "Lasso"),
            Use_When = c(
              "Quick screening of feature importance",
              "Linear relationships, interpretable models",
              "Non-linear relationships, complex models",
              "High-dimensional data, regularization needed"
            ),
            Pros = c(
              "Simple, fast, interpretable",
              "Model-based, considers interactions",
              "Robust, handles non-linearity",
              "Handles multicollinearity, automatic"
            )
          )
        })
      )
    })
    
    output$fsDiagnosticPlots <- renderPlot({
      req(fsResult())
      res <- fsResult()
      
      par(mfrow = c(2, 2))
      
      # Correlation importance plot
      top_cor <- head(res$correlation_ranking, 10)
      barplot(top_cor$Abs_Correlation, names.arg = top_cor$Variable,
              main = "Feature Importance by Correlation", ylab = "|Correlation|",
              col = "lightblue", las = 2)
      
      # Correlation distribution
      hist(res$correlation_ranking$Correlation, main = "Distribution of Correlations",
           xlab = "Correlation", ylab = "Frequency", col = "lightgreen")
      
      # Feature selection overlap
      methods <- c("Correlation", "Stepwise", "RFE", "Lasso")
      selected_counts <- c(
        length(res$top_features),
        length(res$stepwise_selected),
        length(res$rfe_selected),
        length(res$lasso_selected)
      )
      barplot(selected_counts, names.arg = methods,
              main = "Number of Selected Features by Method",
              ylab = "Number of Features", col = "orange")
      
      # Correlation vs response
      plot(res$correlation_ranking$Correlation, res$correlation_ranking$Abs_Correlation,
           main = "Correlation vs Absolute Correlation",
           xlab = "Correlation", ylab = "|Correlation|", pch = 19, col = "purple")
      abline(h = 0, col = "red", lty = 2)
      abline(v = 0, col = "red", lty = 2)
      
      par(mfrow = c(1, 1))
    })
    
    # Uploaded Data Tab Outputs
    output$fsDataTable <- renderDT({
      req(fsData())
      data <- fsData()
      
      DT::datatable(
        data,
        options = list(
          pageLength = 10,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
        ),
        extensions = 'Buttons',
        filter = 'top',
        rownames = FALSE
      )
    })
    
    output$fsDataInfo <- renderUI({
      req(fsData())
      data <- fsData()
      
      tagList(
        h4("Dataset Information"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "File Name"),
            Value = c(nrow(data), ncol(data), 
                     ifelse(!is.null(input$fsUserData), input$fsUserData$name, "Unknown"))
          )
        }),
        h4("Variable Information"),
        renderTable({
          data.frame(
            Variable = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Unique = sapply(data, function(x) length(unique(x)))
          )
        })
      )
    })
    
    # Legacy outputs for backward compatibility
    output$fsSummary <- renderTable({
      req(fsResult())
      res <- fsResult()
      
      data.frame(
        Metric = c("Response Variable", "Number of Predictors", "Top Features", "CV Folds"),
        Value = c(
          res$response_variable,
          res$n_predictors,
          length(res$top_features),
          res$cv_folds
        )
      )
    })
  })
} 