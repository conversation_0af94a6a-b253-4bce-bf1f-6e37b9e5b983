# Multidimensional Scaling (MDS) calculation and output helpers

mds_uploadData_func <- function(mdsUserData) {
  ext <- tools::file_ext(mdsUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(mdsUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(mdsUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(mdsUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(mdsUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

mds_results_func <- function(data, mds_type = "classical", ...) {
  tryCatch({
    args <- list(...)
    
    results <- switch(mds_type,
      "classical" = do.call(perform_classical_mds, c(list(data = data), args)),
      "nonmetric" = do.call(perform_nonmetric_mds, c(list(data = data), args)),
      "sammon" = do.call(perform_sammon_mapping, c(list(data = data), args)),
      stop("Invalid MDS type specified.")
    )
    
    list(
      results = results,
      data = data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during MDS calculation:", e$message))
  })
}

mds_ht_html <- function(results) {
  tagList(
    h4(paste("Multidimensional Scaling (MDS):", results$results$method)),
    p("See summary table for stress and other diagnostics.")
  )
}

mds_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  out <- list(
    h4("MDS Summary"),
    p(paste("Stress value:", round(results$results$stress, 4)))
  )
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

mds_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  plot_data <- as.data.frame(results$results$points)
  names(plot_data) <- c("Dim1", "Dim2")
  p1 <- ggplot(plot_data, aes(x = Dim1, y = Dim2)) +
    geom_point(alpha = 0.7) +
    labs(title = "MDS Plot", x = "Dimension 1", y = "Dimension 2") +
    theme_minimal()
  print(p1)
  # Stress plot if available
  if (!is.null(results$results$stress)) {
    plot(rep(1, length(results$results$stress)), results$results$stress, type = 'b', main = 'Stress Plot', ylab = 'Stress', xlab = 'Iteration')
  }
}

# --- Helper functions from original file ---

perform_classical_mds <- function(data, method = "euclidean", k = 2, scale = TRUE) {
  
  if (!inherits(data, "dist")) {
    if (scale) {
      data <- scale(data)
    }
    dist_matrix <- dist(data, method = method)
  } else {
    dist_matrix <- data
  }
  
  mds_result <- cmdscale(dist_matrix, k = k, eig = TRUE)
  
  stress <- calculate_mds_stress(dist_matrix, mds_result$points)
  
  return(list(
    points = mds_result$points,
    eigenvalues = mds_result$eig,
    stress = stress,
    method = "Classical MDS"
  ))
}

perform_nonmetric_mds <- function(data, method = "euclidean", k = 2, scale = TRUE, max_iter = 100) {
  
  if (!require(MASS, quietly = TRUE)) {
    stop("MASS package is required for non-metric MDS")
  }
  
  if (!inherits(data, "dist")) {
    if (scale) {
      data <- scale(data)
    }
    dist_matrix <- dist(data, method = method)
  } else {
    dist_matrix <- data
  }
  
  mds_result <- MASS::isoMDS(dist_matrix, k = k, maxit = max_iter)
  
  return(list(
    points = mds_result$points,
    stress = mds_result$stress,
    method = "Non-metric MDS"
  ))
}

perform_sammon_mapping <- function(data, method = "euclidean", k = 2, scale = TRUE, max_iter = 100) {
  
  if (!require(MASS, quietly = TRUE)) {
    stop("MASS package is required for Sammon mapping")
  }
  
  if (!inherits(data, "dist")) {
    if (scale) {
      data <- scale(data)
    }
    dist_matrix <- dist(data, method = method)
  } else {
    dist_matrix <- data
  }
  
  mds_result <- MASS::sammon(dist_matrix, k = k, niter = max_iter)
  
  return(list(
    points = mds_result$points,
    stress = mds_result$stress,
    method = "Sammon Mapping"
  ))
}

calculate_mds_stress <- function(original_dist, mds_points) {
  mds_dist <- dist(mds_points)
  stress <- sqrt(sum((as.matrix(original_dist) - as.matrix(mds_dist))^2) / sum(as.matrix(original_dist)^2))
  return(stress)
}