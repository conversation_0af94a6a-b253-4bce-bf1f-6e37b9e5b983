quantileRegressionServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactives
    qrUploadData <- eventReactive(input$qrUserData, {
      handle_file_upload(input$qrUserData)
    })
    
    qrResults <- reactive({
      data <- qrUploadData()
      if (is.null(data) || is.null(input$qrResponse) || is.null(input$qrPredictors) || 
          length(input$qrPredictors) == 0) {
        return(NULL)
      }
      qrResults_func(
        TRUE,
        data,
        input$qrResponse,
        input$qrPredictors,
        input$qrQuantile
      )
    })
    
    # Validation errors
    qrValidationErrors <- reactive({
      errors <- c()
      data <- qrUploadData()
      
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$qrResponse) || input$qrResponse == "") {
        errors <- c(errors, "Please select a response variable.")
      }
      
      if (is.null(input$qrPredictors) || length(input$qrPredictors) == 0) {
        errors <- c(errors, "Please select at least one predictor variable.")
      }
      
      if (!is.null(input$qrResponse) && !is.null(input$qrPredictors) && 
          input$qrResponse %in% input$qrPredictors) {
        errors <- c(errors, "Response variable cannot be included in predictors.")
      }
      
      if (!is.null(input$qrResponse) && input$qrResponse %in% names(data)) {
        if (length(unique(data[[input$qrResponse]])) < 2) {
          errors <- c(errors, "Response variable must have at least two unique values.")
        }
      }
      
      if (input$qrQuantile <= 0 || input$qrQuantile >= 1) {
        errors <- c(errors, "Quantile must be between 0 and 1 (exclusive).")
      }
      
      errors
    })
    
    # Outputs
    output$qrResults <- renderUI({
      results <- qrResults()
      if (is.null(results)) return(NULL)
      quantileRegressionResults(qrResults, reactive({input$qrSigLvl}))
    })
    
    output$quantileRegressionPlot <- renderPlot({
      results <- qrResults()
      if (is.null(results)) return(NULL)
      quantileRegressionPlot(qrResults, reactive({input$qrSigLvl}))
    })
    
    output$qrDiagnostics <- renderUI({
      results <- qrResults()
      if (is.null(results)) return(NULL)
      quantileRegressionDiagnostics(qrResults, reactive({input$qrSigLvl}))
    })
    
    output$qrCoefficientsOutput <- renderUI({
      results <- qrResults()
      if (is.null(results)) return(NULL)
      quantileRegressionCoefficients(qrResults, reactive({input$qrSigLvl}))
    })
    
    output$qrCoefficientsTable <- DT::renderDT({
      results <- qrResults()
      if (is.null(results) || is.null(results$coefficients)) return(NULL)
      
      coef_df <- results$coefficients
      DT::datatable(coef_df,
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(coef_df)))))
    })
    
    output$renderQRData <- renderUI({
      req(qrUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("qrInitialUploadTable")), style = "width: 75%"),
        br(),
        br()
      )
    })
    
    output$qrInitialUploadTable <- DT::renderDT({
      req(qrUploadData())
      DT::datatable(qrUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(qrUploadData())))))
    })
    
    # Update selectizeInput choices after file upload
    observeEvent(qrUploadData(), {
      data <- qrUploadData()
      
      # Clear selectizeInputs and main panel
      updateSelectizeInput(session, 'qrResponse', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'qrPredictors', choices = character(0), selected = NULL, server = TRUE)
      output$quantileRegressionResults <- renderUI({ NULL })
      
      # If data is valid, update choices and show preview
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'qrResponse', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'qrPredictors', choices = names(data), server = TRUE)
        
        output$quantileRegressionResults <- renderUI({
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('qrPreviewTable'))
          )
        })
        
        output$qrPreviewTable <- DT::renderDT({
          head(data, 20)
        })
      }
    })
    
    # Show main results or error screen when Calculate is pressed
    observeEvent(input$goQuantileRegression, {
      output$quantileRegressionResults <- renderUI({
        errors <- qrValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Quantile Regression", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("qrTabset"),
              selected = "Analysis",
              tabPanel(
                id    = ns("qr"),
                title = "Analysis",
                titlePanel("Quantile Regression Results"),
                br(),
                uiOutput(ns('qrResults')),
                br(),
                plotOutput(ns('quantileRegressionPlot'), width = "100%", height = "500px"),
                br(),
                uiOutput(ns('qrDiagnostics'))
              ),
              tabPanel(
                id    = ns("qrCoef"),
                title = "Coefficients",
                DTOutput(ns("qrCoefficientsTable")),
                uiOutput(ns("qrCoefficientsOutput"))
              ),
              tabPanel(
                id    = ns("qrData"),
                title = "Uploaded Data",
                uiOutput(ns("renderQRData"))
              )
            )
          )
        }
      })
    })
  })
} 