# Outlier Detection calculation and output helpers

outlier_detection_uploadData_func <- function(odUserData) {
  ext <- tools::file_ext(odUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(odUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(odUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(odUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(odUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

outlier_detection_results_func <- function(data, vars, method = "boxplot", threshold = 3) {
  tryCatch({
    
    outliers <- lapply(vars, function(v) {
      vals <- data[[v]]
      
      out <- switch(method,
        "boxplot" = boxplot.stats(vals)$out,
        "zscore" = {
          z <- scale(vals)
          vals[abs(z) > threshold]
        },
        "iqr" = {
          q1 <- quantile(vals, 0.25, na.rm = TRUE)
          q3 <- quantile(vals, 0.75, na.rm = TRUE)
          iqr_val <- q3 - q1
          vals[vals < (q1 - 1.5 * iqr_val) | vals > (q3 + 1.5 * iqr_val)]
        },
        NA
      )
      
      list(variable = v, outliers = out)
    })
    
    list(
      outliers = outliers,
      data = data,
      vars = vars,
      method = method,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Outlier Detection:", e$message))
  })
}

outlier_detection_ht_html <- function(results) {
  num_outliers <- sum(sapply(results$outliers, function(x) length(x$outliers)))
  
  tagList(
    h4("Outlier Detection Summary"),
    p(paste("A total of", num_outliers, "outliers were detected using the", results$method, "method."))
  )
}

outlier_detection_summary_html <- function(results) {
  summary_df <- do.call(rbind, lapply(results$outliers, function(x) {
    data.frame(Variable = x$variable, Number_of_Outliers = length(x$outliers))
  }))
  
  tagList(
    h4("Outliers per Variable"),
    renderTable(summary_df)
  )
}

outlier_detection_plot <- function(results) {
  plot_data <- results$data[, results$vars, drop = FALSE]
  
  # Reshape data for ggplot
  plot_data_long <- tidyr::gather(plot_data, key = "variable", value = "value")
  
  ggplot(plot_data_long, aes(x = variable, y = value, fill = variable)) +
    geom_boxplot() +
    labs(title = paste("Outlier Detection -", results$method),
         x = "Variable", y = "Value") +
    theme_minimal()
}