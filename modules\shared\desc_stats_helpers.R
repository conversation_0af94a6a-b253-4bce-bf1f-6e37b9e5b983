# Shared descriptive statistics helpers

DecimalCount <- function(x) {
  stopifnot(is.numeric(x))
  if (grepl("\\.", x)) {
    x <- stringr::str_replace(x, "0+$", "")
    x <- stringr::str_replace(x, "^.+[.]", "")
    x <- stringr::str_length(x)
  } else {
    x <- 0
  }
  return(x)
}

Modes <- function(x) {
  modes <- Mode(x)
  if (anyNA(modes)) {return("No mode exists")}
  else if (length(modes) == 1) {return(paste(modes))}
  else if (length(modes) > 1) {
    modesList <- paste(modes[1])
    for(index in 2:length(modes)) {
      modesList <- paste0(modesList, ", ", modes[index])
    }
    return(modesList)
  }
}

Range <- function(min, max) {
  if(DecimalCount(min) < DecimalCount(max)) {
    numDigits <- DecimalCount(max)
  } else {
    numDigits <- DecimalCount(min)
  }
  range <- round((max - min), digits = numDigits)
  return(range)
}

GetQuartiles <- function(dat) {
  quartiles <- list()
  dat <- dat[order(dat)]
  if(length(dat) %% 2 != 0) { # remove median for odd lists
    dat <- dat[-ceiling(length(dat)/2)]
  }
  mid <- length(dat) / 2
  quartiles$q1 <- median(dat[1:mid])
  quartiles$q3 <- median(dat[(mid+1):length(dat)])
  return(quartiles)
}

GetOutliers <- function(dat, lower, upper) {
  outliers <- c()
  for(x in dat) {
    if(x < lower | x > upper) {
      outliers <-c(outliers, x)
    }
  }
  return(sort(outliers))
}

pop.sd <- function(x) {
  sqrt(sum((x-mean(x))^2)/length(x))
} 