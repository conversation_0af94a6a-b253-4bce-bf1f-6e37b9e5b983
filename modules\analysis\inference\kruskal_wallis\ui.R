kruskalWallisSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(
      inputId = ns("kwUserData"),
      label   = strong("Upload your Data (.csv or .xls or .xlsx or .txt)"),
      accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
    ),
    div(
      id = ns("kwUploadInputs"),
      radioButtons(
        inputId = ns("kwFormat"),
        label   = strong("Data Format"),
        choiceNames = c("Values in multiple columns", "Responses and factors stacked in two columns"),
        choiceValues = c("Multiple", "Stacked")
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Multiple'", ns("kwFormat")),
        selectizeInput(
          inputId = ns("kwMultiColumns"),
          label = strong("Choose columns to conduct analysis"),
          choices = c(""),
          multiple = TRUE,
          selected = NULL,
          options = list(hideSelected = FALSE, placeholder = 'Select two or more columns', onInitialize = I('function() { this.setValue(\"\"); }'))
        )
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Stacked'", ns("kwFormat")),
        selectizeInput(
          inputId = ns("kwResponse"),
          label = strong("Response Variable"),
          choices = c(""),
          selected = NULL,
          options = list(placeholder = 'Select a variable', onInitialize = I('function() { this.setValue(\"\"); }'))
        ),
        selectizeInput(
          inputId = ns("kwFactors"),
          label = strong("Factors"),
          choices = c(""),
          selected = NULL,
          options = list(placeholder = 'Select a factor', onInitialize = I('function() { this.setValue(\"\"); }'))
        )
      )
    ),
    radioButtons(
      inputId = ns("kwSigLvl"),
      label = strong("Significance Level (alpha)"),
      choices  = c("10%", "5%", "1%"),
      selected = "5%",
      inline   = TRUE
    ),
    br(),
    actionButton(ns("goInference"), label = "Calculate", class = "act-btn")
  )
}

kruskalWallisMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('kruskalWallisResults'))
  )
}

kruskalWallisUI <- function(id) {
  ns <- NS(id)
  tagList(
    kruskalWallisSidebarUI(id),
    kruskalWallisMainUI(id),
    tabsetPanel(
      id = ns("kwTabset"),
      selected = "Analysis",
      tabPanel(
        id    = ns("kw"),
        title = "Analysis",
        titlePanel("Hypothesis Test"),
        br(),
        uiOutput(ns('kwHT')),
        br(),
        plotOutput(ns('kruskalWallisPlot'), width = "50%", height = "400px"),
        br(),
        uiOutput(ns('kwConclusionOutput'))
      ),
      tabPanel(
        id    = ns("kwRM"),
        title = "Data table with Ranks",
        DTOutput(ns("renderrankedmean")),
        uiOutput(ns("renderKWRM"))
      ),
      tabPanel(
        id    = ns("kwData"),
        title = "Uploaded Data",
        uiOutput(ns("renderKWData"))
      )
    )
  )
} 