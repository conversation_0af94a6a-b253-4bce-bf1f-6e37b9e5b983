# Logistic Regression Server
logisticRegressionServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    logrUploadData <- eventReactive(input$logrUserData, {
      handle_file_upload(input$logrUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(logrUploadData(), {
      data <- logrUploadData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'logrResponse', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'logrExplanatory', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    logrValidationErrors <- reactive({
      errors <- c()
      
      data <- logrUploadData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$logrResponse) || input$logrResponse == "") {
        errors <- c(errors, "Please select a response variable.")
      }
      
      if (is.null(input$logrExplanatory) || length(input$logrExplanatory) == 0) {
        errors <- c(errors, "Please select at least one explanatory variable.")
      }
      
      if (!is.null(input$logrResponse) && !is.null(input$logrExplanatory) && 
          input$logrResponse != "" && length(input$logrExplanatory) > 0) {
        
        # Check if response variable is in explanatory variables
        if (input$logrResponse %in% input$logrExplanatory) {
          errors <- c(errors, "Response variable cannot be included in explanatory variables.")
        }
        
        # Check if response variable is binary
        response_var <- data[[input$logrResponse]]
        unique_vals <- unique(response_var)
        if (length(unique_vals) != 2) {
          errors <- c(errors, "Response variable must be binary (exactly 2 unique values).")
        }
        
        # Check if explanatory variables are numeric
        for (var in input$logrExplanatory) {
          if (!is.numeric(data[[var]])) {
            errors <- c(errors, sprintf("Explanatory variable '%s' must be numeric.", var))
          }
        }
        
        # Check for sufficient observations
        n_obs <- nrow(data)
        n_vars <- length(input$logrExplanatory) + 1  # +1 for response
        
        if (n_obs <= n_vars) {
          errors <- c(errors, sprintf("Number of observations (%d) must be greater than number of variables (%d).", n_obs, n_vars))
        }
        
        # Check for variance in explanatory variables
        for (var in input$logrExplanatory) {
          if (sd(data[[var]], na.rm = TRUE) == 0) {
            errors <- c(errors, sprintf("Explanatory variable '%s' must have variance (not all the same).", var))
          }
        }
      }
      
      errors
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goLogR, {
      output$logrResults <- renderUI({
        errors <- logrValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Logistic Regression", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("logrTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("logrAnalysis"),
                title = "Analysis",
                titlePanel("Logistic Regression Results"),
                br(),
                h4("Model Summary"),
                uiOutput(ns('logrModelSummary')),
                br(),
                h4("Coefficients"),
                tableOutput(ns('logrCoefficients')),
                br(),
                h4("Model Fit Statistics"),
                tableOutput(ns('logrModelFit')),
                br(),
                h4("Interpretation"),
                uiOutput(ns('logrInterpretation'))
              ),
              tabPanel(
                id = ns("logrDiagnostics"),
                title = "Diagnostics",
                h4("Residuals vs Fitted"),
                plotOutput(ns('logrResidualPlot')),
                br(),
                h4("Influence Plot"),
                plotOutput(ns('logrInfluencePlot')),
                br(),
                h4("Diagnostic Statistics"),
                tableOutput(ns('logrDiagnosticStats'))
              ),
              tabPanel(
                id = ns("logrUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                uiOutput(ns('logrDataTable'))
              )
            )
          )
        }
      })
    })
    
    # Additional outputs for the enhanced tabs
    output$logrModelSummary <- renderUI({
      data <- logrUploadData()
      if (is.null(data)) return(NULL)
      
      tagList(
        p("Response Variable: ", input$logrResponse),
        p("Explanatory Variables: ", paste(input$logrExplanatory, collapse = ", ")),
        p("Number of Observations: ", nrow(data)),
        p("Number of Predictors: ", length(input$logrExplanatory))
      )
    })
    
    output$logrCoefficients <- renderTable({
      # Placeholder for coefficients table
      data.frame(
        Predictor = c("Intercept", input$logrExplanatory),
        Estimate = c("N/A", rep("N/A", length(input$logrExplanatory))),
        Std_Error = c("N/A", rep("N/A", length(input$logrExplanatory))),
        Z_value = c("N/A", rep("N/A", length(input$logrExplanatory))),
        P_value = c("N/A", rep("N/A", length(input$logrExplanatory))),
        Odds_Ratio = c("N/A", rep("N/A", length(input$logrExplanatory))),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    output$logrModelFit <- renderTable({
      # Placeholder for model fit statistics
      data.frame(
        Statistic = c("AIC", "BIC", "Log-likelihood", "Deviance", "Pseudo R-squared"),
        Value = c("N/A", "N/A", "N/A", "N/A", "N/A"),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    output$logrInterpretation <- renderUI({
      tagList(
        h5("Logistic Regression Interpretation:"),
        p("Logistic regression models the probability of a binary outcome."),
        p("Coefficients represent the change in log-odds for a one-unit increase in the predictor."),
        p("Odds ratios represent the multiplicative change in odds for a one-unit increase in the predictor."),
        p("AIC and BIC are model selection criteria - lower values indicate better fit.")
      )
    })
    
    output$logrResidualPlot <- renderPlot({
      # Placeholder for residual plot
      plot.new()
      title("Residuals vs Fitted Values")
      text(0.5, 0.5, "Residual plot will appear here", cex = 1.2)
    })
    
    output$logrInfluencePlot <- renderPlot({
      # Placeholder for influence plot
      plot.new()
      title("Influence Plot")
      text(0.5, 0.5, "Influence plot will appear here", cex = 1.2)
    })
    
    output$logrDiagnosticStats <- renderTable({
      # Placeholder for diagnostic statistics
      data.frame(
        Statistic = c("Hosmer-Lemeshow Test", "Pearson Chi-square", "Deviance", "Max Cook's Distance"),
        Value = c("N/A", "N/A", "N/A", "N/A"),
        P_value = c("N/A", "N/A", "N/A", "N/A"),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    output$logrDataTable <- renderUI({
      req(logrUploadData())
      DT::DTOutput(ns("logrDataTableInner"))
    })
    
    output$logrDataTableInner <- DT::renderDT({
      req(logrUploadData())
      DT::datatable(logrUploadData(),
        options = list(pageLength = 25,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(logrUploadData())))))
    })
  })
} 