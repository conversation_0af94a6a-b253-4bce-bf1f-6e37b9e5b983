# Simple Linear Regression calculation and output helpers

simple_linear_regression_uploadData_func <- function(slrUserData, response_var, predictor_var) {
  tryCatch(
    {
      if (is.null(slrUserData) || is.null(response_var) || is.null(predictor_var)) {
        return(NULL)
      }
      
      # Read data based on file type
      if (grepl("\\.csv$", slrUserData$name, ignore.case = TRUE)) {
        df <- read.csv(slrUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.tsv$", slrUserData$name, ignore.case = TRUE)) {
        df <- read.tsv(slrUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.xlsx$", slrUserData$name, ignore.case = TRUE)) {
        df <- readxl::read_excel(slrUserData$datapath)
      } else {
        stop("Unsupported file type. Please upload a CSV, TSV, or XLSX file.")
      }
      
      # Check for required columns
      required_cols <- c(response_var, predictor_var)
      if (!all(required_cols %in% names(df))) {
        missing_cols <- required_cols[!required_cols %in% names(df)]
        stop(paste("The following columns were not found in the uploaded file:", paste(missing_cols, collapse = ", ")))
      }
      
      return(df)
    },
    error = function(e) {
      return(list(error = paste("Error reading data:", e$message)))
    }
  )
}

simple_linear_regression_results_func <- function(data, response_var, predictor_var) {
  tryCatch({
    if (is.null(data) || nrow(data) == 0) {
      stop("No data provided for analysis.")
    }
    
    formula_str <- as.formula(paste0("`", response_var, "` ~ `", predictor_var, "`"))
    model <- lm(formula_str, data = data)
    
    list(
      model = model,
      summary = summary(model),
      data = data,
      response_var = response_var,
      predictor_var = predictor_var,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Simple Linear Regression calculation:", e$message))
  })
}

simple_linear_regression_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  tagList(
    h4("Simple Linear Regression Model"),
    p("See summary table for model coefficients and diagnostics.")
  )
}

simple_linear_regression_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  renderPrint(results$summary)
}

simple_linear_regression_plot <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  
  if (!requireNamespace("ggplot2", quietly = TRUE)) {
    stop("Package 'ggplot2' is required for plotting.")
  }
  
  p1 <- ggplot(results$data, aes_string(x = results$predictor_var, y = results$response_var)) +
    geom_point(alpha = 0.5) +
    geom_smooth(method = "lm", se = TRUE, color = "blue") +
    labs(title = "Scatter Plot with Regression Line",
         x = results$predictor_var,
         y = results$response_var) +
    theme_minimal()
    
  # Diagnostic plots
  par(mfrow = c(2, 2))
  plot(results$model)
  par(mfrow = c(1, 1))
  
  # Return the ggplot object
  p1
}
