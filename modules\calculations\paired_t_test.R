# Paired t-test calculation and output helpers

paired_t_test_uploadData_func <- function(pttUserData) {
  ext <- tools::file_ext(pttUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(pttUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(pttUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(pttUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(pttUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

paired_t_test_results_func <- function(data, var1, var2, alternative = "two.sided", conf_level = 0.95) {
  tryCatch({
    x <- data[[var1]]
    y <- data[[var2]]
    
    if (length(x) != length(y)) {
      stop("Variables must have the same length.")
    }
    
    complete_cases <- complete.cases(x, y)
    x <- x[complete_cases]
    y <- y[complete_cases]
    
    if (length(x) < 2) {
      stop("At least 2 paired observations are required.")
    }
    
    test_result <- t.test(x, y, paired = TRUE, alternative = alternative, conf.level = conf_level)
    
    list(
      test = test_result,
      data = data.frame(x = x, y = y),
      var1 = var1,
      var2 = var2,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Paired t-test calculation:", e$message))
  })
}

paired_t_test_ht_html <- function(results, sigLvl) {
  test <- results$test
  p_value <- test$p.value
  
  conclusion <- if (p_value < sigLvl) "significant" else "not significant"
  
  withMathJax(tagList(
    h4("Paired t-test"),
    p(sprintf("The mean difference between %s and %s is %s.", results$var1, results$var2, conclusion)),
    p(sprintf("Mean of the differences: %.4f", test$estimate)),
    p(sprintf("t-statistic: %.4f", test$statistic)),
    p(sprintf("P-value: %.4f", p_value))
  ))
}

paired_t_test_summary_html <- function(results) {
  test <- results$test
  
  desc_stats <- data.frame(
    Variable = c(results$var1, results$var2),
    N = c(length(results$data$x), length(results$data$y)),
    Mean = c(mean(results$data$x), mean(results$data$y)),
    SD = c(sd(results$data$x), sd(results$data$y))
  )
  
  tagList(
    h4("Test Details"),
    renderPrint(test),
    h4("Descriptive Statistics"),
    renderTable(desc_stats, digits = 4)
  )
}

paired_t_test_plot <- function(results) {
  plot_data <- data.frame(
    Difference = results$data$x - results$data$y
  )
  
  ggplot(plot_data, aes(x = Difference)) +
    geom_histogram(bins = 20, fill = "steelblue", alpha = 0.7) +
    geom_vline(xintercept = 0, color = "red", linetype = "dashed") +
    labs(title = "Distribution of Differences",
         x = "Difference",
         y = "Frequency") +
    theme_minimal()
}