# Probability Distribution Analysis calculation and output helpers

prob_dist_uploadData_func <- function(pdUserData, variable) {
  tryCatch(
    {
      if (is.null(pdUserData) || is.null(variable)) {
        return(NULL)
      }
      
      # Read data based on file type
      if (grepl("\\.csv$", pdUserData$name, ignore.case = TRUE)) {
        df <- read.csv(pdUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.tsv$", pdUserData$name, ignore.case = TRUE)) {
        df <- read.tsv(pdUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.xlsx$", pdUserData$name, ignore.case = TRUE)) {
        df <- readxl::read_excel(pdUserData$datapath)
      } else {
        stop("Unsupported file type. Please upload a CSV, TSV, or XLSX file.")
      }
      
      if (!variable %in% names(df)) {
        stop(paste("Variable '", variable, "' not found in the uploaded file.", sep = ""))
      }
      
      # Return the selected variable as a numeric vector
      return(as.numeric(df[[variable]]))
    },
    error = function(e) {
      # Return a list with an error message
      return(list(error = paste("Error reading data:", e$message)))
    }
  )
}

# Main function to calculate probability distribution results
prob_dist_results_func <- function(distribution, x, ...) {
  params <- list(...)
  
  tryCatch({
    # Calculate based on distribution type
    result <- switch(
      tolower(distribution),
      "binomial" = calculate_binomial(x, params$n, params$p),
      "poisson" = calculate_poisson(x, params$lambda),
      "hypergeometric" = calculate_hypergeometric(x, params$m, params$n, params$k),
      "negative binomial" = calculate_negative_binomial(x, params$size, params$prob),
      "normal" = calculate_normal(x, params$mean, params$sd),
      stop("Unsupported distribution type")
    )
    
    # Add common properties
    result$distribution <- distribution
    result$parameters <- params
    result$x <- x
    
    # Add density/mass function values
    if (distribution %in% c("Binomial", "Poisson", "Hypergeometric", "Negative Binomial")) {
      result$pmf <- calculate_pmf(distribution, result, params)
      result$cdf <- calculate_cdf(distribution, result, params)
    } else {
      result$pdf <- calculate_pdf(distribution, result, params)
      result$cdf <- calculate_cdf(distribution, result, params)
    }
    
    # Add quantile function and random sample
    result$quantiles <- calculate_quantiles(distribution, result, params)
    result$random_sample <- generate_random_sample(distribution, result, params, 1000)
    
    # Add fit statistics if applicable
    if (distribution == "Normal" && !is.null(params$data)) {
      result$fit <- fitdistrplus::fitdist(params$data, "norm")
    }
    
    return(result)
    
  }, error = function(e) {
    return(list(error = paste("Error in", distribution, "distribution calculation:", e$message)))
  })
}

# Helper function to calculate PMF for discrete distributions
calculate_pmf <- function(distribution, result, params) {
  x_vals <- switch(distribution,
    "Binomial" = 0:params$n,
    "Poisson" = 0:(max(20, 2 * params$lambda)),
    "Hypergeometric" = 0:min(params$k, params$m),
    "Negative Binomial" = 0:(params$size * 3)
  )
  
  pmf_vals <- switch(distribution,
    "Binomial" = dbinom(x_vals, size = params$n, prob = params$p),
    "Poisson" = dpois(x_vals, lambda = params$lambda),
    "Hypergeometric" = dhyper(x_vals, m = params$m, n = params$n, k = params$k),
    "Negative Binomial" = dnbinom(x_vals, size = params$size, prob = params$prob)
  )
  
  return(list(x = x_vals, y = pmf_vals))
}

# Helper function to calculate CDF
calculate_cdf <- function(distribution, result, params) {
  x_vals <- seq(result$mean - 4 * sqrt(result$variance), 
               result$mean + 4 * sqrt(result$variance), 
               length.out = 100)
  
  cdf_vals <- switch(distribution,
    "Binomial" = pbinom(floor(x_vals), size = params$n, prob = params$p),
    "Poisson" = ppois(floor(x_vals), lambda = params$lambda),
    "Hypergeometric" = phyper(floor(x_vals), m = params$m, n = params$n, k = params$k),
    "Negative Binomial" = pnbinom(floor(x_vals), size = params$size, prob = params$prob),
    "Normal" = pnorm(x_vals, mean = params$mean, sd = params$sd)
  )
  
  return(list(x = x_vals, y = cdf_vals))
}

# Helper function to calculate PDF for continuous distributions
calculate_pdf <- function(distribution, result, params) {
  x_vals <- seq(result$mean - 4 * result$sd, 
               result$mean + 4 * result$sd, 
               length.out = 100)
  
  pdf_vals <- switch(distribution,
    "Normal" = dnorm(x_vals, mean = params$mean, sd = params$sd)
  )
  
  return(list(x = x_vals, y = pdf_vals))
}

# Helper function to calculate quantiles
calculate_quantiles <- function(distribution, result, params) {
  p_vals <- seq(0.01, 0.99, 0.01)
  
  quantile_vals <- switch(distribution,
    "Binomial" = qbinom(p_vals, size = params$n, prob = params$p),
    "Poisson" = qpois(p_vals, lambda = params$lambda),
    "Hypergeometric" = qhyper(p_vals, m = params$m, n = params$n, k = params$k),
    "Negative Binomial" = qnbinom(p_vals, size = params$size, prob = params$prob),
    "Normal" = qnorm(p_vals, mean = params$mean, sd = params$sd)
  )
  
  return(quantile_vals)
}

# Helper function to generate random samples
generate_random_sample <- function(distribution, result, params, n) {
  sample_vals <- switch(distribution,
    "Binomial" = rbinom(n, size = params$n, prob = params$p),
    "Poisson" = rpois(n, lambda = params$lambda),
    "Hypergeometric" = rhyper(nn = n, m = params$m, n = params$n, k = params$k),
    "Negative Binomial" = rnbinom(n, size = params$size, prob = params$prob),
    "Normal" = rnorm(n, mean = params$mean, sd = params$sd)
  )
  
  return(sample_vals)
}

# Distribution-specific calculation functions
calculate_binomial <- function(x, n, p) {
  list(
    probability = dbinom(x, size = n, prob = p),
    cumulative_prob = pbinom(x, size = n, prob = p),
    mean = n * p,
    variance = n * p * (1 - p),
    sd = sqrt(n * p * (1 - p)),
    skewness = (1 - 2 * p) / sqrt(n * p * (1 - p)),
    kurtosis = (1 - 6 * p * (1 - p)) / (n * p * (1 - p)),
    support = paste0("{", paste(0:n, collapse = ", "), "}"),
    n_parameters = 2
  )
}

calculate_poisson <- function(x, lambda) {
  list(
    probability = dpois(x, lambda = lambda),
    cumulative_prob = ppois(x, lambda = lambda),
    mean = lambda,
    variance = lambda,
    sd = sqrt(lambda),
    skewness = 1 / sqrt(lambda),
    kurtosis = 1 / lambda,
    support = "Non-negative integers",
    n_parameters = 1
  )
}

calculate_hypergeometric <- function(x, m, n, k) {
  N <- m + n
  p <- m / N
  
  list(
    probability = dhyper(x, m = m, n = n, k = k),
    cumulative_prob = phyper(x, m = m, n = n, k = k),
    mean = k * p,
    variance = k * p * (1 - p) * (N - k) / (N - 1),
    sd = sqrt(k * p * (1 - p) * (N - k) / (N - 1)),
    skewness = ((N - 2 * m) * sqrt(N - 1) * (N - 2 * k)) / 
               ((N - 2) * sqrt(k * m * n * (N - k))),
    support = paste0("{", paste(max(0, k - n):min(k, m), collapse = ", "), "}"),
    n_parameters = 3
  )
}

calculate_negative_binomial <- function(x, size, prob) {
  list(
    probability = dnbinom(x, size = size, prob = prob),
    cumulative_prob = pnbinom(x, size = size, prob = prob),
    mean = size * (1 - prob) / prob,
    variance = size * (1 - prob) / (prob^2),
    sd = sqrt(size * (1 - prob)) / prob,
    skewness = (2 - prob) / sqrt(size * (1 - prob)),
    kurtosis = 6 / size + prob^2 / (size * (1 - prob)),
    support = "Non-negative integers",
    n_parameters = 2
  )
}

calculate_normal <- function(x, mean, sd) {
  list(
    probability = dnorm(x, mean = mean, sd = sd),
    cumulative_prob = pnorm(x, mean = mean, sd = sd),
    mean = mean,
    variance = sd^2,
    sd = sd,
    skewness = 0,
    kurtosis = 0,
    support = "All real numbers",
    n_parameters = 2
  )
}

# HTML output functions
prob_dist_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(
      h4("Error"),
      p(results$error)
    ))
  }
  
  tagList(
    h4(paste("Probability Distribution Analysis:", results$distribution))
  )
}

# Summary output function
prob_dist_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  
  # Create a summary data frame
  summary_df <- data.frame(
    Metric = c("Distribution", "X Value", "Probability", "Cumulative Probability", 
              "Mean", "Variance", "Standard Deviation", "Skewness", "Kurtosis"),
    Value = c(
      results$distribution,
      results$x,
      round(results$probability, 6),
      round(results$cumulative_prob, 6),
      round(results$mean, 4),
      round(results$variance, 4),
      round(results$sd, 4),
      ifelse(is.numeric(results$skewness), round(results$skewness, 4), results$skewness),
      ifelse(is.numeric(results$kurtosis), round(results$kurtosis, 4), results$kurtosis)
    )
  )
  
  # Print the summary
  print(knitr::kable(summary_df, format = "simple"))
}

# Plot function
prob_dist_plot <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  
  tryCatch({
    # Set up the plot layout
    par(mfrow = c(2, 2))
    
    # Plot 1: PMF/PDF
    if (!is.null(results$pmf)) {
      plot(results$pmf$x, results$pmf$y, type = "h", lwd = 2, col = "blue",
           main = "Probability Mass Function", xlab = "x", ylab = "P(X = x)")
      points(results$pmf$x, results$pmf$y, pch = 16, col = "blue")
    } else if (!is.null(results$pdf)) {
      plot(results$pdf$x, results$pdf$y, type = "l", lwd = 2, col = "red",
           main = "Probability Density Function", xlab = "x", ylab = "f(x)")
    }
    
    # Plot 2: CDF
    if (!is.null(results$cdf)) {
      plot(results$cdf$x, results$cdf$y, type = "l", lwd = 2, col = "green",
           main = "Cumulative Distribution Function", xlab = "x", ylab = "F(x)")
    }
    
    # Plot 3: Quantile function
    if (!is.null(results$quantiles)) {
      p_vals <- seq(0.01, 0.99, 0.01)
      plot(p_vals, results$quantiles, type = "l", lwd = 2, col = "purple",
           main = "Quantile Function", xlab = "p", ylab = "Q(p)")
    }
    
    # Plot 4: Random sample histogram
    if (!is.null(results$random_sample)) {
      hist(results$random_sample, main = "Random Sample Distribution",
           xlab = "Value", ylab = "Density", col = "lightblue", freq = FALSE)
      if (results$distribution == "Normal") {
        curve(dnorm(x, mean = results$mean, sd = results$sd), 
              add = TRUE, col = "red", lwd = 2)
      }
    }
    
    # Reset plot layout
    par(mfrow = c(1, 1))
    
  }, error = function(e) {
    plot(1, 1, type = "n", xlab = "", ylab = "", axes = FALSE,
         main = "Error generating plots")
    text(1, 1, paste("Error:", e$message), col = "red")
  })
}