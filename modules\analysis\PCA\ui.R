PCAUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("pcaUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("pcaVars"), "Variables for PCA", choices = NULL, multiple = TRUE),
        checkboxInput(ns("pcaScale"), "Scale variables?", value = TRUE),
        checkboxInput(ns("pcaCenter"), "Center variables?", value = TRUE),
        br(),
        actionButton(ns("goPCA"), label = "Run PCA", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("pcaError")),
        plotOutput(ns("pcaScreePlot")),
        plotOutput(ns("pcaBiplot")),
        tableOutput(ns("pcaLoadings")),
        tableOutput(ns("pcaExplainedVariance"))
      )
    )
  )
} 