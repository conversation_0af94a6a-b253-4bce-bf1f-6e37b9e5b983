CompetingRisksServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    crData <- eventReactive(input$crUserData, {
      handle_file_upload(input$crUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(crData(), {
      data <- crData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'crTime', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'crEvent', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'crCause', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'crCovariates', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    crValidationErrors <- reactive({
      errors <- c()
      data <- crData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$crTime) || input$crTime == "") {
        errors <- c(errors, "Select a time variable.")
      }
      if (is.null(input$crEvent) || input$crEvent == "") {
        errors <- c(errors, "Select an event variable.")
      }
      if (is.null(input$crCause) || input$crCause == "") {
        errors <- c(errors, "Select a cause variable.")
      }
      
      # Check if time variable is numeric
      if (!is.null(input$crTime) && input$crTime != "" && !is.numeric(data[[input$crTime]])) {
        errors <- c(errors, "Time variable must be numeric.")
      }
      
      # Check if event variable has at least 2 values
      if (!is.null(input$crEvent) && input$crEvent != "") {
        event_values <- unique(data[[input$crEvent]])
        if (length(event_values) < 2) {
          errors <- c(errors, "Event variable must have at least 2 distinct values.")
        }
      }
      
      # Check for sufficient observations
      if (nrow(data) < 10) {
        errors <- c(errors, "Competing risks analysis requires at least 10 observations.")
      }
      
      errors
    })
    
    # Competing risks analysis reactive
    crResult <- eventReactive(input$goCR, {
      data <- crData()
      req(data, input$crTime, input$crEvent, input$crCause)
      
      # Remove rows with missing values
      vars_to_check <- c(input$crTime, input$crEvent, input$crCause)
      if (!is.null(input$crCovariates) && length(input$crCovariates) > 0) {
        vars_to_check <- c(vars_to_check, input$crCovariates)
      }
      
      complete_data <- data[complete.cases(data[, vars_to_check]), ]
      
      if (nrow(complete_data) < 10) {
        stop("Insufficient complete cases for competing risks analysis.")
      }
      
      # Perform competing risks analysis
      time <- complete_data[[input$crTime]]
      event <- complete_data[[input$crEvent]]
      cause <- complete_data[[input$crCause]]
      
      competing_risks_analysis(time, event, group = cause)
    })
    
    # Error handling
    output$crError <- renderUI({
      errors <- crValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          crResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "Competing Risks Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$crModelSummary <- renderUI({
      req(crResult())
      res <- crResult()
      
      tagList(
        h4("Competing Risks Analysis Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Metric = c("Number of Observations", "Number of Events", "Number of Causes", "Analysis Type"),
            Value = c(
              res$n_observations,
              res$n_events,
              res$n_causes,
              res$analysis_type
            )
          )
        }),
        h4("Event Summary by Cause"),
        renderTable({
          res$event_summary
        }),
        h4("Cumulative Incidence Functions"),
        renderTable({
          res$cif_summary
        })
      )
    })
    
    output$crPlot <- renderPlot({
      req(crResult())
      res <- crResult()
      
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # Cumulative incidence functions
      if (!is.null(res$cif_plot)) {
        plot(res$cif_plot, main = "Cumulative Incidence Functions",
             xlab = "Time", ylab = "Cumulative Incidence")
      }
      
      # Cause-specific hazard rates
      if (!is.null(res$hazard_rates)) {
        plot(res$hazard_rates, main = "Cause-Specific Hazard Rates",
             xlab = "Time", ylab = "Hazard Rate", type = "l")
      }
      
      # Event distribution by cause
      if (!is.null(res$event_distribution)) {
        barplot(res$event_distribution$Count, 
                names.arg = res$event_distribution$Cause,
                main = "Event Distribution by Cause", 
                ylab = "Count", col = "steelblue")
      }
      
      # Survival curves
      if (!is.null(res$survival_curves)) {
        plot(res$survival_curves, main = "Overall Survival Function",
             xlab = "Time", ylab = "Survival Probability")
      }
      
      par(mfrow = c(1, 1))
    })
    
    output$crDiagnostics <- renderUI({
      req(crResult())
      res <- crResult()
      
      tagList(
        h4("Competing Risks Diagnostics"),
        renderTable({
          data.frame(
            Metric = c("Gray's Test Statistic", "Gray's Test P-value", "Log-rank Test Statistic", "Log-rank Test P-value"),
            Value = c(
              round(res$gray_test_statistic, 3),
              round(res$gray_test_p_value, 4),
              round(res$logrank_test_statistic, 3),
              round(res$logrank_test_p_value, 4)
            )
          )
        }),
        h4("Cause-Specific Analysis"),
        renderTable({
          res$cause_specific_analysis
        }),
        h4("Fine-Gray Model Results"),
        renderTable({
          res$fine_gray_results
        })
      )
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$crDataSummary <- renderUI({
      req(crData(), input$crTime, input$crEvent, input$crCause)
      data <- crData()
      time_var <- input$crTime
      event_var <- input$crEvent
      cause_var <- input$crCause
      
      tagList(
        h4("Dataset Summary"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "Time Variable", "Event Variable", "Cause Variable", "Complete Cases"),
            Value = c(
              nrow(data),
              ncol(data),
              time_var,
              event_var,
              cause_var,
              sum(complete.cases(data[, c(time_var, event_var, cause_var)]))
            )
          )
        }),
        h4("Event Summary"),
        renderTable({
          event_table <- table(data[[event_var]])
          data.frame(
            Event_Status = names(event_table),
            Count = as.numeric(event_table),
            Proportion = round(as.numeric(prop.table(event_table)), 4),
            stringsAsFactors = FALSE
          )
        }),
        h4("Cause Summary"),
        renderTable({
          cause_table <- table(data[[cause_var]])
          data.frame(
            Cause = names(cause_table),
            Count = as.numeric(cause_table),
            Proportion = round(as.numeric(prop.table(cause_table)), 4),
            stringsAsFactors = FALSE
          )
        })
      )
    })
    
    output$crAssumptions <- renderUI({
      req(crResult())
      res <- crResult()
      
      tagList(
        h4("Competing Risks Assumptions Check"),
        renderTable({
          data.frame(
            Assumption = c("Independent Censoring", "No Informative Censoring", "Adequate Sample Size", "Valid Time Scale"),
            Status = c(
              ifelse(res$independent_censoring_test > 0.05, "Pass", "Fail"),
              ifelse(res$informative_censoring_test > 0.05, "Pass", "Fail"),
              ifelse(res$n_observations >= 10, "Pass", "Fail"),
              "Pass"
            ),
            Description = c(
              "Censoring is independent of event times",
              "No informative censoring mechanism",
              "Sufficient observations for stable estimates",
              "Time scale is appropriate for analysis"
            )
          )
        }),
        h4("Model Interpretation Guidelines"),
        renderTable({
          data.frame(
            Metric = c("Cumulative Incidence", "Cause-Specific Hazard", "Subdistribution Hazard"),
            Interpretation = c(
              "Probability of experiencing event by time t",
              "Instantaneous rate of event occurrence",
              "Hazard for subdistribution function"
            )
          )
        })
      )
    })
    
    output$crDiagnosticPlots <- renderPlot({
      req(crResult())
      res <- crResult()
      
      par(mfrow = c(2, 2))
      
      # Schoenfeld residuals plot
      if (!is.null(res$schoenfeld_residuals)) {
        plot(res$schoenfeld_residuals, main = "Schoenfeld Residuals",
             xlab = "Time", ylab = "Residuals")
        abline(h = 0, col = "red", lty = 2)
      }
      
      # Martingale residuals plot
      if (!is.null(res$martingale_residuals)) {
        plot(res$martingale_residuals, main = "Martingale Residuals",
             xlab = "Time", ylab = "Residuals")
        abline(h = 0, col = "red", lty = 2)
      }
      
      # Deviance residuals plot
      if (!is.null(res$deviance_residuals)) {
        plot(res$deviance_residuals, main = "Deviance Residuals",
             xlab = "Time", ylab = "Residuals")
        abline(h = 0, col = "red", lty = 2)
      }
      
      # Q-Q plot of residuals
      if (!is.null(res$residuals_qq)) {
        qqnorm(res$residuals_qq, main = "Q-Q Plot of Residuals")
        qqline(res$residuals_qq, col = "red")
      }
      
      par(mfrow = c(1, 1))
    })
    
    # Uploaded Data Tab Outputs
    output$crDataTable <- renderDT({
      req(crData())
      data <- crData()
      
      DT::datatable(
        data,
        options = list(
          pageLength = 10,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
        ),
        extensions = 'Buttons',
        filter = 'top',
        rownames = FALSE
      )
    })
    
    output$crDataInfo <- renderUI({
      req(crData())
      data <- crData()
      
      tagList(
        h4("Dataset Information"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "File Name"),
            Value = c(nrow(data), ncol(data), 
                     ifelse(!is.null(input$crUserData), input$crUserData$name, "Unknown"))
          )
        }),
        h4("Variable Information"),
        renderTable({
          data.frame(
            Variable = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Unique = sapply(data, function(x) length(unique(x)))
          )
        })
      )
    })
  })
} 