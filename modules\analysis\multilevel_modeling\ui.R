# UI Wrapper Function
multilevelModelingSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    h4("Data Selection"),
    selectInput(ns("mlm_response"), "Response Variable:", choices = NULL),
    selectInput(ns("mlm_predictors"), "Fixed Effects (Predictors):", choices = NULL, multiple = TRUE),
    selectInput(ns("mlm_random_effects"), "Random Effects (Grouping Variables):", choices = NULL, multiple = TRUE),
    selectInput(ns("mlm_level2_predictors"), "Level-2 Predictors (Optional):", choices = NULL, multiple = TRUE),
    checkboxInput(ns("mlm_random_slopes"), "Include Random Slopes", value = FALSE),
    conditionalPanel(
      condition = paste0("input['", ns("mlm_random_slopes"), "'] == true"),
      selectInput(ns("mlm_random_slope_vars"), "Variables with Random Slopes:", choices = NULL, multiple = TRUE)
    ),
    h4("Model Options"),
    selectInput(ns("mlm_family"), "Distribution Family:", 
               choices = c("Gaussian (Normal)" = "gaussian", 
                          "Binomial" = "binomial",
                          "Poisson" = "poisson",
                          "Gamma" = "gamma")),
    selectInput(ns("mlm_link"), "Link Function:", 
               choices = c("Identity" = "identity",
                          "Logit" = "logit", 
                          "Probit" = "probit",
                          "Log" = "log",
                          "Inverse" = "inverse")),
    numericInput(ns("mlm_alpha"), "Significance Level:", value = 0.05, min = 0.001, max = 0.999, step = 0.001),
    checkboxInput(ns("mlm_center_predictors"), "Center Predictors", value = TRUE),
    checkboxInput(ns("mlm_scale_predictors"), "Scale Predictors", value = FALSE),
    h4("Model Diagnostics"),
    checkboxInput(ns("mlm_residuals"), "Residual Plots", value = TRUE),
    checkboxInput(ns("mlm_qq_plot"), "Q-Q Plot", value = TRUE),
    checkboxInput(ns("mlm_icc"), "Intraclass Correlation", value = TRUE),
    checkboxInput(ns("mlm_random_effects_plot"), "Random Effects Plot", value = TRUE),
    checkboxInput(ns("mlm_model_comparison"), "Model Comparison", value = FALSE),
    actionButton(ns("run_mlm"), "Run Multilevel Model", class = "btn-primary btn-lg"),
    actionButton(ns("reset_mlm"), "Reset", class = "btn-secondary")
  )
}

multilevelModelingMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    h4("Results"),
    verbatimTextOutput(ns("mlm_summary")),
    plotOutput(ns("mlm_plots"), height = "600px"),
    downloadButton(ns("download_mlm_results"), "Download Results")
  )
}

multilevelModelingUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(multilevelModelingSidebarUI(id)),
    mainPanel(multilevelModelingMainUI(id))
  )
} 