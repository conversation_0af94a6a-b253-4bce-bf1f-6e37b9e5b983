NonlinearRegressionUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("nlrUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("nlrResponse"), "Response Variable", choices = NULL),
        selectizeInput(ns("nlrPredictors"), "Predictor Variables", choices = NULL, multiple = TRUE),
        textInput(ns("nlrFormula"), "Model Formula (e.g., y ~ a * exp(b * x))", value = ""),
        textInput(ns("nlrStart"), "Start Values (e.g., a=1, b=0.1)", value = ""),
        br(),
        actionButton(ns("goNLR"), label = "Run Nonlinear Regression", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("nlrError")),
        tableOutput(ns("nlrSummary")),
        plotOutput(ns("nlrPlot"))
      )
    )
  )
} 