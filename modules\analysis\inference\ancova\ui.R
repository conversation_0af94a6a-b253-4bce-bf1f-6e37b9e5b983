# ANCOVA UI
# Analysis of Covariance

AncovaSidebarUI <- function(id) {
  ns <- NS(id)
  
  tagList(
    # Data Input Method
    radioButtons(
      inputId = ns("ancovaDataMethod"),
      label = "Data Input Method:",
      choices = c("Manual Entry" = "Manual Entry", "Upload File" = "Upload File"),
      selected = "Manual Entry"
    ),
    
    # Manual Entry
    conditionalPanel(
      condition = "input.ancovaDataMethod == 'Manual Entry'",
      ns = ns,
      textAreaInput(
        inputId = ns("ancovaData"),
        label = "Enter Response Values (comma, space, or newline separated):",
        placeholder = "1, 2, 3, 4, 5, 6",
        rows = 4
      ),
      textAreaInput(
        inputId = ns("ancovaFactorData"),
        label = "Enter Factor Values (comma, space, or newline separated):",
        placeholder = "A, A, B, B, A, B",
        rows = 4
      ),
      textAreaInput(
        inputId = ns("ancovaCovariateData"),
        label = "Enter Covariate Values (comma, space, or newline separated):",
        placeholder = "10, 12, 11, 13, 9, 14",
        rows = 4
      )
    ),
    
    # File Upload
    conditionalPanel(
      condition = "input.ancovaDataMethod == 'Upload File'",
      ns = ns,
      fileInput(
        inputId = ns("ancovaUserData"),
        label = "Upload Data File:",
        accept = c(".csv", ".txt", ".xlsx", ".xls"),
        buttonLabel = "Browse Files",
        placeholder = "No file selected"
      ),
      selectizeInput(
        inputId = ns("ancovaResponseVariable"),
        label = "Select Response Variable:",
        choices = NULL,
        options = list(placeholder = "Select response variable...")
      ),
      selectizeInput(
        inputId = ns("ancovaFactorVariable"),
        label = "Select Factor Variable:",
        choices = NULL,
        options = list(placeholder = "Select factor variable...")
      ),
      selectizeInput(
        inputId = ns("ancovaCovariateVariable"),
        label = "Select Covariate Variable:",
        choices = NULL,
        options = list(placeholder = "Select covariate variable...")
      )
    ),
    
    # Test Parameters
    numericInput(
      inputId = ns("ancovaConfLevel"),
      label = "Confidence Level:",
      value = 0.95,
      min = 0.5,
      max = 0.99,
      step = 0.01
    ),
    
    actionButton(
      inputId = ns("goAncova"),
      label = "Calculate ANCOVA",
      class = "btn-primary",
      style = "width: 100%;"
    ),
    
    br(),
    br(),
    
    # Help text
    helpText(
      "ANCOVA combines ANOVA and regression analysis.",
      "It tests group differences while controlling for a continuous covariate.",
      "This increases statistical power and reduces error variance."
    )
  )
}

AncovaMainUI <- function(id) {
  ns <- NS(id)
  
  tagList(
    uiOutput(ns("ancovaResults"))
  )
} 