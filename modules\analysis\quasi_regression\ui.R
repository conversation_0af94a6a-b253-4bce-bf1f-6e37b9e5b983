QuasiRegressionSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("quasiUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
    selectInput(ns("quasiModelType"), "Model Type", choices = c("Quasi-binomial", "Quasi-Poisson")),
    selectizeInput(ns("quasiResponse"), "Response Variable", choices = NULL),
    selectizeInput(ns("quasiPredictors"), "Predictor Variables", choices = NULL, multiple = TRUE),
    br(),
    actionButton(ns("goQuasi"), label = "Fit Model", class = "act-btn")
  )
}

QuasiRegressionMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns("quasiError")),
    tableOutput(ns("quasiResults")),
    plotOutput(ns("quasiPlot"))
  )
}

QuasiRegressionUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(QuasiRegressionSidebarUI(id)),
    mainPanel(QuasiRegressionMainUI(id))
  )
} 