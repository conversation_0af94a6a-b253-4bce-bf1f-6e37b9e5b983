# Survival Analysis UI
SurvivalAnalysisUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(
      h5("Data Input"),
      fileInput(ns("survUserData"), "Upload your Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
      selectizeInput(ns("survTime"), "Time-to-Event Column", choices = NULL),
      selectizeInput(ns("survEvent"), "Event Occurred Column (1=event, 0=censored)", choices = NULL),
      selectizeInput(ns("survGroup"), "Group Column (optional)", choices = NULL),
      hr(),
      h5("Analysis Method"),
      selectInput(ns("survMethod"), "Survival Method", 
                  choices = c("Kaplan-Meier" = "<PERSON>-Meier",
                             "Cox Regression" = "Cox Regression",
                             "Parametric Models" = "Parametric Models",
                             "Competing Risks" = "Competing Risks",
                             "Frailty Models" = "Frailty Models"),
                  selected = "<PERSON>-Meier"),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Cox Regression' || input['%s'] == 'Parametric Models' || input['%s'] == 'Frailty Models'", 
                           ns("survMethod"), ns("survMethod"), ns("survMethod")),
        selectizeInput(ns("survCovariates"), "Covariates", choices = NULL, multiple = TRUE),
        helpText("Select predictor variables for regression models")
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Parametric Models'", ns("survMethod")),
        selectInput(ns("survDistribution"), "Distribution", 
                    choices = c("Weibull" = "weibull", "Exponential" = "exponential", 
                               "Log-normal" = "lognormal", "Log-logistic" = "loglogistic"),
                    selected = "weibull")
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Frailty Models'", ns("survMethod")),
        selectInput(ns("survFrailtyDist"), "Frailty Distribution", 
                    choices = c("Gamma" = "gamma", "Inverse Gaussian" = "invgauss"),
                    selected = "gamma")
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Cox Regression'", ns("survMethod")),
        selectizeInput(ns("survStratify"), "Stratification Variables", choices = NULL, multiple = TRUE),
        helpText("Select variables for stratified Cox regression")
      ),
      hr(),
      h5("Analysis Options"),
      selectInput(ns("survConfLevel"), "Confidence Level", 
                  choices = c("90%" = 0.90, "95%" = 0.95, "99%" = 0.99), 
                  selected = 0.95),
      checkboxInput(ns("survShowDiagnostics"), "Show Diagnostic Plots", value = TRUE),
      checkboxInput(ns("survShowAdvanced"), "Show Advanced Analysis", value = FALSE),
      br(),
      actionButton(ns("goSurv"), label = "Run Survival Analysis", class = "act-btn")
    ),
    mainPanel(
      textOutput(ns('survError')),
      plotOutput(ns('survPlot')),
      tableOutput(ns('survSummary')),
      plotOutput(ns('survDiagnostics')),
      tableOutput(ns('survAdvanced'))
    )
  )
} 