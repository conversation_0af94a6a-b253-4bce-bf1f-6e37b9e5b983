# Mixed ANOVA Server
# Between and within-subjects factors

source("modules/calculations/mixed_anova.R")

MixedAnovaServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactive values
    mixed_data <- reactiveVal(NULL)
    mixed_results <- reactiveVal(NULL)
    
    # File upload reactive
    mixedUploadData <- eventReactive(input$mixedUserData, {
      handle_file_upload(input$mixedUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(mixedUploadData(), {
      data <- mixedUploadData()
      # Clear selectizeInputs and main panel
      updateSelectizeInput(session, 'mixedDependentVariable', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'mixedBetweenFactor', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'mixedWithinFactor', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'mixedSubjectID', choices = character(0), selected = NULL, server = TRUE)
      output$mixedResults <- renderUI({ NULL })
      
      # If data is valid, update choices
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'mixedDependentVariable', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'mixedBetweenFactor', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'mixedWithinFactor', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'mixedSubjectID', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    mixedValidationErrors <- reactive({
      errors <- c()
      
      if (input$mixedDataMethod == "Manual Entry") {
        # Manual entry validation
        if (is.null(input$mixedManualData) || input$mixedManualData == "") {
          errors <- c(errors, "Manual data entry is required.")
        } else {
          # Basic validation for manual entry
          tryCatch({
            data <- parse_mixed_data(input$mixedManualData)
            if (nrow(data) < 6) {
              errors <- c(errors, "At least 6 observations are required for mixed ANOVA.")
            }
          }, error = function(e) {
            errors <- c(errors, "Invalid manual data format. Please check your input.")
          })
        }
      } else {
        # File upload validation
        data <- mixedUploadData()
        if (is.null(data) || !is.data.frame(data)) {
          errors <- c(errors, "No data uploaded or file could not be read.")
          return(errors)
        }
        if (is.null(input$mixedDependentVariable) || input$mixedDependentVariable == "") {
          errors <- c(errors, "Please select a dependent variable.")
        } else {
          var_data <- data[[input$mixedDependentVariable]]
          if (!is.numeric(var_data)) {
            errors <- c(errors, "Dependent variable must be numeric.")
          }
          if (length(na.omit(var_data)) < 6) {
            errors <- c(errors, "At least 6 non-missing observations are required.")
          }
        }
        if (is.null(input$mixedBetweenFactor) || input$mixedBetweenFactor == "") {
          errors <- c(errors, "Please select a between-subjects factor.")
        }
        if (is.null(input$mixedWithinFactor) || input$mixedWithinFactor == "") {
          errors <- c(errors, "Please select a within-subjects factor.")
        }
        if (is.null(input$mixedSubjectID) || input$mixedSubjectID == "") {
          errors <- c(errors, "Please select a subject ID variable.")
        }
      }
      
      errors
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goMixedAnova, {
      output$mixedResults <- renderUI({
        errors <- mixedValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Mixed ANOVA", errors = errors)
        } else {
          tryCatch({
            # Prepare data
            if (input$mixedDataMethod == "Manual Entry") {
              # Parse manual entry data
              data <- parse_mixed_data(input$mixedManualData)
            } else {
              # Use uploaded data
              req(mixedUploadData(), input$mixedDependentVariable, input$mixedBetweenFactor, 
                  input$mixedWithinFactor, input$mixedSubjectID)
              
              data <- mixedUploadData()
              dependent_var <- input$mixedDependentVariable
              between_factor <- input$mixedBetweenFactor
              within_factor <- input$mixedWithinFactor
              subject_id <- input$mixedSubjectID
              
              # Select relevant columns
              data <- data[c(subject_id, dependent_var, between_factor, within_factor)]
              names(data) <- c("Subject", "Dependent", "Between", "Within")
            }
            
            # Remove rows with missing values
            complete_cases <- complete.cases(data)
            if (sum(complete_cases) < nrow(data)) {
              warning("Removing rows with missing values")
              data <- data[complete_cases, ]
            }
            
            # Convert factors
            data$Subject <- as.factor(data$Subject)
            data$Between <- as.factor(data$Between)
            data$Within <- as.factor(data$Within)
            
            # Check data structure
            if (length(unique(data$Subject)) < 3) {
              stop("At least 3 subjects are required")
            }
            
            if (length(unique(data$Between)) < 2) {
              stop("At least 2 levels required for between-subjects factor")
            }
            
            if (length(unique(data$Within)) < 2) {
              stop("At least 2 levels required for within-subjects factor")
            }
            
            # Perform mixed ANOVA
            results <- perform_mixed_anova(data, 
                                         sphericity_test = input$mixedSphericityTest,
                                         greenhouse_geisser = input$mixedGreenhouseGeisser,
                                         huynh_feldt = input$mixedHuynhFeldt,
                                         effect_size = input$mixedEffectSize,
                                         conf_level = input$mixedConfLevel)
            
            # Store results
            mixed_results(results)
            
            # Display results
            tagList(
              h3("Mixed ANOVA Results"),
              
              # Data summary
              h4("Data Summary"),
              renderTable({
                summary_table <- data.frame(
                  Statistic = c("Total Subjects", "Between-Subjects Groups", "Within-Subjects Levels", 
                               "Total Observations", "Missing Values"),
                  Value = c(results$n_subjects, results$n_between_groups, results$n_within_levels,
                           results$n_observations, results$n_missing)
                )
                summary_table
              }, rownames = FALSE),
              
              br(),
              
              # ANOVA table
              h4("ANOVA Results"),
              renderDataTable({
                anova_table <- results$anova_table
                datatable(anova_table, 
                         options = list(pageLength = 10, scrollX = TRUE),
                         rownames = FALSE) %>%
                  formatRound(columns = c("SS", "MS", "F", "p_value", "Partial_Eta_Squared"), digits = 4)
              }),
              
              br(),
              
              # Sphericity test
              if (!is.null(results$sphericity_test)) tagList(
                h4("Sphericity Test Results"),
                renderTable({
                  sphericity_table <- results$sphericity_test
                  sphericity_table
                }, rownames = FALSE)
              ),
              
              # Greenhouse-Geisser correction
              if (!is.null(results$greenhouse_geisser)) tagList(
                h4("Greenhouse-Geisser Correction"),
                renderTable({
                  gg_table <- results$greenhouse_geisser
                  gg_table
                }, rownames = FALSE)
              ),
              
              # Huynh-Feldt correction
              if (!is.null(results$huynh_feldt)) tagList(
                h4("Huynh-Feldt Correction"),
                renderDataTable({
                  hf_table <- results$huynh_feldt
                  datatable(hf_table, 
                           options = list(pageLength = 10, scrollX = TRUE),
                           rownames = FALSE) %>%
                    formatRound(columns = c("F", "p_value", "Partial_Eta_Squared"), digits = 4)
                }),
                
                br()
              ),
              
              # Descriptive statistics
              h4("Descriptive Statistics"),
              renderDataTable({
                desc_table <- results$descriptive_stats
                datatable(desc_table, 
                         options = list(pageLength = 10, scrollX = TRUE),
                         rownames = FALSE) %>%
                  formatRound(columns = c("Mean", "SD", "SE", "CI_Lower", "CI_Upper"), digits = 3)
              }),
              
              br(),
              
              # Interaction plot
              h4("Interaction Plot"),
              renderPlot({
                ggplot(results$plot_data, aes(x = Within, y = Mean, group = Between, color = Between)) +
                  geom_line(size = 1) +
                  geom_point(size = 3) +
                  geom_errorbar(aes(ymin = Mean - SE, ymax = Mean + SE), width = 0.1) +
                  labs(title = "Interaction Plot",
                       x = "Within-Subjects Factor", y = "Mean Response",
                       color = "Between-Subjects Factor") +
                  theme_minimal() +
                  theme(legend.position = "bottom")
              }),
              
              br(),
              
              # Post-hoc tests
              if (!is.null(results$post_hoc)) tagList(
                h4("Post-Hoc Tests"),
                renderDataTable({
                  post_hoc_table <- results$post_hoc
                  datatable(post_hoc_table, 
                           options = list(pageLength = 10, scrollX = TRUE),
                           rownames = FALSE) %>%
                    formatRound(columns = c("Difference", "SE", "t", "p_value", "Cohen_d"), digits = 4)
                }),
                
                br()
              ),
              
              # Assumptions
              h4("Model Assumptions"),
              renderUI({
                tagList(
                  p(strong("Normality:"), "Residuals should be normally distributed."),
                  p(strong("Sphericity:"), "Variance-covariance matrix should be spherical (for within-subjects factors)."),
                  p(strong("Independence:"), "Observations should be independent."),
                  p(strong("Homogeneity:"), "Equal variances across groups (for between-subjects factors)."),
                  br(),
                  p(strong("Note:"), "Greenhouse-Geisser and Huynh-Feldt corrections are applied when sphericity is violated.")
                )
              })
            )
          }, error = function(e) {
            errorScreenUI(title = "Error in Mixed ANOVA Analysis", errors = e$message)
          })
        }
      })
    })
  })
} 