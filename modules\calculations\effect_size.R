# Effect Size Calculation Helpers

# Main function to calculate effect sizes
effect_size_results_func <- function(group1, group2, type = c("cohens_d", "hedges_g", "glass_delta", "pearson_r")) {
  tryCatch({
    # Input validation
    if (missing(group1) || missing(group2)) {
      stop("Both group1 and group2 must be provided")
    }
    
    # Remove NAs
    group1 <- na.omit(as.numeric(group1))
    group2 <- na.omit(as.numeric(group2))
    
    if (length(group1) < 2 || length(group2) < 2) {
      stop("Each group must have at least 2 observations")
    }
    
    # Basic statistics
    n1 <- length(group1)
    n2 <- length(group2)
    m1 <- mean(group1)
    m2 <- mean(group2)
    s1 <- sd(group1)
    s2 <- sd(group2)
    
    # Pooled standard deviation
    s_pooled <- sqrt(((n1 - 1) * s1^2 + (n2 - 1) * s2^2) / (n1 + n2 - 2))
    
    # Calculate effect sizes
    result <- list()
    
    # <PERSON>'s d
    if (any(c("cohens_d", "all") %in% type)) {
      d <- (m1 - m2) / s_pooled
      result$cohens_d <- d
      
      # Confidence interval for <PERSON>'s d
      df <- n1 + n2 - 2
      ci_d <- d + c(-1, 1) * qt(0.975, df) * sqrt((n1 + n2) / (n1 * n2) + d^2 / (2 * (n1 + n2)))
      result$cohens_d_ci <- ci_d
    }
    
    # Hedges' g (bias-corrected Cohen's d)
    if (any(c("hedges_g", "all") %in% type)) {
      g <- d * (1 - 3 / (4 * (n1 + n2) - 9))
      result$hedges_g <- g
      
      # Confidence interval for Hedges' g
      ci_g <- ci_d * (1 - 3 / (4 * (n1 + n2) - 9))
      result$hedges_g_ci <- ci_g
    }
    
    # Glass's delta
    if (any(c("glass_delta", "all") %in% type)) {
      glass_delta <- (m1 - m2) / s2  # Using group 2 as control
      result$glass_delta <- glass_delta
    }
    
    # Pearson's r (correlation)
    if (any(c("pearson_r", "all") %in% type) && length(group1) == length(group2)) {
      r <- cor(group1, group2)
      result$pearson_r <- r
      
      # Fisher's z transformation for CI
      if (length(group1) > 3) {
        z <- 0.5 * log((1 + r) / (1 - r))
        se_z <- 1 / sqrt(length(group1) - 3)
        ci_z <- z + c(-1, 1) * qnorm(0.975) * se_z
        ci_r <- (exp(2 * ci_z) - 1) / (exp(2 * ci_z) + 1)
        result$pearson_r_ci <- ci_r
      }
    }
    
    # Add group statistics
    result$group_stats <- list(
      group1 = list(n = n1, mean = m1, sd = s1, min = min(group1), max = max(group1)),
      group2 = list(n = n2, mean = m2, sd = s2, min = min(group2), max = max(group2))
    )
    
    # Add test information
    result$test_info <- list(
      type = "Effect Size Calculation",
      timestamp = Sys.time(),
      r_version = R.version.string
    )
    
    return(result)
    
  }, error = function(e) {
    return(list(error = paste("Error in effect size calculation:", e$message)))
  })
}

# Function to calculate odds ratio
calculate_odds_ratio <- function(x, y) {
  tryCatch({
    if (length(unique(x)) != 2 || length(unique(y)) != 2) {
      stop("Both variables must be binary (2 levels)")
    }
    
    tab <- table(x, y)
    if (any(dim(tab) != 2)) {
      stop("Contingency table must be 2x2")
    }
    
    or <- (tab[1,1] * tab[2,2]) / (tab[1,2] * tab[2,1])
    
    # Log odds ratio for CI
    log_or <- log(or)
    se_log_or <- sqrt(sum(1/tab))
    ci_log_or <- log_or + c(-1, 1) * qnorm(0.975) * se_log_or
    ci_or <- exp(ci_log_or)
    
    return(list(
      odds_ratio = or,
      odds_ratio_ci = ci_or,
      log_odds_ratio = log_or,
      se_log_odds_ratio = se_log_or
    ))
    
  }, error = function(e) {
    return(list(error = paste("Error in odds ratio calculation:", e$message)))
  })
}

# Function to calculate eta squared
calculate_eta_squared <- function(x, group) {
  tryCatch({
    if (length(unique(group)) < 2) {
      stop("Grouping variable must have at least 2 levels")
    }
    
    fit <- aov(x ~ as.factor(group))
    ss_effect <- summary(fit)[[1]]["as.factor(group)", "SumSq"]
    ss_total <- sum(summary(fit)[[1]][, "SumSq"])
    eta_sq <- ss_effect / ss_total
    
    # Partial eta squared
    ss_error <- sum(summary(fit)[[1]]["Residuals", "SumSq"])
    partial_eta_sq <- ss_effect / (ss_effect + ss_error)
    
    return(list(
      eta_squared = eta_sq,
      partial_eta_squared = partial_eta_sq,
      ss_effect = ss_effect,
      ss_total = ss_total,
      ss_error = ss_error
    ))
    
  }, error = function(e) {
    return(list(error = paste("Error in eta squared calculation:", e$message)))
  })
}

# Helper function to handle file uploads
effect_size_uploadData_func <- function(uploaded_file, group1_var = NULL, group2_var = NULL) {
  tryCatch({
    if (is.null(uploaded_file)) {
      return(NULL)
    }
    
    # Read file based on extension
    ext <- tools::file_ext(uploaded_file$name)
    data <- switch(
      tolower(ext),
      csv = read.csv(uploaded_file$datapath, stringsAsFactors = FALSE),
      tsv = read.delim(uploaded_file$datapath, stringsAsFactors = FALSE),
      xlsx = readxl::read_excel(uploaded_file$datapath),
      stop("Unsupported file type. Please upload a CSV, TSV, or XLSX file.")
    )
    
    # Convert to data.frame if it's a tibble
    if (inherits(data, "tbl_df")) {
      data <- as.data.frame(data)
    }
    
    # If group variables are specified, extract them
    if (!is.null(group1_var) && !is.null(group2_var)) {
      if (!all(c(group1_var, group2_var) %in% names(data))) {
        stop("One or both specified variables not found in the uploaded data")
      }
      
      # Return list with data and selected variables
      return(list(
        data = data,
        group1 = na.omit(as.numeric(data[[group1_var]])),
        group2 = na.omit(as.numeric(data[[group2_var]])),
        variable_names = c(group1_var, group2_var)
      ))
    }
    
    return(list(data = data))
    
  }, error = function(e) {
    return(list(error = paste("Error reading uploaded file:", e$message)))
  })
}

# Function to generate HTML output
effect_size_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tags$div(
      class = "alert alert-danger",
      h4("Error"),
      p(results$error)
    ))
  }
  
  tags$div(
    class = "effect-size-header",
    h4("Effect Size Analysis"),
    p(paste("Analysis performed on", format(results$test_info$timestamp, "%Y-%m-%d %H:%M:%S")))
  )
}

# Function to generate summary output
effect_size_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tags$div(
      class = "alert alert-danger",
      h4("Error"),
      p(results$error)
    ))
  }
  
  # Create a summary data frame
  summary_df <- data.frame(
    Statistic = c(
      "Group 1 Mean", "Group 1 SD", "Group 1 n",
      "Group 2 Mean", "Group 2 SD", "Group 2 n",
      "Cohen's d", "95% CI",
      "Hedges' g", "95% CI"
    ),
    Value = c(
      round(results$group_stats$group1$mean, 3),
      round(results$group_stats$group1$sd, 3),
      results$group_stats$group1$n,
      round(results$group_stats$group2$mean, 3),
      round(results$group_stats$group2$sd, 3),
      results$group_stats$group2$n,
      round(results$cohens_d, 3),
      paste0("[", round(results$cohens_d_ci[1], 3), 
             ", ", round(results$cohens_d_ci[2], 3), "]"),
      round(results$hedges_g, 3),
      paste0("[", round(results$hedges_g_ci[1], 3), 
             ", ", round(results$hedges_g_ci[2], 3), "]")
    )
  )
  
  # Convert to kable for nice formatting
  knitr::kable(summary_df, format = "html", align = c("l", "r")) %>%
    kableExtra::kable_styling(bootstrap_options = c("striped", "hover"))
}

# Function to generate plots
effect_size_plot <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  
  tryCatch({
    # Prepare data for plotting
    plot_data <- data.frame(
      Group = factor(rep(c("Group 1", "Group 2"), 
                        c(length(results$group_stats$group1), 
                          length(results$group_stats$group2)))),
      Value = c(results$group_stats$group1, results$group_stats$group2)
    )
    
    # Create boxplot with points
    p <- ggplot2::ggplot(plot_data, ggplot2::aes(x = Group, y = Value, fill = Group)) +
      ggplot2::geom_boxplot(alpha = 0.7, outlier.shape = NA) +
      ggplot2::geom_jitter(width = 0.2, alpha = 0.5) +
      ggplot2::labs(
        title = "Group Comparison",
        x = "",
        y = "Value",
        fill = "Group"
      ) +
      ggplot2::theme_minimal() +
      ggplot2::theme(legend.position = "none")
    
    return(p)
    
  }, error = function(e) {
    message("Error generating plot: ", e$message)
    return(NULL)
  })
}