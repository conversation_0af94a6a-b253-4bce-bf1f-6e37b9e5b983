# Logistic Regression UI
LogisticRegressionSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("logrUserData"), "Upload your Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
    selectizeInput(ns("logrResponse"), "Response Variable (y) - Binary", choices = NULL),
    selectizeInput(ns("logrExplanatory"), "Explanatory Variables (x1, x2, ..., xn)", choices = NULL, multiple = TRUE),
    radioButtons(ns("logrSigLvl"), "Significance Level (alpha)", choices = c("10%", "5%", "1%"), selected = "5%", inline = TRUE),
    br(),
    actionButton(ns("goLogR"), label = "Calculate", class = "act-btn")
  )
}

LogisticRegressionMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('logrResults'))
  )
}

logisticRegressionUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(LogisticRegressionSidebarUI(id)),
    mainPanel(LogisticRegressionMainUI(id))
  )
} 