MediationModerationServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    mmData <- eventReactive(input$mmUserData, {
      handle_file_upload(input$mmUserData)
    })
    
    observeEvent(mmData(), {
      data <- mmData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'mmIV', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'mmDV', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'mmMediator', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'mmModerator', choices = names(data), server = TRUE)
      }
    })
    
    mmValidationErrors <- reactive({
      errors <- c()
      data <- mmData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$mmIV)) {
        errors <- c(errors, "Select an independent variable.")
      }
      if (is.null(input$mmDV)) {
        errors <- c(errors, "Select a dependent variable.")
      }
      if (is.null(input$mmModelType)) {
        errors <- c(errors, "Select a model type.")
      }
      
      # Check model-specific requirements
      if (input$mmModelType == "mediation" && is.null(input$mmMediator)) {
        errors <- c(errors, "Select a mediator variable for mediation analysis.")
      }
      if (input$mmModelType == "moderation" && is.null(input$mmModerator)) {
        errors <- c(errors, "Select a moderator variable for moderation analysis.")
      }
      if (input$mmModelType == "moderated_mediation" && (is.null(input$mmMediator) || is.null(input$mmModerator))) {
        errors <- c(errors, "Select both mediator and moderator variables for moderated mediation analysis.")
      }
      
      # Check variable types
      for (var in c(input$mmIV, input$mmDV, input$mmMediator, input$mmModerator)) {
        if (!is.null(var) && !is.numeric(data[[var]])) {
          errors <- c(errors, sprintf("Variable '%s' must be numeric.", var))
        }
      }
      errors
    })
    
    mmResult <- eventReactive(input$goMM, {
      data <- mmData()
      req(data, input$mmIV, input$mmDV, input$mmModelType)
      
      # Get parameters from UI
      model_type <- input$mmModelType
      mediator <- ifelse(is.null(input$mmMediator), NULL, input$mmMediator)
      moderator <- ifelse(is.null(input$mmModerator), NULL, input$mmModerator)
      
      mediation_moderation_analysis(data, input$mmIV, input$mmDV, 
                                  mediator = mediator, moderator = moderator, 
                                  model_type = model_type)
    })
    
    observeEvent(input$goMM, {
      output$mmResultsUI <- renderUI({
        errors <- mmValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Mediation/Moderation Analysis", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("mmTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("mmAnalysis"),
                title = "Analysis",
                titlePanel("Mediation/Moderation Analysis Results"),
                br(),
                h4("Model Type"),
                textOutput(ns('mmModelType')),
                h4("Number of Observations"),
                textOutput(ns('mmObservations')),
                h4("Effects Table (Mediation)"),
                tableOutput(ns('mmEffects')),
                h4("Path Coefficients (Mediation)"),
                tableOutput(ns('mmPathCoefficients')),
                h4("Coefficients Table (Moderation)"),
                tableOutput(ns('mmCoefficients')),
                h4("Simple Slopes (Moderation)"),
                tableOutput(ns('mmSimpleSlopes')),
                h4("Conditional Effects (Moderated Mediation)"),
                tableOutput(ns('mmConditionalEffects')),
                h4("Effect Size Interpretation (Mediation)"),
                textOutput(ns('mmEffectSizeInterpretation')),
                h4("Path Interpretation (Mediation)"),
                textOutput(ns('mmPathInterpretation')),
                h4("Interaction Interpretation (Moderation)"),
                textOutput(ns('mmInteractionInterpretation'))
              ),
              tabPanel(
                id = ns("mmDiagnostics"),
                title = "Diagnostics",
                h4("Model Fit Statistics"),
                tableOutput(ns('mmFitStats'))
              ),
              tabPanel(
                id = ns("mmUploadedData"),
                title = "Uploaded Data",
                h4("Raw Uploaded Data"),
                DT::DTOutput(ns('mmRawDataTable'))
              )
            )
          )
        }
      })
    })
    output$mmRawDataTable <- DT::renderDT({
      req(mmData())
      DT::datatable(mmData(), options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), rownames = FALSE)
    })
    
    # Model type
    output$mmModelType <- renderText({
      res <- mmResult()
      if (is.null(res)) return(NULL)
      paste("Analysis type:", res$type)
    })
    
    # Number of observations
    output$mmObservations <- renderText({
      res <- mmResult()
      if (is.null(res)) return(NULL)
      paste("Number of observations:", res$n_observations)
    })
    
    # Effects table (mediation only)
    output$mmEffects <- renderTable({
      res <- mmResult()
      if (is.null(res) || res$type != "mediation") return(NULL)
      res$effects
    }, rownames = FALSE, digits = 4)
    
    # Path coefficients (mediation only)
    output$mmPathCoefficients <- renderTable({
      res <- mmResult()
      if (is.null(res) || res$type != "mediation") return(NULL)
      res$path_coefficients
    }, rownames = FALSE, digits = 4)
    
    # Coefficients table (moderation only)
    output$mmCoefficients <- renderTable({
      res <- mmResult()
      if (is.null(res) || res$type != "moderation") return(NULL)
      res$coefficients
    }, rownames = FALSE, digits = 4)
    
    # Simple slopes (moderation only)
    output$mmSimpleSlopes <- renderTable({
      res <- mmResult()
      if (is.null(res) || res$type != "moderation") return(NULL)
      res$simple_slopes
    }, rownames = FALSE, digits = 4)
    
    # Conditional effects (moderated mediation only)
    output$mmConditionalEffects <- renderTable({
      res <- mmResult()
      if (is.null(res) || res$type != "moderated_mediation") return(NULL)
      res$conditional_effects
    }, rownames = FALSE, digits = 4)
    
    # Model fit statistics (for all types)
    output$mmFitStats <- renderTable({
      res <- mmResult()
      if (is.null(res)) return(NULL)
      res$fit_statistics
    }, rownames = FALSE, digits = 4)
    
    # Effect size interpretation (mediation only)
    output$mmEffectSizeInterpretation <- renderText({
      res <- mmResult()
      if (is.null(res) || res$type != "mediation") return(NULL)
      
      indirect_effect <- res$effects$Estimate[res$effects$Effect == "Indirect Effect (a*b)"]
      direct_effect <- res$effects$Estimate[res$effects$Effect == "Direct Effect (c')"]
      total_effect <- res$effects$Estimate[res$effects$Effect == "Total Effect (c)"]
      
      if (!is.na(indirect_effect) && !is.na(total_effect)) {
        proportion_mediated <- abs(indirect_effect / total_effect)
        if (proportion_mediated < 0.1) mediation_desc <- "negligible"
        else if (proportion_mediated < 0.3) mediation_desc <- "small"
        else if (proportion_mediated < 0.5) mediation_desc <- "moderate"
        else mediation_desc <- "large"
        
        paste("Mediation effect size:", mediation_desc, 
              "(proportion mediated =", round(proportion_mediated, 4), ")")
      } else {
        "Effect size interpretation not available"
      }
    })
    
    # Path diagram interpretation (mediation only)
    output$mmPathInterpretation <- renderText({
      res <- mmResult()
      if (is.null(res) || res$type != "mediation") return(NULL)
      
      a_coef <- res$path_coefficients$Coefficient[res$path_coefficients$Path == "a (IV -> Mediator)"]
      b_coef <- res$path_coefficients$Coefficient[res$path_coefficients$Path == "b (Mediator -> DV)"]
      c_coef <- res$path_coefficients$Coefficient[res$path_coefficients$Path == "c (IV -> DV)"]
      c_prime_coef <- res$path_coefficients$Coefficient[res$path_coefficients$Path == "c' (IV -> DV | Mediator)"]
      
      interpretations <- c()
      if (!is.na(a_coef) && a_coef != 0) {
        interpretations <- c(interpretations, "Path a (IV→Mediator) is significant")
      }
      if (!is.na(b_coef) && b_coef != 0) {
        interpretations <- c(interpretations, "Path b (Mediator→DV) is significant")
      }
      if (!is.na(c_coef) && c_coef != 0) {
        interpretations <- c(interpretations, "Path c (IV→DV) is significant")
      }
      if (!is.na(c_prime_coef) && c_prime_coef != 0) {
        interpretations <- c(interpretations, "Direct effect (c') remains significant")
      }
      
      if (length(interpretations) > 0) {
        paste("Path analysis:", paste(interpretations, collapse = "; "))
      } else {
        "Path analysis interpretation not available"
      }
    })
    
    # Interaction interpretation (moderation only)
    output$mmInteractionInterpretation <- renderText({
      res <- mmResult()
      if (is.null(res) || res$type != "moderation") return(NULL)
      
      interaction_coef <- res$coefficients$Estimate[res$coefficients$Variable == "IV × Moderator"]
      interaction_p <- res$coefficients$p_value[res$coefficients$Variable == "IV × Moderator"]
      
      if (!is.na(interaction_coef) && !is.na(interaction_p)) {
        if (interaction_p < 0.05) {
          if (interaction_coef > 0) {
            paste("Significant positive interaction (p =", round(interaction_p, 4), 
                  "): The effect of IV on DV increases as moderator increases")
          } else {
            paste("Significant negative interaction (p =", round(interaction_p, 4), 
                  "): The effect of IV on DV decreases as moderator increases")
          }
        } else {
          paste("No significant interaction (p =", round(interaction_p, 4), ")")
        }
      } else {
        "Interaction interpretation not available"
      }
    })
    
    # Conditional effect interpretation (moderated mediation only)
    output$mmConditionalInterpretation <- renderText({
      res <- mmResult()
      if (is.null(res) || res$type != "moderated_mediation") return(NULL)
      
      effects <- res$conditional_effects$Conditional_Indirect_Effect
      if (length(effects) > 0) {
        min_effect <- min(effects, na.rm = TRUE)
        max_effect <- max(effects, na.rm = TRUE)
        effect_range <- max_effect - min_effect
        
        if (effect_range > 0) {
          paste("Conditional indirect effects range from", round(min_effect, 4), 
                "to", round(max_effect, 4), 
                "(range =", round(effect_range, 4), ")")
        } else {
          "No variation in conditional indirect effects"
        }
      } else {
        "Conditional effect interpretation not available"
      }
    })
    
    # Model comparison (if available)
    output$mmModelComparison <- renderTable({
      res <- mmResult()
      if (is.null(res) || is.null(res$model_comparison)) return(NULL)
      res$model_comparison
    }, rownames = FALSE, digits = 4)
    
    # Interaction plot (for moderation)
    output$mmInteractionPlot <- renderPlot({
      res <- mmResult()
      if (is.null(res) || res$type != "moderation") return(NULL)
      
      # Get data for plotting
      data <- mmData()
      iv_var <- input$mmIV
      dv_var <- input$mmDV
      mod_var <- input$mmModerator
      
      # Create interaction plot
      mod_values <- quantile(data[[mod_var]], probs = c(0.25, 0.5, 0.75))
      iv_values <- seq(min(data[[iv_var]]), max(data[[iv_var]]), length.out = 100)
      
      plot_data <- data.frame()
      for (mod_val in mod_values) {
        for (iv_val in iv_values) {
          # Calculate predicted values using model coefficients
          coef_table <- res$coefficients
          intercept <- coef_table$Estimate[coef_table$Variable == "Intercept"]
          iv_coef <- coef_table$Estimate[coef_table$Variable == "IV"]
          mod_coef <- coef_table$Estimate[coef_table$Variable == "Moderator"]
          interaction_coef <- coef_table$Estimate[coef_table$Variable == "IV × Moderator"]
          
          predicted <- intercept + iv_coef * iv_val + mod_coef * mod_val + interaction_coef * iv_val * mod_val
          
          plot_data <- rbind(plot_data, data.frame(
            IV = iv_val,
            DV = predicted,
            Moderator = paste("Moderator =", round(mod_val, 2))
          ))
        }
      }
      
      ggplot2::ggplot(plot_data, ggplot2::aes(x = IV, y = DV, color = Moderator)) +
        ggplot2::geom_line(size = 1) +
        ggplot2::labs(title = "Interaction Plot",
                     x = iv_var, y = dv_var, color = "Moderator Level") +
        ggplot2::theme_minimal()
    })
    
    # Mediation diagram (for mediation)
    output$mmMediationDiagram <- renderPlot({
      res <- mmResult()
      if (is.null(res) || res$type != "mediation") return(NULL)
      
      # Create a simple mediation diagram
      effects <- res$effects
      direct_effect <- effects$Estimate[effects$Effect == "Direct Effect (c')"]
      indirect_effect <- effects$Estimate[effects$Effect == "Indirect Effect (a*b)"]
      total_effect <- effects$Estimate[effects$Effect == "Total Effect (c)"]
      
      # Create diagram data
      diagram_data <- data.frame(
        x = c(0, 2, 4, 2),
        y = c(0, 1, 0, -1),
        label = c("IV", "Mediator", "DV", ""),
        effect = c("", "", "", paste("c' =", round(direct_effect, 3)))
      )
      
      # Create arrows data
      arrows_data <- data.frame(
        x_start = c(0, 2, 0),
        y_start = c(0, 1, 0),
        x_end = c(2, 4, 4),
        y_end = c(1, 0, 0),
        effect = c("a", "b", "c")
      )
      
      ggplot2::ggplot() +
        ggplot2::geom_point(data = diagram_data, ggplot2::aes(x = x, y = y), size = 5, color = "steelblue") +
        ggplot2::geom_text(data = diagram_data, ggplot2::aes(x = x, y = y, label = label), 
                          color = "white", fontface = "bold") +
        ggplot2::geom_segment(data = arrows_data, 
                             ggplot2::aes(x = x_start, y = y_start, xend = x_end, yend = y_end),
                             arrow = ggplot2::arrow(length = ggplot2::unit(0.2, "cm")),
                             size = 1, color = "red") +
        ggplot2::geom_text(data = arrows_data, 
                          ggplot2::aes(x = (x_start + x_end)/2, y = (y_start + y_end)/2, label = effect),
                          color = "red", fontface = "bold", size = 4) +
        ggplot2::labs(title = "Mediation Model") +
        ggplot2::theme_void() +
        ggplot2::xlim(-0.5, 4.5) +
        ggplot2::ylim(-1.5, 1.5)
    })
    
    # Summary statistics
    output$mmSummary <- renderText({
      res <- mmResult()
      if (is.null(res)) return(NULL)
      
      if (res$type == "mediation") {
        paste("Mediation Analysis: Examined", res$n_observations, "observations")
      } else if (res$type == "moderation") {
        paste("Moderation Analysis: Examined", res$n_observations, "observations")
      } else if (res$type == "moderated_mediation") {
        paste("Moderated Mediation Analysis: Examined", res$n_observations, "observations")
      } else {
        "Summary not available"
      }
    })
  })
} 