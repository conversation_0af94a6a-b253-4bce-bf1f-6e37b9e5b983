# Placeholder for Species Distribution Models Server
speciesDistributionServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    # Reactives
    sdmUploadData <- reactive({
      req(input$sdmUserData)
      ext <- tools::file_ext(input$sdmUserData$name)
      if (ext %in% c("csv", "txt")) {
        read.csv(input$sdmUserData$datapath)
      } else if (ext %in% c("xls", "xlsx")) {
        if (!requireNamespace("readxl", quietly = TRUE)) stop("Install 'readxl' for Excel support.")
        readxl::read_excel(input$sdmUserData$datapath)
      } else {
        stop("Unsupported file type.")
      }
    })
    sdmResults <- eventReactive(input$goSDM, {
      df <- sdmUploadData()
      req(input$sdmPresenceCol, input$sdmPredictors)
      speciesDistributionResults_func(
        data = df,
        presence_col = input$sdmPresenceCol,
        predictors = input$sdmPredictors,
        method = input$sdmMethod
      )
    })
    # Outputs
    output$sdmHT <- renderUI({
      results <- sdmResults()
      if (is.null(results)) return(NULL)
      verbatimTextOutput(ns("sdmSummary"))
    })
    output$sdmSummary <- renderPrint({
      results <- sdmResults()
      if (is.null(results)) return(NULL)
      print(results$summary)
    })
    output$sdmPlot <- renderPlot({
      results <- sdmResults()
      if (is.null(results) || is.null(results$plot)) return(NULL)
      results$plot
    })
    output$sdmConclusionOutput <- renderUI({
      results <- sdmResults()
      if (is.null(results)) return(NULL)
      tags$p("Species distribution model fitted. See summary and prediction plot above.")
    })
    output$renderSDMData <- renderUI({
      req(sdmUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("sdmInitialUploadTable")), style = "width: 75%"),
        br(),
        br(),
        uiOutput(ns("sdmColSelectors"))
      )
    })
    output$sdmInitialUploadTable <- DT::renderDT({
      req(sdmUploadData())
      DT::datatable(sdmUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(sdmUploadData())))))
    })
    output$sdmColSelectors <- renderUI({
      df <- sdmUploadData()
      req(df)
      tagList(
        selectInput(ns("sdmPresenceCol"), "Presence/Absence Column", choices = names(df)),
        selectInput(ns("sdmPredictors"), "Predictor Variables", choices = names(df), multiple = TRUE),
        selectInput(ns("sdmMethod"), "Modeling Method", choices = c("glm", "maxent"), selected = "glm")
      )
    })
  })
} 