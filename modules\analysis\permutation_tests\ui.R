PermutationTestsUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("permUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectInput(ns("permTestType"), "Test Type", choices = c("Permutation t-test", "Randomization ANOVA")),
        selectizeInput(ns("permVars"), "Variables", choices = NULL, multiple = TRUE),
        br(),
        actionButton(ns("goPerm"), label = "Run Test", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("permError")),
        tableOutput(ns("permResults")),
        plotOutput(ns("permPlot"))
      )
    )
  )
} 