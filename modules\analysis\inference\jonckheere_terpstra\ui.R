# Jonckheere-Terpstra Test UI
# Non-parametric test for ordered alternatives

JonckheereTerpstraSidebarUI <- function(id) {
  ns <- NS(id)
  
  tagList(
    # Data Input Method
    radioButtons(
      inputId = ns("jtDataMethod"),
      label = "Data Input Method:",
      choices = c("Manual Entry" = "Manual Entry", "Upload File" = "Upload File"),
      selected = "Manual Entry"
    ),
    
    # Manual Entry
    conditionalPanel(
      condition = "input.jtDataMethod == 'Manual Entry'",
      ns = ns,
      textAreaInput(
        inputId = ns("jtData"),
        label = "Enter Response Values (comma, space, or newline separated):",
        placeholder = "1, 2, 3, 4, 5, 6",
        rows = 4
      ),
      textAreaInput(
        inputId = ns("jtGroups"),
        label = "Enter Group Labels (comma, space, or newline separated):",
        placeholder = "1, 1, 2, 2, 3, 3",
        rows = 4
      )
    ),
    
    # File Upload
    conditionalPanel(
      condition = "input.jtDataMethod == 'Upload File'",
      ns = ns,
      fileInput(
        inputId = ns("jtUserData"),
        label = "Upload Data File:",
        accept = c(".csv", ".txt", ".xlsx", ".xls"),
        buttonLabel = "Browse Files",
        placeholder = "No file selected"
      ),
      selectizeInput(
        inputId = ns("jtResponseVariable"),
        label = "Select Response Variable:",
        choices = NULL,
        options = list(placeholder = "Select response variable...")
      ),
      selectizeInput(
        inputId = ns("jtGroupVariable"),
        label = "Select Group Variable:",
        choices = NULL,
        options = list(placeholder = "Select group variable...")
      )
    ),
    
    # Test Parameters
    selectInput(
      inputId = ns("jtAlternative"),
      label = "Alternative Hypothesis:",
      choices = c(
        "Two-sided (≠)" = "two.sided",
        "Increasing trend (>)" = "greater",
        "Decreasing trend (<)" = "less"
      ),
      selected = "two.sided"
    ),
    
    numericInput(
      inputId = ns("jtConfLevel"),
      label = "Confidence Level:",
      value = 0.95,
      min = 0.5,
      max = 0.99,
      step = 0.01
    ),
    
    actionButton(
      inputId = ns("goJT"),
      label = "Calculate Jonckheere-Terpstra Test",
      class = "btn-primary",
      style = "width: 100%;"
    ),
    
    br(),
    br(),
    
    # Help text
    helpText(
      "The Jonckheere-Terpstra test is a non-parametric test for ordered alternatives.",
      "It tests whether there is a trend across ordered groups.",
      "Groups should be ordered in the hypothesized direction."
    )
  )
}

JonckheereTerpstraMainUI <- function(id) {
  ns <- NS(id)
  
  tagList(
    titlePanel("Jonckheere-Terpstra Test"),
    p("A non-parametric test for testing ordered alternatives across multiple groups."),
    
    uiOutput(ns("jtResults"))
  )
} 