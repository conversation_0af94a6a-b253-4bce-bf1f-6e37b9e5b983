# Spectral Analysis Calculations
# Modular spectral analysis with mathematical documentation and advanced validation

#' Spectral Analysis Results Function
#' @description Performs periodogram, spectral density estimation, and harmonic regression
#' @param data Input data (must include time, value columns)
#' @param conf_level Confidence level
#' @return List of spectral analysis results
spectralAnalysisResults_func <- function(data, conf_level = 0.95) {
  if (is.null(data) || nrow(data) < 20) stop("Insufficient data for spectral analysis (min 20 rows)")
  if (!all(c("time", "value") %in% colnames(data))) stop("Data must have time, value columns")
  results <- list()
  tryCatch({
    periodogram <- calculate_periodogram(data)
    spectral_density <- calculate_spectral_density(data)
    harmonic_reg <- perform_harmonic_regression(data)
    results <- list(periodogram = periodogram, spectral_density = spectral_density, harmonic_regression = harmonic_reg, error = NULL)
  }, error = function(e) { results$error <- paste("Spectral analysis error:", e$message) })
  return(results)
}

#' Calculate Periodogram
#' @description Computes the periodogram for frequency analysis
calculate_periodogram <- function(data) {
  x <- data$value
  n <- length(x)
  freq <- (0:(n-1))/n
  spec <- abs(fft(x))^2 / n
  return(data.frame(frequency = freq[1:(n %/% 2)], periodogram = spec[1:(n %/% 2)]))
}

#' Calculate Spectral Density
#' @description Estimates the spectral density using a smoothed periodogram
calculate_spectral_density <- function(data) {
  x <- data$value
  spec <- spectrum(x, plot = FALSE)
  return(data.frame(frequency = spec$freq, spectral_density = spec$spec))
}

#' Perform Harmonic Regression
#' @description Fits a harmonic regression model to the data
perform_harmonic_regression <- function(data, n_harmonics = 2) {
  t <- data$time
  y <- data$value
  n <- length(y)
  X <- matrix(1, n, 1)
  for (k in 1:n_harmonics) {
    X <- cbind(X, sin(2 * pi * k * t / n), cos(2 * pi * k * t / n))
  }
  fit <- lm(y ~ X - 1)
  fitted <- fitted(fit)
  r_squared <- summary(fit)$r.squared
  return(list(coefficients = coef(fit), r_squared = r_squared, fitted = fitted))
}

#' Render Spectral Analysis Results
spectralAnalysisRenderAnalysis <- function(results) {
  renderUI({
    req(results())
    res <- results()
    if (!is.null(res$error)) return(tagList(h4("Spectral Analysis Error"), p(res$error)))
    tagList(
      h4("Harmonic Regression"),
      p(tags$b("R-squared:"), sprintf("%.4f", res$harmonic_regression$r_squared)),
      h4("Mathematical Foundation"),
      p("Spectral analysis decomposes a time series into sinusoidal components. The periodogram estimates the power at each frequency. Harmonic regression fits models of the form:"),
      p("\\[ y_t = a_0 + \\sum_{k=1}^K (a_k \\sin(2\\pi k t / n) + b_k \\cos(2\\pi k t / n)) + \\epsilon_t \\]"),
      p("where \\(a_k, b_k\\) are coefficients and \\(K\\) is the number of harmonics.")
    )
  })
}

#' Render Spectral Plots
spectralAnalysisRenderPlots <- function(results) {
  renderPlot({
    req(results())
    res <- results()
    if (!is.null(res$error)) return(NULL)
    periodogram <- res$periodogram
    spectral_density <- res$spectral_density
    p1 <- ggplot(periodogram, aes(x = frequency, y = periodogram)) +
      geom_line(color = "blue") +
      labs(title = "Periodogram", x = "Frequency", y = "Power") + theme_minimal()
    p2 <- ggplot(spectral_density, aes(x = frequency, y = spectral_density)) +
      geom_line(color = "red") +
      labs(title = "Spectral Density Estimate", x = "Frequency", y = "Density") + theme_minimal()
    gridExtra::grid.arrange(p1, p2, ncol = 2)
  })
}

# Spectral Analysis calculation and output helpers

# 1. Data Upload Function
spectral_analysis_uploadData_func <- function(saUserData) {
  ext <- tools::file_ext(saUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(saUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(saUserData$datapath),
         xlsx = readxl::read_xlsx(saUserData$datapath),
         txt = readr::read_tsv(saUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

# 2. Main Results Function (core calculations)
spectral_analysis_results_func <- function(data, conf_level = 0.95) {
  tryCatch({
    results <- spectralAnalysisResults_func(data, conf_level)
    results$data <- data
    results$error <- NULL
    results
  }, error = function(e) {
    list(error = paste("An error occurred during Spectral Analysis calculation:", e$message))
  })
}

# 3. Hypothesis Test HTML Output
spectral_analysis_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  tagList(
    h4("Spectral Analysis"),
    p("See summary table for analysis details.")
  )
}

# 4. Summary Table HTML Output
spectral_analysis_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  out <- list(h4("Spectral Analysis Summary"), renderPrint(results))
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

# 5. Plot Output
spectral_analysis_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  if (!is.null(results$periodogram) && !is.null(results$spectral_density)) {
    periodogram <- results$periodogram
    spectral_density <- results$spectral_density
    p1 <- ggplot(periodogram, aes(x = frequency, y = periodogram)) +
      geom_line(color = "blue") +
      labs(title = "Periodogram", x = "Frequency", y = "Power") + theme_minimal()
    p2 <- ggplot(spectral_density, aes(x = frequency, y = spectral_density)) +
      geom_line(color = "red") +
      labs(title = "Spectral Density Estimate", x = "Frequency", y = "Density") + theme_minimal()
    gridExtra::grid.arrange(p1, p2, ncol = 2)
    return()
  }
  # Harmonic regression fit
  if (!is.null(results$harmonic_regression)) {
    plot(results$data$time, results$data$value, type = 'l', main = 'Harmonic Regression Fit', ylab = 'Value')
    lines(results$data$time, results$harmonic_regression$fitted, col = 'red', lwd = 2)
    legend('topright', legend = c('Observed', 'Fitted'), col = c('black', 'red'), lty = 1)
    # Residuals
    resids <- results$data$value - results$harmonic_regression$fitted
    plot(results$data$time, resids, type = 'h', main = 'Harmonic Regression Residuals', ylab = 'Residuals')
    return()
  }
  plot.new(); title("No plot available.")
}
