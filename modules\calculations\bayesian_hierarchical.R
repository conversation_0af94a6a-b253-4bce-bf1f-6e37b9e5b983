# Bayesian Hierarchical Analysis Functions

bayesian_hierarchical_analysis <- function(data, response, predictors, group, model_type = "random_intercept") {
  tryCatch({
    # Prepare data
    if (!response %in% names(data)) {
      stop("Response variable not found in data")
    }
    
    if (!all(predictors %in% names(data))) {
      stop("One or more predictor variables not found in data")
    }
    
    if (!group %in% names(data)) {
      stop("Group variable not found in data")
    }
    
    # Remove rows with missing values
    vars_to_check <- c(response, predictors, group)
    complete_cases <- complete.cases(data[, vars_to_check, drop = FALSE])
    if (sum(complete_cases) < nrow(data)) {
      warning("Removing rows with missing values for Bayesian hierarchical analysis")
      data <- data[complete_cases, , drop = FALSE]
    }
    
    # Check if group variable is factor
    if (!is.factor(data[[group]])) {
      data[[group]] <- as.factor(data[[group]])
    }
    
    # Center predictors
    data_centered <- data
    for (pred in predictors) {
      data_centered[[paste0(pred, "_centered")]] <- scale(data[[pred]], center = TRUE, scale = FALSE)
    }
    
    # Create formula based on model type
    if (model_type == "random_intercept") {
      formula_str <- paste(response, "~", paste(paste0(predictors, "_centered"), collapse = " + "), "+ (1|", group, ")")
    } else if (model_type == "random_slope") {
      formula_str <- paste(response, "~", paste(paste0(predictors, "_centered"), collapse = " + "), "+ (1 +", paste0(predictors[1], "_centered"), "|", group, ")")
    } else if (model_type == "custom") {
      # For custom models, use the first predictor as random slope
      formula_str <- paste(response, "~", paste(paste0(predictors, "_centered"), collapse = " + "), "+ (1 +", paste0(predictors[1], "_centered"), "|", group, ")")
    } else {
      stop("Unknown model type. Use 'random_intercept', 'random_slope', or 'custom'")
    }
    
    formula_obj <- as.formula(formula_str)
    
    # Fit Bayesian hierarchical model
    if (!requireNamespace("brms", quietly = TRUE)) {
      stop("Package 'brms' is required for Bayesian hierarchical analysis")
    }
    
    # Set priors (weakly informative)
    priors <- c(
      brms::prior(normal(0, 10), class = "Intercept"),
      brms::prior(normal(0, 2.5), class = "b"),
      brms::prior(student_t(3, 0, 10), class = "sd"),
      brms::prior(student_t(3, 0, 10), class = "sigma")
    )
    
    # Fit model
    fit <- brms::brm(formula_obj, data = data_centered, 
                     prior = priors, 
                     chains = 4, iter = 2000, warmup = 1000,
                     seed = 123, silent = 2)
    
    # Model summary
    summary_fit <- summary(fit)
    
    # Extract fixed effects
    fixed_effects <- summary_fit$fixed
    fixed_effects_table <- data.frame(
      Parameter = rownames(fixed_effects),
      Estimate = round(fixed_effects[, "Estimate"], 4),
      Est_Error = round(fixed_effects[, "Est.Error"], 4),
      Lower_CI = round(fixed_effects[, "l-95% CI"], 4),
      Upper_CI = round(fixed_effects[, "u-95% CI"], 4),
      Rhat = round(fixed_effects[, "Rhat"], 4),
      Bulk_ESS = round(fixed_effects[, "Bulk_ESS"], 0),
      Tail_ESS = round(fixed_effects[, "Tail_ESS"], 0)
    )
    
    # Extract random effects
    random_effects <- summary_fit$random
    random_effects_table <- data.frame(
      Group = names(random_effects),
      Parameter = sapply(random_effects, function(x) rownames(x)),
      Estimate = round(unlist(sapply(random_effects, function(x) x[, "Estimate"])), 4),
      Est_Error = round(unlist(sapply(random_effects, function(x) x[, "Est.Error"])), 4),
      Lower_CI = round(unlist(sapply(random_effects, function(x) x[, "l-95% CI"])), 4),
      Upper_CI = round(unlist(sapply(random_effects, function(x) x[, "u-95% CI"])), 4)
    )
    
    # Model fit statistics
    fit_stats <- data.frame(
      Statistic = c("WAIC", "LOO", "R-squared", "Number of Groups", "Number of Observations"),
      Value = c(round(brms::waic(fit)$estimates["waic", "Estimate"], 4),
                round(brms::loo(fit)$estimates["looic", "Estimate"], 4),
                round(bayes_R2(fit), 4),
                length(unique(data[[group]])),
                nrow(data))
    )
    
    # Posterior predictive checks
    pp_check <- brms::pp_check(fit, type = "dens_overlay", nsamples = 100)
    
    # Group-level predictions
    group_predictions <- brms::ranef(fit)
    
    # Create group summary
    group_summary <- data.frame(
      Group = unique(data[[group]]),
      N = table(data[[group]]),
      Mean_Response = aggregate(data[[response]], by = list(data[[group]]), FUN = mean)$x
    )
    names(group_summary)[2] <- "N"
    
    # Convergence diagnostics
    convergence_diag <- data.frame(
      Metric = c("Max Rhat", "Min Bulk ESS", "Min Tail ESS"),
      Value = c(round(max(fixed_effects[, "Rhat"]), 4),
                round(min(fixed_effects[, "Bulk_ESS"]), 0),
                round(min(fixed_effects[, "Tail_ESS"]), 0))
    )
    
    # Effect sizes (standardized coefficients)
    standardized_coef <- tryCatch({
      brms::standardize(fit)
    }, error = function(e) {
      NULL
    })
    
    # Model comparison (if multiple models)
    model_comparison <- NULL
    if (model_type == "custom") {
      # Fit simpler models for comparison
      simple_formula <- as.formula(paste(response, "~", paste(paste0(predictors, "_centered"), collapse = " + ")))
      simple_fit <- brms::brm(simple_formula, data = data_centered, 
                             prior = priors[1:2], 
                             chains = 4, iter = 2000, warmup = 1000,
                             seed = 123, silent = 2)
      
      model_comparison <- brms::loo_compare(simple_fit, fit)
    }
    
    list(
      fit = fit,
      formula = formula_str,
      model_type = model_type,
      fixed_effects = fixed_effects_table,
      random_effects = random_effects_table,
      fit_statistics = fit_stats,
      convergence_diagnostics = convergence_diag,
      group_summary = group_summary,
      group_predictions = group_predictions,
      standardized_coefficients = standardized_coef,
      model_comparison = model_comparison,
      pp_check = pp_check,
      n_groups = length(unique(data[[group]])),
      n_observations = nrow(data),
      n_predictors = length(predictors)
    )
    
  }, error = function(e) {
    stop(paste("Bayesian hierarchical analysis failed:", e$message))
  })
}

# Helper function for Bayesian R-squared
bayes_R2 <- function(fit) {
  tryCatch({
    r2 <- brms::bayes_R2(fit)
    mean(r2)
  }, error = function(e) {
    NA
  })
}

# Bayesian Hierarchical Analysis calculation and output helpers

# 1. Data Upload Function
bayesian_hierarchical_uploadData_func <- function(bhUserData) {
  ext <- tools::file_ext(bhUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(bhUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(bhUserData$datapath),
         xlsx = readxl::read_xlsx(bhUserData$datapath),
         txt = readr::read_tsv(bhUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

# 2. Main Results Function (core calculations)
bayesian_hierarchical_results_func <- function(data, response, predictors, group, model_type = "random_intercept") {
  tryCatch({
    results <- bayesian_hierarchical_analysis(data, response, predictors, group, model_type)
    results$data <- data
    results$error <- NULL
    results
  }, error = function(e) {
    list(error = paste("An error occurred during Bayesian Hierarchical Analysis calculation:", e$message))
  })
}

# 3. Hypothesis Test HTML Output
bayesian_hierarchical_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  tagList(
    h4("Bayesian Hierarchical Model"),
    p("See summary table for model details.")
  )
}

# 4. Summary Table HTML Output
bayesian_hierarchical_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  out <- list(h4("Bayesian Hierarchical Model Summary"))
  if (!is.null(results$fit_statistics)) {
    out <- c(out, h4("Model Fit Statistics"), renderTable(results$fit_statistics))
  }
  if (!is.null(results$fixed_effects)) {
    out <- c(out, h4("Fixed Effects"), renderTable(results$fixed_effects))
  }
  if (!is.null(results$random_effects)) {
    out <- c(out, h4("Random Effects"), renderTable(results$random_effects))
  }
  if (!is.null(results$convergence_diagnostics)) {
    out <- c(out, h4("Convergence Diagnostics"), renderTable(results$convergence_diagnostics))
  }
  if (!is.null(results$fit) && !is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

# 5. Plot Output
bayesian_hierarchical_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  if (!is.null(results$pp_check)) {
    print(results$pp_check)
  } else if (!is.null(results$fit)) {
    plot(results$fit, ask = FALSE)
  } else {
    plot.new(); title("No plot available.")
  }
} 