robustAnovaSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(
      inputId = ns("raUserData"),
      label   = strong("Upload your Data (.csv or .xls or .xlsx or .txt)"),
      accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
    ),
    div(
      id = ns("raUploadInputs"),
      radioButtons(
        inputId = ns("raFormat"),
        label   = strong("Data Format"),
        choiceNames = c("Values in multiple columns", "Responses and factors stacked in two columns"),
        choiceValues = c("Multiple", "Stacked")
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Multiple'", ns("raFormat")),
        selectizeInput(
          inputId = ns("raMultiColumns"),
          label = strong("Choose columns to conduct analysis"),
          choices = c(""),
          multiple = TRUE,
          selected = NULL,
          options = list(hideSelected = FALSE, placeholder = 'Select two or more columns', onInitialize = I('function() { this.setValue(""); }'))
        )
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Stacked'", ns("raFormat")),
        selectizeInput(
          inputId = ns("raResponse"),
          label = strong("Response Variable"),
          choices = c(""),
          selected = NULL,
          options = list(placeholder = 'Select a variable', onInitialize = I('function() { this.setValue(""); }'))
        ),
        selectizeInput(
          inputId = ns("raFactors"),
          label = strong("Factors"),
          choices = c(""),
          selected = NULL,
          options = list(placeholder = 'Select a factor', onInitialize = I('function() { this.setValue(""); }'))
        )
      ),
      selectizeInput(
        inputId = ns("raMethod"),
        label = strong("Robust Method"),
        choices = c("Welch's F-test", "Brown-Forsythe", "Trimmed Means", "M-estimator", "Rank-based"),
        selected = "Welch's F-test",
        options = list(placeholder = 'Select method')
      ),
      numericInput(
        inputId = ns("raTrimLevel"),
        label = strong("Trim Level (0-0.5)"),
        value = 0.1,
        min = 0,
        max = 0.5,
        step = 0.05
      ),
      radioButtons(
        inputId = ns("raSigLvl"),
        label = strong("Significance Level (alpha)"),
        choices  = c("10%", "5%", "1%"),
        selected = "5%",
        inline   = TRUE
      )
    ),
    br(),
    actionButton(ns("goRobustAnova"), label = "Calculate", class = "act-btn")
  )
}

robustAnovaMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('robustAnovaResults'))
  )
}

robustAnovaUI <- function(id) {
  ns <- NS(id)
  tagList(
    robustAnovaSidebarUI(id),
    robustAnovaMainUI(id)
  )
} 