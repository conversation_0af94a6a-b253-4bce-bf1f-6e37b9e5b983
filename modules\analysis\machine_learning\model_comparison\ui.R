ModelComparisonUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("mcUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("mcResponse"), "Response Variable", choices = NULL),
        selectizeInput(ns("mcPredictors"), "Predictor Variables", choices = NULL, multiple = TRUE),
        checkboxGroupInput(ns("mcModels"), "Models to Compare", choices = c("Random Forest", "SVM", "GBM", "kNN", "Neural Network")),
        numericInput(ns("mcCVFolds"), "Cross-Validation Folds", value = 5, min = 2, max = 20),
        br(),
        actionButton(ns("goMC"), label = "Compare Models", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("mcError")),
        tableOutput(ns("mcSummary")),
        plotOutput(ns("mcPlot"))
      )
    )
  )
} 