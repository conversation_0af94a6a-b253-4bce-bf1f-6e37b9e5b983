# CougarStats Error Handling and Dependencies

## Overview

This document describes the comprehensive error handling system and dependency management implemented in CougarStats to ensure robust and reliable operation.

## Error Handling System

### 1. Error Handling Utilities (`modules/shared/error_handling.R`)

The application includes a comprehensive error handling system with the following features:

#### Core Functions:
- **`safe_execute()`**: Safely executes expressions with error catching
- **`check_package_availability()`**: Verifies required packages are installed
- **`validate_data()`**: Validates data structure and content
- **`validate_input()`**: Validates user inputs with type checking
- **`validate_model()`**: Validates model objects and results
- **`create_user_friendly_error()`**: Converts technical errors to user-friendly messages

#### Validation Functions:
- **`validate_numeric_data()`**: Ensures numeric data meets requirements
- **`validate_categorical_data()`**: Validates categorical variables
- **`validate_results()`**: Checks analysis results for completeness

#### Logging Functions:
- **`log_error()`**: Logs errors with timestamps and context
- **`log_warning()`**: Logs warnings for monitoring
- **`log_info()`**: Logs informational messages

#### Performance Monitoring:
- **`monitor_performance()`**: Tracks execution time and performance
- **`check_memory_usage()`**: Monitors memory consumption

### 2. Error Handling in Server Modules

Each server module now includes comprehensive error handling:

#### Data Upload Validation:
```r
# Example from correlation server
corrUploadData <- eventReactive(input$corrUserData, {
  tryCatch({
    data <- handle_file_upload(input$corrUserData)
    errors <- validate_data(data, min_rows = 2)
    if (length(errors) > 0) {
      log_error(paste("Data validation failed:", paste(errors, collapse = "; ")), "correlation_upload")
      return(NULL)
    }
    return(data)
  }, error = function(e) {
    log_error(e$message, "correlation_upload")
    return(NULL)
  })
})
```

#### Analysis Error Handling:
```r
# Example from Bayesian server
bayesResult <- eventReactive(input$goBayesian, {
  monitor_performance({
    tryCatch({
      # Perform analysis
      result <- bayesian_analysis(...)
      return(result)
    }, error = function(e) {
      log_error(paste("Analysis failed:", e$message), "bayesian_analysis")
      # Fallback to simpler method
      return(fallback_analysis(...))
    })
  }, "Bayesian Analysis")
})
```

#### Input Validation:
```r
# Example validation
errors <- validate_input(input$value, type = "numeric", min_val = 0, max_val = 100)
if (length(errors) > 0) {
  log_warning(paste("Validation errors:", paste(errors, collapse = "; ")), "validation")
}
```

## Dependencies

### 1. Core Dependencies

The application requires the following core packages:

#### Shiny and UI:
- `shiny`, `shinyjs`, `shinyWidgets`, `shinyMatrix`, `shinyvalidate`
- `bslib`, `shinythemes`, `shinyDarkmode`
- `DT`, `plotly`, `leaflet`, `htmlwidgets`

#### Data Manipulation:
- `dplyr`, `readr`, `readxl`, `writexl`
- `MASS`, `rstatix`, `DescTools`

#### Visualization:
- `ggplot2`, `ggpubr`, `ggsci`, `ggResidpanel`
- `scatterplot3d`, `networkD3`, `visNetwork`

### 2. Analysis Dependencies

#### Statistical Analysis:
- `survival`, `cmprsk` - Survival analysis
- `psych`, `metafor` - Psychometrics and meta-analysis
- `lme4`, `nlme` - Mixed effects models
- `lavaan` - Structural equation modeling

#### Machine Learning:
- `caret`, `randomForest`, `gbm`, `xgboost`
- `glmnet`, `cluster`, `fpc`

#### Bayesian Analysis:
- `brms`, `rstanarm`, `BayesFactor`, `bayestestR`
- `loo` - Model comparison

#### Advanced Statistics:
- `mgcv` - Generalized additive models
- `robustbase`, `pscl` - Robust and zero-inflated models
- `mediation`, `MatchIt` - Causal inference

### 3. Specialized Dependencies

#### Text Mining and NLP:
- `tm`, `quanteda`, `wordcloud`, `topicmodels`, `lda`

#### Spatial Analysis:
- `sp`, `sf`, `spdep`, `gstat`, `spatstat`
- `spgwr`, `fields`, `dbscan`