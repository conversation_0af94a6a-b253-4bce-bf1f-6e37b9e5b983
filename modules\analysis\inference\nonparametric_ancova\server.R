# Non-parametric ANCOVA Server
# Rank-based analysis of covariance

source("modules/calculations/nonparametric_ancova.R")

NonparametricAncovaServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactive values
    np_ancova_data <- reactiveVal(NULL)
    np_ancova_results <- reactiveVal(NULL)
    
    # File upload reactive
    npAncovaUploadData <- eventReactive(input$npAncovaUserData, {
      handle_file_upload(input$npAncovaUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(npAncovaUploadData(), {
      data <- npAncovaUploadData()
      # Clear selectizeInputs and main panel
      updateSelectizeInput(session, 'npAncovaDependentVariable', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'npAncovaGroupVariable', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'npAncovaCovariate', choices = character(0), selected = NULL, server = TRUE)
      output$npAncovaResults <- renderUI({ NULL })
      
      # If data is valid, update choices
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'npAncovaDependentVariable', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'npAncovaGroupVariable', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'npAncovaCovariate', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    npAncovaValidationErrors <- reactive({
      errors <- c()
      
      if (input$npAncovaDataMethod == "Manual Entry") {
        # Manual entry validation
        if (is.null(input$npAncovaManualData) || input$npAncovaManualData == "") {
          errors <- c(errors, "Manual data entry is required.")
        } else {
          # Basic validation for manual entry
          tryCatch({
            data <- parse_np_ancova_data(input$npAncovaManualData)
            if (nrow(data) < 10) {
              errors <- c(errors, "At least 10 observations are required for non-parametric ANCOVA.")
            }
            if (ncol(data) < 3) {
              errors <- c(errors, "At least 3 variables (group, dependent, covariate) are required.")
            }
          }, error = function(e) {
            errors <- c(errors, "Invalid manual data format. Please check your input.")
          })
        }
      } else {
        # File upload validation
        data <- npAncovaUploadData()
        if (is.null(data) || !is.data.frame(data)) {
          errors <- c(errors, "No data uploaded or file could not be read.")
          return(errors)
        }
        if (is.null(input$npAncovaDependentVariable) || input$npAncovaDependentVariable == "") {
          errors <- c(errors, "Please select a dependent variable.")
        } else {
          var_data <- data[[input$npAncovaDependentVariable]]
          if (!is.numeric(var_data)) {
            errors <- c(errors, "Dependent variable must be numeric.")
          }
        }
        if (is.null(input$npAncovaGroupVariable) || input$npAncovaGroupVariable == "") {
          errors <- c(errors, "Please select a group variable.")
        }
        if (is.null(input$npAncovaCovariate) || input$npAncovaCovariate == "") {
          errors <- c(errors, "Please select a covariate variable.")
        } else {
          var_data <- data[[input$npAncovaCovariate]]
          if (!is.numeric(var_data)) {
            errors <- c(errors, "Covariate variable must be numeric.")
          }
        }
        if (length(na.omit(data[c(input$npAncovaDependentVariable, input$npAncovaGroupVariable, input$npAncovaCovariate)])) < 10) {
          errors <- c(errors, "At least 10 non-missing observations are required.")
        }
      }
      
      errors
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goNpAncova, {
      output$npAncovaResults <- renderUI({
        errors <- npAncovaValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Non-parametric ANCOVA", errors = errors)
        } else {
          tryCatch({
            # Prepare data
            if (input$npAncovaDataMethod == "Manual Entry") {
              # Parse manual entry data
              data <- parse_np_ancova_data(input$npAncovaManualData)
            } else {
              # Use uploaded data
              req(npAncovaUploadData(), input$npAncovaDependentVariable, 
                  input$npAncovaGroupVariable, input$npAncovaCovariate)
              
              data <- npAncovaUploadData()
              dependent_var <- input$npAncovaDependentVariable
              group_var <- input$npAncovaGroupVariable
              covariate_var <- input$npAncovaCovariate
              
              # Select relevant columns
              data <- data[c(group_var, dependent_var, covariate_var)]
              names(data) <- c("Group", "Dependent", "Covariate")
            }
            
            # Remove rows with missing values
            complete_cases <- complete.cases(data)
            if (sum(complete_cases) < nrow(data)) {
              warning("Removing rows with missing values")
              data <- data[complete_cases, ]
            }
            
            # Convert group to factor
            data$Group <- as.factor(data$Group)
            
            # Check data structure
            if (length(unique(data$Group)) < 2) {
              stop("At least 2 groups are required")
            }
            
            if (nrow(data) < 10) {
              stop("At least 10 observations are required")
            }
            
            # Perform non-parametric ANCOVA
            results <- perform_nonparametric_ancova(data, 
                                                  effect_size = input$npAncovaEffectSize,
                                                  post_hoc = input$npAncovaPostHoc,
                                                  post_hoc_method = input$npAncovaPostHocMethod,
                                                  conf_level = input$npAncovaConfLevel)
            
            # Store results
            np_ancova_results(results)
            
            # Display results
            tagList(
              h3("Non-parametric ANCOVA Results"),
              
              # Data summary
              h4("Data Summary"),
              renderTable({
                summary_table <- data.frame(
                  Statistic = c("Total Observations", "Number of Groups", "Missing Values"),
                  Value = c(results$n_observations, results$n_groups, results$n_missing)
                )
                summary_table
              }, rownames = FALSE),
              
              br(),
              
              # Main test results
              h4("Non-parametric ANCOVA Results"),
              renderDataTable({
                test_table <- results$test_results
                datatable(test_table, 
                         options = list(pageLength = 10, scrollX = TRUE),
                         rownames = FALSE) %>%
                  formatRound(columns = c("Statistic", "p_value", "Effect_Size"), digits = 4)
              }),
              
              br(),
              
              # Descriptive statistics
              h4("Descriptive Statistics"),
              renderDataTable({
                desc_table <- results$descriptive_stats
                datatable(desc_table, 
                         options = list(pageLength = 10, scrollX = TRUE),
                         rownames = FALSE) %>%
                  formatRound(columns = c("Mean", "SD", "Median", "IQR", "CI_Lower", "CI_Upper"), digits = 3)
              }),
              
              br(),
              
              # Adjusted means
              h4("Adjusted Means (Rank-based)"),
              renderDataTable({
                adj_table <- results$adjusted_means
                datatable(adj_table, 
                         options = list(pageLength = 10, scrollX = TRUE),
                         rownames = FALSE) %>%
                  formatRound(columns = c("Adjusted_Mean", "SE", "CI_Lower", "CI_Upper"), digits = 3)
              }),
              
              br(),
              
              # Post-hoc tests
              if (!is.null(results$post_hoc)) tagList(
                h4("Post-hoc Tests"),
                renderDataTable({
                  post_hoc_table <- results$post_hoc
                  datatable(post_hoc_table, 
                           options = list(pageLength = 10, scrollX = TRUE),
                           rownames = FALSE) %>%
                    formatRound(columns = c("Statistic", "p_value", "Effect_Size"), digits = 4)
                }),
                br()
              ),
              
              # Diagnostic plots
              h4("Diagnostic Plots"),
              fluidRow(
                column(6,
                  renderPlot({
                    ggplot(results$data, aes(x = Covariate, y = Dependent, color = Group)) +
                      geom_point(alpha = 0.7) +
                      geom_smooth(method = "lm", se = FALSE) +
                      labs(title = "Scatter Plot with Regression Lines",
                           x = "Covariate", y = "Dependent Variable",
                           color = "Group") +
                      theme_minimal()
                  })
                ),
                column(6,
                  renderPlot({
                    ggplot(results$data, aes(x = Group, y = Dependent, fill = Group)) +
                      geom_boxplot(alpha = 0.7) +
                      labs(title = "Boxplot by Group",
                           x = "Group", y = "Dependent Variable") +
                      theme_minimal() +
                      theme(legend.position = "none")
                  })
                )
              ),
              
              br(),
              
              # Educational content
              h4("Interpretation"),
              p(strong("Non-parametric ANCOVA:"), "Rank-based alternative to ANCOVA that doesn't require normality assumptions."),
              p(strong("Group Effect:"), "Tests whether group differences remain significant after controlling for the covariate."),
              p(strong("Covariate Effect:"), "Tests whether the covariate significantly predicts the dependent variable."),
              p(strong("Adjusted Means:"), "Group means adjusted for the covariate using rank-based methods.")
            )
          }, error = function(e) {
            errorScreenUI(title = "Error in Non-parametric ANCOVA Analysis", errors = e$message)
          })
        }
      })
    })
    
    # Helper function to parse numeric input
    parse_np_ancova_data <- function(input_text) {
      if (is.null(input_text) || input_text == "") {
        stop("Please enter data")
      }
      
      # Parse the input text into a data frame
      lines <- strsplit(input_text, "\n")[[1]]
      lines <- lines[lines != ""]
      
      if (length(lines) < 2) {
        stop("At least 2 lines required (header + data)")
      }
      
      # Parse header
      header <- strsplit(lines[1], ",")[[1]]
      header <- trimws(header)
      
      # Parse data
      data_lines <- lines[-1]
      data_matrix <- matrix(NA, nrow = length(data_lines), ncol = length(header))
      
      for (i in 1:length(data_lines)) {
        values <- strsplit(data_lines[i], ",")[[1]]
        values <- trimws(values)
        
        # Convert to numeric for dependent and covariate, keep as character for group
        if (length(values) != length(header)) {
          stop(paste("Line", i + 1, "has incorrect number of values"))
        }
        
        # Try to convert to numeric, keep as character if it fails
        numeric_values <- suppressWarnings(as.numeric(values))
        data_matrix[i, ] <- ifelse(is.na(numeric_values), values, numeric_values)
      }
      
      data.frame(data_matrix, stringsAsFactors = FALSE)
    }
  })
} 