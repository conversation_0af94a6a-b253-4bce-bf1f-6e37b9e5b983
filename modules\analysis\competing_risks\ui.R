CompetingRisksUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("crUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("crTime"), "Time Variable", choices = NULL),
        selectizeInput(ns("crEvent"), "Event Variable", choices = NULL),
        selectizeInput(ns("crCause"), "Cause Variable", choices = NULL),
        selectizeInput(ns("crCovariates"), "Covariates", choices = NULL, multiple = TRUE),
        br(),
        actionButton(ns("goCR"), label = "Analyze Competing Risks", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("crError")),
        plotOutput(ns("crCIFPlot")),
        verbatimTextOutput(ns("crSummary")),
        plotOutput(ns("crDiagPlot"))
      )
    )
  )
} 