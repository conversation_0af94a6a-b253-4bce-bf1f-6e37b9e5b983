# Placeholder for Causal Inference UI
causalInferenceSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("ciUserData"), "Upload your Data (.csv or .xls or .xlsx or .txt)", accept = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")),
    uiOutput(ns("ciColSelectors")),
    actionButton(ns("goCI"), label = "Calculate", class = "act-btn")
  )
}

causalInferenceMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('ciHT')),
    plotOutput(ns('ciPlot'), width = "50%", height = "400px"),
    uiOutput(ns('ciConclusionOutput'))
  )
}

causalInferenceUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(causalInferenceSidebarUI(id)),
    mainPanel(causalInferenceMainUI(id))
  )
} 