# Shapiro-Wilk Test calculation and output helpers

shapiro_wilk_uploadData_func <- function(swUserData, variable) {
  tryCatch(
    {
      if (is.null(swUserData) || is.null(variable)) {
        return(NULL)
      }
      
      # Read data based on file type
      if (grepl("\\.csv$", swUserData$name, ignore.case = TRUE)) {
        df <- read.csv(swUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.tsv$", swUserData$name, ignore.case = TRUE)) {
        df <- read.tsv(swUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.xlsx$", swUserData$name, ignore.case = TRUE)) {
        df <- readxl::read_excel(swUserData$datapath)
      } else {
        stop("Unsupported file type. Please upload a CSV, TSV, or XLSX file.")
      }
      
      if (!variable %in% names(df)) {
        stop(paste("Variable '", variable, "' not found in the uploaded file.", sep = ""))
      }
      
      # Return the selected variable as a numeric vector
      return(as.numeric(df[[variable]]))
    },
    error = function(e) {
      # Return a list with an error message
      return(list(error = paste("Error reading data:", e$message)))
    }
  )
}

shapiro_wilk_results_func <- function(data) {
  tryCatch({
    if (is.null(data) || length(data) < 3) {
      stop("At least 3 observations are required for Shapiro-Wilk test")
    }
    
    test_result <- shapiro.test(data)
    
    list(
      test = test_result,
      data = data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Shapiro-Wilk test calculation:", e$message))
  })
}

shapiro_wilk_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  
  p_value <- results$test$p.value
  conclusion <- if (p_value < 0.05) "Reject H0. The data does not follow a normal distribution." else "Do not reject H0. The data appears to follow a normal distribution."
  
  tagList(
    h4("Shapiro-Wilk Test for Normality"),
    p(sprintf("Test Statistic (W): %.4f", results$test$statistic)),
    p(sprintf("P-value: %.4f", p_value)),
    p(strong("Conclusion: "), conclusion)
  )
}

shapiro_wilk_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  
  desc_stats <- data.frame(
    N = length(results$data),
    Mean = mean(results$data),
    SD = sd(results$data),
    Median = median(results$data),
    Min = min(results$data),
    Max = max(results$data)
  )
  
  renderTable(desc_stats, digits = 4)
}

shapiro_wilk_plot <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  
  if (!requireNamespace("ggplot2", quietly = TRUE)) {
    stop("Package 'ggplot2' is required for plotting.")
  }
  
  qq_data <- data.frame(Sample = sort(results$data))
  
  ggplot(qq_data, aes(sample = Sample)) +
    stat_qq() +
    stat_qq_line() +
    labs(title = "Q-Q Plot for Normality",
         x = "Theoretical Quantiles",
         y = "Sample Quantiles") +
    theme_minimal()
}