# Mixed-Effects Model calculation and output helpers

mixed_effects_uploadData_func <- function(meUserData) {
  ext <- tools::file_ext(meUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(meUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(meUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(meUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(meUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

mixed_effects_results_func <- function(data, formula, random_effects) {
  tryCatch({
    if (!requireNamespace("lme4", quietly = TRUE)) {
      stop("Package 'lme4' required.")
    }
    
    # Construct the formula with random effects
    random_formula <- paste0("(", random_effects, ")", collapse = " + ")
    full_formula <- as.formula(paste(formula, "+", random_formula))
    
    fit <- lme4::lmer(full_formula, data = data)
    
    list(
      fit = fit,
      data = data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Mixed-Effects Model calculation:", e$message))
  })
}

mixed_effects_ht_html <- function(results) {
  tagList(
    h4("Mixed-Effects Model"),
    p("See summary table for model coefficients and diagnostics.")
  )
}

mixed_effects_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  out <- list(
    h4("Model Summary"),
    renderPrint(summary(results$fit))
  )
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

mixed_effects_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  if (requireNamespace("sjPlot", quietly = TRUE)) {
    sjPlot::plot_model(results$fit, type = "diag")
  } else {
    # Fallback: basic diagnostic plots
    resids <- residuals(results$fit)
    plot(resids, type = 'h', main = 'Mixed Effects Residuals', ylab = 'Residuals')
  }
}