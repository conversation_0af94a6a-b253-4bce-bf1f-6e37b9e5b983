# Bayesian Tests Server
BayesianTestServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload with error handling
    bayesUploadData <- eventReactive(input$bayesUserData, {
      tryCatch({
        data <- handle_file_upload(input$bayesUserData)
        
        # Validate uploaded data
        errors <- validate_data(data, min_rows = 2)
        if (length(errors) > 0) {
          log_error(paste("Data validation failed:", paste(errors, collapse = "; ")), "bayesian_upload")
          return(NULL)
        }
        
        log_info("Data uploaded successfully", "bayesian_upload")
        return(data)
        
      }, error = function(e) {
        log_error(e$message, "bayesian_upload")
        return(NULL)
      })
    })
    
    # Update variable choices when data is uploaded
    observeEvent(bayesUploadData(), {
      data <- bayesUploadData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'bayesGroup', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'bayesResponse', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'bayesVariables', choices = names(data), server = TRUE, multiple = TRUE)
        updateSelectizeInput(session, 'bayesPredictors', choices = names(data), server = TRUE, multiple = TRUE)
      }
    })
    
    # Enhanced validation with error handling
    bayesValidationErrors <- reactive({
      tryCatch({
        errors <- c()
        data <- bayesUploadData()
        
        if (is.null(data) || !is.data.frame(data)) {
          errors <- c(errors, "No data uploaded or file could not be read.")
          return(errors)
        }
        
        # Check based on test type
        if (input$bayesTestType == "Bayesian t-test") {
          if (is.null(input$bayesResponse) || input$bayesResponse == "") {
            errors <- c(errors, "Please select a response column.")
          }
          if (is.null(input$bayesGroup) || input$bayesGroup == "") {
            errors <- c(errors, "Please select a group column.")
          }
        } else if (input$bayesTestType == "Bayesian ANOVA") {
          if (is.null(input$bayesResponse) || input$bayesResponse == "") {
            errors <- c(errors, "Please select a response column.")
          }
          if (is.null(input$bayesGroup) || input$bayesGroup == "") {
            errors <- c(errors, "Please select a group column.")
          }
        } else if (input$bayesTestType == "Bayesian Correlation") {
          if (is.null(input$bayesVariables) || length(input$bayesVariables) < 2) {
            errors <- c(errors, "Please select at least two variables for correlation.")
          }
        } else if (input$bayesTestType == "Bayesian Regression") {
          if (is.null(input$bayesResponse) || input$bayesResponse == "") {
            errors <- c(errors, "Please select a response column.")
          }
          if (is.null(input$bayesPredictors) || length(input$bayesPredictors) == 0) {
            errors <- c(errors, "Please select at least one predictor column.")
          }
        }
        
        # Validate prior specification
        if (input$bayesPrior == "Custom" && (is.null(input$bayesPriorParams) || input$bayesPriorParams == "")) {
          errors <- c(errors, "Please specify custom prior parameters.")
        }
        
        # Check variable types and constraints
        if (!is.null(input$bayesResponse) && input$bayesResponse %in% names(data)) {
          if (!is.numeric(data[[input$bayesResponse]])) {
            errors <- c(errors, "Response variable must be numeric.")
          }
          
          # Check for sufficient variance
          if (is.numeric(data[[input$bayesResponse]])) {
            if (sd(data[[input$bayesResponse]], na.rm = TRUE) == 0) {
              errors <- c(errors, "Response variable must have variance (not all the same).")
            }
          }
        }
        
        if (!is.null(input$bayesGroup) && input$bayesGroup %in% names(data)) {
          group_values <- unique(data[[input$bayesGroup]])
          if (input$bayesTestType == "Bayesian t-test" && length(group_values) != 2) {
            errors <- c(errors, "Group variable must have exactly two distinct values for t-test.")
          }
          
          # Check for sufficient observations per group
          if (input$bayesTestType %in% c("Bayesian t-test", "Bayesian ANOVA")) {
            group_counts <- table(data[[input$bayesGroup]])
            if (any(group_counts < 2)) {
              errors <- c(errors, "Each group must have at least 2 observations.")
            }
          }
        }
        
        # Validate MCMC parameters
        chains <- ifelse(is.null(input$bayesChains), 4, input$bayesChains)
        iterations <- ifelse(is.null(input$bayesIterations), 2000, input$bayesIterations)
        
        if (chains < 1 || chains > 10) {
          errors <- c(errors, "Number of chains must be between 1 and 10.")
        }
        
        if (iterations < 100 || iterations > 10000) {
          errors <- c(errors, "Number of iterations must be between 100 and 10000.")
        }
        
        # Log validation errors if any
        if (length(errors) > 0) {
          log_warning(paste("Validation errors:", paste(errors, collapse = "; ")), "bayesian_validation")
        }
        
        errors
        
      }, error = function(e) {
        log_error(paste("Validation error check failed:", e$message), "bayesian_validation")
        c("An error occurred during validation. Please try again.")
      })
    })
    
    # Bayesian analysis with comprehensive error handling
    bayesResult <- eventReactive(input$goBayesian, {
      data <- bayesUploadData()
      req(data)
      
      # Get parameters from UI with validation
      test_type <- ifelse(is.null(input$bayesTestType), "Bayesian t-test", input$bayesTestType)
      prior <- ifelse(is.null(input$bayesPrior), "Default", input$bayesPrior)
      prior_params <- ifelse(is.null(input$bayesPriorParams), "", input$bayesPriorParams)
      chains <- ifelse(is.null(input$bayesChains), 4, input$bayesChains)
      iterations <- ifelse(is.null(input$bayesIterations), 2000, input$bayesIterations)
      
      # Monitor performance and handle errors
      monitor_performance({
        tryCatch({
          # Perform Bayesian analysis based on test type
          if (test_type == "Bayesian t-test") {
            req(input$bayesResponse, input$bayesGroup)
            
            # Validate required variables
            if (!input$bayesResponse %in% names(data) || !input$bayesGroup %in% names(data)) {
              stop("Required variables not found in data")
            }
            
            bayesian_t_test(
              data = data,
              response = input$bayesResponse,
              group = input$bayesGroup,
              prior = prior,
              prior_params = prior_params,
              chains = chains,
              iterations = iterations
            )
            
          } else if (test_type == "Bayesian ANOVA") {
            req(input$bayesResponse, input$bayesGroup)
            
            # Validate required variables
            if (!input$bayesResponse %in% names(data) || !input$bayesGroup %in% names(data)) {
              stop("Required variables not found in data")
            }
            
            bayesian_anova(
              data = data,
              response = input$bayesResponse,
              group = input$bayesGroup,
              prior = prior,
              prior_params = prior_params,
              chains = chains,
              iterations = iterations
            )
            
          } else if (test_type == "Bayesian Correlation") {
            req(input$bayesVariables)
            
            # Validate variables
            missing_vars <- input$bayesVariables[!input$bayesVariables %in% names(data)]
            if (length(missing_vars) > 0) {
              stop(paste("Variables not found in data:", paste(missing_vars, collapse = ", ")))
            }
            
            bayesian_correlation(
              data = data,
              variables = input$bayesVariables,
              prior = prior,
              prior_params = prior_params,
              chains = chains,
              iterations = iterations
            )
            
          } else if (test_type == "Bayesian Proportion Test") {
            req(input$bayesResponse, input$bayesGroup)
            
            # Validate required variables
            if (!input$bayesResponse %in% names(data) || !input$bayesGroup %in% names(data)) {
              stop("Required variables not found in data")
            }
            
            bayesian_proportion_test(
              data = data,
              response = input$bayesResponse,
              group = input$bayesGroup,
              prior = prior,
              prior_params = prior_params,
              chains = chains,
              iterations = iterations
            )
            
          } else if (test_type == "Bayesian Regression") {
            req(input$bayesResponse, input$bayesPredictors)
            
            # Validate variables
            if (!input$bayesResponse %in% names(data)) {
              stop("Response variable not found in data")
            }
            
            missing_predictors <- input$bayesPredictors[!input$bayesPredictors %in% names(data)]
            if (length(missing_predictors) > 0) {
              stop(paste("Predictor variables not found in data:", paste(missing_predictors, collapse = ", ")))
            }
            
            bayesian_regression(
              data = data,
              response = input$bayesResponse,
              predictors = input$bayesPredictors,
              prior = prior,
              prior_params = prior_params,
              chains = chains,
              iterations = iterations
            )
            
          } else {
            # Fallback to original method
            bayesian_analysis(data, input$bayesResponse, input$bayesGroup, 
                             test_type = test_type, prior = prior, prior_params = prior_params)
          }
          
        }, error = function(e) {
          log_error(paste("Bayesian analysis failed:", e$message), "bayesian_analysis")
          
          # Fallback to original method if new functions fail
          tryCatch({
            bayesian_analysis(data, input$bayesResponse, input$bayesGroup, 
                             test_type = test_type, prior = prior, prior_params = prior_params)
          }, error = function(e2) {
            log_error(paste("Fallback analysis also failed:", e2$message), "bayesian_analysis")
            stop("Analysis failed. Please check your data and parameters.")
          })
        })
        
      }, paste("Bayesian", test_type))
    })
    
    observeEvent(input$goBayesian, {
      output$bayesianResults <- renderUI({
        errors <- bayesValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Bayesian Test", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("bayesTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("bayesAnalysis"),
                title = "Analysis",
                titlePanel("Bayesian Test Results"),
                br(),
                h4("Test Type"),
                textOutput(ns('bayesTestType')),
                h4("Prior Specification"),
                textOutput(ns('bayesPrior')),
                h4("Model Parameters"),
                tableOutput(ns('bayesModelParams')),
                h4("Hypotheses"),
                uiOutput(ns('bayesHypotheses')),
                h4("Bayes Factor"),
                tableOutput(ns('bayesFactor')),
                h4("Posterior Probabilities"),
                tableOutput(ns('bayesPosterior')),
                h4("Effect Size"),
                tableOutput(ns('bayesEffectSize')),
                h4("Credible Intervals"),
                tableOutput(ns('bayesCredibleIntervals')),
                h4("Decision"),
                textOutput(ns('bayesDecision')),
                h4("Interpretation"),
                textOutput(ns('bayesInterpretation')),
                h4("Model Diagnostics"),
                tableOutput(ns('bayesDiagnosticsTable'))
              ),
              tabPanel(
                id = ns("bayesDiagnostics"),
                title = "Diagnostics",
                h4("Prior and Posterior Distributions"),
                plotOutput(ns('bayesPriorPosteriorPlot'), height = "400px"),
                h4("MCMC Diagnostics"),
                plotOutput(ns('bayesMCMCDiagnostics'), height = "400px"),
                h4("Group Comparison Plot"),
                plotOutput(ns('bayesGroupPlot'), height = "300px"),
                h4("Diagnostic Summary"),
                textOutput(ns('bayesDiagnostics'))
              ),
              tabPanel(
                id = ns("bayesModelComparison"),
                title = "Model Comparison",
                h4("Model Comparison Results"),
                tableOutput(ns('bayesModelComparison')),
                h4("Model Weights"),
                tableOutput(ns('bayesModelWeights')),
                h4("Model Selection"),
                textOutput(ns('bayesModelSelection'))
              ),
              tabPanel(
                id = ns("bayesUploadedData"),
                title = "Uploaded Data",
                h4("Raw Uploaded Data"),
                DT::DTOutput(ns('bayesRawDataTable'))
              )
            )
          )
        }
      })
    })
    
    # Test type
    output$bayesTestType <- renderText({
      res <- bayesResult()
      if (is.null(res)) return(NULL)
      paste("Test type:", res$test_type)
    })
    
    # Prior specification
    output$bayesPrior <- renderText({
      res <- bayesResult()
      if (is.null(res)) return(NULL)
      paste("Prior:", res$prior)
    })
    
    # Model parameters
    output$bayesModelParams <- renderTable({
      res <- bayesResult()
      if (is.null(res)) return(NULL)
      
      if ("model_params" %in% names(res)) {
        res$model_params
      } else {
        data.frame(
          Parameter = c("Chains", "Iterations", "Warmup"),
          Value = c(res$chains, res$iterations, res$warmup),
          stringsAsFactors = FALSE
        )
      }
    }, rownames = FALSE, digits = 4)
    
    # Hypotheses
    output$bayesHypotheses <- renderUI({
      res <- bayesResult()
      if (is.null(res)) return(NULL)
      
      if (res$test_type == "Bayesian t-test") {
        tagList(
          p("Null hypothesis: H₀: μ₁ = μ₂"),
          p("Alternative hypothesis: H₁: μ₁ ≠ μ₂")
        )
      } else {
        tagList(
          p("Null hypothesis: H₀: p₁ = p₂"),
          p("Alternative hypothesis: H₁: p₁ ≠ p₂")
        )
      }
    })
    
    # Bayes factor
    output$bayesFactor <- renderTable({
      res <- bayesResult()
      if (is.null(res)) return(NULL)
      res$bayes_factor
    }, rownames = FALSE, digits = 4)
    
    # Posterior probabilities
    output$bayesPosterior <- renderTable({
      res <- bayesResult()
      if (is.null(res)) return(NULL)
      res$posterior_probabilities
    }, rownames = FALSE, digits = 4)
    
    # Effect size
    output$bayesEffectSize <- renderTable({
      res <- bayesResult()
      if (is.null(res)) return(NULL)
      res$effect_size
    }, rownames = FALSE, digits = 4)
    
    # Credible intervals
    output$bayesCredibleIntervals <- renderTable({
      res <- bayesResult()
      if (is.null(res)) return(NULL)
      res$credible_intervals
    }, rownames = FALSE, digits = 4)
    
    # Decision
    output$bayesDecision <- renderText({
      res <- bayesResult()
      if (is.null(res)) return(NULL)
      res$decision
    })
    
    # Interpretation
    output$bayesInterpretation <- renderText({
      res <- bayesResult()
      if (is.null(res)) return(NULL)
      res$interpretation
    })
    
    # Model diagnostics
    output$bayesDiagnosticsTable <- renderTable({
      res <- bayesResult()
      if (is.null(res)) return(NULL)
      res$diagnostics
    }, rownames = FALSE, digits = 4)
    
    # Prior and posterior plot
    output$bayesPriorPosteriorPlot <- renderPlot({
      res <- bayesResult()
      if (is.null(res)) return(NULL)
      
      # Check if we have the new format with posterior samples
      if ("posterior_samples" %in% names(res)) {
        # New format with actual posterior samples
        samples <- res$posterior_samples
        prior_samples <- rnorm(1000, 0, 1)  # Default prior
        
        # Create density plot
        plot(density(samples), main = "Prior and Posterior Distributions",
             xlab = "Parameter", ylab = "Density", col = "red", lwd = 2)
        lines(density(prior_samples), col = "blue", lwd = 2)
        legend("topright", legend = c("Prior", "Posterior"), 
               col = c("blue", "red"), lwd = 2)
      } else {
        # Fallback to original method
        x <- seq(-3, 3, length.out = 100)
        prior <- dnorm(x, 0, 1)
        posterior <- dnorm(x, res$effect_size$Estimate, 0.5)
        
        plot(x, prior, type = "l", col = "blue", lwd = 2, 
             main = "Prior and Posterior Distributions",
             xlab = "Effect Size", ylab = "Density")
        lines(x, posterior, col = "red", lwd = 2)
        legend("topright", legend = c("Prior", "Posterior"), 
               col = c("blue", "red"), lwd = 2)
      }
    })
    
    # MCMC diagnostics
    output$bayesMCMCDiagnostics <- renderPlot({
      res <- bayesResult()
      if (is.null(res)) return(NULL)
      
      # Check if we have MCMC diagnostics
      if ("mcmc_diagnostics" %in% names(res)) {
        # Create MCMC diagnostic plots
        par(mfrow = c(2, 2))
        
        # Trace plot
        if ("trace_plot" %in% names(res$mcmc_diagnostics)) {
          plot(res$mcmc_diagnostics$trace_plot, main = "Trace Plot")
        }
        
        # Autocorrelation plot
        if ("autocorr_plot" %in% names(res$mcmc_diagnostics)) {
          plot(res$mcmc_diagnostics$autocorr_plot, main = "Autocorrelation Plot")
        }
        
        # Density plot
        if ("density_plot" %in% names(res$mcmc_diagnostics)) {
          plot(res$mcmc_diagnostics$density_plot, main = "Density Plot")
        }
        
        # Gelman-Rubin plot
        if ("gelman_plot" %in% names(res$mcmc_diagnostics)) {
          plot(res$mcmc_diagnostics$gelman_plot, main = "Gelman-Rubin Diagnostic")
        }
      } else {
        # Fallback plot
        plot.new()
        text(0.5, 0.5, "MCMC diagnostics not available")
      }
    })
    
    # Group comparison plot
    output$bayesGroupPlot <- renderPlot({
      res <- bayesResult()
      if (is.null(res)) return(NULL)
      
      data <- bayesUploadData()
      if (is.null(data)) return(NULL)
      
      # Create appropriate plot based on test type
      if (input$bayesTestType %in% c("Bayesian t-test", "Bayesian ANOVA")) {
        # Boxplot for group comparisons
        boxplot(as.formula(paste(input$bayesResponse, "~", input$bayesGroup)), 
                data = data, main = "Group Comparison",
                xlab = "Group", ylab = input$bayesResponse)
      } else if (input$bayesTestType == "Bayesian Correlation") {
        # Scatter plot for correlation
        if (length(input$bayesVariables) >= 2) {
          plot(data[[input$bayesVariables[1]]], data[[input$bayesVariables[2]]],
               main = "Correlation Plot",
               xlab = input$bayesVariables[1], ylab = input$bayesVariables[2])
        }
      } else if (input$bayesTestType == "Bayesian Regression") {
        # Residual plot for regression
        if (!is.null(res$fitted_values) && !is.null(res$residuals)) {
          plot(res$fitted_values, res$residuals,
               main = "Residual Plot",
               xlab = "Fitted Values", ylab = "Residuals")
          abline(h = 0, col = "red", lty = 2)
        }
      }
    })
    
    # Model comparison
    output$bayesModelComparison <- renderTable({
      res <- bayesResult()
      if (is.null(res)) return(NULL)
      
      if ("model_comparison" %in% names(res)) {
        res$model_comparison
      } else {
        data.frame(
          Model = "Single Model",
          Log_Likelihood = res$log_likelihood,
          WAIC = res$waic,
          LOOIC = res$looic,
          stringsAsFactors = FALSE
        )
      }
    }, rownames = FALSE, digits = 4)
    
    # Model weights
    output$bayesModelWeights <- renderTable({
      res <- bayesResult()
      if (is.null(res)) return(NULL)
      
      if ("model_weights" %in% names(res)) {
        res$model_weights
      } else {
        data.frame(
          Model = "Single Model",
          Weight = 1.0,
          stringsAsFactors = FALSE
        )
      }
    }, rownames = FALSE, digits = 4)
    
    # Model selection
    output$bayesModelSelection <- renderText({
      res <- bayesResult()
      if (is.null(res)) return(NULL)
      
      if ("model_selection" %in% names(res)) {
        res$model_selection
      } else {
        "Single model analysis - no model comparison performed"
      }
    })
    
    # Diagnostics summary
    output$bayesDiagnostics <- renderText({
      res <- bayesResult()
      if (is.null(res)) return(NULL)
      
      if ("diagnostics_summary" %in% names(res)) {
        res$diagnostics_summary
      } else {
        paste("Bayes factor interpretation: ", res$bf_interpretation,
              "\nEvidence strength: ", res$evidence_strength,
              "\nModel comparison: ", res$model_comparison)
      }
    })
    
    # Raw data table
    output$bayesRawDataTable <- DT::renderDT({
      req(bayesUploadData())
      DT::datatable(bayesUploadData(), options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), rownames = FALSE)
    })
  })
} 