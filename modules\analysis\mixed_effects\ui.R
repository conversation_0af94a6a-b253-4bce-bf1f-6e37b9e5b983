MixedEffectsUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("meUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("meResponse"), "Response Variable", choices = NULL),
        selectizeInput(ns("meFixed"), "Fixed Effects", choices = NULL, multiple = TRUE),
        selectizeInput(ns("meRandom"), "Random Effects (grouping factor)", choices = NULL),
        br(),
        actionButton(ns("goME"), label = "Fit Model", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("meError")),
        verbatimTextOutput(ns("meSummary")),
        plotOutput(ns("meDiagnostics"))
      )
    )
  )
} 