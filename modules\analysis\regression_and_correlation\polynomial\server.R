# Polynomial Regression Server
# Non-linear relationships

source("modules/calculations/polynomial_regression.R")

PolynomialRegressionServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    polyUploadData <- eventReactive(input$polyUserData, {
      handle_file_upload(input$polyUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(polyUploadData(), {
      data <- polyUploadData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'polyResponseVariable', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'polyPredictorVariable', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    polyValidationErrors <- reactive({
      errors <- c()
      
      if (input$polyDataMethod == "Manual Entry") {
        # Manual entry validation
        if (is.null(input$polyData) || input$polyData == "") {
          errors <- c(errors, "Response values are required for manual entry.")
        } else {
          data_vals <- createNumLst(input$polyData)
          if (length(data_vals) < 6) {
            errors <- c(errors, "At least 6 observations are required for polynomial regression.")
          }
        }
        
        if (is.null(input$polyPredictorData) || input$polyPredictorData == "") {
          errors <- c(errors, "Predictor values are required for manual entry.")
        } else {
          pred_vals <- createNumLst(input$polyPredictorData)
          if (length(pred_vals) != length(createNumLst(input$polyData))) {
            errors <- c(errors, "Number of predictor values must match number of response values.")
          }
        }
      } else {
        # File upload validation
        data <- polyUploadData()
        if (is.null(data) || !is.data.frame(data)) {
          errors <- c(errors, "No data uploaded or file could not be read.")
          return(errors)
        }
        if (is.null(input$polyResponseVariable) || input$polyResponseVariable == "") {
          errors <- c(errors, "Please select a response variable.")
        } else {
          var_data <- data[[input$polyResponseVariable]]
          if (!is.numeric(var_data)) {
            errors <- c(errors, "Response variable must be numeric.")
          }
          if (length(na.omit(var_data)) < 6) {
            errors <- c(errors, "At least 6 non-missing observations are required.")
          }
        }
        if (is.null(input$polyPredictorVariable) || input$polyPredictorVariable == "") {
          errors <- c(errors, "Please select a predictor variable.")
        } else {
          pred_data <- data[[input$polyPredictorVariable]]
          if (!is.numeric(pred_data)) {
            errors <- c(errors, "Predictor variable must be numeric.")
          }
        }
      }
      
      # Validate polynomial degree
      if (is.null(input$polyDegree) || input$polyDegree < 1 || input$polyDegree > 5) {
        errors <- c(errors, "Polynomial degree must be between 1 and 5.")
      }
      
      errors
    })
    
    # Polynomial regression analysis reactive
    polyResult <- reactive({
      req(input$polyDataMethod)
      
      if (input$polyDataMethod == "Manual Entry") {
        data_vals <- createNumLst(input$polyData)
        pred_vals <- createNumLst(input$polyPredictorData)
      } else {
        data <- polyUploadData()
        req(data, input$polyResponseVariable, input$polyPredictorVariable)
        data_vals <- data[[input$polyResponseVariable]]
        pred_vals <- data[[input$polyPredictorVariable]]
      }
      
      degree <- input$polyDegree
      conf_level <- input$polyConfLevel
      
      # Perform polynomial regression
      result <- perform_polynomial_regression(data_vals, pred_vals, degree, conf_level)
      
      result
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goPoly, {
      output$polyResults <- renderUI({
        errors <- polyValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Polynomial Regression", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("polyTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("polyAnalysis"),
                title = "Analysis",
                titlePanel("Polynomial Regression Results"),
                br(),
                uiOutput(ns('polySummary')),
                br(),
                h4("Model Summary"),
                tableOutput(ns('polyModelSummary')),
                br(),
                h4("Coefficients"),
                tableOutput(ns('polyCoefficients')),
                br(),
                h4("Model Comparison"),
                tableOutput(ns('polyModelComparison')),
                br(),
                h4("Assumptions and Interpretation"),
                uiOutput(ns('polyAssumptions'))
              ),
              tabPanel(
                id = ns("polyDataSummary"),
                title = "Data Summary",
                h4("Descriptive Statistics"),
                tableOutput(ns('polyDescriptive')),
                br(),
                h4("Correlation Analysis"),
                tableOutput(ns('polyCorrelation')),
                br(),
                h4("Data Visualization"),
                plotOutput(ns('polyScatterplot'), height = "400px")
              ),
              tabPanel(
                id = ns("polyDiagnostics"),
                title = "Diagnostics",
                h4("Residual Analysis"),
                plotOutput(ns('polyResiduals'), height = "600px"),
                br(),
                h4("Influence Analysis"),
                tableOutput(ns('polyInfluence'))
              ),
              tabPanel(
                id = ns("polyUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('polyViewUpload'))
              )
            )
          )
        }
      })
    })
    
    # Analysis Tab Outputs
    output$polySummary <- renderUI({
      req(polyResult())
      result <- polyResult()
      
      tagList(
        h4("Polynomial Regression Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Statistic = c("Sample Size", "Polynomial Degree", "Significance Level", 
                         "R²", "Adjusted R²", "F-statistic", "P-value", "RMSE"),
            Value = c(
              result$n,
              result$degree,
              paste0((1 - result$conf_level) * 100, "%"),
              round(result$r_squared, 4),
              round(result$adj_r_squared, 4),
              round(result$f_statistic, 4),
              ifelse(result$p_value < 0.0001, "< 0.0001", round(result$p_value, 4)),
              round(result$rmse, 4)
            )
          )
        }),
        br(),
        p(strong("Model:"), "Y = β₀ + β₁X + β₂X² + ... + βₖXᵏ")
      )
    })
    
    output$polyModelSummary <- renderTable({
      req(polyResult())
      result <- polyResult()
      
      result$model_summary
    }, digits = 4)
    
    output$polyCoefficients <- renderTable({
      req(polyResult())
      result <- polyResult()
      
      result$coefficients
    }, digits = 4)
    
    output$polyModelComparison <- renderTable({
      req(polyResult())
      result <- polyResult()
      
      result$model_comparison
    }, digits = 4)
    
    output$polyAssumptions <- renderUI({
      req(polyResult())
      result <- polyResult()
      
      tagList(
        h5("Key Assumptions:"),
        p("1. Independence: Observations are independent"),
        p("2. Normality: Residuals are normally distributed"),
        p("3. Homoscedasticity: Constant variance of residuals"),
        p("4. Linearity: Relationship can be modeled by polynomial"),
        br(),
        h5("Assumption Tests:"),
        p("• Normality test p-value: ", round(result$normality_p, 4)),
        p("• Breusch-Pagan test p-value: ", round(result$heteroscedasticity_p, 4)),
        p("• Durbin-Watson statistic: ", round(result$durbin_watson, 4)),
        br(),
        h5("Interpretation:"),
        p("• R²: Proportion of variance explained by the model"),
        p("• Adjusted R²: R² penalized for model complexity"),
        p("• F-statistic: Overall model significance"),
        p("• Coefficients: Change in Y per unit change in X^degree"),
        br(),
        h5("When to Use:"),
        p("• Non-linear relationships between variables"),
        p("• Curved patterns in scatter plots"),
        p("• When linear regression assumptions are violated"),
        p("• Exploring complex functional relationships")
      )
    })
    
    # Data Summary Tab Outputs
    output$polyDescriptive <- renderTable({
      req(polyResult())
      result <- polyResult()
      
      result$descriptive_stats
    }, digits = 4)
    
    output$polyCorrelation <- renderTable({
      req(polyResult())
      result <- polyResult()
      
      result$correlation_analysis
    }, digits = 4)
    
    output$polyScatterplot <- renderPlot({
      req(polyResult())
      result <- polyResult()
      
      # Create scatter plot with polynomial fit
      plot(result$predictor, result$response, 
           main = paste("Polynomial Regression (Degree", result$degree, ")"),
           xlab = "Predictor", ylab = "Response",
           pch = 16, col = "#4F81BD")
      
      # Add polynomial curve
      x_range <- seq(min(result$predictor), max(result$predictor), length.out = 100)
      y_pred <- predict(result$model, newdata = data.frame(x = x_range))
      lines(x_range, y_pred, col = "red", lwd = 2)
      
      # Add legend
      legend("topright", legend = c("Data", paste("Polynomial (degree", result$degree, ")")),
             col = c("#4F81BD", "red"), pch = c(16, NA), lwd = c(NA, 2))
    })
    
    # Diagnostics Tab Outputs
    output$polyResiduals <- renderPlot({
      req(polyResult())
      result <- polyResult()
      
      # Create diagnostic plots
      par(mfrow = c(2, 2))
      
      # Residuals vs Fitted
      plot(fitted(result$model), residuals(result$model),
           main = "Residuals vs Fitted",
           xlab = "Fitted Values", ylab = "Residuals",
           pch = 16, col = "#4F81BD")
      abline(h = 0, col = "red", lty = 2)
      
      # Normal Q-Q plot
      qqnorm(residuals(result$model), main = "Normal Q-Q Plot",
             pch = 16, col = "#4F81BD")
      qqline(residuals(result$model), col = "red")
      
      # Scale-Location plot
      plot(fitted(result$model), sqrt(abs(residuals(result$model))),
           main = "Scale-Location Plot",
           xlab = "Fitted Values", ylab = "√|Residuals|",
           pch = 16, col = "#4F81BD")
      
      # Residuals vs Predictor
      plot(result$predictor, residuals(result$model),
           main = "Residuals vs Predictor",
           xlab = "Predictor", ylab = "Residuals",
           pch = 16, col = "#4F81BD")
      abline(h = 0, col = "red", lty = 2)
    })
    
    output$polyInfluence <- renderTable({
      req(polyResult())
      result <- polyResult()
      
      result$influence_analysis
    }, digits = 4)
    
    # Uploaded Data Tab Output
    output$polyViewUpload <- DT::renderDT({
      req(polyUploadData())
      DT::datatable(polyUploadData(), 
                   options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), 
                   rownames = FALSE)
    })
  })
} 