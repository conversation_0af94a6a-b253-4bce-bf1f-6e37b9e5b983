BayesianRegressionServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    bayesData <- eventReactive(input$bayesUserData, {
      handle_file_upload(input$bayesUserData)
    })
    bayesResult <- eventReactive(input$goBayes, {
      data <- bayesData()
      req(data, input$bayesResponse, input$bayesPredictors)
      formula <- as.formula(paste(input$bayesResponse, "~", paste(input$bayesPredictors, collapse = "+")))
      model_type <- ifelse(input$bayesModelType == "Bayesian Linear Regression", "linear", "anova")
      bayesian_regression(formula, data, model_type)
    })
    observeEvent(input$goBayes, {
      output$bayesError <- renderUI({
        tryCatch({ bayesResult(); NULL }, error = function(e) errorScreenUI(title = "Bayesian Regression Error", errors = e$message))
      })
      output$bayesResults <- renderUI({
        res <- tryCatch(bayesResult(), error = function(e) NULL)
        if (is.null(res)) {
          div(style = "background:#fffbe6; border-left:6px solid #ffcc00; padding:16px; margin-bottom:16px;", "No results to display. Please check your input and try again.")
        } else {
          tagList(
            withMathJax(),
            h3("Bayesian Regression/ANOVA Results"),
            tags$hr(),
            h4("Model Summary"),
            tableOutput(ns("bayesSummaryTable")),
            h4("Posterior/Model Plot"),
            plotOutput(ns("bayesModelPlot"))
          )
        }
      })
      output$bayesSummaryTable <- renderTable({
        res <- tryCatch(bayesResult(), error = function(e) NULL)
        if (is.null(res)) return(NULL)
        as.data.frame(res$summary$fixed)
      }, rownames = TRUE)
      output$bayesModelPlot <- renderPlot({
        res <- tryCatch(bayesResult(), error = function(e) NULL)
        if (is.null(res) || is.null(res$fit)) return(NULL)
        plot(res$fit)
      })
    })
    observeEvent(bayesData(), {
      data <- bayesData()
      updateSelectizeInput(session, 'bayesResponse', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'bayesPredictors', choices = character(0), selected = NULL, server = TRUE)
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'bayesResponse', choices = names(data), selected = NULL, server = TRUE)
        updateSelectizeInput(session, 'bayesPredictors', choices = names(data), selected = NULL, server = TRUE)
      }
    })
  })
} 