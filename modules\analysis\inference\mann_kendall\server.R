mannKendallServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactives
    mkUploadData <- eventReactive(input$mkUserData, {
      handle_file_upload(input$mkUserData)
    })
    
    mkResults <- reactive({
      data <- mkUploadData()
      if (is.null(data)) return(NULL)
      
      if (input$mkFormat == "Single") {
        if (is.null(input$mkValues) || input$mkValues == "") return(NULL)
        values <- data[[input$mkValues]]
        time <- 1:length(values)
      } else {
        if (is.null(input$mkTime) || input$mkTime == "" || 
            is.null(input$mkValues) || input$mkValues == "") return(NULL)
        time <- data[[input$mkTime]]
        values <- data[[input$mkValues]]
      }
      
      # Remove NA values
      complete_cases <- complete.cases(time, values)
      time <- time[complete_cases]
      values <- values[complete_cases]
      
      if (length(values) < 3) return(NULL)
      
      list(time = time, values = values, n = length(values))
    })
    
    # Validation errors
    mkValidationErrors <- reactive({
      errors <- c()
      data <- mkUploadData()
      
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (input$mkFormat == "Single") {
        if (is.null(input$mkValues) || input$mkValues == "") {
          errors <- c(errors, "Please select a values column.")
        } else {
          values <- data[[input$mkValues]]
          if (length(na.omit(values)) < 3) {
            errors <- c(errors, "At least 3 non-missing values are required.")
          }
        }
      } else {
        if (is.null(input$mkTime) || input$mkTime == "") {
          errors <- c(errors, "Please select a time variable.")
        }
        if (is.null(input$mkValues) || input$mkValues == "") {
          errors <- c(errors, "Please select a values variable.")
        }
        if (!is.null(input$mkTime) && !is.null(input$mkValues) && 
            input$mkTime == input$mkValues) {
          errors <- c(errors, "Time and values variables must be different.")
        }
      }
      
      errors
    })
    
    # Outputs
    output$mkHT <- renderUI({
      results <- mkResults()
      if (is.null(results)) return(NULL)
      mannKendallHT(mkResults, reactive({input$mkAlternative}), reactive({input$mkSigLvl}))
    })
    
    output$mannKendallPlot <- renderPlot({
      results <- mkResults()
      if (is.null(results)) return(NULL)
      mannKendallPlot(mkResults)
    })
    
    output$mkConclusionOutput <- renderUI({
      results <- mkResults()
      if (is.null(results)) return(NULL)
      mkConclusion(mkResults, reactive({input$mkAlternative}), reactive({input$mkSigLvl}))
    })
    
    output$renderMKData <- renderUI({
      req(mkUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("mkInitialUploadTable")), style = "width: 75%"),
        br(),
        br()
      )
    })
    
    output$mkInitialUploadTable <- DT::renderDT({
      req(mkUploadData())
      DT::datatable(mkUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(mkUploadData())))))
    })
    
    # Update selectizeInput choices after file upload
    observeEvent(mkUploadData(), {
      data <- mkUploadData()
      updateSelectizeInput(session, 'mkValues', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'mkTime', choices = character(0), selected = NULL, server = TRUE)
      output$mannKendallResults <- renderUI({ NULL })
      
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'mkValues', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'mkTime', choices = names(data), server = TRUE)
        output$mannKendallResults <- renderUI({
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('mkPreviewTable'))
          )
        })
        output$mkPreviewTable <- DT::renderDT({
          head(data, 20)
        })
      }
    })
    
    # Show main results or error screen when Calculate is pressed
    observeEvent(input$goInference, {
      output$mannKendallResults <- renderUI({
        errors <- mkValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Mann-Kendall Trend Test", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("mkTabset"),
              selected = "Analysis",
              tabPanel(
                id    = ns("mk"),
                title = "Analysis",
                titlePanel("Mann-Kendall Trend Test"),
                br(),
                uiOutput(ns('mkHT')),
                br(),
                plotOutput(ns('mannKendallPlot'), width = "50%", height = "400px"),
                br(),
                uiOutput(ns('mkConclusionOutput'))
              ),
              tabPanel(
                id    = ns("mkData"),
                title = "Uploaded Data",
                uiOutput(ns("renderMKData"))
              )
            )
          )
        }
      })
    })
  })
} 