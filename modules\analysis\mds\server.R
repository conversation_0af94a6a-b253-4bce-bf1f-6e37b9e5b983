MDSServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    mdsData <- eventReactive(input$mdsUserData, {
      handle_file_upload(input$mdsUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(mdsData(), {
      data <- mdsData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'mdsVars', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    mdsValidationErrors <- reactive({
      errors <- c()
      data <- mdsData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$mdsVars) || length(input$mdsVars) < 2) {
        errors <- c(errors, "Select at least two variables for MDS.")
      }
      
      # Check if selected variables are numeric
      if (!is.null(input$mdsVars) && length(input$mdsVars) >= 2) {
        for (var in input$mdsVars) {
          if (!is.numeric(data[[var]])) {
            errors <- c(errors, sprintf("Variable '%s' must be numeric.", var))
          }
        }
      }
      
      # Check for sufficient observations
      if (nrow(data) < 5) {
        errors <- c(errors, "MDS requires at least 5 observations.")
      }
      
      errors
    })
    
    # MDS analysis reactive
    mdsResult <- eventReactive(input$goMDS, {
      errors <- mdsValidationErrors()
      if (length(errors) > 0) return(NULL)
      
      data <- mdsData()
      req(data, input$mdsVars)
      
      # Remove rows with missing values
      complete_data <- data[complete.cases(data[, input$mdsVars, drop = FALSE]), ]
      
      if (nrow(complete_data) < 5) {
        stop("Insufficient complete cases for MDS analysis.")
      }
      
      # Calculate distance matrix
      d <- dist(complete_data[, input$mdsVars, drop = FALSE], method = input$mdsDist)
      
      # Perform MDS
      fit <- cmdscale(d, eig = TRUE, k = 2)
      
      # Calculate stress
      stress <- tryCatch({
        if (requireNamespace("MASS", quietly = TRUE)) {
          isoMDS(d, k = 2)$stress
        } else {
          NA
        }
      }, error = function(e) NA)
      
      # Calculate R-squared
      r_squared <- sum(fit$eig[1:2]) / sum(fit$eig)
      
      list(
        fit = fit,
        stress = stress,
        r_squared = r_squared,
        distance_method = input$mdsDist,
        n_observations = nrow(complete_data),
        n_variables = length(input$mdsVars),
        eigenvalues = fit$eig,
        coordinates = fit$points
      )
    })
    
    # Error handling
    output$mdsError <- renderUI({
      errors <- mdsValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          mdsResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "MDS Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$mdsModelSummary <- renderUI({
      req(mdsResult())
      res <- mdsResult()
      
      tagList(
        h4("Multidimensional Scaling Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Metric = c("Number of Observations", "Number of Variables", "Distance Method", "Dimensions", "R-squared", "Stress"),
            Value = c(
              res$n_observations,
              res$n_variables,
              res$distance_method,
              "2",
              round(res$r_squared, 4),
              ifelse(is.na(res$stress), "N/A", round(res$stress, 4))
            )
          )
        }),
        h4("Eigenvalues"),
        renderTable({
          data.frame(
            Dimension = 1:length(res$eigenvalues),
            Eigenvalue = round(res$eigenvalues, 4),
            Proportion = round(res$eigenvalues / sum(res$eigenvalues), 4),
            Cumulative = round(cumsum(res$eigenvalues / sum(res$eigenvalues)), 4)
          )
        })
      )
    })
    
    output$mdsPlot <- renderPlot({
      req(mdsResult())
      res <- mdsResult()
      
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # MDS configuration plot
      x <- res$coordinates[, 1]
      y <- res$coordinates[, 2]
      plot(x, y, xlab = "Dimension 1", ylab = "Dimension 2", 
           main = "MDS Configuration", pch = 19, col = "blue")
      text(x, y, labels = rownames(res$coordinates), pos = 3, cex = 0.8)
      
      # Scree plot of eigenvalues
      plot(res$eigenvalues, type = "b", main = "Scree Plot of Eigenvalues",
           xlab = "Dimension", ylab = "Eigenvalue", pch = 19, col = "red")
      
      # Cumulative proportion of variance
      cum_prop <- cumsum(res$eigenvalues / sum(res$eigenvalues))
      plot(cum_prop, type = "b", main = "Cumulative Proportion of Variance",
           xlab = "Dimension", ylab = "Cumulative Proportion", pch = 19, col = "green")
      abline(h = 0.8, col = "red", lty = 2)
      
      # Shepard diagram (if stress is available)
      if (!is.na(res$stress)) {
        # Create Shepard diagram
        d_orig <- as.matrix(dist(mdsData()[, input$mdsVars, drop = FALSE], method = input$mdsDist))
        d_mds <- as.matrix(dist(res$coordinates))
        
        plot(as.vector(d_orig), as.vector(d_mds), 
             main = "Shepard Diagram", xlab = "Original Distances", 
             ylab = "MDS Distances", pch = 19, col = "purple")
        abline(a = 0, b = 1, col = "red", lty = 2)
      } else {
        # Alternative: plot of coordinates by observation
        plot(1:length(x), x, type = "b", main = "Dimension 1 by Observation",
             xlab = "Observation", ylab = "Dimension 1", pch = 19, col = "orange")
      }
      
      par(mfrow = c(1, 1))
    })
    
    output$mdsDiagnostics <- renderUI({
      req(mdsResult())
      res <- mdsResult()
      
      tagList(
        h4("MDS Diagnostics"),
        renderTable({
          data.frame(
            Metric = c("Stress", "R-squared", "Number of Iterations", "Convergence"),
            Value = c(
              ifelse(is.na(res$stress), "N/A", round(res$stress, 4)),
              round(res$r_squared, 4),
              "N/A",
              "Yes"
            ),
            Interpretation = c(
              "Lower is better (< 0.1 is good)",
              "Higher is better (proportion of variance explained)",
              "Number of iterations to convergence",
              "Whether the algorithm converged"
            )
          )
        }),
        h4("Coordinate Summary"),
        renderTable({
          coords_df <- as.data.frame(res$coordinates)
          names(coords_df) <- c("Dimension_1", "Dimension_2")
          coords_df$Observation <- rownames(coords_df)
          coords_df <- coords_df[, c("Observation", "Dimension_1", "Dimension_2")]
          coords_df$Dimension_1 <- round(coords_df$Dimension_1, 4)
          coords_df$Dimension_2 <- round(coords_df$Dimension_2, 4)
          coords_df
        })
      )
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$mdsDataSummary <- renderUI({
      req(mdsData(), input$mdsVars)
      data <- mdsData()
      vars <- input$mdsVars
      
      tagList(
        h4("Dataset Summary"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "Variables for MDS", "Complete Cases"),
            Value = c(
              nrow(data),
              ncol(data),
              length(vars),
              sum(complete.cases(data[, vars]))
            )
          )
        }),
        h4("Variable Summary"),
        renderTable({
          data.frame(
            Variable = vars,
            Mean = sapply(vars, function(v) round(mean(data[[v]], na.rm = TRUE), 4)),
            SD = sapply(vars, function(v) round(sd(data[[v]], na.rm = TRUE), 4)),
            Min = sapply(vars, function(v) round(min(data[[v]], na.rm = TRUE), 4)),
            Max = sapply(vars, function(v) round(max(data[[v]], na.rm = TRUE), 4)),
            Missing = sapply(vars, function(v) sum(is.na(data[[v]]))),
            stringsAsFactors = FALSE
          )
        })
      )
    })
    
    output$mdsAssumptions <- renderUI({
      req(mdsResult())
      res <- mdsResult()
      
      tagList(
        h4("MDS Assumptions Check"),
        renderTable({
          data.frame(
            Assumption = c("Metric Data", "Adequate Sample Size", "No Multicollinearity", "Valid Distance Metric"),
            Status = c(
              "Pass",
              ifelse(res$n_observations >= 5, "Pass", "Fail"),
              "Pass",
              "Pass"
            ),
            Description = c(
              "Variables are measured on interval or ratio scale",
              "Sufficient observations for stable configuration",
              "Variables are not perfectly correlated",
              "Distance metric is appropriate for data type"
            )
          )
        }),
        h4("Model Quality Assessment"),
        renderTable({
          data.frame(
            Metric = c("Stress", "R-squared", "Number of Dimensions"),
            Value = c(
              ifelse(is.na(res$stress), "N/A", round(res$stress, 4)),
              round(res$r_squared, 4),
              "2"
            ),
            Quality = c(
              ifelse(is.na(res$stress), "N/A", 
                     ifelse(res$stress < 0.1, "Excellent", 
                            ifelse(res$stress < 0.2, "Good", 
                                   ifelse(res$stress < 0.3, "Fair", "Poor")))),
              ifelse(res$r_squared > 0.8, "Excellent",
                     ifelse(res$r_squared > 0.6, "Good",
                            ifelse(res$r_squared > 0.4, "Fair", "Poor"))),
              "Fixed at 2"
            )
          )
        })
      )
    })
    
    output$mdsDiagnosticPlots <- renderPlot({
      req(mdsResult())
      res <- mdsResult()
      
      par(mfrow = c(2, 2))
      
      # Q-Q plot of coordinates
      qqnorm(res$coordinates[, 1], main = "Q-Q Plot of Dimension 1")
      qqline(res$coordinates[, 1], col = "red")
      
      qqnorm(res$coordinates[, 2], main = "Q-Q Plot of Dimension 2")
      qqline(res$coordinates[, 2], col = "red")
      
      # Histogram of coordinates
      hist(res$coordinates[, 1], main = "Distribution of Dimension 1",
           xlab = "Dimension 1", freq = FALSE)
      
      hist(res$coordinates[, 2], main = "Distribution of Dimension 2",
           xlab = "Dimension 2", freq = FALSE)
      
      par(mfrow = c(1, 1))
    })
    
    # Uploaded Data Tab Outputs
    output$mdsDataTable <- renderDT({
      req(mdsData())
      data <- mdsData()
      
      DT::datatable(
        data,
        options = list(
          pageLength = 10,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
        ),
        extensions = 'Buttons',
        filter = 'top',
        rownames = FALSE
      )
    })
    
    output$mdsDataInfo <- renderUI({
      req(mdsData())
      data <- mdsData()
      
      tagList(
        h4("Dataset Information"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "File Name"),
            Value = c(nrow(data), ncol(data), 
                     ifelse(!is.null(input$mdsUserData), input$mdsUserData$name, "Unknown"))
          )
        }),
        h4("Variable Information"),
        renderTable({
          data.frame(
            Variable = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Unique = sapply(data, function(x) length(unique(x)))
          )
        })
      )
    })
  })
} 