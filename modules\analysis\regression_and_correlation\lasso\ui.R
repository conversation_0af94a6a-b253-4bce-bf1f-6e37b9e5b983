# Lasso Regression UI
# Variable selection and regularization with L1 penalty

LassoRegressionSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    radioButtons(
      inputId = ns("lassoDataMethod"),
      label = "Data Input Method:",
      choices = c("Manual Entry" = "Manual Entry", "Upload File" = "Upload File"),
      selected = "Manual Entry"
    ),
    conditionalPanel(
      condition = "input.lassoDataMethod == 'Manual Entry'",
      ns = ns,
      textAreaInput(
        inputId = ns("lassoResponseData"),
        label = "Enter Response Values (comma, space, or newline separated):",
        placeholder = "1, 2, 3, 4, 5, 6",
        rows = 4
      ),
      textAreaInput(
        inputId = ns("lassoPredictorData"),
        label = "Enter Predictor Values (comma, space, or newline separated):",
        placeholder = "1, 2, 3, 4, 5, 6",
        rows = 4
      )
    ),
    conditionalPanel(
      condition = "input.lassoDataMethod == 'Upload File'",
      ns = ns,
      fileInput(
        inputId = ns("lassoUserData"),
        label = "Upload Data File:",
        accept = c(".csv", ".txt", ".xlsx", ".xls"),
        buttonLabel = "Browse Files",
        placeholder = "No file selected"
      ),
      selectizeInput(
        inputId = ns("lassoResponseVariable"),
        label = "Select Response Variable:",
        choices = NULL,
        options = list(placeholder = "Select response variable...")
      ),
      selectizeInput(
        inputId = ns("lassoPredictorVariables"),
        label = "Select Predictor Variables:",
        choices = NULL,
        multiple = TRUE,
        options = list(placeholder = "Select predictor variables...")
      )
    ),
    numericInput(
      inputId = ns("lassoLambda"),
      label = "Lambda (Regularization Parameter):",
      value = 0.1,
      min = 0.001,
      max = 10,
      step = 0.001
    ),
    checkboxInput(
      inputId = ns("lassoCrossValidation"),
      label = "Use Cross-Validation for Lambda Selection",
      value = TRUE
    ),
    conditionalPanel(
      condition = "input.lassoCrossValidation == true",
      ns = ns,
      numericInput(
        inputId = ns("lassoCVFolds"),
        label = "Number of CV Folds:",
        value = 10,
        min = 3,
        max = 20,
        step = 1
      )
    ),
    numericInput(
      inputId = ns("lassoConfLevel"),
      label = "Confidence Level:",
      value = 0.95,
      min = 0.5,
      max = 0.99,
      step = 0.01
    ),
    actionButton(
      inputId = ns("goLasso"),
      label = "Calculate Lasso Regression",
      class = "btn-primary",
      style = "width: 100%;"
    ),
    br(),
    br(),
    helpText(
      "Lasso regression performs variable selection by shrinking coefficients to zero.",
      "Lambda controls the strength of regularization.",
      "Cross-validation helps select optimal lambda value.",
      "Useful for high-dimensional data and feature selection."
    )
  )
}

LassoRegressionMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns("lassoResults"))
  )
}

LassoRegressionUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(LassoRegressionSidebarUI(id)),
    mainPanel(LassoRegressionMainUI(id))
  )
} 