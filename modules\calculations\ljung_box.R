# Ljung-Box Test calculation and output helpers

ljung_box_uploadData_func <- function(lbUserData) {
  ext <- tools::file_ext(lbUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(lbUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(lbUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(lbUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(lbUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

ljung_box_results_func <- function(data, var, lag = 1) {
  tryCatch({
    x <- data[[var]]
    x <- x[!is.na(x)]
    
    if (length(x) < 3) {
      stop("At least 3 observations are required.")
    }
    
    if (lag >= length(x)) {
      stop("Number of lags must be less than the number of observations.")
    }
    
    test_result <- Box.test(x, lag = lag, type = "Ljung-Box")
    
    list(
      test = test_result,
      data = x,
      var = var,
      lag = lag,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Ljung-Box test calculation:", e$message))
  })
}

ljung_box_ht_html <- function(results, sigLvl) {
  test <- results$test
  p_value <- test$p.value
  
  conclusion <- if (p_value < sigLvl) "significant autocorrelation" else "no significant autocorrelation"
  
  withMathJax(tagList(
    h4("Ljung-Box Test"),
    p(sprintf("There is %s in the time series up to lag %d.", conclusion, results$lag)),
    p(sprintf("Test Statistic (Q): %.4f", test$statistic)),
    p(sprintf("P-value: %.4f", p_value))
  ))
}

ljung_box_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  out <- list(
    h4("Test Details"),
    renderPrint(results$test)
  )
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

ljung_box_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  acf_data <- acf(results$data, lag.max = results$lag, plot = FALSE)
  autoplot(acf_data) + labs(title = "Autocorrelation Function")
}