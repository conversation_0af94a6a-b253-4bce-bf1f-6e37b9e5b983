correspondenceAnalysisSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(
      inputId = ns("caUserData"),
      label   = strong("Upload your Data (.csv or .xls or .xlsx or .txt)"),
      accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
    ),
    div(
      id = ns("caUploadInputs"),
      selectizeInput(
        inputId = ns("caRowVar"),
        label = strong("Row Variable"),
        choices = c(""),
        selected = NULL,
        options = list(placeholder = 'Select row variable', onInitialize = I('function() { this.setValue(""); }'))
      ),
      selectizeInput(
        inputId = ns("caColVar"),
        label = strong("Column Variable"),
        choices = c(""),
        selected = NULL,
        options = list(placeholder = 'Select column variable', onInitialize = I('function() { this.setValue(""); }'))
      )
    ),
    numericInput(
      inputId = ns("caNDim"),
      label = strong("Number of Dimensions"),
      value = 2,
      min = 1,
      max = 5,
      step = 1
    ),
    radioButtons(
      inputId = ns("caSigLvl"),
      label = strong("Significance Level (alpha)"),
      choices  = c("10%", "5%", "1%"),
      selected = "5%",
      inline   = TRUE
    ),
    br(),
    actionButton(ns("goCorrespondenceAnalysis"), label = "Calculate", class = "act-btn")
  )
}

correspondenceAnalysisMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('correspondenceAnalysisResults'))
  )
}

correspondenceAnalysisUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(correspondenceAnalysisSidebarUI(id)),
    mainPanel(correspondenceAnalysisMainUI(id))
  )
} 