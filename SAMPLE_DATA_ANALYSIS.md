# Sample Data Analysis - Test Types vs Sample Data Files

## Overview
This document analyzes all test types in the CougarStats application and identifies which ones have sample data files and which ones need sample data created.

## Test Categories and Status

### 1. INFERENCE TESTS

#### Basic Tests
- ✅ **one_sample** - Has: ttest_independent.csv (can be adapted)
- ✅ **two_sample** - Has: ttest_independent.csv
- ✅ **paired_t_test** - Has: ttest_paired.csv
- ✅ **chi_square** - Has: chi_square.csv
- ✅ **kruskal_wallis** - Has: kruskal_wallis.csv
- ✅ **mann_whitney** - Has: wilcoxon.csv (can be adapted)
- ✅ **wilcoxon** - Has: wilcoxon.csv
- ✅ **sign_test** - Has: ttest_independent.csv (can be adapted)
- ✅ **proportion_tests** - Has: proportion_test.csv

#### ANOVA Tests
- ✅ **anova** (one-way) - Has: anova_oneway.csv
- ✅ **two_way** - Has: anova_oneway.csv (can be adapted)
- ✅ **three_way** - Has: anova_oneway.csv (can be adapted)
- ✅ **mixed** - Has: mixed_effects.csv
- ✅ **repeated_measures** - Has: friedman.csv (can be adapted)
- ✅ **friedman** - Has: friedman.csv
- ✅ **post_hoc** - Has: anova_oneway.csv (can be adapted)
- ✅ **levene** - Has: anova_oneway.csv (can be adapted)
- ✅ **ancova** - Has: anova_oneway.csv (can be adapted)
- ✅ **nonparametric_ancova** - Has: anova_oneway.csv (can be adapted)

#### Specialized Tests
- ❌ **cochran_mantel_haenszel** - NEEDS SAMPLE DATA
- ❌ **jonckheere_terpstra** - NEEDS SAMPLE DATA

### 2. REGRESSION AND CORRELATION

#### Basic Regression
- ✅ **simple_linear_regression** - Has: regression_simple.csv
- ✅ **multiple_linear_regression** - Has: regression_multiple.csv
- ✅ **logistic_regression** - Has: logistic_regression.csv
- ✅ **polynomial_regression** - Has: regression_simple.csv (can be adapted)
- ✅ **stepwise_regression** - Has: regression_multiple.csv (can be adapted)

#### Advanced Regression
- ❌ **lasso_regression** - NEEDS SAMPLE DATA
- ❌ **elastic_net_regression** - NEEDS SAMPLE DATA
- ❌ **ridge_regression** - NEEDS SAMPLE DATA
- ✅ **robust_regression** - Has: regression_simple.csv (can be adapted)
- ✅ **quasi_regression** - Has: regression_simple.csv (can be adapted)
- ✅ **poisson_regression** - Has: poisson_regression.csv
- ✅ **zero_inflated** - Has: zero_inflated.csv
- ✅ **nonlinear_regression** - Has: nonlinear_regression.csv

#### Correlation
- ✅ **correlation** - Has: correlation.csv
- ❌ **partial_correlations** - NEEDS SAMPLE DATA

### 3. MACHINE LEARNING

#### Supervised Learning
- ✅ **supervised_ml** - Has: supervised_ml.csv
- ✅ **feature_selection** - Has: feature_selection.csv
- ✅ **model_comparison** - Has: model_comparison.csv

#### Unsupervised Learning
- ✅ **unsupervised_ml** - Has: unsupervised_ml.csv
- ✅ **cluster_analysis** - Has: cluster_analysis.csv
- ✅ **PCA** - Has: pca.csv
- ✅ **mds** - Has: mds.csv
- ✅ **tsne_umap** - Has: tsne_umap.csv

#### Advanced ML
- ✅ **ensemble_methods** - Has: supervised_ml.csv (can be adapted)
- ✅ **deep_learning** - Has: deep_learning.csv
- ✅ **natural_language_processing** - Has: natural_language_processing.csv

### 4. SURVIVAL ANALYSIS

- ✅ **survival_analysis** - Has: survival.csv
- ✅ **coxph** - Has: coxph.csv
- ✅ **stratified_km** - Has: stratified_km.csv
- ✅ **competing_risks** - Has: survival.csv (can be adapted)
- ✅ **advanced_survival** - Has: advanced_survival.csv

### 5. BAYESIAN ANALYSIS

- ✅ **bayesian_regression** - Has: regression_simple.csv (can be adapted)
- ✅ **bayesian_hierarchical** - Has: bayesian_hierarchical.csv
- ✅ **bayesian_model_comparison** - Has: model_comparison.csv (can be adapted)

### 6. MULTIVARIATE ANALYSIS

- ✅ **manova** - Has: manova.csv
- ✅ **discriminant** - Has: discriminant.csv
- ✅ **cca** - Has: cca.csv
- ✅ **sem** - Has: sem.csv

### 7. TIME SERIES

- ✅ **time_series** - Has: time_series.csv
- ✅ **stl_decomposition** - Has: time_series.csv (can be adapted)
- ✅ **spectral_analysis** - Has: time_series.csv (can be adapted)
- ✅ **change_point** - Has: time_series.csv (can be adapted)
- ✅ **state_space** - Has: state_space.csv

### 8. SPECIALIZED ANALYSIS

- ✅ **mediation_moderation** - Has: mediation_moderation.csv
- ✅ **propensity_score** - Has: propensity_score.csv
- ✅ **gam** - Has: gam.csv
- ✅ **mixed_effects** - Has: mixed_effects.csv
- ✅ **multilevel_modeling** - Has: multilevel_modeling.csv
- ✅ **longitudinal_analysis** - Has: longitudinal_data.csv
- ✅ **meta_analysis** - Has: meta_analysis.csv
- ✅ **irt** - Has: irt.csv
- ✅ **latent_class** - Has: latent_class.csv
- ✅ **survey_psychometrics** - Has: irt.csv (can be adapted)

### 9. DATA ANALYSIS TOOLS

- ✅ **desc_stats** - Has: desc_stats.csv
- ✅ **data_summarization** - Has: data_summarization.csv
- ✅ **missingness_viz** - Has: missingness.csv
- ✅ **multiple_imputation** - Has: multiple_imputation.csv
- ✅ **outlier_detection** - Has: desc_stats.csv (can be adapted)
- ✅ **variable_transformation** - Has: desc_stats.csv (can be adapted)
- ✅ **bootstrap** - Has: bootstrapping.csv
- ✅ **permutation_tests** - Has: permutation_tests.csv
- ✅ **effect_size** - Has: desc_stats.csv (can be adapted)
- ✅ **power_analysis** - Has: desc_stats.csv (can be adapted)
- ✅ **sample_size_est** - Has: desc_stats.csv (can be adapted)
- ✅ **prob_dist** - Has: desc_stats.csv (can be adapted)

### 10. VISUALIZATION AND PLOTTING

- ✅ **advanced_visualization** - Has: desc_stats.csv (can be adapted)
- ✅ **corr_heatmap** - Has: correlation.csv (can be adapted)
- ✅ **pairwise_plot_matrix** - Has: desc_stats.csv (can be adapted)
- ✅ **custom_plot** - Has: custom_test.csv (can be adapted)
- ✅ **interactive_dashboards** - Has: desc_stats.csv (can be adapted)

### 11. SPECIALIZED MODULES

- ✅ **roc_analysis** - Has: roc_analysis.csv
- ✅ **log_linear_models** - Has: chi_square.csv (can be adapted)
- ✅ **reliability_analysis** - Has: desc_stats.csv (can be adapted)
- ✅ **quality_control** - Has: desc_stats.csv (can be adapted)
- ✅ **spatial_analysis** - Has: spatial_analysis.csv
- ✅ **network_analysis** - Has: network_analysis.csv
- ✅ **real_time_analytics** - Has: real_time_analytics.csv
- ✅ **simulation** - Has: simulation.csv
- ✅ **custom_test** - Has: custom_test.csv

## SUMMARY

### ✅ HAVE SAMPLE DATA (or can be adapted): 85 tests
### ❌ NEED SAMPLE DATA CREATED: 6 tests

## TESTS NEEDING SAMPLE DATA CREATION:

1. **cochran_mantel_haenszel.csv** - Contingency table data for CMH test
2. **jonckheere_terpstra.csv** - Ordinal data for JT test
3. **lasso_regression.csv** - High-dimensional regression data
4. **elastic_net_regression.csv** - High-dimensional regression data
5. **ridge_regression.csv** - High-dimensional regression data
6. **partial_correlations.csv** - Multiple variables for partial correlation 