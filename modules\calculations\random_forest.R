# Random Forest calculation and output helpers

random_forest_uploadData_func <- function(rfUserData, response, predictors) {
  tryCatch(
    {
      if (is.null(rfUserData) || is.null(response) || is.null(predictors)) {
        return(NULL)
      }
      
      # Read data based on file type
      if (grepl("\\.csv$", rfUserData$name, ignore.case = TRUE)) {
        df <- read.csv(rfUserData$datapath, stringsAsFactors = TRUE)
      } else if (grepl("\\.tsv$", rfUserData$name, ignore.case = TRUE)) {
        df <- read.tsv(rfUserData$datapath, stringsAsFactors = TRUE)
      } else if (grepl("\\.xlsx$", rfUserData$name, ignore.case = TRUE)) {
        df <- readxl::read_excel(rfUserData$datapath)
      } else {
        stop("Unsupported file type. Please upload a CSV, TSV, or XLSX file.")
      }
      
      # Check for required columns
      required_cols <- c(response, predictors)
      if (!all(required_cols %in% names(df))) {
        missing_cols <- required_cols[!required_cols %in% names(df)]
        stop(paste("The following columns were not found in the uploaded file:", paste(missing_cols, collapse = ", ")))
      }
      
      # Ensure response variable is a factor for classification
      df[[response]] <- as.factor(df[[response]])
      
      return(df)
    },
    error = function(e) {
      return(list(error = paste("Error reading data:", e$message)))
    }
  )
}

random_forest_results_func <- function(data, response, predictors, ntree = 500, mtry = NULL) {
  tryCatch({
    if (is.null(data) || nrow(data) == 0) {
      stop("No data provided for analysis.")
    }
    
    if (!requireNamespace("randomForest", quietly = TRUE)) {
      stop("Package 'randomForest' is required for Random Forest analysis.")
    }
    
    formula <- as.formula(paste(response, "~", paste(predictors, collapse = "+")))
    
    # Set mtry to default if not specified
    if (is.null(mtry)) {
      mtry <- floor(sqrt(length(predictors)))
    }
    
    fit <- randomForest::randomForest(formula, data = data, ntree = ntree, mtry = mtry, importance = TRUE)
    
    list(
      model = fit,
      importance = randomForest::importance(fit),
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Random Forest calculation:", e$message))
  })
}

random_forest_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  tagList(
    h4("Random Forest Model"),
    p("See summary and plots for model details.")
  )
}

random_forest_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  renderPrint(results$model)
}

random_forest_plot <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  
  if (!requireNamespace("ggplot2", quietly = TRUE)) {
    stop("Package 'ggplot2' is required for plotting.")
  }
  
  # Variable importance plot
  imp_df <- as.data.frame(results$importance)
  imp_df$Variable <- rownames(imp_df)
  
  p1 <- ggplot(imp_df, aes(x = reorder(Variable, MeanDecreaseGini), y = MeanDecreaseGini)) +
    geom_col(fill = "steelblue") +
    coord_flip() +
    labs(title = "Variable Importance (Mean Decrease Gini)", x = "Variable", y = "Importance") +
    theme_minimal()
    
  # OOB error rate plot
  oob_error_data <- as.data.frame(results$model$err.rate)
  oob_error_data$ntree <- 1:nrow(oob_error_data)
  
  p2 <- ggplot(oob_error_data, aes(x = ntree, y = OOB)) +
    geom_line(color = "red") +
    labs(title = "OOB Error Rate", x = "Number of Trees", y = "OOB Error") +
    theme_minimal()

  gridExtra::grid.arrange(p1, p2, ncol = 2)
}