MissingnessVizServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    missData <- eventReactive(input$missUserData, {
      handle_file_upload(input$missUserData)
    })
    
    # Validation errors reactive
    missValidationErrors <- reactive({
      errors <- c()
      data <- missData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      # Check if VIM package is available
      if (!requireNamespace("VIM", quietly = TRUE)) {
        errors <- c(errors, "Package 'VIM' is required for missingness visualization.")
      }
      
      # Check if there are any missing values
      if (sum(is.na(data)) == 0) {
        errors <- c(errors, "No missing values found in the dataset.")
      }
      
      errors
    })
    
    # Missingness visualization analysis reactive
    missResult <- eventReactive(input$goMiss, {
      data <- missData()
      req(data)
      
      # Check for missing data
      if (sum(is.na(data)) == 0) {
        stop("No missing values found in the dataset.")
      }
      
      # Perform missingness visualization
      missingness_viz(data)
    })
    
    # Error handling
    output$missError <- renderUI({
      errors <- missValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          missResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "Missingness Visualization Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$missModelSummary <- renderUI({
      req(missResult())
      res <- missResult()
      
      tagList(
        h4("Missingness Analysis Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Metric = c("Total Observations", "Variables with Missing Data", "Total Missing Values", "Missing Percentage"),
            Value = c(
              res$total_observations,
              res$variables_with_missing,
              res$total_missing,
              paste(round(res$missing_percentage, 2), "%")
            )
          )
        }),
        h4("Missingness Pattern"),
        renderTable({
          if (!is.null(res$missing_pattern)) {
            res$missing_pattern
          } else {
            data.frame(
              Pattern = "N/A",
              Count = "N/A",
              Percentage = "N/A",
              stringsAsFactors = FALSE
            )
          }
        })
      )
    })
    
    output$missPlot <- renderPlot({
      req(missResult())
      res <- missResult()
      
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # Missing data pattern
      if (requireNamespace("VIM", quietly = TRUE)) {
        VIM::aggr(res$data, plot = TRUE, numbers = TRUE, main = "Missing Data Pattern")
      } else {
        plot.new()
        title("Missing Data Pattern\n(VIM package required)")
      }
      
      # Missing data by variable
      if (!is.null(res$missing_by_variable)) {
        barplot(res$missing_by_variable$Missing_Count, 
                names.arg = res$missing_by_variable$Variable,
                main = "Missing Values by Variable",
                ylab = "Missing Count", col = "red", las = 2)
      }
      
      # Missing data percentage by variable
      if (!is.null(res$missing_by_variable)) {
        barplot(res$missing_by_variable$Missing_Percentage, 
                names.arg = res$missing_by_variable$Variable,
                main = "Missing Percentage by Variable",
                ylab = "Missing Percentage (%)", col = "orange", las = 2)
      }
      
      # Missing data correlation
      if (!is.null(res$missing_correlation)) {
        image(res$missing_correlation, main = "Missing Data Correlation",
              col = heat.colors(10), axes = FALSE)
        axis(1, at = seq(0, 1, length.out = ncol(res$missing_correlation)), 
             labels = colnames(res$missing_correlation), las = 2)
        axis(2, at = seq(0, 1, length.out = nrow(res$missing_correlation)), 
             labels = rownames(res$missing_correlation), las = 2)
      }
      
      par(mfrow = c(1, 1))
    })
    
    output$missDiagnostics <- renderUI({
      req(missResult())
      res <- missResult()
      
      tagList(
        h4("Missingness Diagnostics"),
        renderTable({
          data.frame(
            Metric = c("Missing Data Type", "Missing Pattern Complexity", "Variables Affected", "Observations Affected"),
            Value = c(
              res$missing_type,
              res$pattern_complexity,
              res$variables_with_missing,
              res$observations_with_missing
            )
          )
        }),
        h4("Missingness Statistics"),
        renderTable({
          if (!is.null(res$missing_stats)) {
            res$missing_stats
          } else {
            data.frame(
              Statistic = "N/A",
              Value = "N/A",
              stringsAsFactors = FALSE
            )
          }
        })
      )
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$missDataSummary <- renderUI({
      req(missData())
      data <- missData()
      
      tagList(
        h4("Dataset Summary"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "Complete Cases", "Missing Cases", "Missing Percentage"),
            Value = c(
              nrow(data),
              ncol(data),
              sum(complete.cases(data)),
              sum(!complete.cases(data)),
              paste(round(mean(!complete.cases(data)) * 100, 2), "%")
            )
          )
        }),
        h4("Missing Data by Variable"),
        renderTable({
          missing_summary <- data.frame(
            Variable = names(data),
            Missing_Count = sapply(data, function(x) sum(is.na(x))),
            Missing_Percentage = sapply(data, function(x) round(mean(is.na(x)) * 100, 2)),
            stringsAsFactors = FALSE
          )
          missing_summary[missing_summary$Missing_Count > 0, ]
        })
      )
    })
    
    output$missAssumptions <- renderUI({
      req(missResult())
      res <- missResult()
      
      tagList(
        h4("Missingness Assumptions Check"),
        renderTable({
          data.frame(
            Assumption = c("Missing Data Present", "Adequate Sample Size", "Missing Pattern Identifiable"),
            Status = c(
              ifelse(res$total_missing > 0, "Pass", "Fail"),
              ifelse(res$total_observations >= 10, "Pass", "Fail"),
              "Pass"
            ),
            Description = c(
              "Dataset contains missing values for analysis",
              "Sufficient observations for pattern identification",
              "Missing data pattern can be identified and visualized"
            )
          )
        }),
        h4("Missingness Guidelines"),
        renderTable({
          data.frame(
            Pattern = c("Missing Completely at Random (MCAR)", "Missing at Random (MAR)", "Missing Not at Random (MNAR)"),
            Description = c(
              "Missingness is unrelated to observed or unobserved data",
              "Missingness is related to observed data but not unobserved data",
              "Missingness is related to unobserved data"
            ),
            Implication = c(
              "Listwise deletion is acceptable",
              "Multiple imputation or other methods needed",
              "Special handling required, may introduce bias"
            )
          )
        })
      )
    })
    
    output$missDiagnosticPlots <- renderPlot({
      req(missResult())
      res <- missResult()
      
      par(mfrow = c(2, 2))
      
      # Missing data pattern
      if (requireNamespace("VIM", quietly = TRUE)) {
        VIM::aggr(res$data, plot = TRUE, numbers = TRUE, main = "Missing Data Pattern")
      } else {
        plot.new()
        title("Missing Data Pattern\n(VIM package required)")
      }
      
      # Missing data by variable
      if (!is.null(res$missing_by_variable)) {
        barplot(res$missing_by_variable$Missing_Count, 
                names.arg = res$missing_by_variable$Variable,
                main = "Missing Values by Variable",
                ylab = "Missing Count", col = "red", las = 2)
      }
      
      # Missing data percentage by variable
      if (!is.null(res$missing_by_variable)) {
        barplot(res$missing_by_variable$Missing_Percentage, 
                names.arg = res$missing_by_variable$Variable,
                main = "Missing Percentage by Variable",
                ylab = "Missing Percentage (%)", col = "orange", las = 2)
      }
      
      # Missing data correlation heatmap
      if (!is.null(res$missing_correlation)) {
        image(res$missing_correlation, main = "Missing Data Correlation",
              col = heat.colors(10), axes = FALSE)
        axis(1, at = seq(0, 1, length.out = ncol(res$missing_correlation)), 
             labels = colnames(res$missing_correlation), las = 2)
        axis(2, at = seq(0, 1, length.out = nrow(res$missing_correlation)), 
             labels = rownames(res$missing_correlation), las = 2)
      }
      
      par(mfrow = c(1, 1))
    })
    
    # Uploaded Data Tab Outputs
    output$missDataTable <- renderDT({
      req(missData())
      data <- missData()
      
      DT::datatable(
        data,
        options = list(
          pageLength = 10,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
        ),
        extensions = 'Buttons',
        filter = 'top',
        rownames = FALSE
      )
    })
    
    output$missDataInfo <- renderUI({
      req(missData())
      data <- missData()
      
      tagList(
        h4("Dataset Information"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "File Name"),
            Value = c(nrow(data), ncol(data), 
                     ifelse(!is.null(input$missUserData), input$missUserData$name, "Unknown"))
          )
        }),
        h4("Variable Information"),
        renderTable({
          data.frame(
            Variable = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Unique = sapply(data, function(x) length(unique(x)))
          )
        })
      )
    })
    
    # Legacy outputs for backward compatibility
    output$missSummary <- renderTable({
      req(missResult())
      res <- missResult()
      if (is.null(res) || is.null(res$summary)) {
        data.frame(Message = 'No results to display yet. Please check your input or try again.')
      } else {
        res$summary
      }
    })
  })
} 