quantileRegressionSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(
      inputId = ns("qrUserData"),
      label   = strong("Upload your Data (.csv or .xls or .xlsx or .txt)"),
      accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
    ),
    div(
      id = ns("qrUploadInputs"),
      selectizeInput(
        inputId = ns("qrResponse"),
        label = strong("Response Variable (Y)"),
        choices = c(""),
        selected = NULL,
        options = list(placeholder = 'Select response variable', onInitialize = I('function() { this.setValue(""); }'))
      ),
      selectizeInput(
        inputId = ns("qrPredictors"),
        label = strong("Predictor Variables (X)"),
        choices = c(""),
        multiple = TRUE,
        selected = NULL,
        options = list(hideSelected = FALSE, placeholder = 'Select predictor variables', onInitialize = I('function() { this.setValue(""); }'))
      )
    ),
    numericInput(
      inputId = ns("qrQuantile"),
      label = strong("Quantile (0-1)"),
      value = 0.5,
      min = 0.01,
      max = 0.99,
      step = 0.01
    ),
    radioButtons(
      inputId = ns("qrSigLvl"),
      label = strong("Significance Level (alpha)"),
      choices  = c("10%", "5%", "1%"),
      selected = "5%",
      inline   = TRUE
    ),
    br(),
    actionButton(ns("goQuantileRegression"), label = "Calculate", class = "act-btn")
  )
}

quantileRegressionMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('quantileRegressionResults'))
  )
}

quantileRegressionUI <- function(id) {
  ns <- NS(id)
  tagList(
    quantileRegressionSidebarUI(id),
    quantileRegressionMainUI(id),
    tabsetPanel(
      id = ns("qrTabset"),
      selected = "Analysis",
      tabPanel(
        id    = ns("qr"),
        title = "Analysis",
        titlePanel("Quantile Regression Results"),
        br(),
        uiOutput(ns('qrResults')),
        br(),
        plotOutput(ns('quantileRegressionPlot'), width = "100%", height = "500px"),
        br(),
        uiOutput(ns('qrDiagnostics'))
      ),
      tabPanel(
        id    = ns("qrCoef"),
        title = "Coefficients",
        DTOutput(ns("qrCoefficientsTable")),
        uiOutput(ns("qrCoefficientsOutput"))
      ),
      tabPanel(
        id    = ns("qrData"),
        title = "Uploaded Data",
        uiOutput(ns("renderQRData"))
      )
    )
  )
} 