# Simple Linear Regression UI
SimpleLinearRegressionSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    radioButtons(ns("slrDataMethod"), "Data Input Method", choices = c("Manual Entry", "Upload Data"), selected = "Manual Entry", inline = TRUE),
    conditionalPanel(
      condition = sprintf("input['%s'] == 'Manual Entry'", ns("slrDataMethod")),
      textInput(ns("slrX"), "Enter x values (comma or space separated)", value = ""),
      textInput(ns("slrY"), "Enter y values (comma or space separated)", value = "")
    ),
    conditionalPanel(
      condition = sprintf("input['%s'] == 'Upload Data'", ns("slrDataMethod")),
      fileInput(ns("slrUserData"), "Upload your Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
      selectizeInput(ns("slrXVar"), "X Variable", choices = NULL),
      selectizeInput(ns("slrYVar"), "Y Variable", choices = NULL)
    ),
    radioButtons(ns("slrSigLvl"), "Significance Level (alpha)", choices = c("10%", "5%", "1%"), selected = "5%", inline = TRUE),
    br(),
    actionButton(ns("goSLR"), label = "Calculate", class = "act-btn")
  )
}

SimpleLinearRegressionMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('slrResults'))
  )
}

simpleLinearRegressionUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(SimpleLinearRegressionSidebarUI(id)),
    mainPanel(SimpleLinearRegressionMainUI(id))
  )
} 