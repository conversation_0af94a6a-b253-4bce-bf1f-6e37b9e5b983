EffectSizeUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        shinyjs::useShinyjs(),
        div(
          id = ns("inputPanel"),
          radioButtons(
            inputId      = ns("esDataInput"),
            label        = strong("Data"),
            choiceValues = list("Enter Raw Data", "Upload Data"),
            choiceNames  = list("Enter Raw Data", "Upload Data"),
            selected     = "Enter Raw Data",
            inline       = TRUE
          ),
          conditionalPanel(
            condition = sprintf("input['%s'] == 'Enter Raw Data'", ns("esDataInput")),
            textAreaInput(
              inputId     = ns("esGroup1Data"),
              label       = strong("Group 1 Data"),
              value       = "1, 2, 3, 4, 5",
              placeholder = "Enter values separated by commas",
              rows        = 3
            ),
            textAreaInput(
              inputId     = ns("esGroup2Data"),
              label       = strong("Group 2 Data"),
              value       = "2, 3, 4, 5, 6",
              placeholder = "Enter values separated by commas",
              rows        = 3
            )
          ),
          conditionalPanel(
            condition = sprintf("input['%s'] == 'Upload Data'", ns("esDataInput")),
            fileInput(
              inputId = ns("esUserData"),
              label   = strong("Upload your Data (.csv or .xls or .xlsx or .txt)"),
              accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
            ),
            selectizeInput(
              inputId = ns("esGroup1"),
              label   = strong("Select Group 1 Variable"),
              choices = NULL,
              multiple = FALSE
            ),
            selectizeInput(
              inputId = ns("esGroup2"),
              label   = strong("Select Group 2 Variable"),
              choices = NULL,
              multiple = FALSE
            )
          ),
          selectInput(ns("esTestType"), "Test Type", choices = c("t-test", "ANOVA", "Proportion", "Correlation")),
          uiOutput(ns("esParams")),
          br(),
          actionButton(ns("goES"), label = "Calculate Effect Size", class = "act-btn")
        )
      ),
      mainPanel(
        uiOutput(ns("esError")),
        uiOutput(ns("esResults"))
      )
    )
  )
} 