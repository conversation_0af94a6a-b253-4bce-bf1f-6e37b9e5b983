# Correlation Tests UI
CorrelationTestSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    radioButtons(ns("corrDataMethod"), "Data Input Method", choices = c("Manual Entry", "Upload Data"), selected = "Manual Entry", inline = TRUE),
    conditionalPanel(
      condition = sprintf("input['%s'] == 'Manual Entry'", ns("corrDataMethod")),
      textInput(ns("corrX"), "Enter x values (comma or space separated)", value = ""),
      textInput(ns("corrY"), "Enter y values (comma or space separated)", value = "")
    ),
    conditionalPanel(
      condition = sprintf("input['%s'] == 'Upload Data'", ns("corrDataMethod")),
      fileInput(ns("corrUserData"), "Upload your Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
      selectizeInput(ns("corrXVar"), "X Variable", choices = NULL),
      selectizeInput(ns("corrYVar"), "Y Variable", choices = NULL),
      hr(),
      h5("Multiple Variable Analysis (Optional)"),
      selectizeInput(ns("corrVariables"), "Select Variables for Multiple Correlation", choices = NULL, multiple = TRUE),
      helpText("Select multiple variables to create a correlation matrix and heatmap")
    ),
    hr(),
    h5("Analysis Options"),
    radioButtons(ns("corrType"), "Correlation Method", choices = c("Pearson", "Spearman", "Kendall"), selected = "Pearson", inline = TRUE),
    radioButtons(ns("corrAlternative"), "Alternative Hypothesis", 
                choices = c("Two-sided" = "two.sided", "Greater" = "greater", "Less" = "less"), 
                selected = "two.sided", inline = TRUE),
    selectInput(ns("corrConfLevel"), "Confidence Level", 
                choices = c("90%" = 0.90, "95%" = 0.95, "99%" = 0.99), 
                selected = 0.95),
    selectInput(ns("corrPAdjust"), "P-value Adjustment (Multiple Tests)", 
                choices = c("None" = "none", "Bonferroni" = "bonferroni", "Holm" = "holm", "FDR" = "fdr"), 
                selected = "none"),
    radioButtons(ns("corrSigLvl"), "Significance Level (alpha)", choices = c("10%", "5%", "1%"), selected = "5%", inline = TRUE),
    hr(),
    h5("Output Options"),
    checkboxInput(ns("corrShowDiagnostics"), "Show Diagnostic Plots", value = TRUE),
    checkboxInput(ns("corrShowEffectSize"), "Show Effect Size Analysis", value = TRUE),
    checkboxInput(ns("corrShowMultiple"), "Show Multiple Correlation Analysis", value = FALSE),
    br(),
    actionButton(ns("goCorr"), label = "Calculate", class = "act-btn")
  )
}

CorrelationTestMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('corrResults'))
  )
}

CorrelationTestUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(CorrelationTestSidebarUI(id)),
    mainPanel(CorrelationTestMainUI(id))
  )
}

# Alias function for compatibility with main UI
CorrelationUI <- function(id) {
  CorrelationTestUI(id)
} 