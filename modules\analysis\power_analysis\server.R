PowerAnalysisServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Render UI for power analysis-specific parameters
    output$powerParams <- renderUI({
      switch(input$powerTestType,
        "t-test" = tagList(
          numericInput(ns("powerN"), "Sample Size (n)", value = 30, min = 2),
          numericInput(ns("powerD"), "Effect Size (<PERSON>'s d)", value = 0.5, min = 0.001),
          numericInput(ns("powerSigLevel"), "Significance Level (α)", value = 0.05, min = 0.001, max = 0.999, step = 0.001),
          numericInput(ns("powerPower"), "Power (1-β)", value = 0.8, min = 0.1, max = 0.999, step = 0.001)
        ),
        "ANOVA" = tagList(
          numericInput(ns("powerN"), "Sample Size per Group (n)", value = 30, min = 2),
          numericInput(ns("powerF"), "Effect Size (f)", value = 0.25, min = 0.001),
          numericInput(ns("powerGroups"), "Number of Groups", value = 3, min = 2),
          numericInput(ns("powerSigLevel"), "Significance Level (α)", value = 0.05, min = 0.001, max = 0.999, step = 0.001),
          numericInput(ns("powerPower"), "Power (1-β)", value = 0.8, min = 0.1, max = 0.999, step = 0.001)
        ),
        "Regression" = tagList(
          numericInput(ns("powerN"), "Sample Size (n)", value = 100, min = 3),
          numericInput(ns("powerR2"), "R-squared", value = 0.13, min = 0.001, max = 0.999, step = 0.001),
          numericInput(ns("powerSigLevel"), "Significance Level (α)", value = 0.05, min = 0.001, max = 0.999, step = 0.001),
          numericInput(ns("powerPower"), "Power (1-β)", value = 0.8, min = 0.1, max = 0.999, step = 0.001)
        ),
        "Proportion" = tagList(
          numericInput(ns("powerN"), "Sample Size (n)", value = 100, min = 2),
          numericInput(ns("powerP1"), "Proportion 1 (p₁)", value = 0.5, min = 0.001, max = 0.999, step = 0.001),
          numericInput(ns("powerP2"), "Proportion 2 (p₂)", value = 0.7, min = 0.001, max = 0.999, step = 0.001),
          numericInput(ns("powerSigLevel"), "Significance Level (α)", value = 0.05, min = 0.001, max = 0.999, step = 0.001),
          numericInput(ns("powerPower"), "Power (1-β)", value = 0.8, min = 0.1, max = 0.999, step = 0.001)
        ),
        NULL
      )
    })
    
    powerResult <- eventReactive(input$goPower, {
      req(input$goPower > 0)
      
      # Get parameters from UI
      n <- ifelse(is.null(input$powerN) || input$powerN == "", NULL, as.numeric(input$powerN))
      d <- ifelse(is.null(input$powerD) || input$powerD == "", NULL, as.numeric(input$powerD))
      sig_level <- ifelse(is.null(input$powerSigLevel), 0.05, input$powerSigLevel)
      power <- ifelse(is.null(input$powerPower) || input$powerPower == "", NULL, as.numeric(input$powerPower))
      type <- ifelse(is.null(input$powerTestType), "t-test", input$powerTestType)
      
      # Additional parameters for different test types
      groups <- ifelse(is.null(input$powerGroups), 3, input$powerGroups)
      r2 <- ifelse(is.null(input$powerR2), 0.13, input$powerR2)
      p1 <- ifelse(is.null(input$powerP1), 0.5, input$powerP1)
      p2 <- ifelse(is.null(input$powerP2), 0.7, input$powerP2)
      f <- ifelse(is.null(input$powerF), 0.25, input$powerF)
      
      power_analysis(n = n, d = d, sig.level = sig_level, power = power, type = type,
                    groups = groups, r2 = r2, p1 = p1, p2 = p2, f = f)
    })
    
    # Show main results or error screen in main panel when Calculate is pressed
    observeEvent(input$goPower, {
      output$powerError <- renderUI({
        errors <- powerValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Power Analysis", errors = errors)
        } else {
          tryCatch({ 
            res <- powerResult()
            if (is.null(res)) {
              errorScreenUI(title = "Power Analysis Error", errors = "No results available")
            } else {
              NULL
            }
          }, error = function(e) {
            errorScreenUI(title = "Power Analysis Error", errors = e$message)
          })
        }
      })
      
      output$powerResults <- renderUI({
        res <- powerResult()
        if (is.null(res)) return(NULL)
        
        tagList(
          tabsetPanel(
            id = ns("powerTabset"),
            selected = "Analysis",
            tabPanel(
              id = ns("powerAnalysis"),
              title = "Analysis",
              titlePanel("Power Analysis Results"),
              br(),
              h4("Power Analysis Summary"),
              tableOutput(ns('powerResultsTable')),
              br(),
              h4("Test Information"),
              textOutput(ns('powerTestType')),
              br(),
              h4("Effect Size Interpretation"),
              textOutput(ns('powerEffectSize')),
              br(),
              h4("Power Interpretation"),
              textOutput(ns('powerInterpretation')),
              br(),
              h4("Sample Size Recommendation"),
              textOutput(ns('powerSampleSize')),
              br(),
              h4("Significance Level"),
              textOutput(ns('powerSigInfo')),
              br(),
              h4("Additional Information"),
              textOutput(ns('powerAdditionalInfo'))
            ),
            tabPanel(
              id = ns("powerVisualizations"),
              title = "Visualizations",
              h4("Power Curve"),
              plotOutput(ns('powerCurvePlot'), height = "500px"),
              br(),
              h4("Sample Size vs Power"),
              plotOutput(ns('powerSampleSizePlot'), height = "500px")
            ),
            tabPanel(
              id = ns("powerGuidelines"),
              title = "Guidelines",
              h4("Power Analysis Guidelines"),
              uiOutput(ns('powerGuidelines')),
              br(),
              h4("Effect Size Guidelines"),
              uiOutput(ns('powerEffectSizeGuidelines')),
              br(),
              h4("Sample Size Guidelines"),
              uiOutput(ns('powerSampleSizeGuidelines'))
            )
          )
        )
      })
    })
    
    # Validation errors reactive
    powerValidationErrors <- reactive({
      errors <- c()
      
      # Common validation
      if (is.null(input$powerSigLevel) || input$powerSigLevel <= 0 || input$powerSigLevel >= 1) {
        errors <- c(errors, "Significance level must be between 0 and 1.")
      }
      
      # Type-specific validation
      if (input$powerTestType == "t-test") {
        if (is.null(input$powerN) || input$powerN < 2) {
          errors <- c(errors, "Sample size must be at least 2.")
        }
        if (is.null(input$powerD) || input$powerD <= 0) {
          errors <- c(errors, "Effect size must be positive.")
        }
        if (is.null(input$powerPower) || input$powerPower <= 0 || input$powerPower >= 1) {
          errors <- c(errors, "Power must be between 0 and 1.")
        }
      } else if (input$powerTestType == "ANOVA") {
        if (is.null(input$powerN) || input$powerN < 2) {
          errors <- c(errors, "Sample size per group must be at least 2.")
        }
        if (is.null(input$powerF) || input$powerF <= 0) {
          errors <- c(errors, "Effect size (f) must be positive.")
        }
        if (is.null(input$powerGroups) || input$powerGroups < 2) {
          errors <- c(errors, "Number of groups must be at least 2.")
        }
        if (is.null(input$powerPower) || input$powerPower <= 0 || input$powerPower >= 1) {
          errors <- c(errors, "Power must be between 0 and 1.")
        }
      } else if (input$powerTestType == "Regression") {
        if (is.null(input$powerN) || input$powerN < 3) {
          errors <- c(errors, "Sample size must be at least 3.")
        }
        if (is.null(input$powerR2) || input$powerR2 <= 0 || input$powerR2 >= 1) {
          errors <- c(errors, "R-squared must be between 0 and 1.")
        }
        if (is.null(input$powerPower) || input$powerPower <= 0 || input$powerPower >= 1) {
          errors <- c(errors, "Power must be between 0 and 1.")
        }
      } else if (input$powerTestType == "Proportion") {
        if (is.null(input$powerN) || input$powerN < 2) {
          errors <- c(errors, "Sample size must be at least 2.")
        }
        if (is.null(input$powerP1) || input$powerP1 <= 0 || input$powerP1 >= 1) {
          errors <- c(errors, "Proportion 1 must be between 0 and 1.")
        }
        if (is.null(input$powerP2) || input$powerP2 <= 0 || input$powerP2 >= 1) {
          errors <- c(errors, "Proportion 2 must be between 0 and 1.")
        }
        if (is.null(input$powerPower) || input$powerPower <= 0 || input$powerPower >= 1) {
          errors <- c(errors, "Power must be between 0 and 1.")
        }
      }
      
      errors
    })
    
    # Additional outputs for the enhanced tabs
    output$powerResultsTable <- renderTable({
      res <- powerResult()
      if (is.null(res)) return(NULL)
      res$summary
    }, rownames = FALSE, digits = 4)
    
    output$powerSampleSizePlot <- renderPlot({
      res <- powerResult()
      if (is.null(res) || is.null(res$power_curve)) return(NULL)
      
      # Create sample size vs power plot
      if (res$type == "t-test") {
        # Placeholder for sample size plot
        plot.new()
        title("Sample Size vs Power Plot")
        text(0.5, 0.5, "Sample size plot will appear here", cex = 1.2)
      }
    })
    
    output$powerGuidelines <- renderUI({
      tagList(
        h5("Power Analysis Guidelines:"),
        p("• Power of 0.8 (80%) is generally considered adequate"),
        p("• Power of 0.9 (90%) is recommended for important studies"),
        p("• Power below 0.5 (50%) indicates insufficient sample size"),
        p("• Higher power requires larger sample sizes"),
        p("• Effect size and significance level affect power"),
        br(),
        p("Note: These are general guidelines and may vary by field.")
      )
    })
    
    output$powerEffectSizeGuidelines <- renderUI({
      tagList(
        h5("Effect Size Guidelines:"),
        p("For t-tests (Cohen's d):"),
        p("• 0.2: Small effect"),
        p("• 0.5: Medium effect"),
        p("• 0.8: Large effect"),
        br(),
        p("For ANOVA (f):"),
        p("• 0.1: Small effect"),
        p("• 0.25: Medium effect"),
        p("• 0.4: Large effect")
      )
    })
    
    output$powerSampleSizeGuidelines <- renderUI({
      tagList(
        h5("Sample Size Guidelines:"),
        p("• Larger sample sizes increase power"),
        p("• Smaller effect sizes require larger samples"),
        p("• Higher significance levels (smaller α) require larger samples"),
        p("• More groups in ANOVA require larger total sample size"),
        br(),
        p("Note: Use power analysis to determine optimal sample size for your study.")
      )
    })
    
    # Power curve plot
    output$powerCurvePlot <- renderPlot({
      res <- powerResult()
      if (is.null(res) || is.null(res$power_curve)) return(NULL)
      
      if (res$type == "t-test") {
        ggplot2::ggplot(res$power_curve, ggplot2::aes(x = Effect_Size, y = Power)) +
          ggplot2::geom_line() +
          ggplot2::geom_point() +
          ggplot2::geom_hline(yintercept = 0.8, linetype = "dashed", color = "red") +
          ggplot2::labs(title = "Power Curve - t-test",
                       x = "Effect Size (Cohen's d)", y = "Power") +
          ggplot2::theme_minimal()
      } else if (res$type == "ANOVA") {
        ggplot2::ggplot(res$power_curve, ggplot2::aes(x = Effect_Size, y = Power)) +
          ggplot2::geom_line() +
          ggplot2::geom_point() +
          ggplot2::geom_hline(yintercept = 0.8, linetype = "dashed", color = "red") +
          ggplot2::labs(title = "Power Curve - ANOVA",
                       x = "Effect Size (f)", y = "Power") +
          ggplot2::theme_minimal()
      } else if (res$type == "Regression") {
        ggplot2::ggplot(res$power_curve, ggplot2::aes(x = R_squared, y = Power)) +
          ggplot2::geom_line() +
          ggplot2::geom_point() +
          ggplot2::geom_hline(yintercept = 0.8, linetype = "dashed", color = "red") +
          ggplot2::labs(title = "Power Curve - Regression",
                       x = "R-squared", y = "Power") +
          ggplot2::theme_minimal()
      } else if (res$type == "Proportion") {
        ggplot2::ggplot(res$power_curve, ggplot2::aes(x = Proportion_2, y = Power)) +
          ggplot2::geom_line() +
          ggplot2::geom_point() +
          ggplot2::geom_hline(yintercept = 0.8, linetype = "dashed", color = "red") +
          ggplot2::labs(title = "Power Curve - Proportion Test",
                       x = "Proportion 2", y = "Power") +
          ggplot2::theme_minimal()
      }
    })
    
    # Test type information
    output$powerTestType <- renderText({
      res <- powerResult()
      if (is.null(res)) return(NULL)
      paste("Test type:", res$type)
    })
    
    # Effect size interpretation
    output$powerEffectSize <- renderText({
      res <- powerResult()
      if (is.null(res)) return(NULL)
      
      if (res$type == "t-test") {
        d <- res$summary$Value[res$summary$Parameter == "Effect Size (Cohen's d)"]
        if (!is.na(d)) {
          if (abs(d) < 0.2) effect_desc <- "small"
          else if (abs(d) < 0.5) effect_desc <- "small to medium"
          else if (abs(d) < 0.8) effect_desc <- "medium"
          else if (abs(d) < 1.2) effect_desc <- "medium to large"
          else effect_desc <- "large"
          paste("Effect size interpretation:", effect_desc, "(Cohen's d =", round(d, 3), ")")
        } else {
          "Effect size interpretation not available"
        }
      } else if (res$type == "ANOVA") {
        f <- res$summary$Value[res$summary$Parameter == "Effect Size (f)"]
        if (!is.na(f)) {
          if (f < 0.1) effect_desc <- "small"
          else if (f < 0.25) effect_desc <- "medium"
          else effect_desc <- "large"
          paste("Effect size interpretation:", effect_desc, "(f =", round(f, 3), ")")
        } else {
          "Effect size interpretation not available"
        }
      } else {
        "Effect size interpretation not available for this test type"
      }
    })
    
    # Power interpretation
    output$powerInterpretation <- renderText({
      res <- powerResult()
      if (is.null(res)) return(NULL)
      
      power_val <- res$summary$Value[res$summary$Parameter == "Power"]
      if (!is.na(power_val)) {
        if (power_val < 0.5) power_desc <- "low"
        else if (power_val < 0.8) power_desc <- "moderate"
        else power_desc <- "high"
        paste("Power interpretation:", power_desc, "power (", round(power_val * 100, 1), "%)")
      } else {
        "Power interpretation not available"
      }
    })
    
    # Sample size recommendation
    output$powerSampleSize <- renderText({
      res <- powerResult()
      if (is.null(res)) return(NULL)
      
      n_val <- res$summary$Value[grep("Sample Size", res$summary$Parameter)]
      if (length(n_val) > 0 && !is.na(n_val[1])) {
        paste("Recommended sample size:", round(n_val[1], 0), "per group")
      } else {
        "Sample size recommendation not available"
      }
    })
    
    # Significance level information
    output$powerSigInfo <- renderText({
      res <- powerResult()
      if (is.null(res)) return(NULL)
      
      sig_level <- res$summary$Value[res$summary$Parameter == "Significance Level"]
      if (!is.na(sig_level)) {
        paste("Significance level:", sig_level, "(α =", sig_level, ")")
      } else {
        "Significance level information not available"
      }
    })
    
    # Additional test-specific information
    output$powerAdditionalInfo <- renderText({
      res <- powerResult()
      if (is.null(res)) return(NULL)
      
      if (res$type == "ANOVA") {
        groups <- res$summary$Value[res$summary$Parameter == "Number of Groups"]
        if (!is.na(groups)) {
          paste("Number of groups:", groups)
        } else {
          "Group information not available"
        }
      } else if (res$type == "Proportion") {
        p1 <- res$summary$Value[res$summary$Parameter == "Proportion 1"]
        p2 <- res$summary$Value[res$summary$Parameter == "Proportion 2"]
        if (!is.na(p1) && !is.na(p2)) {
          paste("Proportions: p1 =", p1, ", p2 =", p2)
        } else {
          "Proportion information not available"
        }
      } else {
        "Additional information not available for this test type"
      }
    })
  })
} 