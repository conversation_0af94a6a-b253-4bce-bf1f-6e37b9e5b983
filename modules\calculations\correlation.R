# Correlation calculation and output helpers

correlation_uploadData_func <- function(corrUserData) {
  ext <- tools::file_ext(corrUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(corrUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(corrUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(corrUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(corrUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

correlation_results_func <- function(data, var1, var2, method = "pearson", conf_level = 0.95, alternative = "two.sided") {
  tryCatch({
    x <- data[[var1]]
    y <- data[[var2]]
    
    if (length(x) != length(y)) {
      stop("Variables must have the same length.")
    }
    
    complete_cases <- complete.cases(x, y)
    x <- x[complete_cases]
    y <- y[complete_cases]
    
    if (length(x) < 3) {
      stop("At least 3 complete observations are required.")
    }
    
    test_result <- cor.test(x, y, method = method, conf.level = conf_level, alternative = alternative)
    
    list(
      test = test_result,
      data = data.frame(x = x, y = y),
      var1 = var1,
      var2 = var2,
      method = method,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during correlation calculation:", e$message))
  })
}

correlation_ht_html <- function(results, sigLvl) {
  test <- results$test
  p_value <- test$p.value
  
  conclusion <- if (p_value < sigLvl) "significant" else "not significant"
  
  withMathJax(tagList(
    h4(paste(stringr::str_to_title(results$method), "Correlation")),
    p(sprintf("The correlation between %s and %s is %s.", results$var1, results$var2, conclusion)),
    p(sprintf("Correlation Coefficient: %.4f", test$estimate)),
    p(sprintf("P-value: %.4f", p_value))
  ))
}

correlation_summary_html <- function(results) {
  test <- results$test
  
  desc_stats <- data.frame(
    Variable = c(results$var1, results$var2),
    N = c(length(results$data$x), length(results$data$y)),
    Mean = c(mean(results$data$x), mean(results$data$y)),
    SD = c(sd(results$data$x), sd(results$data$y))
  )
  
  tagList(
    h4("Test Details"),
    renderPrint(test),
    h4("Descriptive Statistics"),
    renderTable(desc_stats, digits = 4)
  )
}

correlation_plot <- function(results) {
  ggplot(results$data, aes(x = x, y = y)) +
    geom_point(alpha = 0.7) +
    geom_smooth(method = "lm", se = FALSE) +
    labs(title = paste("Scatterplot of", results$var1, "vs.", results$var2),
         x = results$var1,
         y = results$var2) +
    theme_minimal()
}