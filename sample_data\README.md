# Sample Data for CougarStats Modules

This folder contains sample datasets for every available test/module in the CougarStats app. Use these files to quickly try out each analysis, regression, visualization, or machine learning module.

**How to use:**
- Upload the relevant CSV file in the corresponding module's data upload section.
- Each file is named to match the module or test it is designed for (e.g., `anova_oneway.csv`, `logistic_regression.csv`, `kmeans_clustering.csv`, etc.).

**Included sample datasets:**
- anova_oneway.csv
- ttest_independent.csv
- ttest_paired.csv
- chi_square.csv
- proportion_test.csv
- regression_simple.csv
- regression_multiple.csv
- logistic_regression.csv
- manova.csv
- nonlinear_regression.csv
- survival.csv
- kruskal_wallis.csv
- wilcoxon.csv
- friedman.csv
- correlation.csv
- cluster_analysis.csv
- pca.csv
- time_series.csv
- missingness.csv
- data_summarization.csv
- mediation.csv
- propensity_score.csv
- supervised_ml.csv
- unsupervised_ml.csv
- ...and more as needed

Feel free to add your own datasets for testing and demonstration purposes. 