# Elastic Net Regression UI
# Combines L1 and L2 penalties for variable selection and regularization

ElasticNetRegressionSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    radioButtons(
      inputId = ns("elasticNetDataMethod"),
      label = "Data Input Method:",
      choices = c("Manual Entry" = "Manual Entry", "Upload File" = "Upload File"),
      selected = "Manual Entry"
    ),
    conditionalPanel(
      condition = "input.elasticNetDataMethod == 'Manual Entry'",
      ns = ns,
      textAreaInput(
        inputId = ns("elasticNetResponseData"),
        label = "Enter Response Values (comma, space, or newline separated):",
        placeholder = "1, 2, 3, 4, 5, 6",
        rows = 4
      ),
      textAreaInput(
        inputId = ns("elasticNetPredictorData"),
        label = "Enter Predictor Values (comma, space, or newline separated):",
        placeholder = "1, 2, 3, 4, 5, 6",
        rows = 4
      )
    ),
    conditionalPanel(
      condition = "input.elasticNetDataMethod == 'Upload File'",
      ns = ns,
      fileInput(
        inputId = ns("elasticNetUserData"),
        label = "Upload Data File:",
        accept = c(".csv", ".txt", ".xlsx", ".xls"),
        buttonLabel = "Browse Files",
        placeholder = "No file selected"
      ),
      selectizeInput(
        inputId = ns("elasticNetResponseVariable"),
        label = "Select Response Variable:",
        choices = NULL,
        options = list(placeholder = "Select response variable...")
      ),
      selectizeInput(
        inputId = ns("elasticNetPredictorVariables"),
        label = "Select Predictor Variables:",
        choices = NULL,
        multiple = TRUE,
        options = list(placeholder = "Select predictor variables...")
      )
    ),
    numericInput(
      inputId = ns("elasticNetAlpha"),
      label = "Alpha (Mixing Parameter):",
      value = 0.5,
      min = 0,
      max = 1,
      step = 0.01
    ),
    numericInput(
      inputId = ns("elasticNetLambda"),
      label = "Lambda (Regularization Parameter):",
      value = 0.1,
      min = 0.001,
      max = 10,
      step = 0.001
    ),
    checkboxInput(
      inputId = ns("elasticNetCrossValidation"),
      label = "Use Cross-Validation for Parameter Selection",
      value = TRUE
    ),
    conditionalPanel(
      condition = "input.elasticNetCrossValidation == true",
      ns = ns,
      numericInput(
        inputId = ns("elasticNetCVFolds"),
        label = "Number of CV Folds:",
        value = 10,
        min = 3,
        max = 20,
        step = 1
      ),
      numericInput(
        inputId = ns("elasticNetAlphaGrid"),
        label = "Number of Alpha Values:",
        value = 10,
        min = 3,
        max = 20,
        step = 1
      )
    ),
    numericInput(
      inputId = ns("elasticNetConfLevel"),
      label = "Confidence Level:",
      value = 0.95,
      min = 0.5,
      max = 0.99,
      step = 0.01
    ),
    actionButton(
      inputId = ns("goElasticNet"),
      label = "Calculate Elastic Net Regression",
      class = "btn-primary",
      style = "width: 100%;"
    ),
    br(),
    br(),
    helpText(
      "Elastic Net combines Lasso (α=1) and Ridge (α=0) regression.",
      "Alpha controls the balance between L1 and L2 penalties.",
      "Lambda controls the overall strength of regularization.",
      "Cross-validation helps select optimal parameters."
    )
  )
}

ElasticNetRegressionMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns("elasticNetResults"))
  )
}

ElasticNetRegressionUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(ElasticNetRegressionSidebarUI(id)),
    mainPanel(ElasticNetRegressionMainUI(id))
  )
} 