PoissonRegressionServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    # Placeholder for data upload and validation
    poisData <- eventReactive(input$poisUserData, {
      handle_file_upload(input$poisUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(poisData(), {
      data <- poisData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'poisResponse', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'poisPredictors', choices = names(data), server = TRUE)
      }
    })
    
    poisResult <- eventReactive(input$goPois, {
      data <- poisData()
      req(data, input$poisResponse, input$poisPredictors)
      x <- as.matrix(data[, input$poisPredictors, drop = FALSE])
      y <- data[[input$poisResponse]]
      model_type <- ifelse(input$poisModelType == "Poisson", "poisson", "negative binomial")
      poisson_regression(x, y, model_type)
    })
    
    observeEvent(input$goPois, {
      output$poisError <- renderUI({
        tryCatch({ 
          res <- poisResult()
          if (is.null(res)) {
            errorScreenUI(title = "Poisson Regression Error", errors = "No results available")
          } else {
            NULL
          }
        }, error = function(e) {
          errorScreenUI(title = "Poisson Regression Error", errors = e$message)
        })
      })
      
      output$poisResults <- renderUI({
        res <- poisResult()
        if (is.null(res)) return(NULL)
        
        tagList(
          tabsetPanel(
            id = ns("poisTabset"),
            selected = "Analysis",
            tabPanel(
              id = ns("poisAnalysis"),
              title = "Analysis",
              titlePanel("Poisson Regression Results"),
              br(),
              h4("Model Summary"),
              uiOutput(ns('poisModelSummary')),
              br(),
              h4("Coefficients"),
              tableOutput(ns('poisCoefficients')),
              br(),
              h4("Model Fit"),
              uiOutput(ns('poisModelFit')),
              br(),
              h4("Interpretation"),
              uiOutput(ns('poisInterpretation'))
            ),
            tabPanel(
              id = ns("poisDiagnostics"),
              title = "Diagnostics",
              h4("Residuals vs Fitted"),
              plotOutput(ns('poisResidualPlot')),
              br(),
              h4("Q-Q Plot"),
              plotOutput(ns('poisQQPlot')),
              br(),
              h4("Diagnostic Statistics"),
              tableOutput(ns('poisDiagnosticStats'))
            ),
            tabPanel(
              id = ns("poisUploadedData"),
              title = "Uploaded Data",
              h4("Raw Data"),
              uiOutput(ns('poisDataTable'))
            )
          )
        )
      })
    })
    
    # Additional outputs for the enhanced tabs
    output$poisModelSummary <- renderUI({
      res <- poisResult()
      if (is.null(res)) return(NULL)
      
      tagList(
        p("Model Type: ", input$poisModelType),
        p("Response Variable: ", input$poisResponse),
        p("Predictors: ", paste(input$poisPredictors, collapse = ", ")),
        p("Observations: ", ifelse(!is.null(res$n), res$n, "N/A"))
      )
    })
    
    output$poisCoefficients <- renderTable({
      res <- poisResult()
      if (is.null(res)) return(NULL)
      as.data.frame(res$coefficients)
    }, rownames = TRUE, digits = 4)
    
    output$poisModelFit <- renderUI({
      res <- poisResult()
      if (is.null(res)) return(NULL)
      
      tagList(
        p("Model fit statistics will be displayed here."),
        p("AIC: ", ifelse(!is.null(res$aic), round(res$aic, 4), "N/A")),
        p("BIC: ", ifelse(!is.null(res$bic), round(res$bic, 4), "N/A")),
        p("Log-likelihood: ", ifelse(!is.null(res$loglik), round(res$loglik, 4), "N/A"))
      )
    })
    
    output$poisInterpretation <- renderUI({
      tagList(
        h5("Poisson Regression Interpretation:"),
        p("Poisson regression models count data with a Poisson distribution."),
        p("Coefficients represent the change in log-count for a one-unit increase in the predictor."),
        p("Exponentiated coefficients represent the multiplicative change in the expected count."),
        p("AIC and BIC are model selection criteria - lower values indicate better fit.")
      )
    })
    
    output$poisResidualPlot <- renderPlot({
      res <- poisResult()
      if (is.null(res)) return(NULL)
      
      if (!is.null(res$summary$residuals) && !is.null(res$summary$fitted.values)) {
        plot(res$summary$fitted.values, res$summary$residuals, 
             xlab = "Fitted Values", ylab = "Residuals", 
             main = "Residuals vs Fitted Values")
        abline(h = 0, col = "red", lty = 2)
        grid()
      }
    })
    
    output$poisQQPlot <- renderPlot({
      res <- poisResult()
      if (is.null(res)) return(NULL)
      
      if (!is.null(res$summary$residuals)) {
        qqnorm(res$summary$residuals, main = "Normal Q-Q Plot of Residuals")
        qqline(res$summary$residuals, col = "red")
        grid()
      }
    })
    
    output$poisDiagnosticStats <- renderTable({
      res <- poisResult()
      if (is.null(res)) return(NULL)
      
      # Placeholder for diagnostic statistics
      data.frame(
        Statistic = c("Mean Residual", "Residual SD", "Max Residual", "Min Residual"),
        Value = c("N/A", "N/A", "N/A", "N/A"),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    output$poisDataTable <- renderUI({
      req(poisData())
      DT::DTOutput(ns("poisDataTableInner"))
    })
    
    output$poisDataTableInner <- DT::renderDT({
      req(poisData())
      DT::datatable(poisData(),
        options = list(pageLength = 25,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(poisData())))))
    })
  })
} 