library(testthat)
library(here)
source(here::here("modules/shared/plot_helpers.R"))

test_that("RenderBoxplot is a function", {
  expect_true(is.function(RenderBoxplot))
})

test_that("RenderHistogram is a function", {
  expect_true(is.function(RenderHistogram))
})

test_that("RenderMeanPlot is a function", {
  expect_true(is.function(RenderMeanPlot))
})

test_that("RenderQQPlot is a function", {
  expect_true(is.function(RenderQQPlot))
})

test_that("RenderScatterplot is a function", {
  expect_true(is.function(RenderScatterplot))
})

test_that("RenderSideBySideBoxplot is a function", {
  expect_true(is.function(RenderSideBySideBoxplot))
}) 