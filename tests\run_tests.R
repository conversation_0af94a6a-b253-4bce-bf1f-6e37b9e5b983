#!/usr/bin/env Rscript
# Simple Test Runner for CougarStats Modules

library(testthat)

# Set up test environment
cat("Starting CougarStats Module Testing\n")
cat("===================================\n\n")

# Initialize test results
test_results <- list()
test_summary <- list(
  total_modules = 0,
  passed = 0,
  failed = 0,
  errors = 0
)

# Helper function to load test data
load_test_data <- function(module_name) {
  sample_data_dir <- "sample_data"
  
  # Default to desc_stats.csv for most tests
  data_file <- "desc_stats.csv"
  file_path <- file.path(sample_data_dir, data_file)
  
  if (file.exists(file_path)) {
    read.csv(file_path, stringsAsFactors = FALSE)
  } else {
    # Create minimal test data
    data.frame(
      x = rnorm(20),
      y = rnorm(20),
      group = rep(c("A", "B"), each = 10),
      stringsAsFactors = FALSE
    )
  }
}

# Function to test a module
test_module <- function(module_name, test_function) {
  cat(sprintf("Testing: %s\n", module_name))
  test_summary$total_modules <<- test_summary$total_modules + 1
  
  tryCatch({
    result <- test_function()
    if (result$success) {
      test_summary$passed <<- test_summary$passed + 1
      cat(sprintf("  ✓ PASSED\n"))
    } else {
      test_summary$failed <<- test_summary$failed + 1
      cat(sprintf("  ✗ FAILED: %s\n", result$error))
    }
    test_results[[module_name]] <<- result
  }, error = function(e) {
    test_summary$errors <<- test_summary$errors + 1
    cat(sprintf("  ✗ ERROR: %s\n", e$message))
    test_results[[module_name]] <<- list(success = FALSE, error = e$message)
  })
}

# Function to test calculation functions
test_calculation <- function(function_name, test_data, ...) {
  if (exists(function_name)) {
    tryCatch({
      func <- get(function_name)
      result <- func(test_data, ...)
      return(list(success = TRUE, result = result))
    }, error = function(e) {
      return(list(success = FALSE, error = e$message))
    })
  } else {
    return(list(success = FALSE, error = "Function not found"))
  }
}

# Test Basic Statistics Modules
cat("Testing Basic Statistics Modules...\n")
cat("-----------------------------------\n")

data <- load_test_data("desc_stats")

# Test Descriptive Statistics
test_module("Descriptive Statistics", function() {
  test_calculation("desc_stats_analysis", data, names(data)[1])
})

# Test Probability Distributions
test_module("Probability Distributions", function() {
  test_calculation("prob_dist_analysis", data, names(data)[1], "normal")
})

# Test Sample Size Estimation
test_module("Sample Size Estimation", function() {
  test_calculation("sample_size_analysis", list(type = "mean", alpha = 0.05, power = 0.8, effect_size = 0.5))
})

# Test Data Summarization
test_module("Data Summarization", function() {
  test_calculation("data_summarization_analysis", data)
})

# Test Missingness Visualization
test_module("Missingness Visualization", function() {
  test_calculation("missingness_analysis", data)
})

# Test Outlier Detection
test_module("Outlier Detection", function() {
  test_calculation("outlier_detection_analysis", data, names(data)[1])
})

# Test Variable Transformation
test_module("Variable Transformation", function() {
  test_calculation("variable_transformation_analysis", data, names(data)[1], "log")
})

# Test Pairwise Plot Matrix
test_module("Pairwise Plot Matrix", function() {
  test_calculation("pairwise_plot_analysis", data, names(data)[1:min(3, ncol(data))])
})

# Test Simulation
test_module("Simulation", function() {
  test_calculation("simulation_analysis", list(n_sim = 100, n_obs = 50, mean = 0, sd = 1))
})

cat("\n")

# Test Inference Modules
cat("Testing Inference Modules...\n")
cat("---------------------------\n")

# Test One Sample Inference
test_module("One Sample Inference", function() {
  test_calculation("one_sample_analysis", data, names(data)[1], mu0 = 0, alpha = 0.05)
})

# Test Two Sample Inference
test_module("Two Sample Inference", function() {
  test_calculation("two_sample_analysis", data, names(data)[1], names(data)[2], alpha = 0.05)
})

# Test Paired T-Test
test_module("Paired T-Test", function() {
  test_calculation("paired_t_test_analysis", data, names(data)[1], names(data)[2], alpha = 0.05)
})

# Test ANOVA
test_module("ANOVA", function() {
  test_calculation("anova_analysis", data, names(data)[1], names(data)[2], alpha = 0.05)
})

# Test Chi-Square
test_module("Chi-Square Test", function() {
  test_calculation("chi_square_analysis", data, names(data)[1], names(data)[2], alpha = 0.05)
})

# Test Kruskal-Wallis
test_module("Kruskal-Wallis Test", function() {
  test_calculation("kruskal_wallis_analysis", data, names(data)[1], names(data)[2], alpha = 0.05)
})

# Test Mann-Whitney
test_module("Mann-Whitney U Test", function() {
  test_calculation("mann_whitney_analysis", data, names(data)[1], names(data)[2], alpha = 0.05)
})

# Test Wilcoxon
test_module("Wilcoxon Signed-Rank Test", function() {
  test_calculation("wilcoxon_analysis", data, names(data)[1], names(data)[2], alpha = 0.05)
})

# Test Friedman
test_module("Friedman Test", function() {
  test_calculation("friedman_analysis", data, names(data)[1], names(data)[2], alpha = 0.05)
})

# Test Levene's Test
test_module("Levene's Test", function() {
  test_calculation("levene_analysis", data, names(data)[1], names(data)[2], alpha = 0.05)
})

# Test Post Hoc Tests
test_module("Post Hoc Tests", function() {
  test_calculation("post_hoc_analysis", data, names(data)[1], names(data)[2], method = "tukey", alpha = 0.05)
})

# Test Proportion Tests
test_module("Proportion Tests", function() {
  test_calculation("proportion_tests_analysis", data, names(data)[1], p0 = 0.5, alpha = 0.05)
})

# Test McNemar Test
test_module("McNemar Test", function() {
  test_calculation("mcnemar_analysis", data, names(data)[1], names(data)[2], alpha = 0.05)
})

# Test Cochran's Q Test
test_module("Cochran's Q Test", function() {
  test_calculation("cochrans_q_analysis", data, names(data)[1], names(data)[2:min(4, ncol(data))], alpha = 0.05)
})

# Test Bayesian Tests
test_module("Bayesian Tests", function() {
  test_calculation("bayesian_analysis", data, names(data)[1], names(data)[2], test_type = "Bayesian t-test")
})

# Test Bayesian Model Comparison
test_module("Bayesian Model Comparison", function() {
  test_calculation("bayesian_model_comparison_analysis", data, names(data)[1], names(data)[2:min(4, ncol(data))])
})

# Test Custom Test
test_module("Custom Test", function() {
  test_calculation("custom_test_analysis", data, names(data)[1], names(data)[2], formula_str = "y ~ x", test_type = "regression")
})

# Test Power Analysis
test_module("Power Analysis", function() {
  test_calculation("power_analysis_analysis", list(test_type = "t.test", n = 30, effect_size = 0.5, alpha = 0.05))
})

cat("\n")

# Test Regression & Correlation Modules
cat("Testing Regression & Correlation Modules...\n")
cat("-------------------------------------------\n")

# Test Simple Linear Regression
test_module("Simple Linear Regression", function() {
  test_calculation("simple_linear_regression_analysis", data, names(data)[1], names(data)[2])
})

# Test Multiple Linear Regression
test_module("Multiple Linear Regression", function() {
  predictors <- names(data)[2:min(4, ncol(data))]
  test_calculation("multiple_linear_regression_analysis", data, names(data)[1], predictors)
})

# Test Logistic Regression
test_module("Logistic Regression", function() {
  test_calculation("logistic_regression_analysis", data, names(data)[1], names(data)[2])
})

# Test Correlation Analysis
test_module("Correlation Analysis", function() {
  test_calculation("correlation_analysis", data, names(data)[1], names(data)[2])
})

# Test Robust Regression
test_module("Robust Regression", function() {
  test_calculation("robust_regression_analysis", data, names(data)[1], names(data)[2])
})

# Test Poisson Regression
test_module("Poisson Regression", function() {
  test_calculation("poisson_regression_analysis", data, names(data)[1], names(data)[2])
})

# Test Quasi Regression
test_module("Quasi Regression", function() {
  test_calculation("quasi_regression_analysis", data, names(data)[1], names(data)[2], family = "quasibinomial")
})

# Test Zero-Inflated Models
test_module("Zero-Inflated Models", function() {
  test_calculation("zero_inflated_analysis", data, names(data)[1], names(data)[2])
})

# Test Bayesian Regression
test_module("Bayesian Regression", function() {
  test_calculation("bayesian_regression_analysis", data, names(data)[1], names(data)[2])
})

# Test Survival Analysis
test_module("Survival Analysis", function() {
  test_calculation("survival_analysis_analysis", data, names(data)[1], names(data)[2])
})

# Test Cox Proportional Hazards
test_module("Cox Proportional Hazards", function() {
  test_calculation("coxph_analysis", data, names(data)[1], names(data)[2], names(data)[3])
})

# Test Nonlinear Regression
test_module("Nonlinear Regression", function() {
  test_calculation("nonlinear_regression_analysis", data, names(data)[1], names(data)[2], formula = "y ~ a * exp(-b * x)")
})

# Test GAM
test_module("GAM", function() {
  test_calculation("gam_analysis", data, names(data)[1], names(data)[2])
})

# Test MANOVA
test_module("MANOVA", function() {
  response_vars <- names(data)[1:2]
  group_var <- names(data)[3]
  test_calculation("manova_analysis", data, response_vars, group_var)
})

# Test Mediation/Moderation
test_module("Mediation/Moderation", function() {
  test_calculation("mediation_moderation_analysis", data, names(data)[1], names(data)[2], names(data)[3], names(data)[4])
})

# Test Propensity Score
test_module("Propensity Score", function() {
  test_calculation("propensity_score_analysis", data, names(data)[1], names(data)[2], names(data)[3:min(5, ncol(data))])
})

# Test Bayesian Hierarchical
test_module("Bayesian Hierarchical", function() {
  test_calculation("bayesian_hierarchical_analysis", data, names(data)[1], names(data)[2], names(data)[3], names(data)[4])
})

cat("\n")

# Test Advanced Analysis Modules
cat("Testing Advanced Analysis Modules...\n")
cat("-----------------------------------\n")

# Test PCA
test_module("PCA", function() {
  test_calculation("pca_analysis", data, names(data))
})

# Test Cluster Analysis
test_module("Cluster Analysis", function() {
  test_calculation("cluster_analysis_analysis", data, names(data), method = "kmeans", k = 3)
})

# Test ROC Analysis
test_module("ROC Analysis", function() {
  test_calculation("roc_analysis_analysis", data, names(data)[1], names(data)[2])
})

# Test Meta Analysis
test_module("Meta Analysis", function() {
  test_calculation("meta_analysis_analysis", data, names(data)[1], names(data)[2], names(data)[3])
})

# Test Mixed Effects
test_module("Mixed Effects", function() {
  test_calculation("mixed_effects_analysis", data, names(data)[1], names(data)[2], names(data)[3])
})

# Test Survey Psychometrics
test_module("Survey Psychometrics", function() {
  test_calculation("survey_psychometrics_analysis", data, names(data))
})

# Test Time Series
test_module("Time Series", function() {
  test_calculation("time_series_analysis", data, names(data)[1], names(data)[2])
})

# Test STL Decomposition
test_module("STL Decomposition", function() {
  test_calculation("stl_decomposition_analysis", data, names(data)[1], names(data)[2])
})

# Test State Space
test_module("State Space", function() {
  test_calculation("state_space_analysis", data, names(data)[1], names(data)[2])
})

# Test Change Point Detection
test_module("Change Point Detection", function() {
  test_calculation("change_point_analysis", data, names(data)[1], names(data)[2])
})

# Test Spectral Analysis
test_module("Spectral Analysis", function() {
  test_calculation("spectral_analysis_analysis", data, names(data)[1], names(data)[2])
})

# Test Network Analysis
test_module("Network Analysis", function() {
  test_calculation("network_analysis_analysis", data, names(data)[1], names(data)[2])
})

# Test SEM
test_module("SEM", function() {
  test_calculation("sem_analysis", data, names(data))
})

# Test Latent Class Analysis
test_module("Latent Class Analysis", function() {
  test_calculation("latent_class_analysis", data, names(data), n_classes = 3)
})

# Test IRT
test_module("IRT", function() {
  test_calculation("irt_analysis", data, names(data))
})

# Test Multiple Imputation
test_module("Multiple Imputation", function() {
  test_calculation("multiple_imputation_analysis", data, names(data), m = 5)
})

# Test TSNE/UMAP
test_module("TSNE/UMAP", function() {
  test_calculation("tsne_umap_analysis", data, names(data), method = "tsne", perplexity = 30)
})

# Test Discriminant Analysis
test_module("Discriminant Analysis", function() {
  test_calculation("discriminant_analysis", data, names(data)[1], names(data)[2:min(4, ncol(data))])
})

# Test CCA
test_module("CCA", function() {
  set1 <- names(data)[1:2]
  set2 <- names(data)[3:4]
  test_calculation("cca_analysis", data, set1, set2)
})

# Test MDS
test_module("MDS", function() {
  test_calculation("mds_analysis", data, names(data), k = 2, method = "classical")
})

# Test Competing Risks
test_module("Competing Risks", function() {
  test_calculation("competing_risks_analysis", data, names(data)[1], names(data)[2], names(data)[3])
})

# Test Correlation Heatmap
test_module("Correlation Heatmap", function() {
  test_calculation("correlation_heatmap_analysis", data, names(data))
})

# Test Stratified KM
test_module("Stratified KM", function() {
  test_calculation("stratified_km_analysis", data, names(data)[1], names(data)[2], names(data)[3])
})

# Test Advanced Survival
test_module("Advanced Survival", function() {
  test_calculation("advanced_survival_analysis", data, names(data)[1], names(data)[2], names(data)[3])
})

cat("\n")

# Test Machine Learning Modules
cat("Testing Machine Learning Modules...\n")
cat("----------------------------------\n")

# Test Supervised ML
test_module("Supervised ML", function() {
  test_calculation("supervised_ml_analysis", data, names(data)[1], names(data)[2:min(4, ncol(data))], task = "classification", method = "random_forest")
})

# Test Unsupervised ML
test_module("Unsupervised ML", function() {
  test_calculation("unsupervised_ml_analysis", data, names(data), task = "clustering", method = "kmeans", k = 3)
})

# Test Model Comparison
test_module("Model Comparison", function() {
  methods <- c("linear_regression", "random_forest", "svm")
  test_calculation("model_comparison_analysis", data, names(data)[1], names(data)[2:min(4, ncol(data))], methods)
})

# Test Feature Selection
test_module("Feature Selection", function() {
  test_calculation("feature_selection_analysis", data, names(data)[1], names(data)[2:min(6, ncol(data))], method = "filter", filter_type = "correlation")
})

cat("\n")

# Test Bootstrap and Permutation Tests
cat("Testing Bootstrap and Permutation Tests...\n")
cat("-----------------------------------------\n")

# Test Bootstrap
test_module("Bootstrap Analysis", function() {
  test_calculation("bootstrap_analysis", data, names(data)[1], names(data)[2], n_bootstrap = 1000)
})

# Test Permutation Tests
test_module("Permutation Tests", function() {
  test_calculation("permutation_tests_analysis", data, names(data)[1], names(data)[2], n_permutations = 1000)
})

cat("\n")

# Print final summary
cat("================================================\n")
cat("TEST SUMMARY\n")
cat("================================================\n")
cat(sprintf("Total Modules Tested: %d\n", test_summary$total_modules))
cat(sprintf("Passed: %d\n", test_summary$passed))
cat(sprintf("Failed: %d\n", test_summary$failed))
cat(sprintf("Errors: %d\n", test_summary$errors))
cat(sprintf("Success Rate: %.1f%%\n", (test_summary$passed / test_summary$total_modules) * 100))

# Save results
saveRDS(test_results, "tests/test_results.rds")
saveRDS(test_summary, "tests/test_summary.rds")

cat("\nResults saved to tests/test_results.rds and tests/test_summary.rds\n")

# Return exit code
if (test_summary$failed + test_summary$errors > 0) {
  cat("\nSome tests failed. Check the detailed results above.\n")
  quit(status = 1)
} else {
  cat("\nAll tests passed successfully!\n")
  quit(status = 0)
} 