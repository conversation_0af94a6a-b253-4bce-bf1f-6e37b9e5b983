ZeroInflatedServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    ziData <- eventReactive(input$ziUserData, {
      handle_file_upload(input$ziUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(ziData(), {
      data <- ziData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'ziResponse', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'ziPredictors', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    ziValidationErrors <- reactive({
      errors <- c()
      data <- ziData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$ziResponse) || input$ziResponse == "") {
        errors <- c(errors, "Select a response variable.")
      }
      
      if (is.null(input$ziPredictors) || length(input$ziPredictors) == 0) {
        errors <- c(errors, "Select at least one predictor variable.")
      }
      
      # Check if response variable is appropriate for zero-inflated models
      if (!is.null(input$ziResponse) && input$ziResponse != "") {
        response_data <- data[[input$ziResponse]]
        if (!is.numeric(response_data)) {
          errors <- c(errors, "Response variable must be numeric.")
        } else {
          # Check for excessive zeros
          zero_proportion <- mean(response_data == 0, na.rm = TRUE)
          if (zero_proportion < 0.1) {
            errors <- c(errors, "Response variable should have at least 10% zeros for zero-inflated models.")
          }
          if (any(response_data < 0, na.rm = TRUE)) {
            errors <- c(errors, "Response variable must be non-negative for zero-inflated models.")
          }
        }
      }
      
      # Check if predictor variables are appropriate
      if (!is.null(input$ziPredictors) && length(input$ziPredictors) > 0) {
        for (var in input$ziPredictors) {
          if (!is.numeric(data[[var]]) && !is.factor(data[[var]])) {
            errors <- c(errors, sprintf("Predictor '%s' must be numeric or factor.", var))
          }
        }
      }
      
      # Check if pscl package is available
      if (!requireNamespace("pscl", quietly = TRUE)) {
        errors <- c(errors, "Package 'pscl' is required for zero-inflated models.")
      }
      
      errors
    })
    
    # Zero-inflated model analysis reactive
    ziResult <- eventReactive(input$goZI, {
      data <- ziData()
      req(data, input$ziResponse, input$ziPredictors)
      
      # Remove rows with missing values
      complete_data <- data[complete.cases(data[, c(input$ziResponse, input$ziPredictors)]), ]
      
      if (nrow(complete_data) < 10) {
        stop("Insufficient complete cases for zero-inflated model.")
      }
      
      # Prepare data
      x <- as.matrix(complete_data[, input$ziPredictors, drop = FALSE])
      y <- complete_data[[input$ziResponse]]
      model_type <- ifelse(input$ziModelType == "Zero-inflated Poisson", "zip", "zinb")
      
      # Fit zero-inflated model
      zero_inflated_model(x, y, model_type)
    })
    
    # Error handling
    output$ziError <- renderUI({
      errors <- ziValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          ziResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "Zero-inflated Model Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$ziModelSummary <- renderUI({
      req(ziResult())
      res <- ziResult()
      
      tagList(
        h4("Zero-inflated Model Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Metric = c("Model Type", "Response Variable", "Number of Predictors", "Number of Observations", "Model Fitted"),
            Value = c(
              input$ziModelType,
              input$ziResponse,
              length(input$ziPredictors),
              res$n_observations,
              "Yes"
            )
          )
        }),
        h4("Model Quality Metrics"),
        renderTable({
          data.frame(
            Metric = c("AIC", "BIC", "Log-likelihood", "Zero Inflation", "Dispersion"),
            Value = c(
              ifelse(!is.null(res$aic), round(res$aic, 4), "N/A"),
              ifelse(!is.null(res$bic), round(res$bic, 4), "N/A"),
              ifelse(!is.null(res$log_likelihood), round(res$log_likelihood, 4), "N/A"),
              ifelse(!is.null(res$zero_inflation), paste(round(res$zero_inflation * 100, 2), "%"), "N/A"),
              ifelse(!is.null(res$dispersion), round(res$dispersion, 4), "N/A")
            )
          )
        })
      )
    })
    
    output$ziPlot <- renderPlot({
      req(ziResult())
      res <- ziResult()
      
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # Observed vs predicted
      if (!is.null(res$observed) && !is.null(res$predicted)) {
        plot(res$observed, res$predicted, main = "Observed vs Predicted",
             xlab = "Observed", ylab = "Predicted", pch = 19, col = "blue")
        abline(a = 0, b = 1, col = "red", lty = 2)
      }
      
      # Residuals plot
      if (!is.null(res$residuals)) {
        plot(res$residuals, main = "Residuals Plot",
             xlab = "Index", ylab = "Residuals", pch = 19, col = "green")
        abline(h = 0, col = "red", lty = 2)
      }
      
      # Q-Q plot of residuals
      if (!is.null(res$residuals)) {
        qqnorm(res$residuals, main = "Q-Q Plot of Residuals")
        qqline(res$residuals, col = "red")
      }
      
      # Zero inflation comparison
      if (!is.null(res$zero_inflation)) {
        barplot(c(res$zero_inflation, 1 - res$zero_inflation), 
                main = "Zero vs Non-zero Observations",
                names.arg = c("Zeros", "Non-zeros"), 
                col = c("red", "blue"))
      }
      
      par(mfrow = c(1, 1))
    })
    
    output$ziDiagnostics <- renderUI({
      req(ziResult())
      res <- ziResult()
      
      tagList(
        h4("Model Diagnostics"),
        renderTable({
          data.frame(
            Metric = c("Model Type", "Convergence", "Number of Parameters", "Computational Time", "Model Quality"),
            Value = c(
              input$ziModelType,
              ifelse(!is.null(res$converged), ifelse(res$converged, "Yes", "No"), "N/A"),
              ifelse(!is.null(res$n_parameters), res$n_parameters, "N/A"),
              ifelse(!is.null(res$computation_time), paste(round(res$computation_time, 2), "seconds"), "N/A"),
              ifelse(!is.null(res$quality_score), round(res$quality_score, 4), "N/A")
            )
          )
        }),
        h4("Coefficient Estimates"),
        renderTable({
          if (!is.null(res$coefficients)) {
            coef_df <- as.data.frame(res$coefficients)
            coef_df$Parameter <- rownames(coef_df)
            coef_df <- coef_df[, c("Parameter", names(coef_df)[-ncol(coef_df)])]
            coef_df[, -1] <- round(coef_df[, -1], 4)
            coef_df
          } else {
            data.frame(
              Parameter = "N/A",
              Estimate = "N/A",
              Std_Error = "N/A",
              Z_value = "N/A",
              P_value = "N/A",
              stringsAsFactors = FALSE
            )
          }
        })
      )
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$ziDataSummary <- renderUI({
      req(ziData(), input$ziResponse, input$ziPredictors)
      data <- ziData()
      response <- input$ziResponse
      predictors <- input$ziPredictors
      
      tagList(
        h4("Dataset Summary"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "Response Variable", "Number of Predictors", "Complete Cases"),
            Value = c(
              nrow(data),
              ncol(data),
              response,
              length(predictors),
              sum(complete.cases(data[, c(response, predictors)]))
            )
          )
        }),
        h4("Response Variable Summary"),
        renderTable({
          if (!is.null(response) && response != "") {
            response_data <- data[[response]]
            data.frame(
              Metric = c("Mean", "Median", "SD", "Min", "Max", "Zeros", "Zero Proportion"),
              Value = c(
                round(mean(response_data, na.rm = TRUE), 4),
                round(median(response_data, na.rm = TRUE), 4),
                round(sd(response_data, na.rm = TRUE), 4),
                round(min(response_data, na.rm = TRUE), 4),
                round(max(response_data, na.rm = TRUE), 4),
                sum(response_data == 0, na.rm = TRUE),
                paste(round(mean(response_data == 0, na.rm = TRUE) * 100, 2), "%")
              )
            )
          } else {
            data.frame(Metric = "N/A", Value = "N/A")
          }
        })
      )
    })
    
    output$ziAssumptions <- renderUI({
      req(ziResult())
      res <- ziResult()
      
      tagList(
        h4("Zero-inflated Model Assumptions Check"),
        renderTable({
          data.frame(
            Assumption = c("Non-negative Response", "Excessive Zeros", "Adequate Sample Size", "Valid Predictors"),
            Status = c(
              "Pass",
              ifelse(!is.null(res$zero_inflation) && res$zero_inflation >= 0.1, "Pass", "Fail"),
              ifelse(res$n_observations >= 10, "Pass", "Fail"),
              "Pass"
            ),
            Description = c(
              "Response variable contains only non-negative values",
              "Response variable has sufficient zero inflation (≥10%)",
              "Sufficient observations for stable parameter estimation",
              "Predictor variables are appropriately coded"
            )
          )
        }),
        h4("Model Selection Guidelines"),
        renderTable({
          data.frame(
            Model = c("Zero-inflated Poisson", "Zero-inflated Negative Binomial"),
            Use_When = c(
              "Count data with excessive zeros, no overdispersion",
              "Count data with excessive zeros and overdispersion"
            ),
            Assumptions = c(
              "Equidispersion (variance = mean)",
              "Overdispersion (variance > mean)"
            )
          )
        })
      )
    })
    
    output$ziDiagnosticPlots <- renderPlot({
      req(ziResult())
      res <- ziResult()
      
      par(mfrow = c(2, 2))
      
      # Residuals vs fitted
      if (!is.null(res$residuals) && !is.null(res$fitted)) {
        plot(res$fitted, res$residuals, main = "Residuals vs Fitted",
             xlab = "Fitted Values", ylab = "Residuals", pch = 19, col = "blue")
        abline(h = 0, col = "red", lty = 2)
      }
      
      # Q-Q plot of residuals
      if (!is.null(res$residuals)) {
        qqnorm(res$residuals, main = "Q-Q Plot of Residuals")
        qqline(res$residuals, col = "red")
      }
      
      # Histogram of residuals
      if (!is.null(res$residuals)) {
        hist(res$residuals, main = "Histogram of Residuals",
             xlab = "Residuals", freq = FALSE, col = "lightblue")
      }
      
      # Zero inflation plot
      if (!is.null(res$zero_inflation)) {
        pie(c(res$zero_inflation, 1 - res$zero_inflation), 
            main = "Zero Inflation",
            col = c("red", "blue"),
            labels = c("Zeros", "Non-zeros"))
      }
      
      par(mfrow = c(1, 1))
    })
    
    # Uploaded Data Tab Outputs
    output$ziDataTable <- renderDT({
      req(ziData())
      data <- ziData()
      
      DT::datatable(
        data,
        options = list(
          pageLength = 10,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
        ),
        extensions = 'Buttons',
        filter = 'top',
        rownames = FALSE
      )
    })
    
    output$ziDataInfo <- renderUI({
      req(ziData())
      data <- ziData()
      
      tagList(
        h4("Dataset Information"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "File Name"),
            Value = c(nrow(data), ncol(data), 
                     ifelse(!is.null(input$ziUserData), input$ziUserData$name, "Unknown"))
          )
        }),
        h4("Variable Information"),
        renderTable({
          data.frame(
            Variable = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Unique = sapply(data, function(x) length(unique(x)))
          )
        })
      )
    })
    
    # Model Results Outputs
    output$ziResults <- renderTable({
      req(ziResult())
      res <- ziResult()
      
      if (!is.null(res$coefficients)) {
        coef_df <- as.data.frame(res$coefficients)
        coef_df$Parameter <- rownames(coef_df)
        coef_df <- coef_df[, c("Parameter", names(coef_df)[-ncol(coef_df)])]
        coef_df[, -1] <- round(coef_df[, -1], 4)
        coef_df
      } else {
        data.frame(
          Parameter = "N/A",
          Estimate = "N/A",
          Std_Error = "N/A",
          Z_value = "N/A",
          P_value = "N/A",
          stringsAsFactors = FALSE
        )
      }
    }, rownames = FALSE)
  })
} 