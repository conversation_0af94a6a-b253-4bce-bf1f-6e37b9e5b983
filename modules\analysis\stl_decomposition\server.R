STLDecompositionServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    stlData <- eventReactive(input$stlUserData, {
      handle_file_upload(input$stlUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(stlData(), {
      data <- stlData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'stlTime', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'stlSeries', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    stlValidationErrors <- reactive({
      errors <- c()
      data <- stlData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$stlSeries) || input$stlSeries == "") {
        errors <- c(errors, "Select a series variable.")
      }
      
      # Check if selected variable is numeric
      if (!is.null(input$stlSeries) && input$stlSeries != "") {
        if (!is.numeric(data[[input$stlSeries]])) {
          errors <- c(errors, "Series variable must be numeric.")
        }
      }
      
      # Check if time variable is provided and valid
      if (!is.null(input$stlTime) && input$stlTime != "") {
        if (!is.numeric(data[[input$stlTime]]) && !inherits(data[[input$stlTime]], "Date")) {
          errors <- c(errors, "Time variable must be numeric or date.")
        }
      }
      
      errors
    })
    
    # STL decomposition analysis reactive
    stlAnalysis <- eventReactive(input$goSTL, {
      data <- stlData()
      req(data, input$stlSeries)
      
      # Get series data
      series_values <- data[[input$stlSeries]]
      series_values <- series_values[!is.na(series_values)]  # Remove missing values
      
      if (length(series_values) < 20) {
        return(NULL)
      }
      
      # Determine frequency for time series
      frequency <- 1  # Default frequency
      if (!is.null(input$stlTime) && input$stlTime != "") {
        time_values <- data[[input$stlTime]]
        if (is.numeric(time_values)) {
          # Try to determine frequency from time differences
          time_diff <- diff(time_values)
          if (length(unique(round(time_diff, 2))) == 1) {
            frequency <- 1 / unique(round(time_diff, 2))[1]
          }
        } else if (inherits(time_values, "Date")) {
          # For date data, try to determine seasonality
          time_diff <- as.numeric(diff(time_values))
          if (all(time_diff == 1)) {
            frequency <- 7  # Daily data, weekly seasonality
          } else if (all(time_diff == 7)) {
            frequency <- 52  # Weekly data, yearly seasonality
          } else if (all(time_diff == 30)) {
            frequency <- 12  # Monthly data, yearly seasonality
          }
        }
      }
      
      # Create time series object
      ts_obj <- ts(series_values, frequency = frequency)
      
      # Perform STL decomposition
      tryCatch({
        # STL decomposition
        stl_result <- stl(ts_obj, s.window = "periodic", robust = TRUE)
        
        # Extract components
        trend <- stl_result$time.series[, "trend"]
        seasonal <- stl_result$time.series[, "seasonal"]
        remainder <- stl_result$time.series[, "remainder"]
        
        # Calculate component statistics
        trend_stats <- list(
          mean = mean(trend, na.rm = TRUE),
          sd = sd(trend, na.rm = TRUE),
          range = range(trend, na.rm = TRUE)
        )
        
        seasonal_stats <- list(
          mean = mean(seasonal, na.rm = TRUE),
          sd = sd(seasonal, na.rm = TRUE),
          range = range(seasonal, na.rm = TRUE)
        )
        
        remainder_stats <- list(
          mean = mean(remainder, na.rm = TRUE),
          sd = sd(remainder, na.rm = TRUE),
          range = range(remainder, na.rm = TRUE)
        )
        
        # Calculate strength of trend and seasonality
        total_variance <- var(series_values, na.rm = TRUE)
        trend_strength <- var(trend, na.rm = TRUE) / total_variance
        seasonal_strength <- var(seasonal, na.rm = TRUE) / total_variance
        
        # Residual diagnostics
        residual_acf <- acf(remainder, plot = FALSE, lag.max = min(20, length(remainder) %/% 4))
        
        list(
          stl_result = stl_result,
          ts_object = ts_obj,
          series_values = series_values,
          trend = trend,
          seasonal = seasonal,
          remainder = remainder,
          trend_stats = trend_stats,
          seasonal_stats = seasonal_stats,
          remainder_stats = remainder_stats,
          trend_strength = trend_strength,
          seasonal_strength = seasonal_strength,
          residual_acf = residual_acf,
          frequency = frequency,
          n_observations = length(series_values)
        )
      }, error = function(e) {
        NULL
      })
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goSTL, {
      output$stlError <- renderUI({
        tryCatch({ 
          res <- stlAnalysis()
          if (is.null(res)) {
            errorScreenUI(title = "STL Decomposition Error", errors = "Decomposition failed. Check your data and ensure at least 20 observations are available.")
          } else {
            NULL
          }
        }, error = function(e) {
          errorScreenUI(title = "STL Decomposition Error", errors = e$message)
        })
      })
      
      output$stlResults <- renderUI({
        res <- stlAnalysis()
        if (is.null(res)) return(NULL)
        
        tagList(
          tabsetPanel(
            id = ns("stlTabset"),
            selected = "Analysis",
            tabPanel(
              id = ns("stlAnalysis"),
              title = "Analysis",
              titlePanel("STL Decomposition Results"),
              br(),
              h4("Component Strengths"),
              tableOutput(ns('stlStrengths')),
              br(),
              h4("Component Statistics"),
              tableOutput(ns('stlComponentStats')),
              br(),
              h4("Decomposition Summary"),
              verbatimTextOutput(ns('stlSummary'))
            ),
            tabPanel(
              id = ns("stlDiagnostics"),
              title = "Diagnostics",
              h4("STL Decomposition Plot"),
              plotOutput(ns('stlDecompositionPlot'), height = "600px"),
              br(),
              h4("Component Plots"),
              plotOutput(ns('stlComponentPlots'), height = "500px"),
              br(),
              h4("Residual Diagnostics"),
              plotOutput(ns('stlResidualDiagnostics'), height = "400px")
            ),
            tabPanel(
              id = ns("stlDataSummary"),
              title = "Data Summary",
              h4("Time Series Overview"),
              tableOutput(ns('stlOverview')),
              br(),
              h4("Original Series Statistics"),
              tableOutput(ns('stlSeriesStats')),
              br(),
              h4("Component Analysis"),
              uiOutput(ns('stlComponentAnalysis'))
            ),
            tabPanel(
              id = ns("stlUploadedData"),
              title = "Uploaded Data",
              h4("Raw Data"),
              uiOutput(ns('stlDataTable'))
            )
          )
        )
      })
    })
    
    # Component strengths table
    output$stlStrengths <- renderTable({
      res <- stlAnalysis()
      if (is.null(res)) return(NULL)
      
      trend_desc <- if (res$trend_strength > 0.7) "Strong" else
                   if (res$trend_strength > 0.3) "Moderate" else "Weak"
      
      seasonal_desc <- if (res$seasonal_strength > 0.7) "Strong" else
                      if (res$seasonal_strength > 0.3) "Moderate" else "Weak"
      
      data.frame(
        Component = c("Trend", "Seasonal"),
        Strength = c(res$trend_strength, res$seasonal_strength),
        Interpretation = c(trend_desc, seasonal_desc),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    # Component statistics table
    output$stlComponentStats <- renderTable({
      res <- stlAnalysis()
      if (is.null(res)) return(NULL)
      
      data.frame(
        Component = c("Trend", "Seasonal", "Remainder"),
        Mean = c(res$trend_stats$mean, res$seasonal_stats$mean, res$remainder_stats$mean),
        SD = c(res$trend_stats$sd, res$seasonal_stats$sd, res$remainder_stats$sd),
        Min = c(res$trend_stats$range[1], res$seasonal_stats$range[1], res$remainder_stats$range[1]),
        Max = c(res$trend_stats$range[2], res$seasonal_stats$range[2], res$remainder_stats$range[2]),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    # STL summary output
    output$stlSummary <- renderPrint({
      res <- stlAnalysis()
      if (is.null(res)) return(NULL)
      
      cat("STL Decomposition Summary:\n")
      cat("========================\n\n")
      cat("Number of observations:", res$n_observations, "\n")
      cat("Frequency:", res$frequency, "\n")
      cat("Trend strength:", round(res$trend_strength, 4), "\n")
      cat("Seasonal strength:", round(res$seasonal_strength, 4), "\n")
      cat("Remainder variance:", round(var(res$remainder, na.rm = TRUE), 4), "\n")
    })
    
    # STL decomposition plot
    output$stlDecompositionPlot <- renderPlot({
      res <- stlAnalysis()
      if (is.null(res)) return(NULL)
      
      plot(res$stl_result, main = "STL Decomposition")
    })
    
    # Component plots
    output$stlComponentPlots <- renderPlot({
      res <- stlAnalysis()
      if (is.null(res)) return(NULL)
      
      par(mfrow = c(3, 1))
      
      # Trend plot
      plot(res$trend, main = "Trend Component", 
           xlab = "Time", ylab = "Trend", type = "l", col = "blue")
      
      # Seasonal plot
      plot(res$seasonal, main = "Seasonal Component", 
           xlab = "Time", ylab = "Seasonal", type = "l", col = "red")
      
      # Remainder plot
      plot(res$remainder, main = "Remainder Component", 
           xlab = "Time", ylab = "Remainder", type = "l", col = "green")
      
      par(mfrow = c(1, 1))
    })
    
    # Residual diagnostics
    output$stlResidualDiagnostics <- renderPlot({
      res <- stlAnalysis()
      if (is.null(res)) return(NULL)
      
      par(mfrow = c(2, 2))
      
      # Residuals vs time
      plot(res$remainder, main = "Remainder vs Time", 
           xlab = "Time", ylab = "Remainder")
      abline(h = 0, col = "red", lty = 2)
      
      # Residual ACF
      acf(res$remainder, main = "ACF of Remainder", lag.max = 20)
      
      # Q-Q plot
      qqnorm(res$remainder, main = "Q-Q Plot of Remainder")
      qqline(res$remainder, col = "red")
      
      # Histogram
      hist(res$remainder, main = "Histogram of Remainder", 
           xlab = "Remainder", probability = TRUE)
      curve(dnorm(x, mean = mean(res$remainder, na.rm = TRUE), 
                  sd = sd(res$remainder, na.rm = TRUE)), 
            add = TRUE, col = "red")
      
      par(mfrow = c(1, 1))
    })
    
    # Time series overview
    output$stlOverview <- renderTable({
      res <- stlAnalysis()
      if (is.null(res)) return(NULL)
      
      data.frame(
        Property = c("Number of Observations", "Frequency", "Time Period", "Missing Values"),
        Value = c(res$n_observations,
                 res$frequency,
                 paste("1 to", res$n_observations),
                 sum(is.na(res$series_values))),
        stringsAsFactors = FALSE
      )
    })
    
    # Original series statistics
    output$stlSeriesStats <- renderTable({
      res <- stlAnalysis()
      if (is.null(res)) return(NULL)
      
      series_median <- median(res$series_values, na.rm = TRUE)
      series_skewness <- moments::skewness(res$series_values, na.rm = TRUE)
      series_kurtosis <- moments::kurtosis(res$series_values, na.rm = TRUE)
      
      data.frame(
        Statistic = c("Mean", "Median", "Standard Deviation", "Skewness", "Kurtosis"),
        Value = c(mean(res$series_values, na.rm = TRUE), 
                 series_median,
                 sd(res$series_values, na.rm = TRUE),
                 series_skewness,
                 series_kurtosis),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    # Component analysis
    output$stlComponentAnalysis <- renderUI({
      res <- stlAnalysis()
      if (is.null(res)) return(NULL)
      
      tagList(
        h5("Component Interpretation:"),
        p(strong("Trend: "), "Long-term systematic change in the series"),
        p(strong("Seasonal: "), "Regular periodic fluctuations"),
        p(strong("Remainder: "), "Irregular, random variation"),
        br(),
        p(strong("Strength Guidelines:")),
        p("- > 0.7: Strong component"),
        p("- 0.3-0.7: Moderate component"),
        p("- < 0.3: Weak component"),
        br(),
        p("Note: STL decomposition assumes additive components.")
      )
    })
    
    # Uploaded data table
    output$stlDataTable <- renderUI({
      req(stlData())
      DT::DTOutput(ns("stlDataTableInner"))
    })
    
    output$stlDataTableInner <- DT::renderDT({
      req(stlData())
      DT::datatable(stlData(),
        options = list(pageLength = 25,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(stlData())))))
    })
  })
} 