# PCA calculation and output helpers

pca_uploadData_func <- function(pcaUserData) {
  ext <- tools::file_ext(pcaUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(pcaUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(pcaUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(pcaUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(pcaUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

pca_results_func <- function(data, vars, scale = TRUE, center = TRUE) {
  tryCatch({
    
    x <- data[, vars, drop = FALSE]
    x <- x[complete.cases(x), ]
    
    fit <- stats::prcomp(x, scale. = scale, center = center)
    
    list(
      fit = fit,
      data = data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during PCA calculation:", e$message))
  })
}

pca_ht_html <- function(results) {
  # Bartlett's test of sphericity
  bartlett_result <- psych::cortest.bartlett(results$data)
  
  tagList(
    h4("Bartlett's Test of Sphericity"),
    p(sprintf("Chi-square: %.4f, p-value: %.4f", bartlett_result$chisq, bartlett_result$p.value)),
    p(ifelse(bartlett_result$p.value < 0.05, "The variables are likely suitable for factor analysis.", "The variables may not be suitable for factor analysis."))
  )
}

pca_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  summary_table <- as.data.frame(summary(results$fit)$importance)
  out <- list(
    h4("Component Summary"),
    renderTable(summary_table, rownames = TRUE)
  )
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

pca_plot <- function(results) {
  if (!requireNamespace("factoextra", quietly = TRUE)) {
    plot.new(); title("Package 'factoextra' required for plotting."); return()
  }
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  factoextra::fviz_eig(results$fit, addlabels = TRUE)
  # Biplot
  factoextra::fviz_pca_biplot(results$fit)
}