# Robust ANOVA calculation and output helpers

robust_anova_uploadData_func <- function(raUserData, response_var, factor_var) {
  tryCatch(
    {
      if (is.null(raUserData) || is.null(response_var) || is.null(factor_var)) {
        return(NULL)
      }
      
      # Read data based on file type
      if (grepl("\\.csv$", raUserData$name, ignore.case = TRUE)) {
        df <- read.csv(raUserData$datapath, stringsAsFactors = TRUE)
      } else if (grepl("\\.tsv$", raUserData$name, ignore.case = TRUE)) {
        df <- read.tsv(raUserData$datapath, stringsAsFactors = TRUE)
      } else if (grepl("\\.xlsx$", raUserData$name, ignore.case = TRUE)) {
        df <- readxl::read_excel(raUserData$datapath)
      } else {
        stop("Unsupported file type. Please upload a CSV, TSV, or XLSX file.")
      }
      
      # Check for required columns
      required_cols <- c(response_var, factor_var)
      if (!all(required_cols %in% names(df))) {
        missing_cols <- required_cols[!required_cols %in% names(df)]
        stop(paste("The following columns were not found in the uploaded file:", paste(missing_cols, collapse = ", ")))
      }
      
      df[[factor_var]] <- as.factor(df[[factor_var]])
      
      return(df)
    },
    error = function(e) {
      return(list(error = paste("Error reading data:", e$message)))
    }
  )
}

robust_anova_results_func <- function(data, response_var, factor_var, method = "t1way") {
  tryCatch({
    if (is.null(data) || nrow(data) == 0) {
      stop("No data provided for analysis.")
    }
    
    if (!requireNamespace("WRS2", quietly = TRUE)) {
      stop("Package 'WRS2' needed for robust ANOVA.")
    }
    
    formula_str <- as.formula(paste0("`", response_var, "` ~ `", factor_var, "`"))
    
    test_result <- switch(method,
      "t1way" = WRS2::t1way(formula_str, data = data),
      "t1waybt" = WRS2::t1waybt(formula_str, data = data),
      "med1way" = WRS2::med1way(formula_str, data = data),
      stop("Invalid method specified for robust ANOVA.")
    )
    
    list(
      test = test_result,
      data = data,
      response_var = response_var,
      factor_var = factor_var,
      method = method,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Robust ANOVA calculation:", e$message))
  })
}

robust_anova_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  
  p_value <- results$test$p.value
  conclusion <- if (p_value < 0.05) "significant" else "not significant"
  
  tagList(
    h4(paste("Robust ANOVA Results (", results$method, ")")),
    p(sprintf("The difference between the groups is %s (p = %.3f).", conclusion, p_value))
  )
}

robust_anova_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  renderPrint(results$test)
}

robust_anova_plot <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  
  if (!requireNamespace("ggplot2", quietly = TRUE)) {
    stop("Package 'ggplot2' is required for plotting.")
  }
  
  ggplot(results$data, aes_string(x = results$factor_var, y = results$response_var, fill = results$factor_var)) +
    geom_boxplot(alpha = 0.7) +
    geom_jitter(width = 0.2, alpha = 0.5) +
    labs(title = "Boxplot of Response by Group",
         x = results$factor_var,
         y = results$response_var) +
    theme_minimal() +
    theme(legend.position = "none")
}