ZeroInflatedSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("ziUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
    selectInput(ns("ziModelType"), "Model Type", choices = c("Zero-inflated Poisson", "Zero-inflated Negative Binomial")),
    selectizeInput(ns("ziResponse"), "Response Variable", choices = NULL),
    selectizeInput(ns("ziPredictors"), "Predictor Variables", choices = NULL, multiple = TRUE),
    br(),
    actionButton(ns("goZI"), label = "Fit Model", class = "act-btn")
  )
}

ZeroInflatedMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns("ziError")),
    tableOutput(ns("ziResults")),
    plotOutput(ns("ziPlot"))
  )
}

ZeroInflatedUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(ZeroInflatedSidebarUI(id)),
    mainPanel(ZeroInflatedMainUI(id))
  )
} 