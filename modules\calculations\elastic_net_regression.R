# Elastic Net Regression calculation and output helpers

elastic_net_regression_uploadData_func <- function(enrUserData) {
  ext <- tools::file_ext(enrUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(enrUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(enrUserData$datapath),
         xlsx = readxl::read_xlsx(enrUserData$datapath),
         txt = readr::read_tsv(enrUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

elastic_net_regression_results_func <- function(data, response_var, predictor_vars, alpha = 0.5, use_cv = TRUE, cv_folds = 10) {
  tryCatch({
    if (!requireNamespace("glmnet", quietly = TRUE)) {
      stop("Package 'glmnet' needed for elastic net regression.")
    }
    
    all_vars <- c(response_var, predictor_vars)
    model_data <- data[complete.cases(data[all_vars]), ]
    
    if (nrow(model_data) < 10) {
      stop("At least 10 observations are required for elastic net regression")
    }
    
    X <- as.matrix(model_data[predictor_vars])
    y <- model_data[[response_var]]
    
    if (use_cv) {
      cv_fit <- glmnet::cv.glmnet(X, y, alpha = alpha, nfolds = cv_folds)
      best_lambda <- cv_fit$lambda.min
      model <- glmnet::glmnet(X, y, alpha = alpha, lambda = best_lambda)
    } else {
      model <- glmnet::glmnet(X, y, alpha = alpha)
      cv_fit <- NULL
    }
    
    list(
      model = model,
      cv_fit = cv_fit,
      data = model_data,
      response_var = response_var,
      predictor_vars = predictor_vars,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Elastic Net regression calculation:", e$message))
  })
}

elastic_net_regression_ht_html <- function(results) {
  # No formal hypothesis test for elastic net, so this will display model info
  tagList(
    h4("Elastic Net Regression Model"),
    p(paste("Alpha:", results$model$alpha)),
    p(paste("Number of Predictors:", length(results$predictor_vars)))
  )
}

elastic_net_regression_summary_html <- function(results) {
  coefs <- coef(results$model)
  
  coef_df <- data.frame(
    Variable = rownames(coefs),
    Coefficient = as.numeric(coefs)
  )
  
  tagList(
    h4("Coefficients"),
    renderTable(coef_df, digits = 4),
    if (!is.null(results$cv_fit)) {
      tagList(
        h4("Cross-Validation Results"),
        p(paste("Optimal Lambda:", results$cv_fit$lambda.min))
      )
    }
  )
}

elastic_net_regression_plot <- function(results) {
  if (!is.null(results$cv_fit)) {
    plot(results$cv_fit)
  } else {
    plot(results$model, xvar = "lambda", label = TRUE)
  }
}