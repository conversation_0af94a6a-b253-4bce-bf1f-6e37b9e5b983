# Three-Way ANOVA UI
# Factorial design with three factors

ThreeWayAnovaSidebarUI <- function(id) {
  ns <- NS(id)
  
  tagList(
    # Data Input Method
    radioButtons(
      inputId = ns("threeWayDataMethod"),
      label = "Data Input Method:",
      choices = c("Manual Entry" = "Manual Entry", "Upload File" = "Upload File"),
      selected = "Manual Entry"
    ),
    
    # Manual Entry
    conditionalPanel(
      condition = "input.threeWayDataMethod == 'Manual Entry'",
      ns = ns,
      textAreaInput(
        inputId = ns("threeWayData"),
        label = "Enter Response Values (comma, space, or newline separated):",
        placeholder = "1, 2, 3, 4, 5, 6, 7, 8",
        rows = 4
      ),
      textAreaInput(
        inputId = ns("threeWayFactor1Data"),
        label = "Enter Factor 1 Values (comma, space, or newline separated):",
        placeholder = "A, A, B, B, A, A, B, B",
        rows = 4
      ),
      textAreaInput(
        inputId = ns("threeWayFactor2Data"),
        label = "Enter Factor 2 Values (comma, space, or newline separated):",
        placeholder = "1, 1, 1, 1, 2, 2, 2, 2",
        rows = 4
      ),
      textAreaInput(
        inputId = ns("threeWayFactor3Data"),
        label = "Enter Factor 3 Values (comma, space, or newline separated):",
        placeholder = "X, Y, X, Y, X, Y, X, Y",
        rows = 4
      )
    ),
    
    # File Upload
    conditionalPanel(
      condition = "input.threeWayDataMethod == 'Upload File'",
      ns = ns,
      fileInput(
        inputId = ns("threeWayUserData"),
        label = "Upload Data File:",
        accept = c(".csv", ".txt", ".xlsx", ".xls"),
        buttonLabel = "Browse Files",
        placeholder = "No file selected"
      ),
      selectizeInput(
        inputId = ns("threeWayResponseVariable"),
        label = "Select Response Variable:",
        choices = NULL,
        options = list(placeholder = "Select response variable...")
      ),
      selectizeInput(
        inputId = ns("threeWayFactor1"),
        label = "Select Factor 1:",
        choices = NULL,
        options = list(placeholder = "Select factor 1...")
      ),
      selectizeInput(
        inputId = ns("threeWayFactor2"),
        label = "Select Factor 2:",
        choices = NULL,
        options = list(placeholder = "Select factor 2...")
      ),
      selectizeInput(
        inputId = ns("threeWayFactor3"),
        label = "Select Factor 3:",
        choices = NULL,
        options = list(placeholder = "Select factor 3...")
      )
    ),
    
    # Test Parameters
    numericInput(
      inputId = ns("threeWayConfLevel"),
      label = "Confidence Level:",
      value = 0.95,
      min = 0.5,
      max = 0.99,
      step = 0.01
    ),
    
    actionButton(
      inputId = ns("goThreeWay"),
      label = "Calculate Three-Way ANOVA",
      class = "btn-primary",
      style = "width: 100%;"
    ),
    
    br(),
    br(),
    
    # Help text
    helpText(
      "Three-way ANOVA tests the effects of three factors and their interactions.",
      "Main effects test individual factor effects.",
      "Two-way interactions test combined effects of two factors.",
      "Three-way interaction tests the combined effect of all three factors."
    )
  )
}

ThreeWayAnovaMainUI <- function(id) {
  ns <- NS(id)
  
  tagList(
    titlePanel("Three-Way ANOVA"),
    p("Analysis of variance for factorial designs with three factors, testing main effects and interactions."),
    
    uiOutput(ns("threeWayResults"))
  )
} 