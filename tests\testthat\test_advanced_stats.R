# Test Suite for Advanced Statistics Modules
# Tests: Advanced Survival, GAM, MANOVA, Nonlinear Regression, etc.

library(testthat)

# Test Advanced Survival Analysis Module
test_that("Advanced Survival Analysis Module", {
  # Load test data
  data <- load_test_data("advanced_survival")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 3)
  
  # Test advanced survival analysis
  if (exists("advanced_survival_analysis")) {
    result <- advanced_survival_analysis(data, names(data)[1], names(data)[2], names(data)[3])
    expect_true(is.list(result))
    expect_true("survival_curves" %in% names(result))
    expect_true("hazard_functions" %in% names(result))
    expect_true("model_comparison" %in% names(result))
    expect_true("time_dependent_covariates" %in% names(result))
  }
  
  cat("  ✓ Advanced Survival: Complex survival modeling tested\n")
})

# Test GAM Module
test_that("Generalized Additive Models Module", {
  # Load test data
  data <- load_test_data("gam")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 3)
  
  # Test GAM
  if (exists("gam_analysis")) {
    result <- gam_analysis(data, names(data)[1], names(data)[2:min(4, ncol(data))])
    expect_true(is.list(result))
    expect_true("smooth_terms" %in% names(result))
    expect_true("r_squared" %in% names(result))
    expect_true("deviance_explained" %in% names(result))
    expect_true("smooth_plots" %in% names(result))
  }
  
  cat("  ✓ GAM: Generalized additive models tested\n")
})

# Test MANOVA Module
test_that("MANOVA Module", {
  # Load test data
  data <- load_test_data("manova")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 4)
  
  # Test MANOVA
  if (exists("manova_analysis")) {
    response_vars <- names(data)[1:2]
    group_var <- names(data)[3]
    result <- manova_analysis(data, response_vars, group_var)
    expect_true(is.list(result))
    expect_true("pillai_trace" %in% names(result))
    expect_true("wilks_lambda" %in% names(result))
    expect_true("hotelling_trace" %in% names(result))
    expect_true("roy_root" %in% names(result))
    expect_true("p_values" %in% names(result))
  }
  
  cat("  ✓ MANOVA: Multivariate analysis of variance tested\n")
})

# Test Nonlinear Regression Module
test_that("Nonlinear Regression Module", {
  # Load test data
  data <- load_test_data("nonlinear_regression")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test nonlinear regression
  if (exists("nonlinear_regression_analysis")) {
    # Test exponential model
    result_exp <- nonlinear_regression_analysis(data, names(data)[1], names(data)[2], 
                                              formula = "y ~ a * exp(-b * x)")
    expect_true(is.list(result_exp))
    expect_true("parameters" %in% names(result_exp))
    expect_true("r_squared" %in% names(result_exp))
    expect_true("aic" %in% names(result_exp))
    
    # Test logistic model
    result_log <- nonlinear_regression_analysis(data, names(data)[1], names(data)[2], 
                                              formula = "y ~ a / (1 + exp(-b * (x - c)))")
    expect_true(is.list(result_log))
    expect_true("parameters" %in% names(result_log))
  }
  
  cat("  ✓ Nonlinear Regression: Nonlinear model fitting tested\n")
})

# Test Propensity Score Module
test_that("Propensity Score Module", {
  # Load test data
  data <- load_test_data("propensity_score")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 4)
  
  # Test propensity score analysis
  if (exists("propensity_score_analysis")) {
    treatment_var <- names(data)[1]
    outcome_var <- names(data)[2]
    covariates <- names(data)[3:min(6, ncol(data))]
    
    result <- propensity_score_analysis(data, treatment_var, outcome_var, covariates)
    expect_true(is.list(result))
    expect_true("propensity_scores" %in% names(result))
    expect_true("balance_statistics" %in% names(result))
    expect_true("treatment_effects" %in% names(result))
    expect_true("matching_results" %in% names(result))
  }
  
  cat("  ✓ Propensity Score: Causal inference methods tested\n")
})

# Test Mediation/Moderation Module
test_that("Mediation/Moderation Module", {
  # Load test data
  data <- load_test_data("mediation_moderation")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 4)
  
  # Test mediation/moderation analysis
  if (exists("mediation_moderation_analysis")) {
    x_var <- names(data)[1]
    m_var <- names(data)[2]
    y_var <- names(data)[3]
    w_var <- names(data)[4]  # moderator
    
    result <- mediation_moderation_analysis(data, x_var, m_var, y_var, w_var)
    expect_true(is.list(result))
    expect_true("direct_effect" %in% names(result))
    expect_true("indirect_effect" %in% names(result))
    expect_true("total_effect" %in% names(result))
    expect_true("moderation_effects" %in% names(result))
    expect_true("conditional_effects" %in% names(result))
  }
  
  cat("  ✓ Mediation/Moderation: Mediation and moderation analysis tested\n")
})

# Test Bayesian Hierarchical Module
test_that("Bayesian Hierarchical Module", {
  # Load test data
  data <- load_test_data("bayesian_hierarchical")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 4)
  
  # Test Bayesian hierarchical model
  if (exists("bayesian_hierarchical_analysis")) {
    response_var <- names(data)[1]
    predictor_var <- names(data)[2]
    group_var <- names(data)[3]
    level2_var <- names(data)[4]
    
    result <- bayesian_hierarchical_analysis(data, response_var, predictor_var, group_var, level2_var)
    expect_true(is.list(result))
    expect_true("group_effects" %in% names(result))
    expect_true("overall_effect" %in% names(result))
    expect_true("random_effects" %in% names(result))
    expect_true("credible_intervals" %in% names(result))
  }
  
  cat("  ✓ Bayesian Hierarchical: Multilevel Bayesian modeling tested\n")
})

# Test Competing Risks Module
test_that("Competing Risks Module", {
  # Load test data
  data <- load_test_data("competing_risks")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 4)
  
  # Test competing risks analysis
  if (exists("competing_risks_analysis")) {
    time_var <- names(data)[1]
    event_var <- names(data)[2]
    cause_var <- names(data)[3]
    covariate_var <- names(data)[4]
    
    result <- competing_risks_analysis(data, time_var, event_var, cause_var, covariate_var)
    expect_true(is.list(result))
    expect_true("cause_specific_hazards" %in% names(result))
    expect_true("cumulative_incidence" %in% names(result))
    expect_true("subdistribution_hazards" %in% names(result))
    expect_true("gray_test" %in% names(result))
  }
  
  cat("  ✓ Competing Risks: Multiple event type analysis tested\n")
})

# Test Stratified Kaplan-Meier Module
test_that("Stratified Kaplan-Meier Module", {
  # Load test data
  data <- load_test_data("stratified_km")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 3)
  
  # Test stratified KM
  if (exists("stratified_km_analysis")) {
    time_var <- names(data)[1]
    event_var <- names(data)[2]
    strata_var <- names(data)[3]
    
    result <- stratified_km_analysis(data, time_var, event_var, strata_var)
    expect_true(is.list(result))
    expect_true("strata_survival" %in% names(result))
    expect_true("log_rank_test" %in% names(result))
    expect_true("stratified_curves" %in% names(result))
    expect_true("pairwise_comparisons" %in% names(result))
  }
  
  cat("  ✓ Stratified KM: Group-specific survival analysis tested\n")
})

# Test Cox Proportional Hazards Module
test_that("Cox Proportional Hazards Module", {
  # Load test data
  data <- load_test_data("coxph")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 4)
  
  # Test Cox PH model
  if (exists("coxph_analysis")) {
    time_var <- names(data)[1]
    event_var <- names(data)[2]
    predictor_vars <- names(data)[3:min(5, ncol(data))]
    
    result <- coxph_analysis(data, time_var, event_var, predictor_vars)
    expect_true(is.list(result))
    expect_true("hazard_ratios" %in% names(result))
    expect_true("confidence_intervals" %in% names(result))
    expect_true("p_values" %in% names(result))
    expect_true("proportional_hazards_test" %in% names(result))
    expect_true("martingale_residuals" %in% names(result))
  }
  
  cat("  ✓ Cox PH: Proportional hazards modeling tested\n")
})

# Test Discriminant Analysis Module
test_that("Discriminant Analysis Module", {
  # Load test data
  data <- load_test_data("discriminant")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 4)
  
  # Test discriminant analysis
  if (exists("discriminant_analysis")) {
    group_var <- names(data)[1]
    predictor_vars <- names(data)[2:min(5, ncol(data))]
    
    result <- discriminant_analysis(data, group_var, predictor_vars)
    expect_true(is.list(result))
    expect_true("discriminant_functions" %in% names(result))
    expect_true("classification_results" %in% names(result))
    expect_true("canonical_correlations" %in% names(result))
    expect_true("eigenvalues" %in% names(result))
  }
  
  cat("  ✓ Discriminant Analysis: Classification analysis tested\n")
})

# Test CCA Module
test_that("Canonical Correlation Analysis Module", {
  # Load test data
  data <- load_test_data("cca")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 5)
  
  # Test CCA
  if (exists("cca_analysis")) {
    set1_vars <- names(data)[1:2]
    set2_vars <- names(data)[3:4]
    
    result <- cca_analysis(data, set1_vars, set2_vars)
    expect_true(is.list(result))
    expect_true("canonical_correlations" %in% names(result))
    expect_true("canonical_weights" %in% names(result))
    expect_true("canonical_loadings" %in% names(result))
    expect_true("canonical_scores" %in% names(result))
  }
  
  cat("  ✓ CCA: Canonical correlation analysis tested\n")
})

# Test MDS Module
test_that("Multidimensional Scaling Module", {
  # Load test data
  data <- load_test_data("mds")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 3)
  
  # Test MDS
  if (exists("mds_analysis")) {
    result <- mds_analysis(data, names(data), k = 2, method = "classical")
    expect_true(is.list(result))
    expect_true("coordinates" %in% names(result))
    expect_true("stress" %in% names(result))
    expect_true("goodness_of_fit" %in% names(result))
  }
  
  cat("  ✓ MDS: Multidimensional scaling tested\n")
})

# Test TSNE/UMAP Module
test_that("TSNE/UMAP Module", {
  # Load test data
  data <- load_test_data("tsne_umap")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 4)
  
  # Test TSNE
  if (exists("tsne_umap_analysis")) {
    result_tsne <- tsne_umap_analysis(data, names(data), method = "tsne", perplexity = 30)
    expect_true(is.list(result_tsne))
    expect_true("coordinates" %in% names(result_tsne))
    expect_true("stress" %in% names(result_tsne))
    
    # Test UMAP
    result_umap <- tsne_umap_analysis(data, names(data), method = "umap", n_neighbors = 15)
    expect_true(is.list(result_umap))
    expect_true("coordinates" %in% names(result_umap))
  }
  
  cat("  ✓ TSNE/UMAP: Nonlinear dimensionality reduction tested\n")
})

# Test Network Analysis Module
test_that("Network Analysis Module", {
  # Load test data
  data <- load_test_data("network_analysis")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 3)
  
  # Test network analysis
  if (exists("network_analysis_analysis")) {
    result <- network_analysis_analysis(data, names(data)[1], names(data)[2])
    expect_true(is.list(result))
    expect_true("network_matrix" %in% names(result))
    expect_true("centrality_measures" %in% names(result))
    expect_true("community_structure" %in% names(result))
    expect_true("network_metrics" %in% names(result))
  }
  
  cat("  ✓ Network Analysis: Graph theory analysis tested\n")
})

# Test SEM Module
test_that("Structural Equation Modeling Module", {
  # Load test data
  data <- load_test_data("sem")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 4)
  
  # Test SEM
  if (exists("sem_analysis")) {
    result <- sem_analysis(data, names(data))
    expect_true(is.list(result))
    expect_true("path_coefficients" %in% names(result))
    expect_true("model_fit" %in% names(result))
    expect_true("modification_indices" %in% names(result))
    expect_true("standardized_coefficients" %in% names(result))
  }
  
  cat("  ✓ SEM: Structural equation modeling tested\n")
})

# Test Latent Class Analysis Module
test_that("Latent Class Analysis Module", {
  # Load test data
  data <- load_test_data("latent_class")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 4)
  
  # Test latent class analysis
  if (exists("latent_class_analysis")) {
    result <- latent_class_analysis(data, names(data), n_classes = 3)
    expect_true(is.list(result))
    expect_true("class_probabilities" %in% names(result))
    expect_true("class_assignments" %in% names(result))
    expect_true("bic" %in% names(result))
    expect_true("aic" %in% names(result))
  }
  
  cat("  ✓ Latent Class Analysis: Mixture modeling tested\n")
})

# Test IRT Module
test_that("Item Response Theory Module", {
  # Load test data
  data <- load_test_data("irt")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 4)
  
  # Test IRT
  if (exists("irt_analysis")) {
    result <- irt_analysis(data, names(data))
    expect_true(is.list(result))
    expect_true("item_parameters" %in% names(result))
    expect_true("ability_estimates" %in% names(result))
    expect_true("item_fit" %in% names(result))
    expect_true("model_fit" %in% names(result))
  }
  
  cat("  ✓ IRT: Item response theory tested\n")
})

cat("Advanced Statistics Modules: All tests completed\n") 