# Longitudinal Data Analysis calculation and output helpers

longitudinal_analysis_uploadData_func <- function(laUserData) {
  ext <- tools::file_ext(laUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(laUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(laUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(laUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(laUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

longitudinal_analysis_results_func <- function(data, analysis_type = "growth_curve", ...) {
  tryCatch({
    args <- list(...)
    
    results <- switch(analysis_type,
      "growth_curve" = do.call(growth_curve_analysis, c(list(data = data), args)),
      "latent_growth" = do.call(latent_growth_analysis, c(list(data = data), args)),
      "autoregressive" = do.call(autoregressive_analysis, c(list(data = data), args)),
      "cross_lagged" = do.call(cross_lagged_analysis, c(list(data = data), args)),
      "time_series" = do.call(time_series_analysis, c(list(data = data), args)),
      stop("Invalid analysis type specified.")
    )
    
    list(
      results = results,
      data = data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Longitudinal Analysis calculation:", e$message))
  })
}

longitudinal_analysis_ht_html <- function(results) {
  tagList(
    h4(paste("Longitudinal Analysis:", results$results$method)),
    p("See summary table for model details.")
  )
}

longitudinal_analysis_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  model <- results$results$model
  out <- list(
    h4("Model Summary"),
    renderPrint(summary(model))
  )
  # Add fit indices and warnings if available
  if (!is.null(model) && !is.null(attr(model, "converged"))) {
    out <- c(out, list(paste("Converged:", attr(model, "converged"))))
  }
  if (!is.null(model) && !is.null(warnings <- warnings())) {
    out <- c(out, list(h4("Warnings"), renderPrint(warnings)))
  }
  tagList(out)
}

longitudinal_analysis_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  model <- results$results$model
  if (!is.null(model)) {
    par(mfrow = c(1, 2))
    plot(fitted(model), residuals(model), main = "Residuals vs Fitted", xlab = "Fitted", ylab = "Residuals")
    abline(h = 0, col = "red")
    plot(fitted(model), results$results$data[[1]], main = "Observed vs Fitted", xlab = "Fitted", ylab = "Observed")
    abline(0, 1, col = "blue")
    par(mfrow = c(1, 1))
  } else {
    plot.new(); title("No model to plot.")
  }
}

# --- Helper functions from original file ---

growth_curve_analysis <- function(data, response, time_var, subject_var, predictors = NULL, 
                                 covariates = NULL, group_var = NULL, growth_type = "linear",
                                 random_slopes = TRUE, random_intercepts = TRUE, 
                                 family = "gaussian", center_time = TRUE) {
  
  if (!require(lme4, quietly = TRUE)) {
    stop("lme4 package is required for growth curve modeling")
  }
  
  model_data <- data
  
  if (center_time) {
    model_data[[time_var]] <- scale(model_data[[time_var]], center = TRUE, scale = FALSE)
  }
  
  if (growth_type == "linear") {
    growth_terms <- time_var
  } else if (growth_type == "quadratic") {
    model_data$time_sq <- model_data[[time_var]]^2
    growth_terms <- paste(time_var, "+ time_sq")
  }
  
  fixed_effects <- growth_terms
  
  if (!is.null(predictors)) {
    fixed_effects <- paste(fixed_effects, "+", paste(predictors, collapse = " + "))
  }
  
  random_effects <- ""
  if (random_intercepts) {
    random_effects <- paste("(1|", subject_var, ")")
  }
  if (random_slopes) {
    if (random_effects != "") {
      random_effects <- paste(random_effects, "+")
    }
    random_effects <- paste(random_effects, "(", time_var, "|", subject_var, ")")
  }
  
  formula_str <- paste(response, "~", fixed_effects, "+", random_effects)
  
  if (family == "gaussian") {
    model <- lmer(as.formula(formula_str), data = model_data)
  } else {
    family_fun <- switch(family,
                        "binomial" = binomial(link = "logit"),
                        "poisson" = poisson(link = "log"))
    model <- glmer(as.formula(formula_str), data = model_data, family = family_fun)
  }
  
  list(
    model = model,
    method = "Growth Curve Modeling",
    formula = formula_str,
    data = model_data
  )
}

latent_growth_analysis <- function(data, response, time_var, subject_var, covariates = NULL,
                                  growth_shape = "linear", n_classes = 1) {
  
  if (!require(lavaan, quietly = TRUE)) {
    stop("lavaan package is required for latent growth modeling")
  }
  
  wide_data <- reshape(data, 
                       idvar = subject_var, 
                       timevar = time_var, 
                       direction = "wide")
  
  if (growth_shape == "linear") {
    growth_syntax <- paste0(response, " ~ 1 + ", time_var)
  } else if (growth_shape == "quadratic") {
    growth_syntax <- paste0(response, " ~ 1 + ", time_var, " + I(", time_var, "^2)")
  }
  
  if (n_classes == 1) {
    model <- lavaan::growth(growth_syntax, data = wide_data)
  } else {
    model <- lavaan::growth(growth_syntax, data = wide_data, 
                   mixture = TRUE, ngroups = n_classes)
  }
  
  list(
    model = model,
    method = "Latent Growth Modeling",
    data = wide_data
  )
}

autoregressive_analysis <- function(data, response, time_var, subject_var, ar_order = 1,
                                   ma_order = 0, predictors = NULL) {
  
  if (!require(nlme, quietly = TRUE)) {
    stop("nlme package is required for autoregressive modeling")
  }
  
  model_data <- data[order(data[[subject_var]], data[[time_var]]), ]
  
  fixed_effects <- response
  if (!is.null(predictors)) {
    fixed_effects <- paste(fixed_effects, "~", paste(predictors, collapse = " + "))
  } else {
    fixed_effects <- paste(fixed_effects, "~ 1")
  }
  
  cor_structure <- paste0("corAR", ar_order)
  if (ma_order > 0) {
    cor_structure <- paste0("corARMA(", ar_order, ",", ma_order, ")")
  }
  
  model <- nlme::gls(as.formula(fixed_effects), 
               data = model_data,
               correlation = eval(parse(text = paste0(cor_structure, "(form = ~", time_var, "|", subject_var, ")"))))
  
  list(
    model = model,
    method = "Autoregressive Modeling",
    data = model_data
  )
}

cross_lagged_analysis <- function(data, var1, var2, time_var, subject_var, lag_order = 1,
                                 covariates = NULL) {
  
  if (!require(lavaan, quietly = TRUE)) {
    stop("lavaan package is required for cross-lagged panel modeling")
  }
  
  # Ensure column names are syntactically valid
  time_points <- unique(data[[time_var]])
  data_wide <- reshape(data, idvar = subject_var, timevar = time_var, direction = "wide")
  
  # Construct model syntax with proper variable names
  var1_t1 <- paste0(var1, ".", time_points[1])
  var1_t2 <- paste0(var1, ".", time_points[2])
  var2_t1 <- paste0(var2, ".", time_points[1])
  var2_t2 <- paste0(var2, ".", time_points[2])

  model_syntax <- paste(
    paste(var1_t2, "~", var1_t1),
    paste(var2_t2, "~", var2_t1),
    paste(var1_t2, "~", var2_t1),
    paste(var2_t2, "~", var1_t1),
    paste(var1_t1, "~~", var2_t1),
    paste(var1_t2, "~~", var2_t2),
    sep = "\n"
  )

  model <- lavaan::sem(model_syntax, data = data_wide)
  
  list(
    model = model,
    method = "Cross-Lagged Panel Modeling",
    data = data_wide
  )
}

time_series_analysis <- function(data, response, time_var, subject_var, 
                                decomposition = TRUE, forecast_horizon = 5) {
  
  if (!require(forecast, quietly = TRUE)) {
    stop("forecast package is required for time series analysis")
  }
  
  model_data <- data[order(data[[subject_var]], data[[time_var]]), ]
  
  agg_data <- aggregate(model_data[[response]], 
                       by = list(time = model_data[[time_var]]), 
                       FUN = mean, na.rm = TRUE)
  
  ts_data <- ts(agg_data$x, frequency = 1)
  
  arima_model <- forecast::auto.arima(ts_data)
  
  list(
    model = arima_model,
    method = "Time Series Analysis",
    data = ts_data
  )
}