BayesianModelComparisonSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("bmcUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
    selectizeInput(ns("bmcModels"), "Models to Compare", choices = NULL, multiple = TRUE),
    br(),
    actionButton(ns("goBMC"), label = "Compare Models", class = "act-btn")
  )
}

BayesianModelComparisonMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns("bmcError")),
    tableOutput(ns("bmcResults")),
    plotOutput(ns("bmcPlot"))
  )
}

BayesianModelComparisonUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(BayesianModelComparisonSidebarUI(id)),
    mainPanel(BayesianModelComparisonMainUI(id))
  )
} 