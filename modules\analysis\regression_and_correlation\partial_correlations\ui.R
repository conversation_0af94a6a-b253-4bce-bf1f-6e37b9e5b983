# Partial and Semi-partial Correlations UI
# Correlations with control variables

PartialCorrelationsSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    radioButtons(
      inputId = ns("partialDataMethod"),
      label = "Data Input Method:",
      choices = c("Upload File" = "Upload File", "Manual Entry" = "Manual Entry"),
      selected = "Upload File"
    ),
    conditionalPanel(
      condition = "input.partialDataMethod == 'Upload File'",
      ns = ns,
      fileInput(
        inputId = ns("partialUserData"),
        label = "Upload Data File:",
        accept = c(".csv", ".txt", ".xlsx", ".xls"),
        buttonLabel = "Browse Files",
        placeholder = "No file selected"
      ),
      selectizeInput(
        inputId = ns("partialVariable1"),
        label = "Select First Variable:",
        choices = NULL,
        options = list(placeholder = "Select first variable...")
      ),
      selectizeInput(
        inputId = ns("partialVariable2"),
        label = "Select Second Variable:",
        choices = NULL,
        options = list(placeholder = "Select second variable...")
      ),
      selectizeInput(
        inputId = ns("partialControlVariables"),
        label = "Select Control Variables:",
        choices = NULL,
        multiple = TRUE,
        options = list(placeholder = "Select control variables...")
      )
    ),
    conditionalPanel(
      condition = "input.partialDataMethod == 'Manual Entry'",
      ns = ns,
      textAreaInput(
        inputId = ns("partialManualData"),
        label = "Enter Data (comma-separated):",
        placeholder = "Var1,Var2,Control1,Control2\n1,2,3,4\n2,3,4,5\n3,4,5,6",
        rows = 8
      )
    ),
    h5("Analysis Options"),
    checkboxInput(
      inputId = ns("partialCorrelation"),
      label = "Partial Correlation",
      value = TRUE
    ),
    checkboxInput(
      inputId = ns("partialSemiPartial"),
      label = "Semi-partial Correlation",
      value = TRUE
    ),
    checkboxInput(
      inputId = ns("partialSignificance"),
      label = "Significance Testing",
      value = TRUE
    ),
    checkboxInput(
      inputId = ns("partialEffectSize"),
      label = "Effect Size Interpretation",
      value = TRUE
    ),
    numericInput(
      inputId = ns("partialConfLevel"),
      label = "Confidence Level:",
      value = 0.95,
      min = 0.5,
      max = 0.99,
      step = 0.01
    ),
    actionButton(
      inputId = ns("goPartial"),
      label = "Calculate Partial Correlations",
      class = "btn-primary",
      style = "width: 100%;"
    ),
    br(),
    br(),
    helpText(
      "Partial correlation controls for all variables simultaneously.",
      "Semi-partial correlation controls for variables from one variable only.",
      "Useful for understanding unique relationships between variables.",
      "Effect sizes help interpret practical significance."
    )
  )
}

PartialCorrelationsMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns("partialResults"))
  )
}

PartialCorrelationsUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(PartialCorrelationsSidebarUI(id)),
    mainPanel(PartialCorrelationsMainUI(id))
  )
} 