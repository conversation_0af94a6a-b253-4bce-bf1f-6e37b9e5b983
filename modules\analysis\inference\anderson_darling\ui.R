andersonDarlingSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(
      inputId = ns("adUserData"),
      label   = strong("Upload your Data (.csv or .xls or .xlsx or .txt)"),
      accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
    ),
    div(
      id = ns("adUploadInputs"),
      selectizeInput(
        inputId = ns("adVariable"),
        label = strong("Select Variable"),
        choices = c(""),
        selected = NULL,
        options = list(placeholder = 'Select a column', onInitialize = I('function() { this.setValue(\"\"); }'))
      ),
      radioButtons(
        inputId = ns("adDistribution"),
        label = strong("Test Distribution"),
        choices = c("normal", "exponential", "uniform", "logistic", "weibull"),
        selected = "normal"
      ),
      radioButtons(
        inputId = ns("adSigLvl"),
        label = strong("Significance Level (alpha)"),
        choices = c("10%", "5%", "1%"),
        selected = "5%",
        inline = TRUE
      )
    ),
    br(),
    actionButton(ns("goInference"), label = "Calculate", class = "act-btn")
  )
}

andersonDarlingMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('andersonDarlingResults'))
  )
}

andersonDarlingUI <- function(id) {
  ns <- NS(id)
  tagList(
    andersonDarlingSidebarUI(id),
    andersonDarlingMainUI(id)
  )
} 