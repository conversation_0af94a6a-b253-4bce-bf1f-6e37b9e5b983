longitudinalAnalysisUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(
      selectInput(ns("long_response"), "Response Variable:", choices = NULL),
      selectInput(ns("long_time"), "Time Variable:", choices = NULL),
      selectInput(ns("long_subject"), "Subject/ID Variable:", choices = NULL),
      selectInput(ns("long_predictors"), "Time-Varying Predictors:", choices = NULL, multiple = TRUE),
      selectInput(ns("long_covariates"), "Time-Invariant Covariates:", choices = NULL, multiple = TRUE),
      selectInput(ns("long_group"), "Group Variable (Optional):", choices = c("None" = "none"), selected = "none"),
      selectInput(ns("long_analysis_type"), "Analysis Method:", 
                 choices = c("Growth Curve Modeling" = "growth_curve",
                            "Latent Growth Models" = "latent_growth",
                            "Autoregressive Models" = "autoregressive",
                            "Cross-Lagged Panel" = "cross_lagged",
                            "Time Series Analysis" = "time_series",
                            "Mixed Effects Growth" = "mixed_growth")),
      conditionalPanel(
        condition = paste0("input['", ns("long_analysis_type"), "'] == 'growth_curve'"),
        selectInput(ns("long_growth_type"), "Growth Function:", 
                   choices = c("Linear" = "linear", "Quadratic" = "quadratic", "Cubic" = "cubic", "Exponential" = "exponential")),
        checkboxInput(ns("long_random_slopes"), "Random Slopes", value = TRUE),
        checkboxInput(ns("long_random_intercepts"), "Random Intercepts", value = TRUE)
      ),
      conditionalPanel(
        condition = paste0("input['", ns("long_analysis_type"), "'] == 'latent_growth'"),
        selectInput(ns("long_latent_shape"), "Latent Growth Shape:", 
                   choices = c("Linear" = "linear", "Quadratic" = "quadratic", "Free" = "free")),
        numericInput(ns("long_latent_classes"), "Number of Classes:", value = 1, min = 1, max = 5, step = 1)
      ),
      conditionalPanel(
        condition = paste0("input['", ns("long_analysis_type"), "'] == 'autoregressive'"),
        numericInput(ns("long_ar_order"), "AR Order:", value = 1, min = 1, max = 5, step = 1),
        checkboxInput(ns("long_ma_terms"), "Include MA Terms", value = FALSE),
        conditionalPanel(
          condition = paste0("input['", ns("long_ma_terms"), "'] == true"),
          numericInput(ns("long_ma_order"), "MA Order:", value = 1, min = 1, max = 3, step = 1)
        )
      ),
      conditionalPanel(
        condition = paste0("input['", ns("long_analysis_type"), "'] == 'cross_lagged'"),
        selectInput(ns("long_var1"), "Variable 1:", choices = NULL),
        selectInput(ns("long_var2"), "Variable 2:", choices = NULL),
        numericInput(ns("long_lag_order"), "Lag Order:", value = 1, min = 1, max = 5, step = 1)
      ),
      selectInput(ns("long_family"), "Distribution Family:", 
                 choices = c("Gaussian" = "gaussian", "Binomial" = "binomial", "Poisson" = "poisson")),
      selectInput(ns("long_link"), "Link Function:", 
                 choices = c("Identity" = "identity", "Logit" = "logit", "Log" = "log")),
      checkboxInput(ns("long_center_time"), "Center Time Variable", value = TRUE),
      checkboxInput(ns("long_scale_predictors"), "Scale Predictors", value = FALSE),
      checkboxInput(ns("long_handle_missing"), "Handle Missing Data", value = TRUE),
      selectInput(ns("long_missing_method"), "Missing Data Method:", 
                 choices = c("Listwise Deletion" = "listwise", "Multiple Imputation" = "imputation", "FIML" = "fiml")),
      numericInput(ns("long_imputations"), "Number of Imputations:", value = 5, min = 3, max = 20, step = 1),
      numericInput(ns("long_alpha"), "Significance Level:", value = 0.05, min = 0.001, max = 0.999, step = 0.001),
      checkboxInput(ns("long_bootstrap"), "Bootstrap Confidence Intervals", value = FALSE),
      numericInput(ns("long_bootstrap_samples"), "Bootstrap Samples:", value = 1000, min = 100, max = 5000, step = 100),
      actionButton(ns("run_longitudinal"), "Run Longitudinal Analysis", class = "btn-primary btn-lg"),
      actionButton(ns("reset_longitudinal"), "Reset", class = "btn-secondary"),
      downloadButton(ns("download_longitudinal_results"), "Download Results")
    ),
    mainPanel(
      tabsetPanel(
        tabPanel("Model Summary",
          verbatimTextOutput(ns("longitudinal_summary")),
          plotOutput(ns("longitudinal_trajectory_plot"), height = "400px")
        ),
        tabPanel("Growth Curves",
          plotOutput(ns("longitudinal_growth_plot"), height = "500px"),
          plotOutput(ns("longitudinal_individual_plot"), height = "400px")
        ),
        tabPanel("Model Diagnostics",
          plotOutput(ns("longitudinal_diagnostic_plots"), height = "600px"),
          verbatimTextOutput(ns("longitudinal_diagnostics"))
        ),
        tabPanel("Parameter Estimates",
          dataTableOutput(ns("longitudinal_parameters")),
          plotOutput(ns("longitudinal_effects_plot"), height = "400px")
        ),
        tabPanel("Model Comparison",
          plotOutput(ns("longitudinal_comparison_plot"), height = "500px"),
          dataTableOutput(ns("longitudinal_comparison_table"))
        ),
        tabPanel("Predictions",
          plotOutput(ns("longitudinal_prediction_plot"), height = "400px"),
          dataTableOutput(ns("longitudinal_prediction_table"))
        )
      )
    )
  )
} 