# Ensemble Methods Calculation Functions

#' Fit Random Forest model with comprehensive analysis
#' @param data Input dataset
#' @param response Response variable name
#' @param predictors Predictor variables
#' @param ntree Number of trees
#' @param mtry Number of variables per split
#' @param nodesize Minimum node size
#' @param importance Whether to calculate importance
#' @param task Task type (regression/classification)
#' @return Random Forest model results
random_forest_analysis <- function(data, response, predictors, ntree = 500, mtry = NULL, 
                                  nodesize = 5, importance = TRUE, task = "regression") {
  
  # Load required packages
  if (!require(randomForest, quietly = TRUE)) {
    stop("randomForest package is required")
  }
  
  # Prepare data
  model_data <- data[c(response, predictors)]
  
  # Set default mtry if not specified
  if (is.null(mtry)) {
    mtry <- if(task == "regression") max(floor(length(predictors)/3), 1) else floor(sqrt(length(predictors)))
  }
  
  # Fit model
  formula_str <- paste(response, "~", paste(predictors, collapse = " + "))
  
  if (task == "regression") {
    model <- randomForest(as.formula(formula_str), 
                         data = model_data,
                         ntree = ntree,
                         mtry = mtry,
                         nodesize = nodesize,
                         importance = importance)
  } else {
    model <- randomForest(as.formula(formula_str), 
                         data = model_data,
                         ntree = ntree,
                         mtry = mtry,
                         nodesize = nodesize,
                         importance = importance)
  }
  
  # Extract feature importance
  importance_df <- NULL
  if (importance) {
    imp <- importance(model)
    importance_df <- data.frame(
      Variable = rownames(imp),
      Importance = imp[, if(task == "regression") "%IncMSE" else "MeanDecreaseAccuracy"]
    )
    importance_df <- importance_df[order(-importance_df$Importance), ]
  }
  
  # Model diagnostics
  diagnostics <- list(
    oob_error = model$mse[length(model$mse)],
    n_trees = ntree,
    mtry = mtry,
    nodesize = nodesize
  )
  
  results <- list(
    model = model,
    method = "Random Forest",
    task = task,
    formula = formula_str,
    importance = importance_df,
    diagnostics = diagnostics
  )
  
  return(results)
}

#' Fit Gradient Boosting model
#' @param data Input dataset
#' @param response Response variable name
#' @param predictors Predictor variables
#' @param n_trees Number of trees
#' @param depth Maximum tree depth
#' @param shrinkage Learning rate
#' @param bag_fraction Bagging fraction
#' @param cv_folds Cross-validation folds
#' @param task Task type
#' @return Gradient Boosting model results
gradient_boosting_analysis <- function(data, response, predictors, n_trees = 100, depth = 3, 
                                      shrinkage = 0.1, bag_fraction = 0.8, cv_folds = 5, task = "regression") {
  
  # Load required packages
  if (!require(gbm, quietly = TRUE)) {
    stop("gbm package is required")
  }
  
  # Prepare data
  model_data <- data[c(response, predictors)]
  
  # Set distribution
  distribution <- if(task == "regression") "gaussian" else "bernoulli"
  
  # Fit model
  formula_str <- paste(response, "~", paste(predictors, collapse = " + "))
  
  model <- gbm(as.formula(formula_str),
               data = model_data,
               distribution = distribution,
               n.trees = n_trees,
               interaction.depth = depth,
               shrinkage = shrinkage,
               bag.fraction = bag_fraction,
               cv.folds = cv_folds,
               verbose = FALSE)
  
  # Optimal number of trees
  best_iter <- gbm.perf(model, plot.it = FALSE)
  
  # Feature importance
  importance <- summary(model, plotit = FALSE)
  importance_df <- data.frame(
    Variable = importance$var,
    Importance = importance$rel.inf
  )
  importance_df <- importance_df[order(-importance_df$Importance), ]
  
  # Model diagnostics
  diagnostics <- list(
    best_iter = best_iter,
    cv_error = model$cv.error[best_iter],
    n_trees = n_trees,
    depth = depth,
    shrinkage = shrinkage
  )
  
  results <- list(
    model = model,
    method = "Gradient Boosting",
    task = task,
    formula = formula_str,
    importance = importance_df,
    diagnostics = diagnostics,
    best_iter = best_iter
  )
  
  return(results)
}

#' Fit XGBoost model
#' @param data Input dataset
#' @param response Response variable name
#' @param predictors Predictor variables
#' @param n_rounds Number of rounds
#' @param max_depth Maximum tree depth
#' @param eta Learning rate
#' @param subsample Subsample ratio
#' @param colsample_bytree Column sample ratio
#' @param task Task type
#' @return XGBoost model results
xgboost_analysis <- function(data, response, predictors, n_rounds = 100, max_depth = 3, 
                            eta = 0.1, subsample = 0.8, colsample_bytree = 0.8, task = "regression") {
  
  # Load required packages
  if (!require(xgboost, quietly = TRUE)) {
    stop("xgboost package is required")
  }
  
  # Prepare data
  X <- as.matrix(data[predictors])
  y <- data[[response]]
  
  # Set objective
  objective <- if(task == "regression") "reg:squarederror" else "binary:logistic"
  
  # XGBoost parameters
  params <- list(
    objective = objective,
    max_depth = max_depth,
    eta = eta,
    subsample = subsample,
    colsample_bytree = colsample_bytree,
    eval_metric = if(task == "regression") "rmse" else "logloss"
  )
  
  # Create DMatrix
  dtrain <- xgb.DMatrix(data = X, label = y)
  
  # Train model
  model <- xgb.train(params = params,
                     data = dtrain,
                     nrounds = n_rounds,
                     verbose = 0)
  
  # Feature importance
  importance <- xgb.importance(feature_names = predictors, model = model)
  importance_df <- data.frame(
    Variable = importance$Feature,
    Importance = importance$Gain
  )
  importance_df <- importance_df[order(-importance_df$Importance), ]
  
  # Model diagnostics
  diagnostics <- list(
    n_rounds = n_rounds,
    max_depth = max_depth,
    eta = eta,
    subsample = subsample,
    colsample_bytree = colsample_bytree
  )
  
  results <- list(
    model = model,
    method = "XGBoost",
    task = task,
    predictors = predictors,
    importance = importance_df,
    diagnostics = diagnostics
  )
  
  return(results)
}

#' Create stacking ensemble
#' @param data Input dataset
#' @param response Response variable name
#' @param predictors Predictor variables
#' @param base_models List of base model names
#' @param meta_learner Meta-learner method
#' @param cv_folds Cross-validation folds
#' @param task Task type
#' @return Stacking ensemble results
stacking_analysis <- function(data, response, predictors, base_models = c("lm", "rf", "svm"), 
                             meta_learner = "lm", cv_folds = 5, task = "regression") {
  
  # Load required packages
  if (!require(caret, quietly = TRUE)) {
    stop("caret package is required for stacking")
  }
  
  # Prepare data
  model_data <- data[c(response, predictors)]
  
  # Define base models
  base_model_list <- list()
  
  for (model_name in base_models) {
    if (model_name == "lm") {
      base_model_list$lm <- train(as.formula(paste(response, "~", paste(predictors, collapse = " + "))), 
                                 data = model_data, method = "lm")
    } else if (model_name == "rf") {
      base_model_list$rf <- train(as.formula(paste(response, "~", paste(predictors, collapse = " + "))), 
                                 data = model_data, method = "rf", tuneLength = 3)
    } else if (model_name == "svm") {
      base_model_list$svm <- train(as.formula(paste(response, "~", paste(predictors, collapse = " + "))), 
                                  data = model_data, method = "svmRadial", tuneLength = 3)
    } else if (model_name == "nnet") {
      base_model_list$nnet <- train(as.formula(paste(response, "~", paste(predictors, collapse = " + "))), 
                                   data = model_data, method = "nnet", tuneLength = 3)
    }
  }
  
  # Generate cross-validation predictions
  cv_preds <- data.frame()
  for (name in names(base_model_list)) {
    cv_pred <- predict(base_model_list[[name]], model_data)
    cv_preds[[name]] <- cv_pred
  }
  
  # Add response variable
  cv_preds[[response]] <- model_data[[response]]
  
  # Train meta-learner
  meta_formula <- paste(response, "~", paste(names(base_model_list), collapse = " + "))
  meta_model <- train(as.formula(meta_formula), data = cv_preds, method = meta_learner)
  
  # Results
  results <- list(
    base_models = base_model_list,
    meta_model = meta_model,
    method = "Stacking",
    task = task,
    base_model_names = names(base_model_list),
    meta_learner = meta_learner
  )
  
  return(results)
}

#' Create bagging ensemble
#' @param data Input dataset
#' @param response Response variable name
#' @param predictors Predictor variables
#' @param n_bags Number of bags
#' @param task Task type
#' @return Bagging ensemble results
bagging_analysis <- function(data, response, predictors, n_bags = 25, task = "regression") {
  
  # Load required packages
  if (!require(ipred, quietly = TRUE)) {
    stop("ipred package is required for bagging")
  }
  
  # Prepare data
  model_data <- data[c(response, predictors)]
  
  # Fit bagging model
  formula_str <- paste(response, "~", paste(predictors, collapse = " + "))
  
  model <- bagging(as.formula(formula_str), 
                   data = model_data,
                   nbagg = n_bags)
  
  # Results
  results <- list(
    model = model,
    method = "Bagging",
    task = task,
    formula = formula_str,
    n_bags = n_bags
  )
  
  return(results)
}

#' Create voting ensemble
#' @param data Input dataset
#' @param response Response variable name
#' @param predictors Predictor variables
#' @param models List of model types
#' @param weights Model weights (optional)
#' @param task Task type
#' @return Voting ensemble results
voting_analysis <- function(data, response, predictors, models = c("lm", "rf", "svm"), 
                           weights = NULL, task = "regression") {
  
  # Prepare data
  model_data <- data[c(response, predictors)]
  
  # Create models
  model_list <- list()
  formula_str <- paste(response, "~", paste(predictors, collapse = " + "))
  
  for (model_type in models) {
    if (model_type == "lm") {
      model_list$lm <- lm(as.formula(formula_str), data = model_data)
    } else if (model_type == "rf") {
      if (require(randomForest, quietly = TRUE)) {
        model_list$rf <- randomForest(as.formula(formula_str), data = model_data, ntree = 100)
      }
    } else if (model_type == "svm") {
      if (require(e1071, quietly = TRUE)) {
        model_list$svm <- svm(as.formula(formula_str), data = model_data)
      }
    }
  }
  
  # Set equal weights if not provided
  if (is.null(weights)) {
    weights <- rep(1/length(model_list), length(model_list))
  }
  
  # Results
  results <- list(
    models = model_list,
    method = "Voting",
    task = task,
    formula = formula_str,
    weights = weights,
    model_names = names(model_list)
  )
  
  return(results)
}

#' Calculate ensemble performance metrics
#' @param actual Actual values
#' @param predicted Predicted values
#' @param task Task type
#' @return Performance metrics
calculate_ensemble_performance_metrics <- function(actual, predicted, task = "regression") {
  
  if (task == "regression") {
    # Regression metrics
    rmse <- sqrt(mean((actual - predicted)^2))
    mae <- mean(abs(actual - predicted))
    r2 <- 1 - sum((actual - predicted)^2) / sum((actual - mean(actual))^2)
    mape <- mean(abs((actual - predicted) / actual)) * 100
    
    metrics <- data.frame(
      Metric = c("RMSE", "MAE", "R_Squared", "MAPE (%)"),
      Value = c(rmse, mae, r2, mape)
    )
  } else {
    # Classification metrics
    accuracy <- mean(actual == predicted)
    
    # Confusion matrix
    cm <- table(actual, predicted)
    precision <- cm[2,2] / sum(cm[,2])
    recall <- cm[2,2] / sum(cm[2,])
    f1 <- 2 * (precision * recall) / (precision + recall)
    
    metrics <- data.frame(
      Metric = c("Accuracy", "Precision", "Recall", "F1-Score"),
      Value = c(accuracy, precision, recall, f1)
    )
  }
  
  return(metrics)
}

#' Generate ensemble diagnostic plots
#' @param results Ensemble model results
#' @param actual Actual values
#' @param predicted Predicted values
#' @param plot_types Types of plots to generate
#' @return List of diagnostic plots
generate_ensemble_diagnostic_plots <- function(results, actual, predicted, 
                                              plot_types = c("residuals", "importance", "performance")) {
  
  plots <- list()
  
  # Residuals plot
  if ("residuals" %in% plot_types && results$task == "regression") {
    residuals <- actual - predicted
    
    p1 <- ggplot(data.frame(residuals = residuals), aes(sample = residuals)) +
      stat_qq() +
      stat_qq_line() +
      labs(title = "Q-Q Plot of Residuals",
           x = "Theoretical Quantiles", y = "Sample Quantiles") +
      theme_minimal()
    
    plots$residuals <- p1
  }
  
  # Feature importance plot
  if ("importance" %in% plot_types && !is.null(results$importance)) {
    p2 <- ggplot(results$importance, aes(x = reorder(Variable, Importance), y = Importance)) +
      geom_bar(stat = "identity", fill = "steelblue") +
      coord_flip() +
      labs(title = "Feature Importance",
           x = "Variable", y = "Importance") +
      theme_minimal()
    
    plots$importance <- p2
  }
  
  # Performance plot
  if ("performance" %in% plot_types) {
    if (results$task == "regression") {
      p3 <- ggplot(data.frame(actual = actual, predicted = predicted), 
                   aes(x = actual, y = predicted)) +
        geom_point(alpha = 0.6) +
        geom_abline(intercept = 0, slope = 1, linetype = "dashed", color = "red") +
        labs(title = "Actual vs Predicted Values",
             x = "Actual", y = "Predicted") +
        theme_minimal()
    } else {
      # Classification performance
      cm <- table(actual, predicted)
      cm_df <- as.data.frame(cm)
      names(cm_df) <- c("Actual", "Predicted", "Count")
      
      p3 <- ggplot(cm_df, aes(x = Actual, y = Predicted, fill = Count)) +
        geom_tile() +
        scale_fill_gradient(low = "white", high = "steelblue") +
        labs(title = "Confusion Matrix",
             x = "Actual", y = "Predicted") +
        theme_minimal()
    }
    
    plots$performance <- p3
  }
  
  return(plots)
} 