factorAnalysisSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(
      inputId = ns("faUserData"),
      label   = strong("Upload your Data (.csv or .xls or .xlsx or .txt)"),
      accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
    ),
    div(
      id = ns("faUploadInputs"),
      selectizeInput(
        inputId = ns("faVariables"),
        label = strong("Select Variables for Analysis"),
        choices = c(""),
        multiple = TRUE,
        selected = NULL,
        options = list(hideSelected = FALSE, placeholder = 'Select variables', onInitialize = I('function() { this.setValue(""); }'))
      )
    ),
    numericInput(
      inputId = ns("faNFactors"),
      label = strong("Number of Factors"),
      value = 3,
      min = 1,
      max = 10,
      step = 1
    ),
    radioButtons(
      inputId = ns("faRotation"),
      label = strong("Rotation Method"),
      choices = c("varimax", "promax", "oblimin", "none"),
      selected = "varimax"
    ),
    radioButtons(
      inputId = ns("faMethod"),
      label = strong("Extraction Method"),
      choices = c("principal", "ml", "minres"),
      selected = "principal"
    ),
    br(),
    actionButton(ns("goFactorAnalysis"), label = "Calculate", class = "act-btn")
  )
}

factorAnalysisMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('factorAnalysisResults'))
  )
}

factorAnalysisUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(factorAnalysisSidebarUI(id)),
    mainPanel(factorAnalysisMainUI(id))
  )
} 