# SVM calculations for CougarStats

svmResults_func <- function(data, response, predictors, type = "C-classification", kernel = "radial") {
  if (!requireNamespace("e1071", quietly = TRUE)) {
    stop("Package 'e1071' is required for SVM analysis.")
  }
  if (!requireNamespace("ggplot2", quietly = TRUE)) {
    stop("Package 'ggplot2' is required for plotting.")
  }
  # Validate input
  if (is.null(response) || is.null(predictors)) {
    stop("Response and predictors must be specified.")
  }
  formula <- as.formula(paste(response, "~", paste(predictors, collapse = "+")))
  fit <- e1071::svm(formula, data = data, type = type, kernel = kernel)
  preds <- predict(fit, data)
  acc <- mean(preds == data[[response]])
  # Only plot if 2 predictors
  plot_obj <- NULL
  if (length(predictors) == 2) {
    plot_obj <- ggplot2::ggplot(data, ggplot2::aes_string(x = predictors[1], y = predictors[2], color = response)) +
      ggplot2::geom_point() +
      ggplot2::labs(title = "SVM Decision Surface (data only)")
  }
  list(
    model = fit,
    predictions = preds,
    accuracy = acc,
    plot = plot_obj,
    summary = summary(fit)
  )
} 