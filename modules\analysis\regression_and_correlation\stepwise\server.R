# Stepwise Regression Server
# Variable selection

source("modules/calculations/stepwise_regression.R")

StepwiseRegressionServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    stepwiseUploadData <- eventReactive(input$stepwiseUserData, {
      handle_file_upload(input$stepwiseUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(stepwiseUploadData(), {
      data <- stepwiseUploadData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'stepwiseResponseVariable', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'stepwisePredictorVariables', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    stepwiseValidationErrors <- reactive({
      errors <- c()
      
      if (input$stepwiseDataMethod == "Manual Entry") {
        # Manual entry validation
        if (is.null(input$stepwiseData) || input$stepwiseData == "") {
          errors <- c(errors, "Response values are required for manual entry.")
        } else {
          data_vals <- createNumLst(input$stepwiseData)
          if (length(data_vals) < 10) {
            errors <- c(errors, "At least 10 observations are required for stepwise regression.")
          }
        }
        
        if (is.null(input$stepwisePredictorData) || input$stepwisePredictorData == "") {
          errors <- c(errors, "Predictor values are required for manual entry.")
        }
      } else {
        # File upload validation
        data <- stepwiseUploadData()
        if (is.null(data) || !is.data.frame(data)) {
          errors <- c(errors, "No data uploaded or file could not be read.")
          return(errors)
        }
        if (is.null(input$stepwiseResponseVariable) || input$stepwiseResponseVariable == "") {
          errors <- c(errors, "Please select a response variable.")
        } else {
          var_data <- data[[input$stepwiseResponseVariable]]
          if (!is.numeric(var_data)) {
            errors <- c(errors, "Response variable must be numeric.")
          }
          if (length(na.omit(var_data)) < 10) {
            errors <- c(errors, "At least 10 non-missing observations are required.")
          }
        }
        if (is.null(input$stepwisePredictorVariables) || length(input$stepwisePredictorVariables) < 2) {
          errors <- c(errors, "Please select at least 2 predictor variables.")
        } else {
          for (var in input$stepwisePredictorVariables) {
            pred_data <- data[[var]]
            if (!is.numeric(pred_data)) {
              errors <- c(errors, paste("Predictor variable", var, "must be numeric."))
            }
          }
        }
      }
      
      errors
    })
    
    # Stepwise regression analysis reactive
    stepwiseResult <- reactive({
      req(input$stepwiseDataMethod)
      
      if (input$stepwiseDataMethod == "Manual Entry") {
        data_vals <- createNumLst(input$stepwiseData)
        # For manual entry, we'll need to parse predictor data differently
        # This is a simplified version - in practice, you'd need more complex parsing
        stop("Manual entry for stepwise regression requires more complex data structure")
      } else {
        data <- stepwiseUploadData()
        req(data, input$stepwiseResponseVariable, input$stepwisePredictorVariables)
        response_var <- input$stepwiseResponseVariable
        predictor_vars <- input$stepwisePredictorVariables
      }
      
      direction <- input$stepwiseDirection
      alpha_entry <- input$stepwiseAlphaEntry
      alpha_remove <- input$stepwiseAlphaRemove
      conf_level <- input$stepwiseConfLevel
      
      # Perform stepwise regression
      result <- perform_stepwise_regression(data, response_var, predictor_vars, 
                                          direction, alpha_entry, alpha_remove, conf_level)
      
      result
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goStepwise, {
      output$stepwiseResults <- renderUI({
        errors <- stepwiseValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Stepwise Regression", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("stepwiseTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("stepwiseAnalysis"),
                title = "Analysis",
                titlePanel("Stepwise Regression Results"),
                br(),
                uiOutput(ns('stepwiseSummary')),
                br(),
                h4("Final Model Summary"),
                tableOutput(ns('stepwiseModelSummary')),
                br(),
                h4("Variable Selection History"),
                tableOutput(ns('stepwiseHistory')),
                br(),
                h4("Final Model Coefficients"),
                tableOutput(ns('stepwiseCoefficients')),
                br(),
                h4("Model Comparison"),
                tableOutput(ns('stepwiseModelComparison')),
                br(),
                h4("Assumptions and Interpretation"),
                uiOutput(ns('stepwiseAssumptions'))
              ),
              tabPanel(
                id = ns("stepwiseDataSummary"),
                title = "Data Summary",
                h4("Descriptive Statistics"),
                tableOutput(ns('stepwiseDescriptive')),
                br(),
                h4("Correlation Matrix"),
                tableOutput(ns('stepwiseCorrelation')),
                br(),
                h4("Variable Importance"),
                plotOutput(ns('stepwiseImportance'), height = "400px")
              ),
              tabPanel(
                id = ns("stepwiseDiagnostics"),
                title = "Diagnostics",
                h4("Residual Analysis"),
                plotOutput(ns('stepwiseResiduals'), height = "600px"),
                br(),
                h4("Influence Analysis"),
                tableOutput(ns('stepwiseInfluence'))
              ),
              tabPanel(
                id = ns("stepwiseUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('stepwiseViewUpload'))
              )
            )
          )
        }
      })
    })
    
    # Analysis Tab Outputs
    output$stepwiseSummary <- renderUI({
      req(stepwiseResult())
      result <- stepwiseResult()
      
      tagList(
        h4("Stepwise Regression Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Statistic = c("Sample Size", "Initial Predictors", "Final Predictors", 
                         "Direction", "Entry Alpha", "Remove Alpha", "Significance Level",
                         "Final R²", "Final Adjusted R²", "Final F-statistic", "Final P-value"),
            Value = c(
              result$n,
              result$initial_predictors,
              result$final_predictors,
              result$direction,
              result$alpha_entry,
              result$alpha_remove,
              paste0((1 - result$conf_level) * 100, "%"),
              round(result$final_r_squared, 4),
              round(result$final_adj_r_squared, 4),
              round(result$final_f_statistic, 4),
              ifelse(result$final_p_value < 0.0001, "< 0.0001", round(result$final_p_value, 4))
            )
          )
        }),
        br(),
        p(strong("Final Model:"), result$final_formula)
      )
    })
    
    output$stepwiseModelSummary <- renderTable({
      req(stepwiseResult())
      result <- stepwiseResult()
      
      result$final_model_summary
    }, digits = 4)
    
    output$stepwiseHistory <- renderTable({
      req(stepwiseResult())
      result <- stepwiseResult()
      
      result$selection_history
    }, digits = 4)
    
    output$stepwiseCoefficients <- renderTable({
      req(stepwiseResult())
      result <- stepwiseResult()
      
      result$final_coefficients
    }, digits = 4)
    
    output$stepwiseModelComparison <- renderTable({
      req(stepwiseResult())
      result <- stepwiseResult()
      
      result$model_comparison
    }, digits = 4)
    
    output$stepwiseAssumptions <- renderUI({
      req(stepwiseResult())
      result <- stepwiseResult()
      
      tagList(
        h5("Key Assumptions:"),
        p("1. Independence: Observations are independent"),
        p("2. Normality: Residuals are normally distributed"),
        p("3. Homoscedasticity: Constant variance of residuals"),
        p("4. Linearity: Linear relationships between predictors and response"),
        p("5. Multicollinearity: Predictors are not highly correlated"),
        br(),
        h5("Assumption Tests:"),
        p("• Normality test p-value: ", round(result$normality_p, 4)),
        p("• Breusch-Pagan test p-value: ", round(result$heteroscedasticity_p, 4)),
        p("• VIF (max): ", round(result$max_vif, 4)),
        br(),
        h5("Interpretation:"),
        p("• Selected variables: Variables that significantly improve the model"),
        p("• R²: Proportion of variance explained by the final model"),
        p("• Adjusted R²: R² penalized for model complexity"),
        p("• AIC/BIC: Model selection criteria (lower is better)"),
        br(),
        h5("When to Use:"),
        p("• Large number of potential predictors"),
        p("• Need for parsimonious model"),
        p("• Exploratory analysis"),
        p("• Variable selection in high-dimensional data")
      )
    })
    
    # Data Summary Tab Outputs
    output$stepwiseDescriptive <- renderTable({
      req(stepwiseResult())
      result <- stepwiseResult()
      
      result$descriptive_stats
    }, digits = 4)
    
    output$stepwiseCorrelation <- renderTable({
      req(stepwiseResult())
      result <- stepwiseResult()
      
      result$correlation_matrix
    }, digits = 4)
    
    output$stepwiseImportance <- renderPlot({
      req(stepwiseResult())
      result <- stepwiseResult()
      
      # Create variable importance plot
      importance_data <- result$variable_importance
      barplot(importance_data$Importance, names.arg = importance_data$Variable,
              main = "Variable Importance (Standardized Coefficients)",
              xlab = "Variables", ylab = "Importance",
              col = "#4F81BD", border = "white", las = 2)
    })
    
    # Diagnostics Tab Outputs
    output$stepwiseResiduals <- renderPlot({
      req(stepwiseResult())
      result <- stepwiseResult()
      
      # Create diagnostic plots
      par(mfrow = c(2, 2))
      
      # Residuals vs Fitted
      plot(fitted(result$final_model), residuals(result$final_model),
           main = "Residuals vs Fitted",
           xlab = "Fitted Values", ylab = "Residuals",
           pch = 16, col = "#4F81BD")
      abline(h = 0, col = "red", lty = 2)
      
      # Normal Q-Q plot
      qqnorm(residuals(result$final_model), main = "Normal Q-Q Plot",
             pch = 16, col = "#4F81BD")
      qqline(residuals(result$final_model), col = "red")
      
      # Scale-Location plot
      plot(fitted(result$final_model), sqrt(abs(residuals(result$final_model))),
           main = "Scale-Location Plot",
           xlab = "Fitted Values", ylab = "√|Residuals|",
           pch = 16, col = "#4F81BD")
      
      # Residuals vs Leverage
      plot(hatvalues(result$final_model), residuals(result$final_model),
           main = "Residuals vs Leverage",
           xlab = "Leverage", ylab = "Residuals",
           pch = 16, col = "#4F81BD")
      abline(h = 0, col = "red", lty = 2)
    })
    
    output$stepwiseInfluence <- renderTable({
      req(stepwiseResult())
      result <- stepwiseResult()
      
      result$influence_analysis
    }, digits = 4)
    
    # Uploaded Data Tab Output
    output$stepwiseViewUpload <- DT::renderDT({
      req(stepwiseUploadData())
      DT::datatable(stepwiseUploadData(), 
                   options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), 
                   rownames = FALSE)
    })
  })
} 