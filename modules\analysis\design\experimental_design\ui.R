experimentalDesignUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(
      radioButtons(
        inputId = ns("edDesignType"),
        label = strong("Design Type"),
        choices = c("Completely Randomized Design (CRD)", 
                    "Randomized Block Design (RBD)", 
                    "Latin Square Design (LSD)",
                    "Factorial Design",
                    "Response Surface Design"),
        selected = "Completely Randomized Design (CRD)"
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Completely Randomized Design (CRD)'", ns("edDesignType")),
        numericInput(ns("edCRDTreatments"), "Number of Treatments", value = 4, min = 2, max = 20),
        numericInput(ns("edCRDReplicates"), "Replicates per Treatment", value = 5, min = 2, max = 50)
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Randomized Block Design (RBD)'", ns("edDesignType")),
        numericInput(ns("edRBDTreatments"), "Number of Treatments", value = 4, min = 2, max = 20),
        numericInput(ns("edRBDBlocks"), "Number of Blocks", value = 5, min = 2, max = 50)
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Latin Square Design (LSD)'", ns("edDesignType")),
        numericInput(ns("edLSDSize"), "Square Size (n)", value = 4, min = 3, max = 10)
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Factorial Design'", ns("edDesignType")),
        numericInput(ns("edFactorialFactors"), "Number of Factors", value = 2, min = 2, max = 5),
        numericInput(ns("edFactorialLevels"), "Levels per Factor", value = 2, min = 2, max = 5),
        checkboxInput(ns("edFactorialReplicates"), "Include Replicates", value = TRUE)
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Response Surface Design'", ns("edDesignType")),
        numericInput(ns("edRSDFactors"), "Number of Factors", value = 2, min = 2, max = 5),
        radioButtons(ns("edRSDType"), "Design Type", 
                    choices = c("Central Composite Design (CCD)", "Box-Behnken Design (BBD)"),
                    selected = "Central Composite Design (CCD)")
      ),
      radioButtons(
        inputId = ns("edSigLvl"),
        label = strong("Significance Level (alpha)"),
        choices  = c("10%", "5%", "1%"),
        selected = "5%",
        inline   = TRUE
      ),
      br(),
      actionButton(ns("goExperimentalDesign"), label = "Generate Design", class = "act-btn")
    ),
    mainPanel(
      tabsetPanel(
        tabPanel("Design",
          textOutput(ns('edError')),
          tableOutput(ns('edResults')),
          plotOutput(ns('experimentalDesignPlot'), width = "100%", height = "500px"),
          textOutput(ns('edInstructions'))
        ),
        tabPanel("Design Layout",
          DT::dataTableOutput(ns("edLayoutTable")),
          textOutput(ns("edLayoutOutput"))
        ),
        tabPanel("Analysis Plan",
          textOutput(ns("edAnalysisPlan"))
        )
      )
    )
  )
} 