# Two-Way ANOVA UI
TwoWayAnovaSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("twAnovaUserData"), "Upload your Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
    selectizeInput(ns("twAnovaResponse"), "Response Variable", choices = NULL),
    selectizeInput(ns("twAnovaFactorA"), "Factor A", choices = NULL),
    selectizeInput(ns("twAnovaFactorB"), "Factor B", choices = NULL),
    checkboxInput(ns("twAnovaInteraction"), "Include Interaction Term", value = TRUE),
    radioButtons(ns("twAnovaSigLvl"), "Significance Level (alpha)", choices = c("10%", "5%", "1%"), selected = "5%", inline = TRUE),
    br(),
    actionButton(ns("goTwoWayAnova"), label = "Calculate", class = "act-btn")
  )
}

TwoWayAnovaMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('twoWayAnovaResults'))
  )
}

TwoWayAnovaUI <- function(id) {
  ns <- NS(id)
  tagList(
    TwoWayAnovaSidebarUI(id),
    TwoWayAnovaMainUI(id)
  )
} 