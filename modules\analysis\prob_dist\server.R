source('modules/calculations/prob_dist.R')

probDistServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Dynamic UI for distribution-specific inputs
    output$distInputs <- renderUI({
      switch(input$probability,
        "Binomial" = tagList(
          numericInput(ns("binomialN"), "Number of trials (n)", value = 10, min = 1),
          numericInput(ns("binomialP"), "Probability of success (p)", value = 0.5, min = 0, max = 1, step = 0.01),
          numericInput(ns("binomialX"), "Number of successes (x)", value = 5, min = 0)
        ),
        "Poisson" = tagList(
          numericInput(ns("poissonLambda"), "Lambda (λ)", value = 1, min = 0, step = 0.01),
          numericInput(ns("poissonX"), "Number of events (x)", value = 1, min = 0)
        ),
        "Hypergeometric" = tagList(
          numericInput(ns("hypergeometricM"), "Number of success states in population (m)", value = 20, min = 1),
          numericInput(ns("hypergeometricN"), "Number of failure states in population (n)", value = 30, min = 1),
          numericInput(ns("hypergeometricK"), "Number of draws (k)", value = 10, min = 1),
          numericInput(ns("hypergeometricX"), "Number of observed successes (x)", value = 5, min = 0)
        ),
        "Negative Binomial" = tagList(
          numericInput(ns("negBinomSize"), "Number of failures until experiment is stopped (size)", value = 10, min = 1),
          numericInput(ns("negBinomProb"), "Probability of success in each trial (prob)", value = 0.5, min = 0, max = 1, step = 0.01),
          numericInput(ns("negBinomX"), "Number of successes (x)", value = 5, min = 0)
        ),
        "Normal" = tagList(
          numericInput(ns("normalMean"), "Mean (μ)", value = 0),
          numericInput(ns("normalSD"), "Standard deviation (σ)", value = 1, min = 0.0001, step = 0.01),
          numericInput(ns("normalX"), "Value of interest (x)", value = 0)
        ),
        NULL
      )
    })
    
    # Validation errors reactive
    probValidationErrors <- reactive({
      errors <- c()
      
      # Distribution-specific validation
      if (input$probability == "Binomial") {
        if (is.null(input$binomialN) || input$binomialN <= 0) {
          errors <- c(errors, "Number of trials (n) must be positive.")
        }
        if (is.null(input$binomialP) || input$binomialP < 0 || input$binomialP > 1) {
          errors <- c(errors, "Probability of success (p) must be between 0 and 1.")
        }
        if (is.null(input$binomialX) || input$binomialX < 0 || input$binomialX > input$binomialN) {
          errors <- c(errors, "Number of successes (x) must be between 0 and n.")
        }
      } else if (input$probability == "Poisson") {
        if (is.null(input$poissonLambda) || input$poissonLambda <= 0) {
          errors <- c(errors, "Lambda (λ) must be positive.")
        }
        if (is.null(input$poissonX) || input$poissonX < 0) {
          errors <- c(errors, "Number of events (x) must be non-negative.")
        }
      } else if (input$probability == "Hypergeometric") {
        if (is.null(input$hypergeometricM) || input$hypergeometricM <= 0) {
          errors <- c(errors, "Number of success states (m) must be positive.")
        }
        if (is.null(input$hypergeometricN) || input$hypergeometricN <= 0) {
          errors <- c(errors, "Number of failure states (n) must be positive.")
        }
        if (is.null(input$hypergeometricK) || input$hypergeometricK <= 0) {
          errors <- c(errors, "Number of draws (k) must be positive.")
        }
        if (is.null(input$hypergeometricX) || input$hypergeometricX < 0 || input$hypergeometricX > min(input$hypergeometricK, input$hypergeometricM)) {
          errors <- c(errors, "Number of observed successes (x) must be between 0 and min(k, m).")
        }
      } else if (input$probability == "Negative Binomial") {
        if (is.null(input$negBinomSize) || input$negBinomSize <= 0) {
          errors <- c(errors, "Size must be positive.")
        }
        if (is.null(input$negBinomProb) || input$negBinomProb < 0 || input$negBinomProb > 1) {
          errors <- c(errors, "Probability must be between 0 and 1.")
        }
        if (is.null(input$negBinomX) || input$negBinomX < 0) {
          errors <- c(errors, "Number of successes (x) must be non-negative.")
        }
      } else if (input$probability == "Normal") {
        if (is.null(input$normalSD) || input$normalSD <= 0) {
          errors <- c(errors, "Standard deviation must be positive.")
        }
      }
      
      errors
    })
    
    # Main calculation using the calculation file's function
    probResult <- eventReactive(input$goProb, {
      req(input$goProb > 0)
      
      # Prepare parameters based on distribution type
      params <- list(
        distribution = input$probability,
        x = switch(input$probability,
          "Binomial" = input$binomialX,
          "Poisson" = input$poissonX,
          "Hypergeometric" = input$hypergeometricX,
          "Negative Binomial" = input$negBinomX,
          "Normal" = input$normalX
        )
      )
      
      # Add distribution-specific parameters
      if (input$probability == "Binomial") {
        params$n <- input$binomialN
        params$p <- input$binomialP
      } else if (input$probability == "Poisson") {
        params$lambda <- input$poissonLambda
      } else if (input$probability == "Hypergeometric") {
        params$m <- input$hypergeometricM
        params$n <- input$hypergeometricN
        params$k <- input$hypergeometricK
      } else if (input$probability == "Negative Binomial") {
        params$size <- input$negBinomSize
        params$prob <- input$negBinomProb
      } else if (input$probability == "Normal") {
        params$mean <- input$normalMean
        params$sd <- input$normalSD
      }
      
      # Call the calculation function from the calculation file
      do.call(prob_dist_results_func, params)
    })
    
    # Show main results or error screen in main panel when Calculate is pressed
    observeEvent(input$goProb, {
      output$distOutputs <- renderUI({
        errors <- probValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Probability Distribution", errors = errors)
        } else {
          tryCatch({ 
            res <- probResult()
            if (is.null(res)) {
              errorScreenUI(title = "Probability Distribution Error", errors = "No results available")
            } else if (!is.null(res$error)) {
              errorScreenUI(title = "Probability Distribution Error", errors = res$error)
            } else {
              tagList(
                tabsetPanel(
                  id = ns("probTabset"),
                  selected = "Analysis",
                  tabPanel(
                    id = ns("probAnalysis"),
                    title = "Analysis",
                    titlePanel(prob_dist_ht_html(res)),
                    br(),
                    h4("Distribution Summary"),
                    verbatimTextOutput(ns('probModelSummary')),
                    br(),
                    h4("Visualizations"),
                    plotOutput(ns('probPlot'), height = "600px")
                  )
                )
              )
            }
          }, error = function(e) {
            errorScreenUI(title = "Probability Distribution Error", errors = e$message)
          })
        }
      })
    })
    
    # Analysis Tab Outputs
    output$probModelSummary <- renderPrint({
      res <- probResult()
      if (is.null(res) || !is.null(res$error)) return(NULL)
      prob_dist_summary_html(res)
    })
    
    # Plot output
    output$probPlot <- renderPlot({
      res <- probResult()
      if (is.null(res) || !is.null(res$error)) return(NULL)
      prob_dist_plot(res)
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # Probability mass/density function
      if (!is.null(res$pmf) || !is.null(res$pdf)) {
        if (input$probability %in% c("Binomial", "Poisson", "Hypergeometric", "Negative Binomial")) {
          # Discrete distribution
          x_vals <- res$x_values
          y_vals <- res$pmf
          plot(x_vals, y_vals, type = "h", main = "Probability Mass Function",
               xlab = "x", ylab = "P(X = x)", col = "blue", lwd = 2)
        } else {
          # Continuous distribution
          x_vals <- res$x_values
          y_vals <- res$pdf
          plot(x_vals, y_vals, type = "l", main = "Probability Density Function",
               xlab = "x", ylab = "f(x)", col = "red", lwd = 2)
        }
      }
      
      # Cumulative distribution function
      if (!is.null(res$cdf)) {
        x_vals <- res$x_values
        y_vals <- res$cdf
        plot(x_vals, y_vals, type = "l", main = "Cumulative Distribution Function",
             xlab = "x", ylab = "F(x)", col = "green", lwd = 2)
      }
      
      # Quantile function
      if (!is.null(res$quantiles)) {
        p_vals <- seq(0.01, 0.99, 0.01)
        q_vals <- res$quantiles
        plot(p_vals, q_vals, type = "l", main = "Quantile Function",
             xlab = "p", ylab = "Q(p)", col = "purple", lwd = 2)
      }
      
      # Random sample histogram
      if (!is.null(res$random_sample)) {
        hist(res$random_sample, main = "Random Sample Distribution",
             xlab = "Value", ylab = "Frequency", col = "lightblue", freq = FALSE)
      }
      
      par(mfrow = c(1, 1))
    })
    
    output$probDiagnostics <- renderUI({
      res <- probResult()
      if (is.null(res)) return(NULL)
      
      tagList(
        h4("Distribution Diagnostics"),
        renderTable({
          data.frame(
            Metric = c("Distribution Type", "Parameter Count", "Support", "Mean", "Variance"),
            Value = c(
              input$probability,
              ifelse(!is.null(res$n_parameters), res$n_parameters, "N/A"),
              ifelse(!is.null(res$support), res$support, "N/A"),
              ifelse(!is.null(res$mean), round(res$mean, 4), "N/A"),
              ifelse(!is.null(res$variance), round(res$variance, 4), "N/A")
            )
          )
        }),
        br(),
        h4("Distribution Properties"),
        p("This section provides additional information about the selected probability distribution."),
        br(),
        h4("Parameter Interpretation"),
        uiOutput(ns('probParameterInterpretation'))
      )
    })
    
    output$probParameterInterpretation <- renderUI({
      if (input$probability == "Binomial") {
        tagList(
          p("n: Number of trials"),
          p("p: Probability of success in each trial"),
          p("x: Number of successes")
        )
      } else if (input$probability == "Poisson") {
        tagList(
          p("λ (lambda): Average number of events per unit time/space"),
          p("x: Number of events")
        )
      } else if (input$probability == "Normal") {
        tagList(
          p("μ (mu): Mean of the distribution"),
          p("σ (sigma): Standard deviation of the distribution"),
          p("x: Value of interest")
        )
      } else {
        p("Parameter interpretation depends on the specific distribution.")
      }
    })
  })
} 