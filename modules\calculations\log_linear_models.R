# Log-linear Models calculation and output helpers

log_linear_models_uploadData_func <- function(llmUserData) {
  ext <- tools::file_ext(llmUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(llmUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(llmUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(llmUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(llmUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

log_linear_models_results_func <- function(data, variables, hierarchical = TRUE, model_selection = TRUE, selection_criterion = "aic") {
  tryCatch({
    
    # Ensure data has a 'Count' column if it's not already a frequency table
    if (!"Count" %in% names(data)) {
        data <- as.data.frame(table(data[, variables]))
        names(data)[ncol(data)] <- "Count"
    }
    
    results <- perform_log_linear_analysis(data, hierarchical, model_selection, selection_criterion)
    
    list(
      results = results,
      data = data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Log-linear Model calculation:", e$message))
  })
}

log_linear_models_ht_html <- function(results) {
  fit_stats <- results$results$fit_statistics
  lr_test <- fit_stats[fit_stats$Statistic == "Likelihood Ratio Chi-Square", "Value"]
  p_val <- fit_stats[fit_stats$Statistic == "LR p-value", "Value"]
  
  tagList(
    h4("Model Fit Hypothesis Test (Likelihood Ratio Test)"),
    p(paste0("Likelihood Ratio Chi-Square: ", round(lr_test, 3))),
    p(paste0("P-value: ", round(p_val, 4))),
    p(ifelse(p_val < 0.05, "The model provides a significantly better fit than the null model.", "The model does not provide a significantly better fit than the null model."))
  )
}

log_linear_models_summary_html <- function(results) {
  param_est <- results$results$parameter_estimates
  tagList(
    h4("Parameter Estimates"),
    renderDataTable({
      DT::datatable(param_est, options = list(pageLength = 10, searching = FALSE))
    })
  )
}

log_linear_models_plot <- function(results) {
  generate_log_linear_plots(results$results)
}

# --- Helper functions from original file ---

perform_log_linear_analysis <- function(data, hierarchical = TRUE, model_selection = TRUE,
                                       selection_criterion = "aic", residuals = TRUE, conf_level = 0.95) {
  
  if (ncol(data) < 2) {
    stop("At least 2 categorical variables are required")
  }
  
  n_variables <- ncol(data) -1
  n_observations <- sum(data$Count, na.rm = TRUE)
  n_cells <- nrow(data)
  n_missing <- sum(is.na(data$Count))
  
  data <- data[complete.cases(data), ]
  
  contingency_table <- create_contingency_table(data)
  
  models <- fit_log_linear_models(data, hierarchical)
  
  model_selection_result <- NULL
  if (model_selection && length(models) > 1) {
    model_selection_result <- perform_model_selection(models, selection_criterion)
  }
  
  best_model <- if (!is.null(model_selection_result)) {
    models[[model_selection_result$best_model]]
  } else {
    models[[1]]
  }
  
  residuals_result <- NULL
  if (residuals) {
    residuals_result <- calculate_log_linear_residuals(best_model, data)
  }
  
  fit_statistics <- calculate_model_fit_statistics(best_model, data)
  
  parameter_estimates <- extract_parameter_estimates(best_model)
  
  list(
    data = data,
    n_variables = n_variables,
    n_observations = n_observations,
    n_cells = n_cells,
    n_missing = n_missing,
    contingency_table = contingency_table,
    models = models,
    best_model = best_model,
    model_selection = model_selection_result,
    residuals = residuals_result,
    fit_statistics = fit_statistics,
    parameter_estimates = parameter_estimates,
    conf_level = conf_level
  )
}

create_contingency_table <- function(data) {
  var_names <- setdiff(names(data), "Count")
  table_formula <- as.formula(paste("Count ~", paste(var_names, collapse = " + ")))
  xtabs(table_formula, data = data)
}

fit_log_linear_models <- function(data, hierarchical) {
  models <- list()
  var_names <- setdiff(names(data), "Count")
  n_vars <- length(var_names)
  
  # Create formula strings safely
  formula_indep_str <- paste("Count ~", paste(var_names, collapse = " + "))
  models[["Independence"]] <- glm(as.formula(formula_indep_str), data = data, family = poisson)

  if (hierarchical) {
    if (n_vars >= 2) {
        # Saturated model for 2-way
        if(n_vars == 2) {
            formula_sat_str <- paste("Count ~", paste(var_names, collapse = " * "))
            models[["Saturated"]] <- glm(as.formula(formula_sat_str), data = data, family = poisson)
        }
        # Add higher-order interactions
        for (i in 2:n_vars) {
            interactions <- combn(var_names, i, simplify = FALSE)
            for (interaction in interactions) {
                model_name <- paste(interaction, collapse = ":")
                formula_interaction_str <- paste("Count ~", paste(var_names, collapse=" + "), "+", paste(interaction, collapse = ":"))
                models[[model_name]] <- glm(as.formula(formula_interaction_str), data = data, family = poisson)
            }
        }
    }
  } else {
      formula_sat_str <- paste("Count ~", paste(var_names, collapse = " * "))
      models[["Saturated"]] <- glm(as.formula(formula_sat_str), data = data, family = poisson)
  }
  
  return(models)
}

perform_model_selection <- function(models, criterion) {
  model_comparison <- data.frame(
    Model = names(models),
    AIC = sapply(models, AIC),
    BIC = sapply(models, BIC),
    Deviance = sapply(models, function(m) m$deviance),
    df = sapply(models, function(m) m$df.residual),
    stringsAsFactors = FALSE
  )
  
  model_comparison$LR_Chi_Square <- model_comparison$Deviance
  model_comparison$LR_p_value <- pchisq(model_comparison$LR_Chi_Square, model_comparison$df, lower.tail = FALSE)
  
  best_model_idx <- if (criterion == "aic") {
    which.min(model_comparison$AIC)
  } else if (criterion == "bic") {
    which.min(model_comparison$BIC)
  } else {
    which.max(model_comparison$LR_p_value)
  }
  
  list(
    comparison_table = model_comparison,
    best_model = names(models)[best_model_idx],
    criterion = criterion
  )
}

calculate_log_linear_residuals <- function(model, data) {
  observed <- data$Count
  fitted <- fitted(model)
  
  residuals_result <- data.frame(
    Cell = 1:length(observed),
    Observed = observed,
    Fitted = fitted,
    Raw_Residual = observed - fitted,
    Pearson_Residual = (observed - fitted) / sqrt(fitted),
    stringsAsFactors = FALSE
  )
  
  # Deviance residuals need careful handling for zero counts
  dev_res <- numeric(length(observed))
  non_zero <- observed > 0
  dev_res[non_zero] <- sign(observed[non_zero] - fitted[non_zero]) * 
                       sqrt(2 * (observed[non_zero] * log(observed[non_zero]/fitted[non_zero]) - (observed[non_zero] - fitted[non_zero])))
  residuals_result$Deviance_Residual <- dev_res
  
  residuals_result$Standardized_Pearson <- residuals_result$Pearson_Residual / sqrt(1 - hatvalues(model))
  
  var_names <- setdiff(names(data), "Count")
  for (var in var_names) {
    residuals_result[[var]] <- data[[var]]
  }
  
  return(residuals_result)
}

calculate_model_fit_statistics <- function(model, data) {
  deviance <- model$deviance
  df_residual <- model$df.residual
  df_model <- model$df.null - df_residual
  
  lr_chi_square <- deviance
  lr_p_value <- pchisq(lr_chi_square, df_residual, lower.tail = FALSE)
  
  observed <- data$Count
  fitted <- fitted(model)
  pearson_chi_square <- sum((observed - fitted)^2 / fitted)
  pearson_p_value <- pchisq(pearson_chi_square, df_residual, lower.tail = FALSE)
  
  aic <- AIC(model)
  bic <- BIC(model)
  
  null_deviance <- model$null.deviance
  pseudo_r_squared <- 1 - (deviance / null_deviance)
  
  data.frame(
    Statistic = c("Deviance", "df Residual", "df Model", "Likelihood Ratio Chi-Square", 
                  "LR p-value", "Pearson Chi-Square", "Pearson p-value", "AIC", "BIC", 
                  "Pseudo R-squared"),
    Value = c(deviance, df_residual, df_model, lr_chi_square, lr_p_value, 
              pearson_chi_square, pearson_p_value, aic, bic, pseudo_r_squared)
    stringsAsFactors = FALSE
  )
}

extract_parameter_estimates <- function(model) {
  coef_summary <- summary(model)$coefficients
  
  parameter_estimates <- data.frame(
    Parameter = rownames(coef_summary),
    Estimate = coef_summary[, "Estimate"],
    Std_Error = coef_summary[, "Std. Error"],
    z_value = coef_summary[, "z value"],
    p_value = coef_summary[, "Pr(>|z|)"],
    stringsAsFactors = FALSE
  )
  
  alpha <- 0.05
  z_critical <- qnorm(1 - alpha/2)
  
  parameter_estimates$CI_Lower <- parameter_estimates$Estimate - z_critical * parameter_estimates$Std_Error
  parameter_estimates$CI_Upper <- parameter_estimates$Estimate + z_critical * parameter_estimates$Std_Error
  
  parameter_estimates$Significant <- parameter_estimates$p_value < 0.05
  parameter_estimates$Significance_Level <- cut(parameter_estimates$p_value,
                                               breaks = c(0, 0.001, 0.01, 0.05, 1),
                                               labels = c("***", "**", "*", "ns"),
                                               include.lowest = TRUE)
  
  return(parameter_estimates)
}

generate_log_linear_plots <- function(results, plot_types = c("residuals", "fitted_vs_observed")) {
  plots <- list()
  
  if ("residuals" %in% plot_types && !is.null(results$residuals)) {
    p1 <- ggplot(results$residuals, aes(x = Fitted, y = Pearson_Residual)) +
      geom_point(alpha = 0.7) +
      geom_hline(yintercept = 0, color = "red", linetype = "dashed") +
      labs(title = "Pearson Residuals vs Fitted Values",
           x = "Fitted Values", y = "Pearson Residuals") +
      theme_minimal()
    
    plots$residuals <- p1
  }
  
  if ("fitted_vs_observed" %in% plot_types && !is.null(results$residuals)) {
    p2 <- ggplot(results$residuals, aes(x = Observed, y = Fitted)) +
      geom_point(alpha = 0.7) +
      geom_abline(intercept = 0, slope = 1, color = "red", linetype = "dashed") +
      labs(title = "Fitted vs Observed Values",
           x = "Observed Values", y = "Fitted Values") +
      theme_minimal()
    
    plots$fitted_vs_observed <- p2
  }
  
  return(plots)
}