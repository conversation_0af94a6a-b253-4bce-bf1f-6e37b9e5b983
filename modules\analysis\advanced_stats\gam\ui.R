GAMUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("gamUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("gamResponse"), "Response Variable", choices = NULL),
        selectizeInput(ns("gamPredictors"), "Predictor Variables", choices = NULL, multiple = TRUE),
        selectizeInput(ns("gamSmooths"), "Smooth Terms (select predictors to smooth)", choices = NULL, multiple = TRUE),
        br(),
        actionButton(ns("goGAM"), label = "Run GAM", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("gamError")),
        tableOutput(ns("gamSummary")),
        plotOutput(ns("gamPlot"))
      )
    )
  )
} 