# Levene's Test calculation and output helpers

levene_uploadData_func <- function(leveneUserData) {
  ext <- tools::file_ext(leveneUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(leveneUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(leveneUserData$datapath),
         xlsx = readxl::read_xlsx(leveneUserData$datapath),
         txt = readr::read_tsv(leveneUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

levene_results_func <- function(data, response_var, factor_var) {
  tryCatch({
    leveneData <- na.omit(data)
    
    # Perform Levene's test
    levene_test <- car::leveneTest(y = leveneData[[response_var]], group = as.factor(leveneData[[factor_var]]))
    
    # Descriptive Statistics
    desc_stats <- leveneData %>%
      dplyr::group_by_at(factor_var) %>%
      dplyr::summarise(
        N = n(),
        Mean = mean(.data[[response_var]]),
        SD = sd(.data[[response_var]]),
        .groups = 'drop'
      )
      
    list(
      levene_test = levene_test,
      desc_stats = desc_stats,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Levene's test calculation:", e$message))
  })
}

levene_ht_html <- function(results, sigLvl) {
  levene_test <- results$levene_test
  p_value <- levene_test$`Pr(>F)`[1]
  
  conclusion <- if (p_value < sigLvl) {
    "Reject H0. The variances of the groups are not equal."
  } else {
    "Do not reject H0. The variances of the groups are equal."
  }
  
  withMathJax(tagList(
    h4("Levene's Test for Homogeneity of Variance"),
    p("$H_0$: The variances of the groups are equal."),
    p("$H_A$: The variances of at least two groups are not equal."),
    p(sprintf("Test Statistic (F): %.4f", levene_test$`F value`[1])),
    p(sprintf("P-value: %.4f", p_value)),
    p(strong("Conclusion: "), conclusion)
  ))
}

levene_summary_html <- function(results) {
  tagList(
    h4("Descriptive Statistics by Group"),
    renderTable(results$desc_stats, digits = 4)
  )
}

levene_plot <- function(data, response_var, factor_var) {
  ggplot(data, aes(x = as.factor(.data[[factor_var]]), y = .data[[response_var]], fill = as.factor(.data[[factor_var]]))) +
    geom_boxplot(alpha = 0.7) +
    labs(title = "Boxplot of Response by Group",
         x = factor_var,
         y = response_var) +
    theme_minimal() +
    theme(legend.position = "none")
}