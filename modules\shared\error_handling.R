# Error Handling Utilities for CougarStats

# Global error handler for Shiny applications
setup_error_handling <- function() {
  # Set up global error handling
  options(error = function() {
    # Log error to console
    cat("Error occurred:", geterrmessage(), "\n")
    cat("Call stack:\n")
    print(sys.calls())
  })
}

# Safe function execution with error handling
safe_execute <- function(expr, error_msg = "An error occurred", default_value = NULL) {
  tryCatch({
    eval(expr)
  }, error = function(e) {
    warning(paste(error_msg, ":", e$message))
    return(default_value)
  }, warning = function(w) {
    warning(paste("Warning:", w$message))
    return(eval(expr))
  })
}

# Package availability checker
check_package_availability <- function(packages, install_if_missing = FALSE) {
  missing_packages <- packages[!sapply(packages, requireNamespace, quietly = TRUE)]
  
  if (length(missing_packages) > 0) {
    if (install_if_missing) {
      message("Installing missing packages: ", paste(missing_packages, collapse = ", "))
      install.packages(missing_packages, dependencies = TRUE)
      # Check again after installation
      missing_packages <- packages[!sapply(packages, requireNamespace, quietly = TRUE)]
    }
    
    if (length(missing_packages) > 0) {
      stop("Missing required packages: ", paste(missing_packages, collapse = ", "))
    }
  }
  
  return(TRUE)
}

# Data validation functions
validate_data <- function(data, required_columns = NULL, min_rows = 1, max_rows = NULL) {
  errors <- character()
  
  # Check if data exists
  if (is.null(data) || !is.data.frame(data)) {
    errors <- c(errors, "Data must be a data frame")
    return(errors)
  }
  
  # Check number of rows
  if (nrow(data) < min_rows) {
    errors <- c(errors, paste("Data must have at least", min_rows, "rows"))
  }
  
  if (!is.null(max_rows) && nrow(data) > max_rows) {
    errors <- c(errors, paste("Data must have at most", max_rows, "rows"))
  }
  
  # Check required columns
  if (!is.null(required_columns)) {
    missing_cols <- required_columns[!required_columns %in% names(data)]
    if (length(missing_cols) > 0) {
      errors <- c(errors, paste("Missing required columns:", paste(missing_cols, collapse = ", ")))
    }
  }
  
  return(errors)
}

validate_numeric_data <- function(data, min_values = 2, allow_na = FALSE) {
  errors <- c()
  
  if (is.null(data) || length(data) == 0) {
    errors <- c(errors, "Data is null or empty.")
    return(errors)
  }
  
  if (!is.numeric(data)) {
    errors <- c(errors, "Data must be numeric.")
    return(errors)
  }
  
  if (!allow_na && any(is.na(data))) {
    errors <- c(errors, "Data contains missing values.")
  }
  
  if (length(na.omit(data)) < min_values) {
    errors <- c(errors, paste("At least", min_values, "non-missing values are required."))
  }
  
  if (sd(na.omit(data)) == 0) {
    errors <- c(errors, "Data must have variance (not all values the same).")
  }
  
  errors
}

validate_categorical_data <- function(data, columns, max_categories = 50) {
  errors <- character()
  
  for (col in columns) {
    if (!col %in% names(data)) {
      errors <- c(errors, paste("Column", col, "not found"))
      next
    }
    
    unique_values <- unique(data[[col]])
    if (length(unique_values) > max_categories) {
      errors <- c(errors, paste("Column", col, "has too many categories (", length(unique_values), ">", max_categories, ")"))
    }
  }
  
  return(errors)
}

# Input validation functions
validate_input <- function(input, type = "numeric", min_val = NULL, max_val = NULL, 
                          required = TRUE, allow_na = FALSE) {
  errors <- character()
  
  # Check if input is required
  if (required && (is.null(input) || is.na(input) || input == "")) {
    errors <- c(errors, "Input is required")
    return(errors)
  }
  
  # Skip validation if input is NULL/NA and not required
  if (!required && (is.null(input) || is.na(input) || input == "")) {
    return(errors)
  }
  
  # Type validation
  if (type == "numeric") {
    if (!is.numeric(input)) {
      errors <- c(errors, "Input must be numeric")
    } else {
      if (!is.null(min_val) && input < min_val) {
        errors <- c(errors, paste("Input must be at least", min_val))
      }
      if (!is.null(max_val) && input > max_val) {
        errors <- c(errors, paste("Input must be at most", max_val))
      }
    }
  } else if (type == "integer") {
    if (!is.numeric(input) || input != as.integer(input)) {
      errors <- c(errors, "Input must be an integer")
    } else {
      if (!is.null(min_val) && input < min_val) {
        errors <- c(errors, paste("Input must be at least", min_val))
      }
      if (!is.null(max_val) && input > max_val) {
        errors <- c(errors, paste("Input must be at most", max_val))
      }
    }
  } else if (type == "positive") {
    if (!is.numeric(input) || input <= 0) {
      errors <- c(errors, "Input must be a positive number")
    }
  } else if (type == "probability") {
    if (!is.numeric(input) || input < 0 || input > 1) {
      errors <- c(errors, "Input must be a probability between 0 and 1")
    }
  } else if (type == "character") {
    if (!is.character(input)) {
      errors <- c(errors, "Input must be a character string")
    }
  }
  
  return(errors)
}

# Model validation functions
validate_model <- function(model, model_type = "general") {
  errors <- character()
  
  if (is.null(model)) {
    errors <- c(errors, "Model is null")
    return(errors)
  }
  
  # Check for common model issues
  if (inherits(model, "try-error")) {
    errors <- c(errors, "Model fitting failed")
    return(errors)
  }
  
  # Model-specific validations
  if (model_type == "linear") {
    if (inherits(model, "lm")) {
      # Check for perfect multicollinearity
      if (any(is.na(coef(model)))) {
        errors <- c(errors, "Perfect multicollinearity detected")
      }
      
      # Check for singularity
      if (any(is.infinite(coef(model)))) {
        errors <- c(errors, "Singularity detected in model")
      }
    }
  } else if (model_type == "glm") {
    if (inherits(model, "glm")) {
      # Check for convergence
      if (!model$converged) {
        errors <- c(errors, "Model did not converge")
      }
    }
  }
  
  return(errors)
}

# Result validation functions
validate_results <- function(results, required_components = NULL) {
  errors <- character()
  
  if (is.null(results)) {
    errors <- c(errors, "Results are null")
    return(errors)
  }
  
  # Check required components
  if (!is.null(required_components)) {
    missing_components <- required_components[!required_components %in% names(results)]
    if (length(missing_components) > 0) {
      errors <- c(errors, paste("Missing required components:", paste(missing_components, collapse = ", ")))
    }
  }
  
  return(errors)
}

# User-friendly error messages
create_user_friendly_error <- function(error_type, details = NULL) {
  error_messages <- list(
    "data_missing" = "Please upload or select data for analysis.",
    "data_invalid" = "The uploaded data is not in the correct format. Please check your file.",
    "columns_missing" = "Required columns are missing from the data.",
    "insufficient_data" = "There is not enough data for this analysis.",
    "package_missing" = "A required package is not installed. Please contact the administrator.",
    "model_failed" = "The model could not be fitted. Please check your data and parameters.",
    "computation_error" = "An error occurred during computation. Please try again.",
    "memory_error" = "The analysis requires too much memory. Please reduce the data size.",
    "timeout_error" = "The analysis took too long to complete. Please try with smaller data."
  )
  
  base_message <- error_messages[[error_type]] %||% "An unexpected error occurred."
  
  if (!is.null(details)) {
    return(paste(base_message, "Details:", details))
  } else {
    return(base_message)
  }
}

# Logging functions
log_error <- function(error, context = NULL) {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  error_msg <- paste("[ERROR]", timestamp, ifelse(!is.null(context), paste("Context:", context), ""), error)
  cat(error_msg, "\n")
  
  # Could also write to a log file
  # write(error_msg, file = "cougarstats_errors.log", append = TRUE)
}

log_warning <- function(warning, context = NULL) {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  warning_msg <- paste("[WARNING]", timestamp, ifelse(!is.null(context), paste("Context:", context), ""), warning)
  cat(warning_msg, "\n")
}

log_info <- function(info, context = NULL) {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  info_msg <- paste("[INFO]", timestamp, ifelse(!is.null(context), paste("Context:", context), ""), info)
  cat(info_msg, "\n")
}

# Performance monitoring
monitor_performance <- function(expr, operation_name = "Operation") {
  start_time <- Sys.time()
  
  result <- tryCatch({
    eval(expr)
  }, error = function(e) {
    log_error(e$message, paste("Performance monitoring for:", operation_name))
    stop(e)
  })
  
  end_time <- Sys.time()
  duration <- as.numeric(difftime(end_time, start_time, units = "secs"))
  
  if (duration > 30) {
    log_warning(paste("Slow operation detected:", operation_name, "took", round(duration, 2), "seconds"))
  }
  
  return(result)
}

# Memory usage monitoring
check_memory_usage <- function() {
  if (requireNamespace("pryr", quietly = TRUE)) {
    memory_usage <- pryr::mem_used()
    if (memory_usage > 1e9) {  # 1GB
      log_warning(paste("High memory usage detected:", round(memory_usage / 1e9, 2), "GB"))
    }
    return(memory_usage)
  }
  return(NULL)
}

# Initialize error handling
setup_error_handling()

# Enhanced validation functions
validate_dataframe <- function(data, required_cols = NULL, min_rows = 2) {
  errors <- c()
  
  if (is.null(data)) {
    errors <- c(errors, "Data is null.")
    return(errors)
  }
  
  if (!is.data.frame(data)) {
    errors <- c(errors, "Data must be a data frame.")
    return(errors)
  }
  
  if (nrow(data) < min_rows) {
    errors <- c(errors, paste("At least", min_rows, "rows are required."))
  }
  
  if (!is.null(required_cols)) {
    missing_cols <- required_cols[!required_cols %in% names(data)]
    if (length(missing_cols) > 0) {
      errors <- c(errors, paste("Missing required columns:", paste(missing_cols, collapse = ", ")))
    }
  }
  
  errors
}

validate_matrix <- function(mat, min_dim = 2, allow_negative = FALSE, require_integers = FALSE) {
  errors <- c()
  
  if (is.null(mat)) {
    errors <- c(errors, "Matrix is null.")
    return(errors)
  }
  
  if (!is.matrix(mat)) {
    errors <- c(errors, "Data must be a matrix.")
    return(errors)
  }
  
  if (nrow(mat) < min_dim || ncol(mat) < min_dim) {
    errors <- c(errors, paste("Matrix must be at least", min_dim, "x", min_dim))
  }
  
  if (any(is.na(mat))) {
    errors <- c(errors, "Matrix contains missing values.")
  }
  
  if (!allow_negative && any(mat < 0)) {
    errors <- c(errors, "Matrix contains negative values.")
  }
  
  if (require_integers && any(mat %% 1 != 0)) {
    errors <- c(errors, "Matrix contains non-integer values.")
  }
  
  if (all(mat == 0)) {
    errors <- c(errors, "All matrix values cannot be zero.")
  }
  
  errors
}

# Enhanced error screen UI with more detailed information
errorScreenUI <- function(title = "Validation Error(s)", errors = NULL, warnings = NULL, suggestions = NULL) {
  tagList(
    div(
      class = "error-container",
      style = "background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 15px; margin: 10px 0;",
      h4(icon("exclamation-triangle"), title, style = "color: #721c24; margin-top: 0;"),
      
      if (!is.null(errors) && length(errors) > 0) {
        tagList(
          h5("Errors:", style = "color: #721c24;"),
          tags$ul(
            lapply(errors, function(error) {
              tags$li(error, style = "color: #721c24; margin-bottom: 5px;")
            })
          )
        )
      },
      
      if (!is.null(warnings) && length(warnings) > 0) {
        tagList(
          h5("Warnings:", style = "color: #856404;"),
          tags$ul(
            lapply(warnings, function(warning) {
              tags$li(warning, style = "color: #856404; margin-bottom: 5px;")
            })
          )
        )
      },
      
      if (!is.null(suggestions) && length(suggestions) > 0) {
        tagList(
          h5("Suggestions:", style = "color: #155724;"),
          tags$ul(
            lapply(suggestions, function(suggestion) {
              tags$li(suggestion, style = "color: #155724; margin-bottom: 5px;")
            })
          )
        )
      }
    )
  )
}

# Data quality assessment
assess_data_quality <- function(data) {
  if (is.null(data)) return(NULL)
  
  quality_report <- list()
  
  if (is.data.frame(data)) {
    quality_report$n_rows <- nrow(data)
    quality_report$n_cols <- ncol(data)
    quality_report$missing_values <- colSums(is.na(data))
    quality_report$missing_percentage <- round(colSums(is.na(data)) / nrow(data) * 100, 2)
    quality_report$duplicate_rows <- sum(duplicated(data))
    quality_report$complete_cases <- sum(complete.cases(data))
    
    # Check for constant columns
    constant_cols <- sapply(data, function(x) length(unique(na.omit(x))) <= 1)
    quality_report$constant_columns <- names(data)[constant_cols]
    
  } else if (is.numeric(data)) {
    quality_report$length <- length(data)
    quality_report$missing_values <- sum(is.na(data))
    quality_report$missing_percentage <- round(sum(is.na(data)) / length(data) * 100, 2)
    quality_report$unique_values <- length(unique(na.omit(data)))
    quality_report$constant <- length(unique(na.omit(data))) <= 1
  }
  
  quality_report
}

# Enhanced logging functions
log_info <- function(message, context = "general") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  cat(sprintf("[INFO] %s [%s]: %s\n", timestamp, context, message))
}

log_warning <- function(message, context = "general") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  cat(sprintf("[WARNING] %s [%s]: %s\n", timestamp, context, message))
}

log_error <- function(message, context = "general") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  cat(sprintf("[ERROR] %s [%s]: %s\n", timestamp, context, message))
} 