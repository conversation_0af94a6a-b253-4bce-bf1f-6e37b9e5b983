durbinWatsonSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(
      inputId = ns("dwUserData"),
      label   = strong("Upload your Data (.csv or .xls or .xlsx or .txt)"),
      accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
    ),
    div(
      id = ns("dwUploadInputs"),
      selectizeInput(
        inputId = ns("dwResponse"),
        label = strong("Response Variable (Y)"),
        choices = c(""),
        selected = NULL,
        options = list(placeholder = 'Select response variable', onInitialize = I('function() { this.setValue(\"\"); }'))
      ),
      selectizeInput(
        inputId = ns("dwPredictors"),
        label = strong("Predictor Variables (X)"),
        choices = c(""),
        multiple = TRUE,
        selected = NULL,
        options = list(hideSelected = FALSE, placeholder = 'Select predictor variables', onInitialize = I('function() { this.setValue(\"\"); }'))
      ),
      radioButtons(
        inputId = ns("dwSigLvl"),
        label = strong("Significance Level (alpha)"),
        choices = c("10%", "5%", "1%"),
        selected = "5%",
        inline = TRUE
      )
    ),
    br(),
    actionButton(ns("goInference"), label = "Calculate", class = "act-btn")
  )
}

durbinWatsonMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('durbinWatsonResults'))
  )
}

durbinWatsonUI <- function(id) {
  ns <- NS(id)
  tagList(
    durbinWatsonSidebarUI(id),
    durbinWatsonMainUI(id)
  )
} 