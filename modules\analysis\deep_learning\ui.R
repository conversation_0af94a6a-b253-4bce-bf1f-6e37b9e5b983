DeepLearningUI <- function(id) {
  ns <- NS(id)
  
  sidebarLayout(
    sidebarPanel(
      fileInput(ns("data_file"), "Upload Data (CSV)", accept = ".csv"),
      selectInput(ns("task_type"), "Task Type", 
                 choices = c("Classification" = "classification", 
                           "Regression" = "regression",
                           "Autoencoder" = "autoencoder",
                           "Transfer Learning" = "transfer")),
      conditionalPanel(
        condition = paste0("input['", ns("task_type"), "'] != 'autoencoder'"),
        selectInput(ns("target_var"), "Target Variable", choices = NULL),
        selectInput(ns("feature_vars"), "Feature Variables", choices = NULL, multiple = TRUE)
      ),
      conditionalPanel(
        condition = paste0("input['", ns("task_type"), "'] == 'autoencoder'"),
        selectInput(ns("autoencoder_vars"), "Variables for Autoencoder", choices = NULL, multiple = TRUE)
      ),
      selectInput(ns("model_type"), "Model Architecture",
                 choices = c("Feedforward Neural Network" = "ffnn",
                           "Convolutional Neural Network" = "cnn", 
                           "Recurrent Neural Network" = "rnn",
                           "Long Short-Term Memory" = "lstm",
                           "Autoencoder" = "autoencoder",
                           "Transfer Learning (ResNet)" = "resnet")),
      numericInput(ns("n_layers"), "Number of Hidden Layers", value = 2, min = 1, max = 10),
      textInput(ns("layer_sizes"), "Layer Sizes (comma-separated)", value = "64,32"),
      selectInput(ns("activation"), "Activation Function",
                 choices = c("ReLU" = "relu", "Sigmoid" = "sigmoid", 
                           "Tanh" = "tanh", "Leaky ReLU" = "leaky_relu")),
      numericInput(ns("dropout_rate"), "Dropout Rate", value = 0.2, min = 0, max = 0.9, step = 0.1),
      numericInput(ns("learning_rate"), "Learning Rate", value = 0.001, min = 0.0001, max = 0.1, step = 0.0001),
      numericInput(ns("batch_size"), "Batch Size", value = 32, min = 1, max = 512),
      numericInput(ns("epochs"), "Number of Epochs", value = 100, min = 1, max = 1000),
      selectInput(ns("optimizer"), "Optimizer",
                 choices = c("Adam" = "adam", "SGD" = "sgd", "RMSprop" = "rmsprop", "Adagrad" = "adagrad")),
      numericInput(ns("validation_split"), "Validation Split", value = 0.2, min = 0.1, max = 0.5, step = 0.05),
      conditionalPanel(
        condition = paste0("input['", ns("model_type"), "'] == 'resnet'"),
        selectInput(ns("pretrained_model"), "Pre-trained Model",
                   choices = c("ResNet50" = "resnet50", "ResNet101" = "resnet101", 
                             "VGG16" = "vgg16", "InceptionV3" = "inceptionv3")),
        checkboxInput(ns("freeze_base"), "Freeze Base Layers", value = TRUE),
        numericInput(ns("fine_tune_layers"), "Fine-tune Last N Layers", value = 10, min = 1, max = 50)
      ),
      actionButton(ns("run_analysis"), "Run Deep Learning Analysis", class = "btn-primary"),
      actionButton(ns("stop_training"), "Stop Training", class = "btn-warning"),
      downloadButton(ns("download_model"), "Download Model"),
      downloadButton(ns("download_results"), "Download Results")
    ),
    mainPanel(
      h4("Results"),
      plotOutput(ns("training_plot")),
      verbatimTextOutput(ns("training_log")),
      plotOutput(ns("performance_plot")),
      tableOutput(ns("performance_table")),
      plotOutput(ns("feature_importance")),
      plotOutput(ns("confusion_matrix")),
      plotOutput(ns("roc_curve")),
      conditionalPanel(
        condition = paste0("input['", ns("task_type"), "'] == 'autoencoder'"),
        plotOutput(ns("reconstruction_plot")),
        plotOutput(ns("latent_space")),
        plotOutput(ns("anomaly_detection"))
      ),
      conditionalPanel(
        condition = paste0("input['", ns("model_type"), "'] == 'resnet'"),
        plotOutput(ns("layer_activations")),
        plotOutput(ns("feature_maps")),
        tableOutput(ns("transfer_performance"))
      )
    )
  )
} 