# Bayesian Tests calculation and output helpers

bayesian_uploadData_func <- function(bayesianUserData) {
  ext <- tools::file_ext(bayesianUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(bayesianUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(bayesianUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(bayesianUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(bayesianUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

bayesian_results_func <- function(data, analysis_type = "t_test", x_var = NULL, y_var = NULL, response_var = NULL, predictor_vars = NULL, rscale = 0.707) {
  tryCatch({
    if (!requireNamespace("BayesFactor", quietly = TRUE)) {
      stop("Package 'BayesFactor' is required for Bayesian analysis.")
    }
    
    if (analysis_type == "t_test") {
      x <- data[[x_var]]
      y <- if (!is.null(y_var)) data[[y_var]] else NULL
      test_result <- BayesFactor::ttestBF(x = x, y = y, rscale = rscale)
    } else if (analysis_type == "anova") {
      formula_str <- paste0("`", response_var, "` ~ ", paste0("`", predictor_vars, "`", collapse = " + "))
      test_result <- BayesFactor::anovaBF(as.formula(formula_str), data = data, rscale = rscale)
    } else if (analysis_type == "correlation") {
      x <- data[[x_var]]
      y <- data[[y_var]]
      test_result <- BayesFactor::correlationBF(x, y, rscale = rscale)
    } else {
      stop("Unknown analysis type.")
    }
    
    list(
      test = test_result,
      data = data,
      analysis_type = analysis_type,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Bayesian analysis:", e$message))
  })
}

bayesian_ht_html <- function(results) {
  bf <- summary(results$test)[1, "bf"]
  interpretation <- bayestestR::interpret_bayes_factor(bf)
  
  withMathJax(tagList(
    h4(paste("Bayesian", stringr::str_to_title(results$analysis_type))),
    p(sprintf("The Bayes Factor (BF10) is %.4f, which provides %s evidence for the alternative hypothesis.", bf, interpretation))
  ))
}

bayesian_summary_html <- function(results) {
  tagList(
    h4("Model Summary"),
    renderPrint(summary(results$test))
  )
}

bayesian_plot <- function(results) {
  plot(results$test)
}