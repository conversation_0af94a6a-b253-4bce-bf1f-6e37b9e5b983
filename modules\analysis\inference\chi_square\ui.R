chiSquareSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    radioButtons(
      inputId = ns("chisquareDimension"),
      label   = strong("Dimension"),
      choices = c("2 x 2", "2 x 3", "3 x 2", "3 x 3"),
      inline  = TRUE
    ),
    conditionalPanel(
      condition = sprintf("input['%s'] == '2 x 2'", ns("chisquareDimension")),
      matrixInput(
        inputId = ns("chiSqInput2x2"),
        inputClass = "cMatrix",
        value = matrix(c(173,599, 160,851), nrow = 2, ncol = 2, dimnames = list(c("R1", "R2"), c("C1", "C2"))),
        rows = list(editableNames = TRUE),
        cols = list(editableNames = TRUE),
        class = "numeric"
      )
    ),
    conditionalPanel(
      condition = sprintf("input['%s'] == '2 x 3'", ns("chisquareDimension")),
      matrixInput(
        inputId = ns("chiSqInput2x3"),
        inputClass = "cMatrix",
        value = matrix(c(160,40, 140,60, 40,60), nrow = 2, ncol = 3, dimnames = list(c("R1", "R2"), c("C1", "C2", "C3"))),
        rows = list(editableNames = TRUE),
        cols = list(editableNames = TRUE),
        class = "numeric"
      )
    ),
    conditionalPanel(
      condition = sprintf("input['%s'] == '3 x 2'", ns("chisquareDimension")),
      matrixInput(
        inputId = ns("chiSqInput3x2"),
        inputClass = "cMatrix",
        value = matrix(c(162,106,201, 353,259,332), nrow = 3, ncol = 2, dimnames = list(c("R1", "R2", "R3"), c("C1", "C2"))),
        rows = list(editableNames = TRUE),
        cols = list(editableNames = TRUE),
        class = "numeric"
      )
    ),
    conditionalPanel(
      condition = sprintf("input['%s'] == '3 x 3'", ns("chisquareDimension")),
      matrixInput(
        inputId = ns("chiSqInput3x3"),
        inputClass = "cMatrix",
        value = matrix(c(6,14,50, 38,31,50, 31,4,5), nrow = 3, ncol = 3, dimnames = list(c("R1", "R2", "R3"), c("C1", "C2", "C3"))),
        rows = list(editableNames = TRUE),
        cols = list(editableNames = TRUE),
        class = "numeric"
      )
    ),
    textInput(
      inputId = ns("chiSquareRowHeader"),
      label = "Name for Row Variable (optional):",
      value = ""
    ),
    textInput(
      inputId = ns("chiSquareColHeader"),
      label = "Name for Column Variable (optional):",
      value = ""
    ),
    radioButtons(
      inputId  = ns("chisquareMethod"),
      label    = strong("Hypothesis Test"),
      choiceNames  = c("Chi-Square test for independence", "Fisher's Exact test"),
      choiceValues = c("Chi-Square", "Fisher"),
      selected = "Chi-Square",
      inline   = TRUE
    ),
    conditionalPanel(
      condition = sprintf("input['%s'] == 'Chi-Square' && input['%s'] == '2 x 2'", ns("chisquareMethod"), ns("chisquareDimension")),
      checkboxInput(
        inputId = ns("chiSquareYates"),
        label   = "with Yates continuity correction",
        value   = FALSE
      )
    ),
    radioButtons(
      inputId  = ns("chisquareSigLvl"),
      label    = strong("Significance Level (\\( \\alpha \\))"),
      choices  = c("10%", "5%", "1%"),
      selected = "5%",
      inline   = TRUE
    ),
    actionButton(
      inputId = ns("goInference"),
      label   = "Calculate",
      class = "act-btn"
    )
  )
}

chiSquareMainUI <- function(id) {
  ns <- NS(id)
  uiOutput(ns('chiSquareResults'))
}