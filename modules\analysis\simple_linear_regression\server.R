source('modules/calculations/simple_linear_regression.R')

simpleLinearRegressionServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns

    # Data upload reactive using calculation function
    slrUploadData <- eventReactive(input$slrUserData, {
      if (input$dataRegCor == 'Upload Data' && !is.null(input$slrResponse) && !is.null(input$slrExplanatory)) {
        simple_linear_regression_uploadData_func(input$slrUserData, input$slrResponse, input$slrExplanatory)
      } else {
        handle_file_upload(input$slrUserData)
      }
    })

    # Data input reactives for manual entry
    slrRawX <- reactive({ parse_numeric(input$x) })
    slrRawY <- reactive({ parse_numeric(input$y) })
    # Main results reactive using calculation function
    slrResults <- reactive({
      if (input$dataRegCor == 'Upload Data') {
        data <- slrUploadData()
        if (is.null(data) || is.null(input$slrResponse) || is.null(input$slrExplanatory)) return(NULL)
        simple_linear_regression_results_func(data, input$slrResponse, input$slrExplanatory)
      } else {
        # Manual data entry
        x_vals <- slrRawX()
        y_vals <- slrRawY()
        if (length(x_vals) != length(y_vals) || length(x_vals) < 2) return(NULL)

        # Create temporary data frame for manual entry
        temp_data <- data.frame(x = x_vals, y = y_vals)
        simple_linear_regression_results_func(temp_data, "y", "x")
      }
    })

    # Legacy reactives for backward compatibility
    slrX <- reactive({
      if (input$dataRegCor == 'Upload Data' && !is.null(input$slrExplanatory) && !is.null(slrUploadData())) {
        as.numeric(na.omit(as.data.frame(slrUploadData())[, input$slrExplanatory]))
      } else {
        slrRawX()
      }
    })
    slrY <- reactive({
      if (input$dataRegCor == 'Upload Data' && !is.null(input$slrResponse) && !is.null(slrUploadData())) {
        as.numeric(na.omit(as.data.frame(slrUploadData())[, input$slrResponse]))
      } else {
        slrRawY()
      }
    })
    # Legacy regression model for backward compatibility
    slrModel <- reactive({
      results <- slrResults()
      if (is.null(results) || !is.null(results$error)) return(NULL)
      results$model
    })
    # Data table output
    output$slrDataTable <- renderUI({
      req(slrX(), slrY())
      DT::DTOutput(ns("slrDataTableInner"))
    })
    
    output$slrDataTableInner <- DT::renderDT({
      req(slrX(), slrY())
      x <- slrX(); y <- slrY()
      df <- data.frame(x = x, y = y, xy = x*y, x2 = x^2, y2 = y^2)
      names(df) <- c("x", "y", "xy", "x<sup>2</sup>", "y<sup>2</sup>")
      dfTotaled <- rbind(df, Totals = colSums(df))
      DT::datatable(round(dfTotaled, digits = 3), options = list(pageLength = -1, autoWidth = TRUE, scrollX = TRUE), rownames = TRUE, escape = FALSE)
    })
    # Regression equation output
    output$regLineEquation <- renderUI({
      model <- slrModel()
      s <- calc_slr_summary(model)
      intercept <- round(s$coefficients[1,1], 4)
      slope <- round(s$coefficients[2,1], 4)
      tagList(
        withMathJax(),
        sprintf("\\( \\hat{y} = %0.4f %+0.4f x \\)", intercept, slope),
        br(),
        br(),
        p(tags$b("Interpretation of regression coefficients:"),
          br(),
          br(),
          "Within the scope of observation, ", intercept, " is the estimated value of ",
          em("y"), " when ", em("x"), "= 0. A slope of ", slope,
          " represents the estimated change in ", em("y"),
          " for a unit increase of ", em("x.")),
        br(),
        br()
      )
    })
    # Scatterplot output
    output$renderSLRScatterplot <- renderUI({
      req(input$scatterPlot)
      x <- slrX(); y <- slrY()
      if(length(x) < 2) return(NULL)
      plot(x, y, main = "Scatterplot of x vs y", xlab = "x", ylab = "y", col = "#4F81BD", pch = 19)
      abline(slrModel(), col = "red", lwd = 2)
    })
    # Correlation outputs
    output$pearsonCorFormula <- renderUI({
      x <- slrX(); y <- slrY()
      corrs <- calc_correlations(x, y)
      r <- corrs$pearson$estimate
      tagList(
        withMathJax(),
        sprintf("\\( r = %0.4f \\)", r)
      )
    })
    output$kendallEstimate <- renderUI({
      x <- slrX(); y <- slrY()
      corrs <- calc_correlations(x, y)
      tau <- corrs$kendall$estimate
      sprintf("\\( \\tau = %0.4f \\)", tau)
    })
    output$spearmanEstimate <- renderUI({
      x <- slrX(); y <- slrY()
      corrs <- calc_correlations(x, y)
      rs <- corrs$spearman$estimate
      sprintf("\\( r_s = %0.4f \\)", rs)
    })
    # Uploaded data table
    output$slrViewUpload <- renderUI({
      req(slrUploadData())
      DT::DTOutput(ns("slrViewUploadInner"))
    })
    
    output$slrViewUploadInner <- DT::renderDT({
      req(slrUploadData())
      DT::datatable(slrUploadData(), options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), rownames = FALSE)
    })
    # Validation output
    output$slrValidation <- renderUI({
      x <- slrX(); y <- slrY()
      if(length(x) != length(y)) return("x and y must have the same number of observations.")
      if(length(x) < 2) return("Must have at least 2 observations for x and y.")
      if(any(is.na(x)) || any(is.na(y))) return("Data must be numeric.")
      NULL
    })
    output$slrExplanatorySelect <- renderUI({
      req(input$dataRegCor == 'Upload Data', slrUploadData())
      choices <- names(slrUploadData())
      selectInput(ns("slrExplanatory"), "Choose the Explanatory Variable (x)", choices = choices, selected = choices[1])
    })
    output$slrResponseSelect <- renderUI({
      req(input$dataRegCor == 'Upload Data', slrUploadData())
      choices <- names(slrUploadData())
      selectInput(ns("slrResponse"), "Choose the Response Variable (y)", choices = choices, selected = choices[2])
    })
    
    # Update variable choices when data is uploaded
    observeEvent(slrUploadData(), {
      data <- slrUploadData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'slrXVar', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'slrYVar', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    slrValidationErrors <- reactive({
      errors <- c()

      # Check if results have errors
      results <- slrResults()
      if (!is.null(results) && !is.null(results$error)) {
        errors <- c(errors, results$error)
      }

      if (input$dataRegCor == 'Enter Raw Data') {
        # Manual entry validation
        if (is.null(input$x) || input$x == "") {
          errors <- c(errors, "X values are required for manual entry.")
        }
        if (is.null(input$y) || input$y == "") {
          errors <- c(errors, "Y values are required for manual entry.")
        }
        if (!is.null(input$x) && !is.null(input$y) && input$x != "" && input$y != "") {
          x_vals <- slrRawX()
          y_vals <- slrRawY()
          if (length(x_vals) != length(y_vals)) {
            errors <- c(errors, "X and Y values must have the same number of observations.")
          }
          if (length(x_vals) < 2) {
            errors <- c(errors, "At least 2 observations are required.")
          }
          if (length(x_vals) > 0 && sd(x_vals) == 0) {
            errors <- c(errors, "X values must have variance (not all the same).")
          }
          if (length(y_vals) > 0 && sd(y_vals) == 0) {
            errors <- c(errors, "Y values must have variance (not all the same).")
          }
        }
      } else {
        # File upload validation
        if (is.null(input$slrResponse) || input$slrResponse == "") {
          errors <- c(errors, "Please select a response variable.")
        }
        if (is.null(input$slrExplanatory) || input$slrExplanatory == "") {
          errors <- c(errors, "Please select an explanatory variable.")
        }
        if (!is.null(input$slrXVar) && !is.null(input$slrYVar) && 
            input$slrXVar != "" && input$slrYVar != "") {
          if (input$slrXVar == input$slrYVar) {
            errors <- c(errors, "X and Y variables must be different.")
          }
          x_vals <- data[[input$slrXVar]]
          y_vals <- data[[input$slrYVar]]
          if (!is.numeric(x_vals) || !is.numeric(y_vals)) {
            errors <- c(errors, "Both X and Y variables must be numeric.")
          }
          if (length(x_vals) != length(y_vals)) {
            errors <- c(errors, "X and Y variables must have the same number of observations.")
          }
          if (length(x_vals) < 2) {
            errors <- c(errors, "At least 2 observations are required.")
          }
          if (sd(x_vals, na.rm = TRUE) == 0) {
            errors <- c(errors, "X variable must have variance (not all the same).")
          }
          if (sd(y_vals, na.rm = TRUE) == 0) {
            errors <- c(errors, "Y variable must have variance (not all the same).")
          }
        }
      }
      errors
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goSLR, {
      output$slrResults <- renderUI({
        errors <- slrValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Simple Linear Regression", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("slrTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("slrAnalysis"),
                title = "Analysis",
                titlePanel("Simple Linear Regression Analysis"),
                br(),
                uiOutput(ns('slrHT')),
                br(),
                plotOutput(ns('slrPlot'), height = "600px"),
                br(),
                verbatimTextOutput(ns('slrSummary'))
              ),
              tabPanel(
                id = ns("slrDataSummary"),
                title = "Data Summary",
                h4("Regression Data Table"),
                uiOutput(ns('slrDataTable')),
                br(),
                h4("Descriptive Statistics"),
                tableOutput(ns('slrDescriptiveStats')),
                br(),
                h4("Correlation Matrix"),
                tableOutput(ns('slrCorrelationMatrix'))
              ),
              tabPanel(
                id = ns("slrUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                uiOutput(ns('slrViewUpload'))
              )
            )
          )
        }
      })
    })
    
    # Main outputs using calculation functions
    output$slrHT <- renderUI({
      results <- slrResults()
      if (is.null(results)) return(NULL)
      simple_linear_regression_ht_html(results)
    })

    output$slrPlot <- renderPlot({
      results <- slrResults()
      if (is.null(results)) return(NULL)
      simple_linear_regression_plot(results)
    })

    output$slrSummary <- renderPrint({
      results <- slrResults()
      if (is.null(results)) return(NULL)
      simple_linear_regression_summary_html(results)
    })

    # Legacy outputs for backward compatibility
    output$slrDiagnosticPlots <- renderPlot({
      model <- slrModel()
      if (is.null(model)) return(NULL)

      par(mfrow = c(2, 2))
      plot(model, main = "Regression Diagnostics")
    })

    output$slrModelSummary <- renderPrint({
      model <- slrModel()
      if (is.null(model)) return(NULL)
      summary(model)
    })
    
    output$slrDescriptiveStats <- renderTable({
      x <- slrX(); y <- slrY()
      if (length(x) == 0 || length(y) == 0) return(NULL)
      
      stats <- data.frame(
        Variable = c("X", "Y"),
        Mean = c(mean(x, na.rm = TRUE), mean(y, na.rm = TRUE)),
        SD = c(sd(x, na.rm = TRUE), sd(y, na.rm = TRUE)),
        Min = c(min(x, na.rm = TRUE), min(y, na.rm = TRUE)),
        Max = c(max(x, na.rm = TRUE), max(y, na.rm = TRUE)),
        N = c(length(na.omit(x)), length(na.omit(y)))
      )
      stats
    }, digits = 4)
    
    output$slrCorrelationMatrix <- renderTable({
      x <- slrX(); y <- slrY()
      if (length(x) == 0 || length(y) == 0) return(NULL)
      
      cor_matrix <- cor(data.frame(X = x, Y = y), use = "complete.obs")
      as.data.frame(cor_matrix)
    }, digits = 4)
  })
} 