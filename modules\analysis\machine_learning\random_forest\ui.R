# Placeholder for Random Forest UI
randomForestSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("rfUserData"), "Upload your Data (.csv or .xls or .xlsx or .txt)", accept = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")),
    uiOutput(ns("rfColSelectors")),
    actionButton(ns("goRF"), label = "Calculate", class = "act-btn")
  )
}

randomForestMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('rfHT')),
    plotOutput(ns('rfPlot'), width = "50%", height = "400px"),
    uiOutput(ns('rfConclusionOutput'))
  )
}

randomForestUI <- function(id) {
  ns <- NS(id)
  tagList(
    randomForestSidebarUI(id),
    randomForestMainUI(id),
    tabsetPanel(
      id = ns("rfTabset"),
      selected = "Analysis",
      tabPanel(
        id    = ns("rfAnalysis"),
        title = "Analysis",
        titlePanel("Random Forest Analysis"),
        br(),
        uiOutput(ns('rfHT')),
        br(),
        plotOutput(ns('rfPlot'), width = "50%", height = "400px"),
        br(),
        uiOutput(ns('rfConclusionOutput'))
      ),
      tabPanel(
        id    = ns("rfData"),
        title = "Uploaded Data",
        uiOutput(ns("renderRFData"))
      )
    )
  )
} 