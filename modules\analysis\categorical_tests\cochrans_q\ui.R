# Cochran's Q Test UI
CochransQTestSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("cochransQUserData"), "Upload your Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
    selectizeInput(ns("cochransQSubject"), "Subject ID Column", choices = NULL),
    selectizeInput(ns("cochransQBinaryVars"), "Binary Variables (Select 3 or more)", choices = NULL, multiple = TRUE),
    radioButtons(ns("cochransQSigLvl"), "Significance Level (alpha)", choices = c("10%", "5%", "1%"), selected = "5%", inline = TRUE),
    br(),
    actionButton(ns("goCochransQ"), label = "Calculate", class = "act-btn")
  )
}

CochransQTestMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('cochransQResults'))
  )
}

CochransQTestUI <- function(id) {
  ns <- NS(id)
  tagList(
    CochransQTestSidebarUI(id),
    CochransQTestMainUI(id)
  )
} 