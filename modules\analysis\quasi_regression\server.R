QuasiRegressionServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    quasiData <- eventReactive(input$quasiUserData, {
      handle_file_upload(input$quasiUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(quasiData(), {
      data <- quasiData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'quasiResponse', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'quasiPredictors', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    quasiValidationErrors <- reactive({
      errors <- c()
      data <- quasiData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$quasiResponse) || input$quasiResponse == "") {
        errors <- c(errors, "Select a response variable.")
      }
      
      if (is.null(input$quasiPredictors) || length(input$quasiPredictors) == 0) {
        errors <- c(errors, "Select at least one predictor variable.")
      }
      
      # Check if response variable is appropriate for quasi models
      if (!is.null(input$quasiResponse) && input$quasiResponse != "") {
        response_data <- data[[input$quasiResponse]]
        if (!is.numeric(response_data)) {
          errors <- c(errors, "Response variable must be numeric.")
        } else {
          if (input$quasiModelType == "Quasi-binomial") {
            # Check for proportions between 0 and 1
            if (any(response_data < 0 | response_data > 1, na.rm = TRUE)) {
              errors <- c(errors, "Response variable must be between 0 and 1 for quasi-binomial model.")
            }
          } else if (input$quasiModelType == "Quasi-Poisson") {
            # Check for non-negative values
            if (any(response_data < 0, na.rm = TRUE)) {
              errors <- c(errors, "Response variable must be non-negative for quasi-Poisson model.")
            }
          }
        }
      }
      
      # Check if predictor variables are appropriate
      if (!is.null(input$quasiPredictors) && length(input$quasiPredictors) > 0) {
        for (var in input$quasiPredictors) {
          if (!is.numeric(data[[var]]) && !is.factor(data[[var]])) {
            errors <- c(errors, sprintf("Predictor '%s' must be numeric or factor.", var))
          }
        }
      }
      
      errors
    })
    
    # Quasi regression analysis reactive
    quasiResult <- eventReactive(input$goQuasi, {
      data <- quasiData()
      req(data, input$quasiResponse, input$quasiPredictors)
      
      # Remove rows with missing values
      complete_data <- data[complete.cases(data[, c(input$quasiResponse, input$quasiPredictors)]), ]
      
      if (nrow(complete_data) < 10) {
        stop("Insufficient complete cases for quasi regression.")
      }
      
      # Prepare data
      x <- as.matrix(complete_data[, input$quasiPredictors, drop = FALSE])
      y <- complete_data[[input$quasiResponse]]
      model_type <- ifelse(input$quasiModelType == "Quasi-binomial", "quasi-binomial", "quasi-poisson")
      
      # Fit quasi regression model
      quasi_regression(x, y, model_type)
    })
    
    # Error handling
    output$quasiError <- renderUI({
      errors <- quasiValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          quasiResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "Quasi Regression Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$quasiModelSummary <- renderUI({
      req(quasiResult())
      res <- quasiResult()
      
      tagList(
        h4("Quasi Regression Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Metric = c("Model Type", "Response Variable", "Number of Predictors", "Number of Observations", "Model Fitted"),
            Value = c(
              input$quasiModelType,
              input$quasiResponse,
              length(input$quasiPredictors),
              res$n_observations,
              "Yes"
            )
          )
        }),
        h4("Model Quality Metrics"),
        renderTable({
          data.frame(
            Metric = c("AIC", "BIC", "Log-likelihood", "Dispersion Parameter", "Residual Deviance"),
            Value = c(
              ifelse(!is.null(res$aic), round(res$aic, 4), "N/A"),
              ifelse(!is.null(res$bic), round(res$bic, 4), "N/A"),
              ifelse(!is.null(res$log_likelihood), round(res$log_likelihood, 4), "N/A"),
              ifelse(!is.null(res$dispersion), round(res$dispersion, 4), "N/A"),
              ifelse(!is.null(res$residual_deviance), round(res$residual_deviance, 4), "N/A")
            )
          )
        })
      )
    })
    
    output$quasiPlot <- renderPlot({
      req(quasiResult())
      res <- quasiResult()
      
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # Residuals vs fitted
      if (!is.null(res$residuals) && !is.null(res$fitted)) {
        plot(res$fitted, res$residuals, main = "Residuals vs Fitted",
             xlab = "Fitted Values", ylab = "Residuals", pch = 19, col = "blue")
        abline(h = 0, col = "red", lty = 2)
      }
      
      # Q-Q plot of residuals
      if (!is.null(res$residuals)) {
        qqnorm(res$residuals, main = "Q-Q Plot of Residuals")
        qqline(res$residuals, col = "red")
      }
      
      # Scale-location plot
      if (!is.null(res$residuals) && !is.null(res$fitted)) {
        sqrt_abs_resid <- sqrt(abs(res$residuals))
        plot(res$fitted, sqrt_abs_resid, main = "Scale-Location Plot",
             xlab = "Fitted Values", ylab = "sqrt(|Residuals|)", pch = 19, col = "green")
      }
      
      # Observed vs predicted
      if (!is.null(res$observed) && !is.null(res$predicted)) {
        plot(res$observed, res$predicted, main = "Observed vs Predicted",
             xlab = "Observed", ylab = "Predicted", pch = 19, col = "purple")
        abline(a = 0, b = 1, col = "red", lty = 2)
      }
      
      par(mfrow = c(1, 1))
    })
    
    output$quasiDiagnostics <- renderUI({
      req(quasiResult())
      res <- quasiResult()
      
      tagList(
        h4("Model Diagnostics"),
        renderTable({
          data.frame(
            Metric = c("Model Type", "Convergence", "Number of Parameters", "Computational Time", "Model Quality"),
            Value = c(
              input$quasiModelType,
              ifelse(!is.null(res$converged), ifelse(res$converged, "Yes", "No"), "N/A"),
              ifelse(!is.null(res$n_parameters), res$n_parameters, "N/A"),
              ifelse(!is.null(res$computation_time), paste(round(res$computation_time, 2), "seconds"), "N/A"),
              ifelse(!is.null(res$quality_score), round(res$quality_score, 4), "N/A")
            )
          )
        }),
        h4("Coefficient Estimates"),
        renderTable({
          if (!is.null(res$coefficients)) {
            coef_df <- as.data.frame(res$coefficients)
            coef_df$Parameter <- rownames(coef_df)
            coef_df <- coef_df[, c("Parameter", names(coef_df)[-ncol(coef_df)])]
            coef_df[, -1] <- round(coef_df[, -1], 4)
            coef_df
          } else {
            data.frame(
              Parameter = "N/A",
              Estimate = "N/A",
              Std_Error = "N/A",
              T_value = "N/A",
              P_value = "N/A",
              stringsAsFactors = FALSE
            )
          }
        })
      )
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$quasiDataSummary <- renderUI({
      req(quasiData(), input$quasiResponse, input$quasiPredictors)
      data <- quasiData()
      response <- input$quasiResponse
      predictors <- input$quasiPredictors
      
      tagList(
        h4("Dataset Summary"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "Response Variable", "Number of Predictors", "Complete Cases"),
            Value = c(
              nrow(data),
              ncol(data),
              response,
              length(predictors),
              sum(complete.cases(data[, c(response, predictors)]))
            )
          )
        }),
        h4("Response Variable Summary"),
        renderTable({
          if (!is.null(response) && response != "") {
            response_data <- data[[response]]
            data.frame(
              Metric = c("Mean", "Median", "SD", "Min", "Max", "Missing"),
              Value = c(
                round(mean(response_data, na.rm = TRUE), 4),
                round(median(response_data, na.rm = TRUE), 4),
                round(sd(response_data, na.rm = TRUE), 4),
                round(min(response_data, na.rm = TRUE), 4),
                round(max(response_data, na.rm = TRUE), 4),
                sum(is.na(response_data))
              )
            )
          } else {
            data.frame(Metric = "N/A", Value = "N/A")
          }
        })
      )
    })
    
    output$quasiAssumptions <- renderUI({
      req(quasiResult())
      res <- quasiResult()
      
      tagList(
        h4("Quasi Regression Assumptions Check"),
        renderTable({
          data.frame(
            Assumption = c("Appropriate Response", "Adequate Sample Size", "Valid Predictors", "Overdispersion"),
            Status = c(
              "Pass",
              ifelse(res$n_observations >= 10, "Pass", "Fail"),
              "Pass",
              ifelse(!is.null(res$dispersion) && res$dispersion > 1, "Present", "Not Present")
            ),
            Description = c(
              "Response variable is appropriate for chosen quasi model",
              "Sufficient observations for stable parameter estimation",
              "Predictor variables are appropriately coded",
              "Overdispersion detected (dispersion parameter > 1)"
            )
          )
        }),
        h4("Model Selection Guidelines"),
        renderTable({
          data.frame(
            Model = c("Quasi-binomial", "Quasi-Poisson"),
            Use_When = c(
              "Proportion data with overdispersion",
              "Count data with overdispersion"
            ),
            Assumptions = c(
              "Response between 0 and 1, overdispersion present",
              "Non-negative integer response, overdispersion present"
            )
          )
        })
      )
    })
    
    output$quasiDiagnosticPlots <- renderPlot({
      req(quasiResult())
      res <- quasiResult()
      
      par(mfrow = c(2, 2))
      
      # Residuals vs fitted
      if (!is.null(res$residuals) && !is.null(res$fitted)) {
        plot(res$fitted, res$residuals, main = "Residuals vs Fitted",
             xlab = "Fitted Values", ylab = "Residuals", pch = 19, col = "blue")
        abline(h = 0, col = "red", lty = 2)
      }
      
      # Q-Q plot of residuals
      if (!is.null(res$residuals)) {
        qqnorm(res$residuals, main = "Q-Q Plot of Residuals")
        qqline(res$residuals, col = "red")
      }
      
      # Histogram of residuals
      if (!is.null(res$residuals)) {
        hist(res$residuals, main = "Histogram of Residuals",
             xlab = "Residuals", freq = FALSE, col = "lightblue")
      }
      
      # Cook's distance
      if (!is.null(res$cooks_distance)) {
        plot(res$cooks_distance, main = "Cook's Distance",
             xlab = "Observation", ylab = "Cook's Distance", pch = 19, col = "orange")
        abline(h = 4/length(res$cooks_distance), col = "red", lty = 2)
      }
      
      par(mfrow = c(1, 1))
    })
    
    # Uploaded Data Tab Outputs
    output$quasiDataTable <- renderDT({
      req(quasiData())
      data <- quasiData()
      
      DT::datatable(
        data,
        options = list(
          pageLength = 10,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
        ),
        extensions = 'Buttons',
        filter = 'top',
        rownames = FALSE
      )
    })
    
    output$quasiDataInfo <- renderUI({
      req(quasiData())
      data <- quasiData()
      
      tagList(
        h4("Dataset Information"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "File Name"),
            Value = c(nrow(data), ncol(data), 
                     ifelse(!is.null(input$quasiUserData), input$quasiUserData$name, "Unknown"))
          )
        }),
        h4("Variable Information"),
        renderTable({
          data.frame(
            Variable = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Unique = sapply(data, function(x) length(unique(x)))
          )
        })
      )
    })
    
    # Model Results Outputs
    output$quasiResults <- renderTable({
      req(quasiResult())
      res <- quasiResult()
      
      if (!is.null(res$coefficients)) {
        coef_df <- as.data.frame(res$coefficients)
        coef_df$Parameter <- rownames(coef_df)
        coef_df <- coef_df[, c("Parameter", names(coef_df)[-ncol(coef_df)])]
        coef_df[, -1] <- round(coef_df[, -1], 4)
        coef_df
      } else {
        data.frame(
          Parameter = "N/A",
          Estimate = "N/A",
          Std_Error = "N/A",
          T_value = "N/A",
          P_value = "N/A",
          stringsAsFactors = FALSE
        )
      }
    }, rownames = FALSE)
  })
} 