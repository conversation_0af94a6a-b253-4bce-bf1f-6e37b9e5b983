# Placeholder for SVM UI
svmSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("svmUserData"), "Upload your Data (.csv or .xls or .xlsx or .txt)", accept = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")),
    uiOutput(ns("svmColSelectors")),
    actionButton(ns("goSVM"), label = "Calculate", class = "act-btn")
  )
}

svmMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('svmHT')),
    plotOutput(ns('svmPlot'), width = "50%", height = "400px"),
    uiOutput(ns('svmConclusionOutput'))
  )
}

svmUI <- function(id) {
  ns <- NS(id)
  tagList(
    svmSidebarUI(id),
    svmMainUI(id),
    tabsetPanel(
      id = ns("svmTabset"),
      selected = "Analysis",
      tabPanel(
        id    = ns("svmAnalysis"),
        title = "Analysis",
        titlePanel("SVM Analysis"),
        br(),
        uiOutput(ns('svmHT')),
        br(),
        plotOutput(ns('svmPlot'), width = "50%", height = "400px"),
        br(),
        uiOutput(ns('svmConclusionOutput'))
      ),
      tabPanel(
        id    = ns("svmData"),
        title = "Uploaded Data",
        uiOutput(ns("renderSVMData"))
      )
    )
  )
} 