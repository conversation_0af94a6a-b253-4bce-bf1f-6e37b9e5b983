# Placeholder for Risk Analysis Server
riskAnalysisServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    # Reactives
    riskUploadData <- reactive({
      req(input$riskUserData)
      ext <- tools::file_ext(input$riskUserData$name)
      if (ext %in% c("csv", "txt")) {
        read.csv(input$riskUserData$datapath)
      } else if (ext %in% c("xls", "xlsx")) {
        if (!requireNamespace("readxl", quietly = TRUE)) stop("Install 'readxl' for Excel support.")
        readxl::read_excel(input$riskUserData$datapath)
      } else {
        stop("Unsupported file type.")
      }
    })
    riskResults <- eventReactive(input$goRisk, {
      df <- riskUploadData()
      req(input$riskVariable)
      riskAnalysisResults_func(
        data = df,
        variable = input$riskVariable,
        conf_level = input$riskConfLevel
      )
    })
    # Outputs
    output$riskHT <- renderUI({
      results <- riskResults()
      if (is.null(results)) return(NULL)
      tagList(
        tags$p(paste("VaR:", round(results$VaR, 4), "CVaR:", round(results$CVaR, 4))),
        verbatimTextOutput(ns("riskSummary"))
      )
    })
    output$riskSummary <- renderPrint({
      results <- riskResults()
      if (is.null(results)) return(NULL)
      print(results$summary)
    })
    output$riskPlot <- renderPlot({
      results <- riskResults()
      if (is.null(results) || is.null(results$plot)) return(NULL)
      results$plot
    })
    output$riskConclusionOutput <- renderUI({
      results <- riskResults()
      if (is.null(results)) return(NULL)
      tags$p("Risk analysis complete. See VaR, CVaR, summary, and plot above.")
    })
    output$renderRiskData <- renderUI({
      req(riskUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("riskInitialUploadTable")), style = "width: 75%"),
        br(),
        br(),
        uiOutput(ns("riskColSelectors"))
      )
    })
    output$riskInitialUploadTable <- DT::renderDT({
      req(riskUploadData())
      DT::datatable(riskUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(riskUploadData())))))
    })
    output$riskColSelectors <- renderUI({
      df <- riskUploadData()
      req(df)
      tagList(
        selectInput(ns("riskVariable"), "Variable", choices = names(df)),
        numericInput(ns("riskConfLevel"), "Confidence Level", 0.95, min = 0.8, max = 0.999, step = 0.01)
      )
    })
  })
} 