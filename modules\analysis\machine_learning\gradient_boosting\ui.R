# Placeholder for Gradient Boosting UI
gbSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("gbUserData"), "Upload your Data (.csv or .xls or .xlsx or .txt)", accept = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")),
    uiOutput(ns("gbColSelectors")),
    actionButton(ns("goGB"), label = "Calculate", class = "act-btn")
  )
}

gbMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('gbHT')),
    plotOutput(ns('gbPlot'), width = "50%", height = "400px"),
    uiOutput(ns('gbConclusionOutput'))
  )
}

gbUI <- function(id) {
  ns <- NS(id)
  tagList(
    gbSidebarUI(id),
    gbMainUI(id),
    tabsetPanel(
      id = ns("gbTabset"),
      selected = "Analysis",
      tabPanel(
        id    = ns("gbAnalysis"),
        title = "Gradient Boosting Analysis",
        titlePanel("Gradient Boosting Analysis"),
        br(),
        uiOutput(ns('gbHT')),
        br(),
        plotOutput(ns('gbPlot'), width = "50%", height = "400px"),
        br(),
        uiOutput(ns('gbConclusionOutput'))
      ),
      tabPanel(
        id    = ns("gbData"),
        title = "Uploaded Data",
        uiOutput(ns("renderGBData"))
      )
    )
  )
} 