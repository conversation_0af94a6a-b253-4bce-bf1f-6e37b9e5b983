MixedEffectsServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    meData <- eventReactive(input$meUserData, {
      handle_file_upload(input$meUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(meData(), {
      data <- meData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'meResponse', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'meFixed', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'meRandom', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    meValidationErrors <- reactive({
      errors <- c()
      data <- meData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$meResponse) || input$meResponse == "") {
        errors <- c(errors, "Select a response variable.")
      }
      if (is.null(input$meFixed) || length(input$meFixed) == 0) {
        errors <- c(errors, "Select at least one fixed effect.")
      }
      if (is.null(input$meRandom) || input$meRandom == "") {
        errors <- c(errors, "Select a random effect (grouping factor).")
      }
      
      # Check if variables are numeric where appropriate
      if (!is.null(input$meResponse) && input$meResponse != "") {
        if (!is.numeric(data[[input$meResponse]])) {
          errors <- c(errors, "Response variable must be numeric.")
        }
      }
      
      if (!is.null(input$meFixed) && length(input$meFixed) > 0) {
        for (var in input$meFixed) {
          if (!is.numeric(data[[var]]) && !is.factor(data[[var]])) {
            errors <- c(errors, sprintf("Fixed effect '%s' must be numeric or factor.", var))
          }
        }
      }
      
      errors
    })
    
    # Mixed effects model reactive
    meModel <- eventReactive(input$goME, {
      data <- meData()
      req(data, input$meResponse, input$meFixed, input$meRandom)
      
      # Check if lme4 package is available
      if (!requireNamespace("lme4", quietly = TRUE)) {
        return(NULL)
      }
      
      # Create formula
      fixed_terms <- paste(input$meFixed, collapse = " + ")
      formula_str <- paste(input$meResponse, "~", fixed_terms, "+ (1|", input$meRandom, ")")
      formula_obj <- as.formula(formula_str)
      
      # Fit mixed effects model
      tryCatch({
        fit <- lme4::lmer(formula_obj, data = data)
        
        # Extract model information
        summary_fit <- summary(fit)
        anova_fit <- anova(fit)
        
        # Random effects summary
        ranef_summary <- lme4::ranef(fit)
        
        # Model diagnostics
        residuals_fit <- residuals(fit)
        fitted_fit <- fitted(fit)
        
        # Model fit statistics
        aic_val <- AIC(fit)
        bic_val <- BIC(fit)
        log_lik <- logLik(fit)
        
        # Variance components
        vc <- lme4::VarCorr(fit)
        
        list(
          fit = fit,
          summary = summary_fit,
          anova = anova_fit,
          ranef = ranef_summary,
          residuals = residuals_fit,
          fitted = fitted_fit,
          aic = aic_val,
          bic = bic_val,
          log_lik = log_lik,
          vc = vc,
          formula = formula_str,
          data = data
        )
      }, error = function(e) {
        NULL
      })
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goME, {
      output$meError <- renderUI({
        tryCatch({ 
          res <- meModel()
          if (is.null(res)) {
            errorScreenUI(title = "Mixed Effects Error", errors = "Model fitting failed. Check your data and formula.")
          } else {
            NULL
          }
        }, error = function(e) {
          errorScreenUI(title = "Mixed Effects Error", errors = e$message)
        })
      })
      
      output$meResults <- renderUI({
        res <- meModel()
        if (is.null(res)) return(NULL)
        
        tagList(
          tabsetPanel(
            id = ns("meTabset"),
            selected = "Analysis",
            tabPanel(
              id = ns("meAnalysis"),
              title = "Analysis",
              titlePanel("Mixed Effects Model Results"),
              br(),
              h4("Model Summary"),
              verbatimTextOutput(ns('meModelSummary')),
              br(),
              h4("Fixed Effects"),
              tableOutput(ns('meFixedEffects')),
              br(),
              h4("Random Effects"),
              verbatimTextOutput(ns('meRandomEffects')),
              br(),
              h4("Model Fit Statistics"),
              tableOutput(ns('meFitStats')),
              br(),
              h4("Variance Components"),
              verbatimTextOutput(ns('meVarianceComponents'))
            ),
            tabPanel(
              id = ns("meDiagnostics"),
              title = "Diagnostics",
              h4("Residual Plots"),
              plotOutput(ns('meResidualPlots'), height = "600px"),
              br(),
              h4("Random Effects Plots"),
              plotOutput(ns('meRandomEffectsPlots'), height = "400px"),
              br(),
              h4("Model Assumptions"),
              uiOutput(ns('meAssumptions'))
            ),
            tabPanel(
              id = ns("meDataSummary"),
              title = "Data Summary",
              h4("Data Overview"),
              tableOutput(ns('meDataOverview')),
              br(),
              h4("Variable Summary"),
              tableOutput(ns('meVariableSummary')),
              br(),
              h4("Group Summary"),
              tableOutput(ns('meGroupSummary'))
            ),
            tabPanel(
              id = ns("meUploadedData"),
              title = "Uploaded Data",
              h4("Raw Data"),
              DT::DTOutput(ns('meDataTable'))
            )
          )
        )
      })
    })
    
    # Model summary output
    output$meModelSummary <- renderPrint({
      res <- meModel()
      if (is.null(res)) return(NULL)
      print(res$summary)
    })
    
    # Fixed effects table
    output$meFixedEffects <- renderTable({
      res <- meModel()
      if (is.null(res)) return(NULL)
      
      coef_table <- res$summary$coefficients
      result <- data.frame(
        Estimate = coef_table[, 1],
        Std_Error = coef_table[, 2],
        t_value = coef_table[, 3],
        p_value = coef_table[, 4],
        stringsAsFactors = FALSE
      )
      rownames(result) <- rownames(coef_table)
      result
    }, digits = 4)
    
    # Random effects output
    output$meRandomEffects <- renderPrint({
      res <- meModel()
      if (is.null(res)) return(NULL)
      print(res$ranef)
    })
    
    # Model fit statistics
    output$meFitStats <- renderTable({
      res <- meModel()
      if (is.null(res)) return(NULL)
      
      data.frame(
        Statistic = c("AIC", "BIC", "Log Likelihood"),
        Value = c(res$aic, res$bic, as.numeric(res$log_lik)),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    # Variance components
    output$meVarianceComponents <- renderPrint({
      res <- meModel()
      if (is.null(res)) return(NULL)
      print(res$vc)
    })
    
    # Residual plots
    output$meResidualPlots <- renderPlot({
      res <- meModel()
      if (is.null(res)) return(NULL)
      
      par(mfrow = c(2, 2))
      
      # Residuals vs fitted
      plot(res$fitted, res$residuals, 
           xlab = "Fitted Values", ylab = "Residuals",
           main = "Residuals vs Fitted")
      abline(h = 0, col = "red", lty = 2)
      
      # Q-Q plot
      qqnorm(res$residuals, main = "Normal Q-Q Plot")
      qqline(res$residuals, col = "red")
      
      # Histogram of residuals
      hist(res$residuals, main = "Histogram of Residuals",
           xlab = "Residuals", probability = TRUE)
      curve(dnorm(x, mean = mean(res$residuals), sd = sd(res$residuals)),
            add = TRUE, col = "red")
      
      # Scale-location plot
      plot(res$fitted, sqrt(abs(res$residuals)),
           xlab = "Fitted Values", ylab = "sqrt(|Residuals|)",
           main = "Scale-Location Plot")
      
      par(mfrow = c(1, 1))
    })
    
    # Random effects plots
    output$meRandomEffectsPlots <- renderPlot({
      res <- meModel()
      if (is.null(res)) return(NULL)
      
      ranef_data <- res$ranef[[1]]
      if (nrow(ranef_data) <= 20) {
        # Dotplot for small number of groups
        dotchart(ranef_data[, 1], main = "Random Effects by Group")
      } else {
        # Histogram for many groups
        hist(ranef_data[, 1], main = "Distribution of Random Effects",
             xlab = "Random Effect", probability = TRUE)
        curve(dnorm(x, mean = mean(ranef_data[, 1]), sd = sd(ranef_data[, 1])),
              add = TRUE, col = "red")
      }
    })
    
    # Model assumptions
    output$meAssumptions <- renderUI({
      tagList(
        h5("Key Assumptions:"),
        p("1. Independence of observations within groups"),
        p("2. Normal distribution of random effects"),
        p("3. Normal distribution of residuals"),
        p("4. Homoscedasticity of residuals"),
        p("5. Linear relationship between predictors and response"),
        br(),
        p("Note: Check residual plots and random effects plots for assumption violations.")
      )
    })
    
    # Data overview
    output$meDataOverview <- renderTable({
      data <- meData()
      if (is.null(data)) return(NULL)
      
      data.frame(
        Statistic = c("Number of Observations", "Number of Variables", "Number of Groups"),
        Value = c(nrow(data), ncol(data), 
                 if (!is.null(input$meRandom) && input$meRandom != "") {
                   length(unique(data[[input$meRandom]]))
                 } else {
                   "N/A"
                 }),
        stringsAsFactors = FALSE
      )
    })
    
    # Variable summary
    output$meVariableSummary <- renderTable({
      data <- meData()
      if (is.null(data)) return(NULL)
      
      if (!is.null(input$meResponse) && input$meResponse != "") {
        response_var <- data[[input$meResponse]]
        data.frame(
          Variable = c("Response", "Response (SD)", "Response (Min)", "Response (Max)"),
          Value = c(mean(response_var, na.rm = TRUE), 
                   sd(response_var, na.rm = TRUE),
                   min(response_var, na.rm = TRUE),
                   max(response_var, na.rm = TRUE)),
          stringsAsFactors = FALSE
        )
      } else {
        data.frame(
          Variable = "Response",
          Value = "Not selected",
          stringsAsFactors = FALSE
        )
      }
    }, digits = 4)
    
    # Group summary
    output$meGroupSummary <- renderTable({
      data <- meData()
      if (is.null(data) || is.null(input$meRandom) || input$meRandom == "") return(NULL)
      
      group_var <- data[[input$meRandom]]
      group_counts <- table(group_var)
      
      data.frame(
        Group = names(group_counts),
        Count = as.numeric(group_counts),
        stringsAsFactors = FALSE
      )
    })
    
    # Uploaded data table
    output$meDataTable <- DT::renderDT({
      req(meData())
      DT::datatable(meData(),
        options = list(pageLength = 25,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(meData())))))
    })
  })
} 