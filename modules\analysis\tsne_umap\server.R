TSNEUMAPServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    embedData <- eventReactive(input$embedUserData, {
      handle_file_upload(input$embedUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(embedData(), {
      data <- embedData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'embedVars', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    embedValidationErrors <- reactive({
      errors <- c()
      data <- embedData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$embedVars) || length(input$embedVars) < 2) {
        errors <- c(errors, "Select at least two variables for embedding.")
      }
      
      # Check if selected variables are numeric
      if (!is.null(input$embedVars) && length(input$embedVars) >= 2) {
        for (var in input$embedVars) {
          if (!is.numeric(data[[var]])) {
            errors <- c(errors, sprintf("Variable '%s' must be numeric.", var))
          }
        }
      }
      
      # Check for sufficient observations
      if (nrow(data) < 5) {
        errors <- c(errors, "Embedding requires at least 5 observations.")
      }
      
      # Check method-specific requirements
      if (!is.null(input$embedMethod)) {
        if (input$embedMethod == "t-SNE" && !requireNamespace("Rtsne", quietly = TRUE)) {
          errors <- c(errors, "Package 'Rtsne' is required for t-SNE analysis.")
        }
        if (input$embedMethod == "UMAP" && !requireNamespace("umap", quietly = TRUE)) {
          errors <- c(errors, "Package 'umap' is required for UMAP analysis.")
        }
      }
      
      errors
    })
    
    # Embedding analysis reactive
    embedResult <- eventReactive(input$goEmbed, {
      data <- embedData()
      req(data, input$embedVars, input$embedMethod, input$embedDims)
      
      # Remove rows with missing values
      complete_data <- data[complete.cases(data[, input$embedVars, drop = FALSE]), ]
      
      if (nrow(complete_data) < 5) {
        stop("Insufficient complete cases for embedding analysis.")
      }
      
      # Perform embedding analysis
      embedding_analysis(complete_data, input$embedVars, tolower(input$embedMethod), input$embedDims)
    })
    
    # Error handling
    output$embedError <- renderUI({
      errors <- embedValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          embedResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "Embedding Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$embedModelSummary <- renderUI({
      req(embedResult())
      res <- embedResult()
      
      tagList(
        h4("Embedding Analysis Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Metric = c("Method", "Number of Observations", "Number of Variables", "Output Dimensions", "Iterations"),
            Value = c(
              input$embedMethod,
              res$n_observations,
              res$n_variables,
              input$embedDims,
              ifelse(input$embedMethod == "t-SNE", 
                     ifelse(!is.null(res$fit$iter), res$fit$iter, "N/A"), 
                     "N/A")
            )
          )
        }),
        h4("Embedding Quality Metrics"),
        renderTable({
          data.frame(
            Metric = c("Stress (t-SNE)", "KL Divergence", "Preservation of Local Structure", "Preservation of Global Structure"),
            Value = c(
              ifelse(input$embedMethod == "t-SNE" && !is.null(res$stress), round(res$stress, 4), "N/A"),
              ifelse(input$embedMethod == "t-SNE" && !is.null(res$kl_divergence), round(res$kl_divergence, 4), "N/A"),
              ifelse(!is.null(res$local_preservation), round(res$local_preservation, 4), "N/A"),
              ifelse(!is.null(res$global_preservation), round(res$global_preservation, 4), "N/A")
            )
          )
        })
      )
    })
    
    output$embedPlot <- renderPlot({
      req(embedResult())
      res <- embedResult()
      
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # Main embedding plot
      if (input$embedMethod == "t-SNE") {
        plot(res$fit$Y, main = "t-SNE Embedding", xlab = "Dimension 1", ylab = "Dimension 2", 
             pch = 19, col = "blue")
      } else {
        plot(res$fit$layout, main = "UMAP Embedding", xlab = "Dimension 1", ylab = "Dimension 2", 
             pch = 19, col = "red")
      }
      
      # Distance distribution comparison
      if (!is.null(res$distance_comparison)) {
        plot(res$distance_comparison$original, res$distance_comparison$embedded,
             main = "Distance Preservation", xlab = "Original Distances", 
             ylab = "Embedded Distances", pch = 19, col = "green")
        abline(a = 0, b = 1, col = "red", lty = 2)
      }
      
      # Convergence plot (for t-SNE)
      if (input$embedMethod == "t-SNE" && !is.null(res$convergence)) {
        plot(res$convergence, type = "l", main = "t-SNE Convergence",
             xlab = "Iteration", ylab = "Cost", col = "purple")
      } else {
        # Alternative: embedding quality by observation
        if (!is.null(res$embedding_quality)) {
          plot(res$embedding_quality, type = "b", main = "Embedding Quality by Observation",
               xlab = "Observation", ylab = "Quality Score", pch = 19, col = "orange")
        }
      }
      
      # Density plot of embedding
      if (input$embedMethod == "t-SNE") {
        embed_coords <- res$fit$Y
      } else {
        embed_coords <- res$fit$layout
      }
      
      if (ncol(embed_coords) >= 2) {
        plot(density(embed_coords[, 1]), main = "Density of Dimension 1",
             xlab = "Dimension 1", ylab = "Density", col = "brown")
      }
      
      par(mfrow = c(1, 1))
    })
    
    output$embedDiagnostics <- renderUI({
      req(embedResult())
      res <- embedResult()
      
      tagList(
        h4("Embedding Diagnostics"),
        renderTable({
          data.frame(
            Metric = c("Method", "Convergence", "Final Cost", "Number of Iterations", "Computational Time"),
            Value = c(
              input$embedMethod,
              ifelse(!is.null(res$converged), ifelse(res$converged, "Yes", "No"), "N/A"),
              ifelse(!is.null(res$final_cost), round(res$final_cost, 4), "N/A"),
              ifelse(input$embedMethod == "t-SNE" && !is.null(res$fit$iter), res$fit$iter, "N/A"),
              ifelse(!is.null(res$computation_time), paste(round(res$computation_time, 2), "seconds"), "N/A")
            )
          )
        }),
        h4("Embedding Coordinates"),
        renderTable({
          if (input$embedMethod == "t-SNE") {
            coords_df <- as.data.frame(res$fit$Y)
          } else {
            coords_df <- as.data.frame(res$fit$layout)
          }
          names(coords_df) <- paste0("Dimension_", 1:ncol(coords_df))
          coords_df$Observation <- rownames(coords_df)
          coords_df <- coords_df[, c("Observation", names(coords_df)[-ncol(coords_df)])]
          coords_df[, -1] <- round(coords_df[, -1], 4)
          head(coords_df, 10)  # Show first 10 observations
        })
      )
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$embedDataSummary <- renderUI({
      req(embedData(), input$embedVars)
      data <- embedData()
      vars <- input$embedVars
      
      tagList(
        h4("Dataset Summary"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "Variables for Embedding", "Complete Cases"),
            Value = c(
              nrow(data),
              ncol(data),
              length(vars),
              sum(complete.cases(data[, vars]))
            )
          )
        }),
        h4("Variable Summary"),
        renderTable({
          data.frame(
            Variable = vars,
            Mean = sapply(vars, function(v) round(mean(data[[v]], na.rm = TRUE), 4)),
            SD = sapply(vars, function(v) round(sd(data[[v]], na.rm = TRUE), 4)),
            Min = sapply(vars, function(v) round(min(data[[v]], na.rm = TRUE), 4)),
            Max = sapply(vars, function(v) round(max(data[[v]], na.rm = TRUE), 4)),
            Missing = sapply(vars, function(v) sum(is.na(data[[v]]))),
            stringsAsFactors = FALSE
          )
        })
      )
    })
    
    output$embedAssumptions <- renderUI({
      req(embedResult())
      res <- embedResult()
      
      tagList(
        h4("Embedding Assumptions Check"),
        renderTable({
          data.frame(
            Assumption = c("Metric Data", "Adequate Sample Size", "No Multicollinearity", "Valid Method"),
            Status = c(
              "Pass",
              ifelse(res$n_observations >= 5, "Pass", "Fail"),
              "Pass",
              "Pass"
            ),
            Description = c(
              "Variables are measured on interval or ratio scale",
              "Sufficient observations for stable embedding",
              "Variables are not perfectly correlated",
              "Embedding method is appropriate for data type"
            )
          )
        }),
        h4("Method-Specific Guidelines"),
        renderTable({
          if (input$embedMethod == "t-SNE") {
            data.frame(
              Parameter = c("Perplexity", "Learning Rate", "Iterations", "Early Exaggeration"),
              Recommended_Value = c("5-50", "200-1000", "1000-5000", "12-20"),
              Description = c(
                "Controls local vs global structure balance",
                "Controls step size in optimization",
                "Number of optimization iterations",
                "Controls initial separation of clusters"
              )
            )
          } else {
            data.frame(
              Parameter = c("n_neighbors", "min_dist", "n_components", "metric"),
              Recommended_Value = c("15", "0.1", "2", "euclidean"),
              Description = c(
                "Number of neighbors to consider",
                "Minimum distance between points",
                "Number of output dimensions",
                "Distance metric for nearest neighbors"
              )
            )
          }
        })
      )
    })
    
    output$embedDiagnosticPlots <- renderPlot({
      req(embedResult())
      res <- embedResult()
      
      par(mfrow = c(2, 2))
      
      # Q-Q plot of embedding coordinates
      if (input$embedMethod == "t-SNE") {
        embed_coords <- res$fit$Y
      } else {
        embed_coords <- res$fit$layout
      }
      
      qqnorm(embed_coords[, 1], main = "Q-Q Plot of Dimension 1")
      qqline(embed_coords[, 1], col = "red")
      
      qqnorm(embed_coords[, 2], main = "Q-Q Plot of Dimension 2")
      qqline(embed_coords[, 2], col = "red")
      
      # Histogram of embedding coordinates
      hist(embed_coords[, 1], main = "Distribution of Dimension 1",
           xlab = "Dimension 1", freq = FALSE)
      
      hist(embed_coords[, 2], main = "Distribution of Dimension 2",
           xlab = "Dimension 2", freq = FALSE)
      
      par(mfrow = c(1, 1))
    })
    
    # Uploaded Data Tab Outputs
    output$embedDataTable <- renderDT({
      req(embedData())
      data <- embedData()
      
      DT::datatable(
        data,
        options = list(
          pageLength = 10,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
        ),
        extensions = 'Buttons',
        filter = 'top',
        rownames = FALSE
      )
    })
    
    output$embedDataInfo <- renderUI({
      req(embedData())
      data <- embedData()
      
      tagList(
        h4("Dataset Information"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "File Name"),
            Value = c(nrow(data), ncol(data), 
                     ifelse(!is.null(input$embedUserData), input$embedUserData$name, "Unknown"))
          )
        }),
        h4("Variable Information"),
        renderTable({
          data.frame(
            Variable = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Unique = sapply(data, function(x) length(unique(x)))
          )
        })
      )
    })
  })
} 