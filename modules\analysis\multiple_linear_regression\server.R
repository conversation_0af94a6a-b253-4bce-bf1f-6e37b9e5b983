# Multiple Linear Regression Server
multipleLinearRegressionServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    mlrUploadData <- eventReactive(input$mlrUserData, {
      handle_file_upload(input$mlrUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(mlrUploadData(), {
      data <- mlrUploadData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'mlrResponse', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'mlrExplanatory', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    mlrValidationErrors <- reactive({
      errors <- c()
      
      data <- mlrUploadData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$mlrResponse) || input$mlrResponse == "") {
        errors <- c(errors, "Please select a response variable.")
      }
      
      if (is.null(input$mlrExplanatory) || length(input$mlrExplanatory) == 0) {
        errors <- c(errors, "Please select at least one explanatory variable.")
      }
      
      if (!is.null(input$mlrResponse) && !is.null(input$mlrExplanatory) && 
          input$mlrResponse != "" && length(input$mlrExplanatory) > 0) {
        
        # Check if response variable is in explanatory variables
        if (input$mlrResponse %in% input$mlrExplanatory) {
          errors <- c(errors, "Response variable cannot be included in explanatory variables.")
        }
        
        # Check if all variables are numeric
        response_var <- data[[input$mlrResponse]]
        if (!is.numeric(response_var)) {
          errors <- c(errors, "Response variable must be numeric.")
        }
        
        for (var in input$mlrExplanatory) {
          if (!is.numeric(data[[var]])) {
            errors <- c(errors, sprintf("Explanatory variable '%s' must be numeric.", var))
          }
        }
        
        # Check for sufficient observations
        n_obs <- nrow(data)
        n_vars <- length(input$mlrExplanatory) + 1  # +1 for response
        
        if (n_obs <= n_vars) {
          errors <- c(errors, sprintf("Number of observations (%d) must be greater than number of variables (%d).", n_obs, n_vars))
        }
        
        # Check for variance in response variable
        if (sd(response_var, na.rm = TRUE) == 0) {
          errors <- c(errors, "Response variable must have variance (not all the same).")
        }
        
        # Check for variance in explanatory variables
        for (var in input$mlrExplanatory) {
          if (sd(data[[var]], na.rm = TRUE) == 0) {
            errors <- c(errors, sprintf("Explanatory variable '%s' must have variance (not all the same).", var))
          }
        }
      }
      
      errors
    })
    
    # MLR model reactive
    mlrModel <- reactive({
      req(mlrUploadData(), input$mlrResponse, input$mlrExplanatory)
      data <- mlrUploadData()
      
      # Remove rows with missing values
      complete_data <- data[complete.cases(data[, c(input$mlrResponse, input$mlrExplanatory)]), ]
      
      # Create formula
      formula_str <- paste(input$mlrResponse, "~", paste(input$mlrExplanatory, collapse = " + "))
      formula_obj <- as.formula(formula_str)
      
      # Fit model
      tryCatch({
        lm(formula_obj, data = complete_data)
      }, error = function(e) {
        NULL
      })
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goMLR, {
      output$mlrResults <- renderUI({
        errors <- mlrValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Multiple Linear Regression", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("mlrTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("mlrAnalysis"),
                title = "Analysis",
                titlePanel("Multiple Linear Regression Results"),
                br(),
                h4("Model Summary"),
                verbatimTextOutput(ns('mlrModelSummary')),
                br(),
                h4("Coefficients"),
                tableOutput(ns('mlrCoefficients')),
                br(),
                h4("Model Fit Statistics"),
                tableOutput(ns('mlrFitStats')),
                br(),
                h4("ANOVA Table"),
                tableOutput(ns('mlrAnova')),
                br(),
                h4("Effect Size"),
                uiOutput(ns('mlrEffectSize')),
                br(),
                h4("Assumptions Check"),
                uiOutput(ns('mlrAssumptions'))
              ),
              tabPanel(
                id = ns("mlrDiagnostics"),
                title = "Data Summary/Diagnostics",
                h4("Data Summary"),
                tableOutput(ns('mlrDataSummary')),
                br(),
                h4("Correlation Matrix"),
                tableOutput(ns('mlrCorrelationMatrix')),
                br(),
                h4("Residuals vs Fitted Plot"),
                plotOutput(ns('mlrResidualPlot'), height = "300px"),
                br(),
                h4("Q-Q Plot of Residuals"),
                plotOutput(ns('mlrQQPlot'), height = "300px"),
                br(),
                h4("Scale-Location Plot"),
                plotOutput(ns('mlrScaleLocationPlot'), height = "300px"),
                br(),
                h4("Residuals vs Leverage Plot"),
                plotOutput(ns('mlrLeveragePlot'), height = "300px"),
                br(),
                h4("Cook's Distance"),
                plotOutput(ns('mlrCooksPlot'), height = "300px")
              ),
              tabPanel(
                id = ns("mlrUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                uiOutput(ns('mlrDataTable'))
              )
            )
          )
        }
      })
    })
    
    # Model summary output
    output$mlrModelSummary <- renderPrint({
      req(mlrModel())
      model <- mlrModel()
      if (!is.null(model)) {
        summary(model)
      } else {
        cat("Error: Could not fit the model. Please check your data and variables.")
      }
    })
    
    # Coefficients table
    output$mlrCoefficients <- renderTable({
      req(mlrModel())
      model <- mlrModel()
      if (!is.null(model)) {
        coef_summary <- summary(model)$coefficients
        conf_int <- confint(model)
        
        result <- data.frame(
          Variable = rownames(coef_summary),
          Estimate = round(coef_summary[, 1], 4),
          Std_Error = round(coef_summary[, 2], 4),
          t_value = round(coef_summary[, 3], 4),
          P_value = round(coef_summary[, 4], 4),
          CI_Lower = round(conf_int[, 1], 4),
          CI_Upper = round(conf_int[, 2], 4),
          stringsAsFactors = FALSE
        )
        names(result) <- c("Variable", "Estimate", "Std Error", "t-value", "P-value", "CI Lower", "CI Upper")
        result
      } else {
        data.frame(
          Variable = "Error",
          Estimate = "N/A",
          Std_Error = "N/A",
          t_value = "N/A",
          P_value = "N/A",
          CI_Lower = "N/A",
          CI_Upper = "N/A",
          stringsAsFactors = FALSE
        )
      }
    }, digits = 4)
    
    # Fit statistics table
    output$mlrFitStats <- renderTable({
      req(mlrModel())
      model <- mlrModel()
      if (!is.null(model)) {
        model_summary <- summary(model)
        anova_result <- anova(model)
        
        result <- data.frame(
          Statistic = c("R-squared", "Adjusted R-squared", "F-statistic", "P-value (F-test)", "AIC", "BIC", "Residual Std Error"),
          Value = c(
            round(model_summary$r.squared, 4),
            round(model_summary$adj.r.squared, 4),
            round(model_summary$fstatistic[1], 4),
            round(pf(model_summary$fstatistic[1], model_summary$fstatistic[2], model_summary$fstatistic[3], lower.tail = FALSE), 4),
            round(AIC(model), 4),
            round(BIC(model), 4),
            round(model_summary$sigma, 4)
          ),
          stringsAsFactors = FALSE
        )
        result
      } else {
        data.frame(
          Statistic = c("R-squared", "Adjusted R-squared", "F-statistic", "P-value", "AIC", "BIC", "Residual Std Error"),
          Value = rep("N/A", 7),
          stringsAsFactors = FALSE
        )
      }
    }, digits = 4)
    
    # ANOVA table
    output$mlrAnova <- renderTable({
      req(mlrModel())
      model <- mlrModel()
      if (!is.null(model)) {
        anova_result <- anova(model)
        result <- data.frame(
          Source = rownames(anova_result),
          DF = anova_result$Df,
          SS = round(anova_result$`Sum Sq`, 4),
          MS = round(anova_result$`Mean Sq`, 4),
          F = round(anova_result$`F value`, 4),
          P = round(anova_result$`Pr(>F)`, 4),
          stringsAsFactors = FALSE
        )
        names(result) <- c("Source", "DF", "Sum Sq", "Mean Sq", "F-value", "P-value")
        result
      } else {
        data.frame(
          Source = c("Regression", "Residual", "Total"),
          DF = rep("N/A", 3),
          SS = rep("N/A", 3),
          MS = rep("N/A", 3),
          F = rep("N/A", 3),
          P = rep("N/A", 3),
          stringsAsFactors = FALSE
        )
      }
    }, digits = 4)
    
    # Data summary table
    output$mlrDataSummary <- renderTable({
      req(mlrUploadData(), input$mlrResponse, input$mlrExplanatory)
      data <- mlrUploadData()
      vars <- c(input$mlrResponse, input$mlrExplanatory)
      
      summary_stats <- data.frame(
        Variable = vars,
        N = sapply(vars, function(v) sum(!is.na(data[[v]]))),
        Mean = sapply(vars, function(v) round(mean(data[[v]], na.rm = TRUE), 4)),
        SD = sapply(vars, function(v) round(sd(data[[v]], na.rm = TRUE), 4)),
        Min = sapply(vars, function(v) round(min(data[[v]], na.rm = TRUE), 4)),
        Max = sapply(vars, function(v) round(max(data[[v]], na.rm = TRUE), 4)),
        stringsAsFactors = FALSE
      )
      summary_stats
    }, digits = 4)
    
    # Correlation matrix
    output$mlrCorrelationMatrix <- renderTable({
      req(mlrUploadData(), input$mlrResponse, input$mlrExplanatory)
      data <- mlrUploadData()
      vars <- c(input$mlrResponse, input$mlrExplanatory)
      
      cor_matrix <- cor(data[, vars], use = "complete.obs")
      result <- data.frame(
        Variable = vars,
        cor_matrix,
        stringsAsFactors = FALSE
      )
      names(result) <- c("Variable", vars)
      result
    }, digits = 4)
    
    # Effect size interpretation
    output$mlrEffectSize <- renderUI({
      req(mlrModel())
      model <- mlrModel()
      if (!is.null(model)) {
        r_squared <- summary(model)$r.squared
        adj_r_squared <- summary(model)$adj.r.squared
        
        # Effect size interpretation
        effect_size <- if (r_squared < 0.1) "Negligible" else
                      if (r_squared < 0.3) "Small" else
                      if (r_squared < 0.5) "Medium" else "Large"
        
        tagList(
          p(strong("R-squared effect size: "), round(r_squared, 4)),
          p(strong("Adjusted R-squared: "), round(adj_r_squared, 4)),
          br(),
          p(strong("Effect size interpretation: "), effect_size),
          p("- 0.0 to 0.1: Negligible"),
          p("- 0.1 to 0.3: Small"),
          p("- 0.3 to 0.5: Medium"),
          p("- 0.5 to 1.0: Large")
        )
      } else {
        tagList(
          p("R-squared effect size: N/A"),
          p("Effect size interpretation: N/A")
        )
      }
    })
    
    # Assumptions check
    output$mlrAssumptions <- renderUI({
      req(mlrModel())
      model <- mlrModel()
      if (!is.null(model)) {
        # Check for multicollinearity using VIF
        vif_values <- tryCatch({
          if (length(input$mlrExplanatory) > 1) {
            car::vif(model)
          } else {
            NULL
          }
        }, error = function(e) NULL)
        
        multicollinearity_check <- if (is.null(vif_values)) {
          "Cannot check (only one predictor)"
        } else {
          max_vif <- max(vif_values)
          if (max_vif > 10) "High multicollinearity detected" else
          if (max_vif > 5) "Moderate multicollinearity detected" else
          "No significant multicollinearity"
        }
        
        tagList(
          h5("Key Assumptions:"),
          p("1. Linear relationship between predictors and response"),
          p("2. Independent observations"),
          p("3. Normal distribution of residuals"),
          p("4. Homoscedasticity (constant variance)"),
          p("5. No multicollinearity"),
          br(),
          p(strong("Multicollinearity check: "), multicollinearity_check),
          br(),
          p("Note: These assumptions should be checked with diagnostic plots.")
        )
      } else {
        tagList(
          h5("Key Assumptions:"),
          p("1. Linear relationship between predictors and response"),
          p("2. Independent observations"),
          p("3. Normal distribution of residuals"),
          p("4. Homoscedasticity (constant variance)"),
          p("5. No multicollinearity"),
          br(),
          p("Note: These assumptions should be checked with diagnostic plots.")
        )
      }
    })
    
    # Diagnostic plots
    output$mlrResidualPlot <- renderPlot({
      req(mlrModel())
      model <- mlrModel()
      if (!is.null(model)) {
        plot(model, which = 1, main = "Residuals vs Fitted")
      } else {
        plot.new()
        text(0.5, 0.5, "Residuals vs Fitted plot will appear here")
      }
    })
    
    output$mlrQQPlot <- renderPlot({
      req(mlrModel())
      model <- mlrModel()
      if (!is.null(model)) {
        plot(model, which = 2, main = "Q-Q Plot of Residuals")
      } else {
        plot.new()
        text(0.5, 0.5, "Q-Q plot will appear here")
      }
    })
    
    output$mlrScaleLocationPlot <- renderPlot({
      req(mlrModel())
      model <- mlrModel()
      if (!is.null(model)) {
        plot(model, which = 3, main = "Scale-Location Plot")
      } else {
        plot.new()
        text(0.5, 0.5, "Scale-Location plot will appear here")
      }
    })
    
    output$mlrLeveragePlot <- renderPlot({
      req(mlrModel())
      model <- mlrModel()
      if (!is.null(model)) {
        plot(model, which = 5, main = "Residuals vs Leverage")
      } else {
        plot.new()
        text(0.5, 0.5, "Residuals vs Leverage plot will appear here")
      }
    })
    
    output$mlrCooksPlot <- renderPlot({
      req(mlrModel())
      model <- mlrModel()
      if (!is.null(model)) {
        plot(model, which = 4, main = "Cook's Distance")
      } else {
        plot.new()
        text(0.5, 0.5, "Cook's Distance plot will appear here")
      }
    })
    
    # Data table output
    output$mlrDataTable <- renderUI({
      req(mlrUploadData())
      DT::DTOutput(ns("mlrDataTableInner"))
    })
    
    output$mlrDataTableInner <- DT::renderDT({
      req(mlrUploadData())
      DT::datatable(mlrUploadData(),
        options = list(pageLength = 25,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(mlrUploadData())))))
    })
  })
} 