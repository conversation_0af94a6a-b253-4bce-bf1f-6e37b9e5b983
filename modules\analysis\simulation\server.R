SimulationServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Source the calculations file
    source("modules/calculations/simulation.R")
    
    # Validation errors reactive
    simValidationErrors <- reactive({
      validate_simulation_inputs(input)
    })
    
    # Simulation analysis reactive
    simResult <- eventReactive(input$goSim, {
      perform_simulation_analysis(input)
    })
    
    # Error handling
    output$simError <- renderUI({
      errors <- simValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          simResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "Simulation Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$simModelSummary <- renderUI({
      req(simResult())
      render_simulation_summary_ui(simResult())
    })
    
    output$simPlot <- renderPlot({
      req(simResult())
      render_simulation_plots(simResult())
    })
    
    output$simDiagnostics <- renderUI({
      req(simResult())
      render_simulation_diagnostics(simResult())
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$simDataSummary <- renderUI({
      req(simResult())
      render_simulation_data_summary(simResult())
    })
    
    output$simAssumptions <- renderUI({
      req(simResult())
      render_simulation_assumptions_check(simResult())
    })
    
    output$simDiagnosticPlots <- renderPlot({
      req(simResult())
      render_simulation_diagnostic_plots(simResult())
    })
    
    # Uploaded Data Tab Outputs
    output$simDataTable <- DT::renderDT({
      req(simResult())
      render_simulation_data_table(simResult())
    })
    
    output$simDataInfo <- renderUI({
      req(simResult())
      render_simulation_data_info(simResult())
    })
  })
} 