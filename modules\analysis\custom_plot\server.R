CustomPlotServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    plotData <- eventReactive(input$plotUserData, {
      handle_file_upload(input$plotUserData)
    })
    
    observeEvent(plotData(), {
      data <- plotData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectInput(session, 'plotType', choices = c("Scatterplot", "Boxplot", "Histogram", "Barplot", "Line Plot", "Density Plot"))
      }
    })
    
    output$plotVars <- renderUI({
      data <- plotData()
      if (is.null(data) || !is.data.frame(data)) return(NULL)
      switch(input$plotType,
        "Scatterplot" = tagList(
          selectizeInput(ns("plotX"), "X Variable", choices = names(data)),
          selectizeInput(ns("plotY"), "Y Variable", choices = names(data))
        ),
        "Boxplot" = selectizeInput(ns("plotBox"), "Variable", choices = names(data)),
        "Histogram" = selectizeInput(ns("plotHist"), "Variable", choices = names(data)),
        "Barplot" = selectizeInput(ns("plotBar"), "Variable", choices = names(data)),
        "Line Plot" = tagList(
          selectizeInput(ns("plotLineX"), "X Variable", choices = names(data)),
          selectizeInput(ns("plotLineY"), "Y Variable", choices = names(data))
        ),
        "Density Plot" = selectizeInput(ns("plotDensity"), "Variable", choices = names(data))
      )
    })
    
    plotValidationErrors <- reactive({
      errors <- c()
      data <- plotData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$plotType) || input$plotType == "") {
        errors <- c(errors, "Select a plot type.")
      }
      if (input$plotType == "Scatterplot") {
        if (is.null(input$plotX) || is.null(input$plotY) || input$plotX == "" || input$plotY == "") {
          errors <- c(errors, "Select X and Y variables.")
        }
      } else if (input$plotType == "Boxplot" && (is.null(input$plotBox) || input$plotBox == "")) {
        errors <- c(errors, "Select a variable for boxplot.")
      } else if (input$plotType == "Histogram" && (is.null(input$plotHist) || input$plotHist == "")) {
        errors <- c(errors, "Select a variable for histogram.")
      } else if (input$plotType == "Barplot" && (is.null(input$plotBar) || input$plotBar == "")) {
        errors <- c(errors, "Select a variable for barplot.")
      } else if (input$plotType == "Line Plot") {
        if (is.null(input$plotLineX) || is.null(input$plotLineY) || input$plotLineX == "" || input$plotLineY == "") {
          errors <- c(errors, "Select X and Y variables for line plot.")
        }
      } else if (input$plotType == "Density Plot" && (is.null(input$plotDensity) || input$plotDensity == "")) {
        errors <- c(errors, "Select a variable for density plot.")
      }
      errors
    })
    
    plotResult <- eventReactive(input$goPlot, {
      data <- plotData()
      req(data, input$plotType)
      
      # Generate plot and statistics
      custom_plot_analysis(data, input$plotType, input)
    })
    
    observeEvent(input$goPlot, {
      output$plotResults <- renderUI({
        errors <- plotValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Custom Plot Validation Error", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("plotTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("plotAnalysis"),
                title = "Analysis",
                titlePanel("Custom Plot Analysis"),
                br(),
                h4("Plot Type"),
                textOutput(ns('plotTypeOutput')),
                h4("Variables Used"),
                textOutput(ns('plotVariables')),
                h4("Plot"),
                plotOutput(ns('customPlot'), height = "400px"),
                h4("Plot Statistics"),
                tableOutput(ns('plotStatistics')),
                h4("Data Summary"),
                tableOutput(ns('plotDataSummary')),
                h4("Number of Observations"),
                textOutput(ns('plotObservations'))
              ),
              tabPanel(
                id = ns("plotDiagnostics"),
                title = "Diagnostics",
                h4("Additional Plots"),
                plotOutput(ns('plotDiagnostics'), height = "400px"),
                h4("Plot Diagnostics"),
                textOutput(ns('plotDiagnosticsText'))
              ),
              tabPanel(
                id = ns("plotUploadedData"),
                title = "Uploaded Data",
                h4("Raw Uploaded Data"),
                DT::DTOutput(ns('plotRawDataTable'))
              )
            )
          )
        }
      })
    })
    
    # Plot type
    output$plotTypeOutput <- renderText({
      res <- plotResult()
      if (is.null(res)) return(NULL)
      paste("Plot type:", res$plot_type)
    })
    
    # Variables used
    output$plotVariables <- renderText({
      res <- plotResult()
      if (is.null(res)) return(NULL)
      res$variables_used
    })
    
    # Main plot
    output$customPlot <- renderPlot({
      res <- plotResult()
      if (is.null(res)) return(NULL)
      res$plot
    })
    
    # Plot statistics
    output$plotStatistics <- renderTable({
      res <- plotResult()
      if (is.null(res)) return(NULL)
      res$plot_statistics
    }, rownames = FALSE, digits = 4)
    
    # Data summary
    output$plotDataSummary <- renderTable({
      res <- plotResult()
      if (is.null(res)) return(NULL)
      res$data_summary
    }, rownames = FALSE, digits = 4)
    
    # Number of observations
    output$plotObservations <- renderText({
      res <- plotResult()
      if (is.null(res)) return(NULL)
      paste("Number of observations:", res$n_observations)
    })
    
    # Additional diagnostic plots
    output$plotDiagnostics <- renderPlot({
      res <- plotResult()
      if (is.null(res)) return(NULL)
      res$diagnostic_plot
    })
    
    # Plot diagnostics text
    output$plotDiagnosticsText <- renderText({
      res <- plotResult()
      if (is.null(res)) return(NULL)
      res$diagnostics_text
    })
    
    # Raw data table
    output$plotRawDataTable <- DT::renderDT({
      req(plotData())
      DT::datatable(plotData(), options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), rownames = FALSE)
    })
  })
} 