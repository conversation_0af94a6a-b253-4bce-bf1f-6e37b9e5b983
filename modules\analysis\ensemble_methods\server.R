# Ensemble Methods Server Logic

ensembleMethodsServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    # Reactive values for storing results
    ensemble_results <- reactiveVal(NULL)
    ensemble_plots <- reactiveVal(NULL)
    
    # Data upload reactive
    ensembleData <- eventReactive(input$ensembleUserData, {
      handle_file_upload(input$ensembleUserData)
    })

    # Update variable choices when data changes
    observeEvent(ensembleData(), {
      data <- ensembleData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectInput(session, "ensemble_response", choices = names(data))
        updateSelectInput(session, "ensemble_predictors", choices = names(data), selected = names(data)[1:min(5, ncol(data))])
      }
    })

    # Run ensemble model
    observeEvent(input$run_ensemble, {
      req(ensembleData(), input$ensemble_response, input$ensemble_predictors)
      
      tryCatch({
        # Set random seed
        set.seed(input$ensemble_seed)
        
        # Prepare data
        model_data <- ensembleData()
        response_var <- input$ensemble_response
        predictor_vars <- input$ensemble_predictors
        
        # Remove response from predictors if included
        predictor_vars <- predictor_vars[predictor_vars != response_var]
        
        # Split data
        train_ratio <- input$ensemble_test_split
        train_indices <- sample(1:nrow(model_data), size = floor(train_ratio * nrow(model_data)))
        train_data <- model_data[train_indices, ]
        test_data <- model_data[-train_indices, ]
        
        # Prepare formula
        formula_str <- paste(response_var, "~", paste(predictor_vars, collapse = " + "))
        
        # Fit ensemble model
        if (input$ensemble_method == "random_forest") {
          model <- fit_random_forest(train_data, formula_str, response_var, predictor_vars)
        } else if (input$ensemble_method == "gradient_boosting") {
          model <- fit_gradient_boosting(train_data, formula_str, response_var, predictor_vars)
        } else if (input$ensemble_method == "xgboost") {
          model <- fit_xgboost(train_data, formula_str, response_var, predictor_vars)
        } else if (input$ensemble_method == "stacking") {
          model <- fit_stacking(train_data, formula_str, response_var, predictor_vars)
        } else if (input$ensemble_method == "bagging") {
          model <- fit_bagging(train_data, formula_str, response_var, predictor_vars)
        } else if (input$ensemble_method == "voting") {
          model <- fit_voting(train_data, formula_str, response_var, predictor_vars)
        }
        
        # Make predictions
        train_pred <- predict(model, train_data)
        test_pred <- predict(model, test_data)
        
        # Calculate performance metrics
        performance <- calculate_ensemble_performance(train_data[[response_var]], test_data[[response_var]], 
                                                    train_pred, test_pred, input$ensemble_task)
        
        # Feature importance
        importance <- NULL
        if (input$ensemble_feature_importance) {
          importance <- calculate_feature_importance(model, predictor_vars)
        }
        
        # Store results
        results <- list(
          model = model,
          method = input$ensemble_method,
          formula = formula_str,
          train_data = train_data,
          test_data = test_data,
          train_pred = train_pred,
          test_pred = test_pred,
          performance = performance,
          importance = importance,
          task = input$ensemble_task
        )
        
        ensemble_results(results)
        
        # Generate plots
        generate_ensemble_plots(results)
        
      }, error = function(e) {
        showNotification(paste("Error in ensemble modeling:", e$message), type = "error")
      })
    })

    # Random Forest
    fit_random_forest <- function(train_data, formula_str, response_var, predictor_vars) {
      if (!require(randomForest, quietly = TRUE)) {
        stop("randomForest package is required")
      }
      
      model <- randomForest(as.formula(formula_str), 
                           data = train_data,
                           ntree = input$ensemble_rf_ntree,
                           mtry = input$ensemble_rf_mtry,
                           nodesize = input$ensemble_rf_nodesize,
                           importance = input$ensemble_feature_importance)
      
      return(model)
    }

    # Gradient Boosting
    fit_gradient_boosting <- function(train_data, formula_str, response_var, predictor_vars) {
      if (!require(gbm, quietly = TRUE)) {
        stop("gbm package is required")
      }
      
      distribution <- if(input$ensemble_task == "regression") "gaussian" else "bernoulli"
      
      model <- gbm(as.formula(formula_str),
                   data = train_data,
                   distribution = distribution,
                   n.trees = input$ensemble_gb_ntrees,
                   interaction.depth = input$ensemble_gb_depth,
                   shrinkage = input$ensemble_gb_shrinkage,
                   bag.fraction = 0.8,
                   cv.folds = if(input$ensemble_cross_validation) input$ensemble_cv_folds else 0)
      
      return(model)
    }

    # XGBoost
    fit_xgboost <- function(train_data, formula_str, response_var, predictor_vars) {
      if (!require(xgboost, quietly = TRUE)) {
        stop("xgboost package is required")
      }
      
      # Prepare data for XGBoost
      X_train <- as.matrix(train_data[predictor_vars])
      y_train <- train_data[[response_var]]
      
      # XGBoost parameters
      params <- list(
        objective = if(input$ensemble_task == "regression") "reg:squarederror" else "binary:logistic",
        max_depth = input$ensemble_xgb_depth,
        eta = input$ensemble_xgb_eta,
        subsample = input$ensemble_xgb_subsample,
        colsample_bytree = 0.8
      )
      
      # Train model
      model <- xgboost(data = X_train, 
                       label = y_train,
                       params = params,
                       nrounds = input$ensemble_xgb_ntrees,
                       verbose = 0)
      
      # Store predictor names for later use
      attr(model, "predictor_vars") <- predictor_vars
      
      return(model)
    }

    # Stacking
    fit_stacking <- function(train_data, formula_str, response_var, predictor_vars) {
      if (!require(caret, quietly = TRUE)) {
        stop("caret package is required for stacking")
      }
      
      # Define base models
      base_models <- list()
      
      if ("Linear Regression" %in% input$ensemble_stack_models) {
        base_models$lm <- train(as.formula(formula_str), data = train_data, method = "lm")
      }
      if ("Random Forest" %in% input$ensemble_stack_models) {
        base_models$rf <- train(as.formula(formula_str), data = train_data, method = "rf")
      }
      if ("SVM" %in% input$ensemble_stack_models) {
        base_models$svm <- train(as.formula(formula_str), data = train_data, method = "svmRadial")
      }
      if ("Neural Network" %in% input$ensemble_stack_models) {
        base_models$nnet <- train(as.formula(formula_str), data = train_data, method = "nnet")
      }
      
      # Meta-learner
      meta_learner <- input$ensemble_stack_meta
      
      # Simple stacking implementation
      predictions <- data.frame()
      for (name in names(base_models)) {
        pred <- predict(base_models[[name]], train_data)
        predictions[[name]] <- pred
      }
      
      # Add response variable
      predictions[[response_var]] <- train_data[[response_var]]
      
      # Train meta-learner
      meta_formula <- paste(response_var, "~", paste(names(base_models), collapse = " + "))
      meta_model <- train(as.formula(meta_formula), data = predictions, method = "lm")
      
      # Return stacked model
      stacked_model <- list(
        base_models = base_models,
        meta_model = meta_model,
        predictor_vars = predictor_vars
      )
      
      return(stacked_model)
    }

    # Bagging
    fit_bagging <- function(train_data, formula_str, response_var, predictor_vars) {
      if (!require(ipred, quietly = TRUE)) {
        stop("ipred package is required for bagging")
      }
      
      model <- bagging(as.formula(formula_str), 
                      data = train_data,
                      nbagg = input$ensemble_bag_nbagg,
                      coob = TRUE)
      
      return(model)
    }

    # Voting
    fit_voting <- function(train_data, formula_str, response_var, predictor_vars) {
      # Simple voting implementation
      models <- list()
      
      if ("Linear" %in% input$ensemble_vote_models) {
        models$linear <- lm(as.formula(formula_str), data = train_data)
      }
      if ("Random Forest" %in% input$ensemble_vote_models) {
        models$rf <- randomForest(as.formula(formula_str), data = train_data, ntree = 100)
      }
      if ("SVM" %in% input$ensemble_vote_models) {
        models$svm <- svm(as.formula(formula_str), data = train_data)
      }
      
      voting_model <- list(
        models = models,
        predictor_vars = predictor_vars,
        method = input$ensemble_vote_method
      )
      
      return(voting_model)
    }

    # Calculate performance metrics
    calculate_ensemble_performance <- function(train_actual, test_actual, train_pred, test_pred, task) {
      if (task == "regression") {
        # Regression metrics
        train_rmse <- sqrt(mean((train_actual - train_pred)^2, na.rm = TRUE))
        test_rmse <- sqrt(mean((test_actual - test_pred)^2, na.rm = TRUE))
        train_mae <- mean(abs(train_actual - train_pred), na.rm = TRUE)
        test_mae <- mean(abs(test_actual - test_pred), na.rm = TRUE)
        train_r2 <- cor(train_actual, train_pred, use = "complete.obs")^2
        test_r2 <- cor(test_actual, test_pred, use = "complete.obs")^2
        
        return(list(
          train_rmse = train_rmse,
          test_rmse = test_rmse,
          train_mae = train_mae,
          test_mae = test_mae,
          train_r2 = train_r2,
          test_r2 = test_r2
        ))
      } else {
        # Classification metrics
        train_accuracy <- mean(train_actual == train_pred, na.rm = TRUE)
        test_accuracy <- mean(test_actual == test_pred, na.rm = TRUE)
        
        # Confusion matrix for test set
        test_cm <- table(test_actual, test_pred)
        
        return(list(
          train_accuracy = train_accuracy,
          test_accuracy = test_accuracy,
          confusion_matrix = test_cm
        ))
      }
    }

    # Calculate feature importance
    calculate_feature_importance <- function(model, predictor_vars) {
      if (inherits(model, "randomForest")) {
        importance <- importance(model)
        return(data.frame(
          variable = rownames(importance),
          importance = importance[, "%IncMSE"]
        ))
      } else if (inherits(model, "gbm")) {
        importance <- summary(model, plotit = FALSE)
        return(importance)
      } else if (inherits(model, "xgb.Booster")) {
        importance <- xgb.importance(feature_names = predictor_vars, model = model)
        return(importance)
      } else {
        return(NULL)
      }
    }

    # Generate plots
    generate_ensemble_plots <- function(results) {
      plots <- list()
      
      # Performance comparison plot
      if (results$task == "regression") {
        p1 <- ggplot(data.frame(
          actual = results$test_data[[input$ensemble_response]],
          predicted = results$test_pred
        ), aes(x = actual, y = predicted)) +
          geom_point(alpha = 0.6) +
          geom_abline(intercept = 0, slope = 1, color = "red", linetype = "dashed") +
          labs(title = "Actual vs Predicted Values",
               x = "Actual", y = "Predicted") +
          theme_minimal()
        plots$performance <- p1
      } else {
        # Classification performance
        p1 <- ggplot(data.frame(
          actual = results$test_data[[input$ensemble_response]],
          predicted = results$test_pred
        ), aes(x = actual, fill = predicted)) +
          geom_bar(position = "dodge") +
          labs(title = "Classification Results",
               x = "Actual", y = "Count", fill = "Predicted") +
          theme_minimal()
        plots$performance <- p1
      }
      
      # Feature importance plot
      if (!is.null(results$importance)) {
        p2 <- ggplot(results$importance, aes(x = reorder(variable, importance), y = importance)) +
          geom_bar(stat = "identity", fill = "steelblue") +
          coord_flip() +
          labs(title = "Feature Importance",
               x = "Variable", y = "Importance") +
          theme_minimal()
        plots$importance <- p2
      }
      
      ensemble_plots(plots)
    }

    # Output summary
    output$ensemble_summary <- renderPrint({
      req(ensemble_results())
      results <- ensemble_results()
      
      cat("ENSEMBLE MODEL SUMMARY\n")
      cat("=====================\n\n")
      cat("Method:", results$method, "\n")
      cat("Task:", results$task, "\n")
      cat("Formula:", results$formula, "\n\n")
      
      cat("Performance Metrics:\n")
      if (results$task == "regression") {
        cat("Train RMSE:", round(results$performance$train_rmse, 4), "\n")
        cat("Test RMSE:", round(results$performance$test_rmse, 4), "\n")
        cat("Train MAE:", round(results$performance$train_mae, 4), "\n")
        cat("Test MAE:", round(results$performance$test_mae, 4), "\n")
        cat("Train R²:", round(results$performance$train_r2, 4), "\n")
        cat("Test R²:", round(results$performance$test_r2, 4), "\n")
      } else {
        cat("Train Accuracy:", round(results$performance$train_accuracy, 4), "\n")
        cat("Test Accuracy:", round(results$performance$test_accuracy, 4), "\n")
        cat("\nConfusion Matrix:\n")
        print(results$performance$confusion_matrix)
      }
      
      if (!is.null(results$importance)) {
        cat("\nTop 10 Most Important Features:\n")
        print(head(results$importance, 10))
      }
    })

    # Output plots
    output$ensemble_plots <- renderPlot({
      req(ensemble_plots())
      plots <- ensemble_plots()
      if (length(plots) == 1) {
        print(plots[[1]])
      } else if (length(plots) > 1) {
        # Arrange multiple plots
        n_plots <- length(plots)
        n_cols <- min(2, n_plots)
        n_rows <- ceiling(n_plots / n_cols)
        do.call(grid.arrange, c(plots, ncol = n_cols))
      }
    })

    # Feature importance table
    output$ensemble_importance_table <- renderDataTable({
      req(ensemble_results())
      results <- ensemble_results()
      
      if (!is.null(results$importance)) {
        importance_df <- results$importance
        if (inherits(importance_df, "data.frame")) {
          # Ensure proper column names
          if (ncol(importance_df) >= 2) {
            names(importance_df)[1:2] <- c("Variable", "Importance")
            importance_df$Importance <- round(importance_df$Importance, 4)
            return(importance_df)
          }
        }
      }
      
      # Fallback
      data.frame(
        Variable = "No importance data available",
        Importance = NA
      )
    })

    # Prediction table
    output$ensemble_prediction_table <- renderDataTable({
      req(ensemble_results())
      results <- ensemble_results()
      
      if (!is.null(results$test_data) && !is.null(results$test_pred)) {
        pred_data <- data.frame(
          Actual = results$test_data[[input$ensemble_response]],
          Predicted = round(results$test_pred, 4),
          Residual = round(results$test_data[[input$ensemble_response]] - results$test_pred, 4)
        )
        
        # Add row numbers
        pred_data$Row <- 1:nrow(pred_data)
        pred_data <- pred_data[, c("Row", "Actual", "Predicted", "Residual")]
        
        return(pred_data)
      }
      
      # Fallback
      data.frame(
        Row = "No prediction data available",
        Actual = NA,
        Predicted = NA,
        Residual = NA
      )
    })

    # Model comparison table
    output$ensemble_comparison_table <- renderDataTable({
      req(ensemble_results())
      results <- ensemble_results()
      
      if (!is.null(results$performance)) {
        if (results$task == "regression") {
          comparison_df <- data.frame(
            Metric = c("Train RMSE", "Test RMSE", "Train MAE", "Test MAE", "Train R²", "Test R²"),
            Value = round(c(
              results$performance$train_rmse,
              results$performance$test_rmse,
              results$performance$train_mae,
              results$performance$test_mae,
              results$performance$train_r2,
              results$performance$test_r2
            ), 4)
          )
        } else {
          # Classification metrics
          comparison_df <- data.frame(
            Metric = c("Train Accuracy", "Test Accuracy"),
            Value = round(c(
              results$performance$train_accuracy,
              results$performance$test_accuracy
            ), 4)
          )
        }
        return(comparison_df)
      }
      
      # Fallback
      data.frame(
        Metric = "No performance data available",
        Value = NA
      )
    })

    # Download results
    output$download_ensemble_results <- downloadHandler(
      filename = function() {
        paste("ensemble_model_results_", format(Sys.time(), "%Y%m%d_%H%M%S"), ".txt", sep = "")
      },
      content = function(file) {
        req(ensemble_results())
        results <- ensemble_results()
        sink(file)
        cat("ENSEMBLE MODEL RESULTS\n")
        cat("=====================\n\n")
        cat("Method:", results$method, "\n")
        cat("Task:", results$task, "\n")
        cat("Formula:", results$formula, "\n\n")
        cat("Performance Metrics:\n")
        print(results$performance)
        if (!is.null(results$importance)) {
          cat("\nFeature Importance:\n")
          print(results$importance)
        }
        sink()
      }
    )

    # Reset functionality
    observeEvent(input$reset_ensemble, {
      ensemble_results(NULL)
      ensemble_plots(NULL)
      updateSelectInput(session, "ensemble_response", selected = "")
      updateSelectInput(session, "ensemble_predictors", selected = "")
    })
  })
} 