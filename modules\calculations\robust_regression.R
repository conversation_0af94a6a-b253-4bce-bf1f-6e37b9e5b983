# Robust Regression calculation and output helpers

robust_regression_uploadData_func <- function(rrUserData, response_var, predictor_vars) {
  tryCatch(
    {
      if (is.null(rrUserData) || is.null(response_var) || is.null(predictor_vars)) {
        return(NULL)
      }
      
      # Read data based on file type
      if (grepl("\\.csv$", rrUserData$name, ignore.case = TRUE)) {
        df <- read.csv(rrUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.tsv$", rrUserData$name, ignore.case = TRUE)) {
        df <- read.tsv(rrUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.xlsx$", rrUserData$name, ignore.case = TRUE)) {
        df <- readxl::read_excel(rrUserData$datapath)
      } else {
        stop("Unsupported file type. Please upload a CSV, TSV, or XLSX file.")
      }
      
      # Check for required columns
      required_cols <- c(response_var, predictor_vars)
      if (!all(required_cols %in% names(df))) {
        missing_cols <- required_cols[!required_cols %in% names(df)]
        stop(paste("The following columns were not found in the uploaded file:", paste(missing_cols, collapse = ", ")))
      }
      
      return(df)
    },
    error = function(e) {
      return(list(error = paste("Error reading data:", e$message)))
    }
  )
}

robust_regression_results_func <- function(data, response_var, predictor_vars, method = "lmrob") {
  tryCatch({
    if (is.null(data) || nrow(data) == 0) {
      stop("No data provided for analysis.")
    }
    
    if (!requireNamespace("robustbase", quietly = TRUE)) {
      stop("Package 'robustbase' is required for Robust Regression.")
    }
    
    formula_str <- as.formula(paste0("`", response_var, "` ~ ", paste0("`", predictor_vars, "`", collapse = " + ")))
    
    model <- robustbase::lmrob(formula_str, data = data)
    
    list(
      model = model,
      summary = summary(model),
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Robust Regression calculation:", e$message))
  })
}

robust_regression_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  tagList(
    h4("Robust Regression Model"),
    p("See summary table for model coefficients and diagnostics.")
  )
}

robust_regression_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  renderPrint(results$summary)
}

robust_regression_plot <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  
  # Generate diagnostic plots
  par(mfrow = c(2, 2))
  plot(results$model)
  par(mfrow = c(1, 1))
}
