MissingnessVizUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("missUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        br(),
        actionButton(ns("goMiss"), label = "Visualize Missingness", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("missError")),
        plotOutput(ns("missPlot")),
        tableOutput(ns("missSummary"))
      )
    )
  )
} 