# Breusch-Pagan Test calculation and output helpers

breusch_pagan_uploadData_func <- function(bpUserData) {
  ext <- tools::file_ext(bpUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(bpUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(bpUserData$datapath),
         xlsx = readxl::read_xlsx(bpUserData$datapath),
         txt = readr::read_tsv(bpUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

breusch_pagan_results_func <- function(data, response_var, predictor_vars) {
  tryCatch({
    all_vars <- c(response_var, predictor_vars)
    complete_data <- data[complete.cases(data[all_vars]), ]
    
    if (nrow(complete_data) < 3) {
      stop("At least 3 complete observations are required for Breusch-Pagan test")
    }
    
    formula_str <- paste(response_var, "~", paste(predictor_vars, collapse = " + "))
    model <- lm(as.formula(formula_str), data = complete_data)
    
    test_result <- lmtest::bptest(model)
    
    list(
      test = test_result,
      model = model,
      data = complete_data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Breusch-Pagan test calculation:", e$message))
  })
}

breusch_pagan_ht_html <- function(results, sigLvl) {
  test <- results$test
  p_value <- test$p.value
  
  conclusion <- if (p_value < sigLvl) {
    "Reject H0. There is evidence of heteroscedasticity."
  } else {
    "Do not reject H0. There is no evidence of heteroscedasticity."
  }
  
  withMathJax(tagList(
    h4("Breusch-Pagan Test for Heteroscedasticity"),
    p("$H_0$: Homoscedasticity (constant variance)."),
    p("$H_A$: Heteroscedasticity (non-constant variance)."),
    p(sprintf("Test Statistic (BP): %.4f", test$statistic)),
    p(sprintf("Degrees of Freedom: %d", test$parameter)),
    p(sprintf("P-value: %.4f", p_value)),
    p(strong("Conclusion: "), conclusion)
  ))
}

breusch_pagan_summary_html <- function(results) {
  model_summary <- summary(results$model)
  
  tagList(
    h4("Linear Model Summary"),
    renderPrint(model_summary)
  )
}

breusch_pagan_plot <- function(results) {
  plot_data <- data.frame(
    Fitted = fitted(results$model),
    Residuals = residuals(results$model)
  )
  
  ggplot(plot_data, aes(x = Fitted, y = Residuals)) +
    geom_point(alpha = 0.7) +
    geom_hline(yintercept = 0, color = "red", linetype = "dashed") +
    labs(title = "Residuals vs. Fitted Values",
         x = "Fitted Values",
         y = "Residuals") +
    theme_minimal()
}