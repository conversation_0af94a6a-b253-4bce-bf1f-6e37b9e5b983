# Quality Control Charts calculation and output helpers

quality_control_charts_uploadData_func <- function(qccUserData, data_col, group_col = NULL) {
  tryCatch(
    {
      if (is.null(qccUserData) || is.null(data_col)) {
        return(NULL)
      }
      
      # Read data based on file type
      if (grepl("\\.csv$", qccUserData$name, ignore.case = TRUE)) {
        df <- read.csv(qccUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.tsv$", qccUserData$name, ignore.case = TRUE)) {
        df <- read.tsv(qccUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.xlsx$", qccUserData$name, ignore.case = TRUE)) {
        df <- readxl::read_excel(qccUserData$datapath)
      } else {
        stop("Unsupported file type. Please upload a CSV, TSV, or XLSX file.")
      }
      
      # Check for required columns
      if (!data_col %in% names(df)) {
        stop(paste("Data column '", data_col, "' not found in the uploaded file.", sep = ""))
      }
      if (!is.null(group_col) && !group_col %in% names(df)) {
        stop(paste("Grouping column '", group_col, "' not found in the uploaded file.", sep = ""))
      }
      
      return(df)
    },
    error = function(e) {
      return(list(error = paste("Error reading data:", e$message)))
    }
  )
}

quality_control_charts_results_func <- function(data, data_col, group_col = NULL, chart_type = "xbar.one", ...) {
  tryCatch({
    if (is.null(data) || nrow(data) == 0) {
      stop("No data provided for analysis.")
    }
    
    if (!requireNamespace("qcc", quietly = TRUE)) {
      stop("Package 'qcc' is required for Quality Control Charts.")
    }
    
    # Prepare data for qcc
    qcc_data <- data[[data_col]]
    if (!is.null(group_col)) {
      qcc_data <- qcc.groups(qcc_data, data[[group_col]])
    }
    
    # Create the control chart
    qcc_obj <- qcc::qcc(qcc_data, type = chart_type, ...)
    
    list(
      qcc_obj = qcc_obj,
      summary = summary(qcc_obj),
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during QCC analysis:", e$message))
  })
}

quality_control_charts_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(
      h4("Error"),
      p(results$error)
    ))
  }
  
  tagList(
    h4(paste("Quality Control Chart:", results$qcc_obj$type))
  )
}

quality_control_charts_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  
  renderPrint({
    results$summary
  })
}

quality_control_charts_plot <- function(results) {
  if (!is.null(results$error) || is.null(results$qcc_obj)) {
    return(NULL)
  }
  
  plot(results$qcc_obj)
}