# Placeholder for survey/psychometrics calculation helpers if needed in future 

survey_psychometrics <- function(data, vars) {
  if (!requireNamespace("psych", quietly = TRUE)) stop("Package 'psych' required.")
  res <- psych::alpha(data[, vars, drop = FALSE])
  omega <- if (requireNamespace("psych", quietly = TRUE)) psych::omega(data[, vars, drop = FALSE], plot = FALSE)$omega.tot else NA
  mean_iic <- mean(res$item.stats$r.drop, na.rm = TRUE)
  plot_fun <- function() { plot(res) }
  list(
    alpha = res$total$raw_alpha,
    omega = omega,
    mean_interitem_correlation = mean_iic,
    summary = summary(res),
    plot = plot_fun
  )
} 