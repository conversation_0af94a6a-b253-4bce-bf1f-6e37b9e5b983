STLDecompositionUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("stlUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("stlTime"), "Time Variable", choices = NULL),
        selectizeInput(ns("stlSeries"), "Series Variable", choices = NULL),
        br(),
        actionButton(ns("goSTL"), label = "Run STL Decomposition", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("stlError")),
        plotOutput(ns("stlPlot")),
        tableOutput(ns("stlSummary"))
      )
    )
  )
} 