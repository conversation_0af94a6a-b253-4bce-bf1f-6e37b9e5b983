library(testthat)
library(here)
source(here::here("modules/calculations/simple_linear_regression.R"))
source(here::here("modules/calculations/multiple_linear_regression.R"))
source(here::here("modules/calculations/logistic_regression.R"))
source(here::here("modules/calculations/one_sample.R"))
source(here::here("modules/calculations/two_sample.R"))
source(here::here("modules/calculations/anova.R"))
source(here::here("modules/calculations/chi_square.R"))
source(here::here("modules/calculations/kruskal_wallis.R"))
source(here::here("modules/calculations/prob_dist.R"))
source(here::here("modules/calculations/sample_size_est.R"))

test_that("Simple linear regression model works", {
  x <- 1:10
  y <- 2 * x + 1
  model <- calc_slr_model(x, y)
  expect_equal(round(coef(model)[2], 8), 2)
})

test_that("Multiple linear regression model works", {
  df <- data.frame(y = 1:10, x1 = 1:10, x2 = 11:20)
  model <- calc_mlr_model(df, "y", c("x1", "x2"))
  expect_equal(length(coef(model)), 3)
})

test_that("Logistic regression model works", {
  df <- data.frame(y = c(0,1,0,1), x1 = c(1,2,3,4))
  model <- calc_logr_model(df, "y", "x1")
  expect_equal(length(coef(model)), 2)
})

test_that("One sample Z interval returns correct length", {
  res <- ZInterval(10, 5, 2, 0.95)
  expect_true(length(res) > 0)
})

test_that("Two sample Z test returns correct length", {
  res <- TwoSampZTest(5, 2, 10, 6, 2, 10, "two.sided", 0.05)
  expect_true(length(res) > 0)
})

test_that("ANOVA PrintANOVA returns a tagList", {
  expect_true(is.list(PrintANOVA()))
})

test_that("ChiSquareTest returns a list", {
  mat <- matrix(c(10, 20, 20, 40), nrow = 2)
  res <- ChiSquareTest(mat)
  expect_true(is.list(res))
})

test_that("Kruskal-Wallis calculation returns a list", {
  x <- rnorm(10)
  g <- rep(1:2, each=5)
  res <- kruskal.test(x, g)
  expect_true(!is.null(res$p.value))
})

test_that("Probability distribution binomial calculation stub runs", {
  expect_null(calc_binomial(10, 0.5, 5))
})

test_that("Sample size estimation stub runs", {
  expect_null(calc_ss_one_mean(1, 0.1, 0.95))
}) 