# ANCOVA calculation and output helpers

ancova_uploadData_func <- function(ancovaUserData) {
  ext <- tools::file_ext(ancovaUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(ancovaUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(ancovaUserData$datapath),
         xlsx = readxl::read_xlsx(ancovaUserData$datapath),
         txt = readr::read_tsv(ancovaUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

ancova_results_func <- function(data, response_var, factor_var, covariate_var) {
  tryCatch({
    all_vars <- c(response_var, factor_var, covariate_var)
    complete_data <- data[complete.cases(data[all_vars]), ]
    
    if (nrow(complete_data) < 6) {
      stop("At least 6 observations are required for ANCOVA")
    }
    
    complete_data[[factor_var]] <- as.factor(complete_data[[factor_var]])
    
    formula_str <- paste0("`", response_var, "` ~ `", factor_var, "` + `", covariate_var, "`")
    model <- aov(as.formula(formula_str), data = complete_data)
    
    # Assumption Checks
    residuals <- residuals(model)
    normality_test <- shapiro.test(residuals)
    levene_test <- car::leveneTest(as.formula(paste0("`", response_var, "` ~ `", factor_var, "`")), data = complete_data)
    
    interaction_formula <- as.formula(paste0("`", response_var, "` ~ `", factor_var, "` * `", covariate_var, "`"))
    interaction_model <- aov(interaction_formula, data = complete_data)
    slopes_test <- anova(interaction_model)
    
    list(
      model = model,
      data = complete_data,
      response_var = response_var,
      factor_var = factor_var,
      covariate_var = covariate_var,
      normality_test = normality_test,
      levene_test = levene_test,
      slopes_test = slopes_test,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during ANCOVA calculation:", e$message))
  })
}

ancova_ht_html <- function(results, sigLvl) {
  model_summary <- summary(results$model)
  p_values <- model_summary[[1]]$`Pr(>F)`
  
  factor_conclusion <- if (p_values[1] < sigLvl) "significant" else "not significant"
  covariate_conclusion <- if (p_values[2] < sigLvl) "significant" else "not significant"

  withMathJax(tagList(
    h4("ANCOVA Results"),
    p(sprintf("The effect of the factor (%s) on the response (%s), after adjusting for the covariate (%s), is %s.", 
              results$factor_var, results$response_var, results$covariate_var, factor_conclusion)),
    p(sprintf("The effect of the covariate (%s) on the response (%s) is %s.", 
              results$covariate_var, results$response_var, covariate_conclusion))
  ))
}

ancova_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  model_summary <- summary(results$model)
  # Adjusted Means
  adj_means <- data.frame()
  if (requireNamespace("emmeans", quietly = TRUE)) {
    emm <- emmeans::emmeans(results$model, specs = results$factor_var)
    adj_means <- as.data.frame(summary(emm))
  }
  out <- list(
    h4("ANOVA Table"),
    renderTable(model_summary[[1]], rownames = TRUE, digits = 4),
    h4("Adjusted Means"),
    renderTable(adj_means, digits = 4),
    h4("Assumption Checks"),
    p(paste("Shapiro-Wilk Normality Test of Residuals: W =", round(results$normality_test$statistic, 4), ", p-value =", round(results$normality_test$p.value, 4))),
    p(paste("Levene's Test for Homogeneity of Variances: F =", round(results$levene_test$`F value`[1], 4), ", p-value =", round(results$levene_test$`Pr(>F)`[1], 4))),
    p(paste("Test for Homogeneity of Regression Slopes (Interaction): F =", round(results$slopes_test$`F value`[3], 4), ", p-value =", round(results$slopes_test$`Pr(>F)`[3], 4)))
  )
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

ancova_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  p1 <- ggplot(results$data, aes_string(x = results$covariate_var, y = results$response_var, color = results$factor_var)) +
    geom_point(alpha = 0.7) +
    geom_smooth(method = "lm", se = FALSE) +
    labs(title = "ANCOVA Plot", x = results$covariate_var, y = results$response_var, color = results$factor_var) +
    theme_minimal()
  print(p1)
  # Residuals plot
  resids <- residuals(results$model)
  plot(resids, type = 'h', main = 'ANCOVA Residuals', ylab = 'Residuals')
}