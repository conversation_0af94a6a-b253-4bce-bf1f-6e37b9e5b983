# Placeholder for Bayesian Networks UI
bayesianNetworksSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("bnUserData"), "Upload your Data (.csv or .xls or .xlsx or .txt)", accept = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")),
    uiOutput(ns("bnColSelectors")),
    actionButton(ns("goBN"), label = "Calculate", class = "act-btn")
  )
}

bayesianNetworksMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('bnHT')),
    plotOutput(ns('bnPlot'), width = "50%", height = "400px"),
    uiOutput(ns('bnConclusionOutput'))
  )
}

bayesianNetworksUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(bayesianNetworksSidebarUI(id)),
    mainPanel(bayesianNetworksMainUI(id))
  )
} 