SurveyPsychometricsUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("surveyUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("surveyVars"), "Variables (items)", choices = NULL, multiple = TRUE),
        br(),
        actionButton(ns("goSurvey"), label = "Analyze", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("surveyError")),
        tableOutput(ns("surveyReliability")),
        tableOutput(ns("surveyItemAnalysis")),
        tableOutput(ns("surveyScaleScores"))
      )
    )
  )
} 