# Proportion Tests calculation and output helpers

proportion_tests_uploadData_func <- function(ptUserData, successes_col, trials_col) {
  tryCatch(
    {
      if (is.null(ptUserData) || is.null(successes_col) || is.null(trials_col)) {
        return(NULL)
      }
      
      # Read data based on file type
      if (grepl("\\.csv$", ptUserData$name, ignore.case = TRUE)) {
        df <- read.csv(ptUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.tsv$", ptUserData$name, ignore.case = TRUE)) {
        df <- read.tsv(ptUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.xlsx$", ptUserData$name, ignore.case = TRUE)) {
        df <- readxl::read_excel(ptUserData$datapath)
      } else {
        stop("Unsupported file type. Please upload a CSV, TSV, or XLSX file.")
      }
      
      # Check for required columns
      if (!successes_col %in% names(df) || !trials_col %in% names(df)) {
        stop("One or both specified columns not found in the uploaded file.")
      }
      
      return(df)
    },
    error = function(e) {
      return(list(error = paste("Error reading data:", e$message)))
    }
  )
}

proportion_tests_results_func <- function(data, successes_col, trials_col, test_type = "one_sample", ...) {
  tryCatch({
    if (is.null(data) || nrow(data) == 0) {
      stop("No data provided for analysis.")
    }
    
    successes <- data[[successes_col]]
    trials <- data[[trials_col]]
    
    result <- switch(test_type,
      "one_sample" = prop.test(sum(successes), sum(trials), ...),
      "two_sample" = prop.test(successes, trials, ...),
      "chi_square" = chisq.test(matrix(c(successes[1], trials[1] - successes[1], successes[2], trials[2] - successes[2]), nrow = 2), ...),
      stop("Invalid test type specified.")
    )
    
    list(
      result = result,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during the proportion test:", e$message))
  })
}

proportion_tests_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(
      h4("Error"),
      p(results$error)
    ))
  }
  
  tagList(
    h4("Proportion Test Results"),
    p(paste("Method:", results$result$method))
  )
}

proportion_tests_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  
  renderPrint({
    results$result
  })
}

proportion_tests_plot <- function(results) {
  # No standard plot for proportion tests, but could be implemented
  return(NULL)
}