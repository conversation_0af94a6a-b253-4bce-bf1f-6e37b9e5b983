run_supervised_ml <- function(data, response, predictors, model_type = "rf", cv_folds = 5) {
  tryCatch({
    x <- as.matrix(data[, predictors, drop = FALSE])
    y <- data[[response]]
    if (!requireNamespace("caret", quietly = TRUE)) stop("Package 'caret' required.")
    ctrl <- caret::trainControl(method = "cv", number = cv_folds)
    if (model_type == "rf") {
      if (!requireNamespace("randomForest", quietly = TRUE)) stop("Package 'randomForest' required.")
      fit <- caret::train(x, y, method = "rf", trControl = ctrl)
      plot_obj <- function() { varImpPlot(fit$finalModel) }
      list(model = fit, accuracy = max(fit$results$Accuracy), plot = plot_obj, error = NULL)
    } else if (model_type == "svm") {
      if (!requireNamespace("e1071", quietly = TRUE)) stop("Package 'e1071' required.")
      fit <- caret::train(x, y, method = "svmRadial", trControl = ctrl)
      list(model = fit, accuracy = max(fit$results$Accuracy), plot = NULL, error = NULL)
    } else if (model_type == "logistic") {
      fit <- caret::train(x, y, method = "glm", family = "binomial", trControl = ctrl)
      list(model = fit, accuracy = max(fit$results$Accuracy), plot = NULL, error = NULL)
    } else {
      stop("Unknown model type.")
    }
  }, error = function(e) {
    list(model = NULL, accuracy = NULL, plot = NULL, error = e$message)
  })
}

# Enhanced supervised machine learning with comprehensive analysis
enhanced_supervised_ml <- function(data, response, predictors, model_type = "rf", cv_folds = 5, 
                                   tune_length = 10, preprocess = TRUE) {
  tryCatch({
    # Data preparation
    x <- data[, predictors, drop = FALSE]
    y <- data[[response]]
    
    # Check if response is categorical or continuous
    is_classification <- is.factor(y) || is.character(y) || length(unique(y)) <= 10
    
    # Preprocessing
    if (preprocess) {
      preprocess_methods <- c("center", "scale")
      if (any(sapply(x, is.numeric))) {
        preprocess_methods <- c(preprocess_methods, "nzv")  # Remove near-zero variance
      }
    } else {
      preprocess_methods <- NULL
    }
    
    # Cross-validation control
    ctrl <- caret::trainControl(
      method = "cv", 
      number = cv_folds,
      savePredictions = TRUE,
      returnResamp = "all"
    )
    
    # Model training based on type
    model_result <- train_model_by_type(x, y, model_type, ctrl, tune_length, preprocess_methods, is_classification)
    
    # Performance metrics
    performance_metrics <- calculate_performance_metrics(model_result, x, y, is_classification)
    
    # Feature importance
    feature_importance <- calculate_feature_importance(model_result, predictors)
    
    # Model diagnostics
    diagnostics <- calculate_model_diagnostics(model_result, x, y, is_classification)
    
    # Cross-validation results
    cv_results <- extract_cv_results(model_result)
    
    # Prediction intervals (for regression)
    prediction_intervals <- if (!is_classification) {
      calculate_prediction_intervals(model_result, x, y)
    } else {
      NULL
    }
    
    list(
      model = model_result,
      model_type = model_type,
      is_classification = is_classification,
      performance = performance_metrics,
      feature_importance = feature_importance,
      diagnostics = diagnostics,
      cv_results = cv_results,
      prediction_intervals = prediction_intervals,
      data_info = list(
        n_observations = nrow(data),
        n_predictors = length(predictors),
        response_variable = response,
        predictor_variables = predictors
      )
    )
    
  }, error = function(e) {
    list(error = e$message)
  })
}

# Train model based on type
train_model_by_type <- function(x, y, model_type, ctrl, tune_length, preprocess_methods, is_classification) {
  if (model_type == "random_forest") {
    if (!requireNamespace("randomForest", quietly = TRUE)) stop("Package 'randomForest' required.")
    caret::train(x, y, method = "rf", trControl = ctrl, tuneLength = tune_length, 
                 preProcess = preprocess_methods)
  } else if (model_type == "svm") {
    if (!requireNamespace("e1071", quietly = TRUE)) stop("Package 'e1071' required.")
    caret::train(x, y, method = "svmRadial", trControl = ctrl, tuneLength = tune_length,
                 preProcess = preprocess_methods)
  } else if (model_type == "gbm") {
    if (!requireNamespace("gbm", quietly = TRUE)) stop("Package 'gbm' required.")
    caret::train(x, y, method = "gbm", trControl = ctrl, tuneLength = tune_length,
                 preProcess = preprocess_methods)
  } else if (model_type == "neural_network") {
    if (!requireNamespace("nnet", quietly = TRUE)) stop("Package 'nnet' required.")
    caret::train(x, y, method = "nnet", trControl = ctrl, tuneLength = tune_length,
                 preProcess = preprocess_methods, linout = !is_classification)
  } else if (model_type == "elastic_net") {
    if (!requireNamespace("glmnet", quietly = TRUE)) stop("Package 'glmnet' required.")
    caret::train(x, y, method = "glmnet", trControl = ctrl, tuneLength = tune_length,
                 preProcess = preprocess_methods)
  } else if (model_type == "knn") {
    caret::train(x, y, method = "knn", trControl = ctrl, tuneLength = tune_length,
                 preProcess = preprocess_methods)
  } else if (model_type == "linear_regression") {
    caret::train(x, y, method = "lm", trControl = ctrl, preProcess = preprocess_methods)
  } else if (model_type == "logistic_regression") {
    if (is_classification) {
      caret::train(x, y, method = "glm", family = "binomial", trControl = ctrl,
                   preProcess = preprocess_methods)
    } else {
      stop("Logistic regression requires categorical response variable")
    }
  } else {
    stop(paste("Unknown model type:", model_type))
  }
}

# Calculate comprehensive performance metrics
calculate_performance_metrics <- function(model, x, y, is_classification) {
  predictions <- predict(model, x)
  
  if (is_classification) {
    # Classification metrics
    cm <- confusionMatrix(predictions, y)
    
    metrics <- list(
      accuracy = cm$overall["Accuracy"],
      kappa = cm$overall["Kappa"],
      sensitivity = cm$byClass["Sensitivity"],
      specificity = cm$byClass["Specificity"],
      precision = cm$byClass["Precision"],
      recall = cm$byClass["Recall"],
      f1_score = cm$byClass["F1"],
      balanced_accuracy = cm$byClass["Balanced Accuracy"]
    )
    
    # ROC and AUC if binary classification
    if (length(unique(y)) == 2) {
      roc_result <- tryCatch({
        if (requireNamespace("pROC", quietly = TRUE)) {
          roc_obj <- pROC::roc(y, as.numeric(predictions))
          list(auc = pROC::auc(roc_obj), roc_curve = roc_obj)
        } else {
          list(auc = NA, roc_curve = NULL)
        }
      }, error = function(e) {
        list(auc = NA, roc_curve = NULL)
      })
      metrics$auc <- roc_result$auc
      metrics$roc_curve <- roc_result$roc_curve
    }
    
  } else {
    # Regression metrics
    residuals <- y - predictions
    
    metrics <- list(
      rmse = sqrt(mean(residuals^2)),
      mae = mean(abs(residuals)),
      mape = mean(abs(residuals / y)) * 100,
      r_squared = 1 - (sum(residuals^2) / sum((y - mean(y))^2)),
      adjusted_r_squared = 1 - ((1 - (sum(residuals^2) / sum((y - mean(y))^2))) * 
                                (length(y) - 1) / (length(y) - length(predictors) - 1)),
      mape = mean(abs(residuals / y)) * 100
    )
  }
  
  metrics
}

# Calculate feature importance
calculate_feature_importance <- function(model, predictor_names) {
  importance_result <- tryCatch({
    if (model$method == "rf") {
      # Random Forest importance
      imp <- randomForest::importance(model$finalModel)
      data.frame(
        variable = rownames(imp),
        importance = imp[, 1],
        stringsAsFactors = FALSE
      )
    } else if (model$method == "gbm") {
      # GBM importance
      imp <- summary(model$finalModel, plotit = FALSE)
      data.frame(
        variable = imp$var,
        importance = imp$rel.inf,
        stringsAsFactors = FALSE
      )
    } else if (model$method == "glmnet") {
      # Elastic net coefficients
      coef_matrix <- coef(model$finalModel, s = model$bestTune$lambda)
      coef_values <- as.matrix(coef_matrix)[-1, 1]  # Remove intercept
      data.frame(
        variable = names(coef_values),
        importance = abs(coef_values),
        stringsAsFactors = FALSE
      )
    } else {
      # Generic variable importance using caret
      imp <- varImp(model)
      data.frame(
        variable = rownames(imp$importance),
        importance = imp$importance[, 1],
        stringsAsFactors = FALSE
      )
    }
  }, error = function(e) {
    data.frame(variable = predictor_names, importance = rep(NA, length(predictor_names)))
  })
  
  # Sort by importance
  importance_result[order(-importance_result$importance), ]
}

# Calculate model diagnostics
calculate_model_diagnostics <- function(model, x, y, is_classification) {
  if (is_classification) {
    # Classification diagnostics
    predictions <- predict(model, x)
    probabilities <- predict(model, x, type = "prob")
    
    diagnostics <- list(
      confusion_matrix = confusionMatrix(predictions, y),
      class_distribution = table(y),
      prediction_distribution = table(predictions)
    )
    
    # Calibration plot data
    if (length(unique(y)) == 2 && !is.null(probabilities)) {
      calibration_data <- tryCatch({
        if (requireNamespace("caret", quietly = TRUE)) {
          cal_obj <- caret::calibration(y ~ probabilities[, 2], data = data.frame(y = y, probabilities = probabilities[, 2]))
          cal_obj$data
        } else {
          NULL
        }
      }, error = function(e) NULL)
      diagnostics$calibration_data <- calibration_data
    }
    
  } else {
    # Regression diagnostics
    predictions <- predict(model, x)
    residuals <- y - predictions
    
    diagnostics <- list(
      residuals = residuals,
      fitted_values = predictions,
      residual_stats = summary(residuals),
      normality_test = shapiro.test(residuals),
      heteroscedasticity = tryCatch({
        if (requireNamespace("lmtest", quietly = TRUE)) {
          lmtest::bptest(lm(residuals ~ predictions))
        } else {
          NULL
        }
      }, error = function(e) NULL)
    )
  }
  
  diagnostics
}

# Extract cross-validation results
extract_cv_results <- function(model) {
  cv_results <- list(
    cv_accuracy = model$results,
    best_tune = model$bestTune,
    cv_predictions = model$pred,
    resample_results = model$resample
  )
  
  # Add model-specific CV results
  if (!is.null(model$finalModel)) {
    cv_results$final_model_summary <- summary(model$finalModel)
  }
  
  cv_results
}

# Calculate prediction intervals for regression
calculate_prediction_intervals <- function(model, x, y) {
  tryCatch({
    # Simple approach using residuals
    predictions <- predict(model, x)
    residuals <- y - predictions
    residual_sd <- sd(residuals)
    
    # 95% prediction intervals
    pi_lower <- predictions - 1.96 * residual_sd
    pi_upper <- predictions + 1.96 * residual_sd
    
    list(
      predictions = predictions,
      pi_lower = pi_lower,
      pi_upper = pi_upper,
      residual_sd = residual_sd
    )
  }, error = function(e) {
    NULL
  })
}

# Model comparison function
compare_models <- function(data, response, predictors, model_types = c("rf", "svm", "gbm"), 
                          cv_folds = 5, tune_length = 10) {
  results <- list()
  
  for (model_type in model_types) {
    tryCatch({
      result <- enhanced_supervised_ml(data, response, predictors, model_type, cv_folds, tune_length)
      if (is.null(result$error)) {
        results[[model_type]] <- result
      }
    }, error = function(e) {
      warning(paste("Failed to train", model_type, "model:", e$message))
    })
  }
  
  # Compare models
  if (length(results) > 1) {
    comparison <- compare_model_performance(results)
    results$comparison <- comparison
  }
  
  results
}

# Compare performance across models
compare_model_performance <- function(model_results) {
  # Extract performance metrics
  performance_data <- data.frame()
  
  for (model_name in names(model_results)) {
    if (model_name != "comparison" && !is.null(model_results[[model_name]]$performance)) {
      perf <- model_results[[model_name]]$performance
      perf_df <- data.frame(
        model = model_name,
        metric = names(perf),
        value = unlist(perf),
        stringsAsFactors = FALSE
      )
      performance_data <- rbind(performance_data, perf_df)
    }
  }
  
  # Create comparison summary
  comparison_summary <- list(
    performance_data = performance_data,
    best_model = determine_best_model(model_results)
  )
  
  comparison_summary
}

# Determine best model based on performance
determine_best_model <- function(model_results) {
  # This is a simplified approach - could be enhanced based on specific criteria
  best_model <- names(model_results)[1]
  best_score <- -Inf
  
  for (model_name in names(model_results)) {
    if (model_name != "comparison" && !is.null(model_results[[model_name]]$performance)) {
      perf <- model_results[[model_name]]$performance
      
      # Use appropriate metric based on problem type
      if (!is.null(perf$accuracy)) {
        score <- perf$accuracy  # Classification
      } else if (!is.null(perf$r_squared)) {
        score <- perf$r_squared  # Regression
      } else {
        score <- -Inf
      }
      
      if (score > best_score) {
        best_score <- score
        best_model <- model_name
      }
    }
  }
  
  list(model = best_model, score = best_score)
} 