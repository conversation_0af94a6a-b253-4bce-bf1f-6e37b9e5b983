# Test Suite for Inference Test Modules
# Tests: T-tests, ANOVA, Nonparametric Tests, Categorical Tests, Bayesian Tests

library(testthat)

# Test One Sample Inference Module
test_that("One Sample Inference Module", {
  # Load test data
  data <- load_test_data("one_sample")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(nrow(data) > 0)
  
  # Test one sample t-test
  if (exists("one_sample_analysis")) {
    result <- one_sample_analysis(data, names(data)[1], mu0 = 0, alpha = 0.05)
    expect_true(is.list(result))
    expect_true("test_statistic" %in% names(result))
    expect_true("p_value" %in% names(result))
    expect_true("confidence_interval" %in% names(result))
  }
  
  cat("  ✓ One Sample Inference: T-test functionality tested\n")
})

# Test Two Sample Inference Module
test_that("Two Sample Inference Module", {
  # Load test data
  data <- load_test_data("two_sample")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test two sample t-test
  if (exists("two_sample_analysis")) {
    result <- two_sample_analysis(data, names(data)[1], names(data)[2], alpha = 0.05)
    expect_true(is.list(result))
    expect_true("test_statistic" %in% names(result))
    expect_true("p_value" %in% names(result))
    expect_true("effect_size" %in% names(result))
  }
  
  cat("  ✓ Two Sample Inference: Independent t-test tested\n")
})

# Test Paired T-Test Module
test_that("Paired T-Test Module", {
  # Load test data
  data <- load_test_data("paired_t_test")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test paired t-test
  if (exists("paired_t_test_analysis")) {
    result <- paired_t_test_analysis(data, names(data)[1], names(data)[2], alpha = 0.05)
    expect_true(is.list(result))
    expect_true("test_statistic" %in% names(result))
    expect_true("p_value" %in% names(result))
    expect_true("mean_difference" %in% names(result))
  }
  
  cat("  ✓ Paired T-Test: Dependent t-test tested\n")
})

# Test ANOVA Modules
test_that("ANOVA Modules", {
  # Load test data
  data <- load_test_data("anova")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test one-way ANOVA
  if (exists("anova_analysis")) {
    result <- anova_analysis(data, names(data)[1], names(data)[2], alpha = 0.05)
    expect_true(is.list(result))
    expect_true("f_statistic" %in% names(result))
    expect_true("p_value" %in% names(result))
    expect_true("effect_size" %in% names(result))
  }
  
  # Test two-way ANOVA
  if (exists("two_way_anova_analysis")) {
    result <- two_way_anova_analysis(data, names(data)[1], names(data)[2], names(data)[3], alpha = 0.05)
    expect_true(is.list(result))
    expect_true("main_effects" %in% names(result))
    expect_true("interaction" %in% names(result))
  }
  
  # Test repeated measures ANOVA
  if (exists("repeated_measures_anova_analysis")) {
    result <- repeated_measures_anova_analysis(data, names(data)[1], names(data)[2], alpha = 0.05)
    expect_true(is.list(result))
    expect_true("f_statistic" %in% names(result))
    expect_true("p_value" %in% names(result))
  }
  
  cat("  ✓ ANOVA Modules: One-way, two-way, and repeated measures tested\n")
})

# Test Nonparametric Tests
test_that("Nonparametric Test Modules", {
  # Load test data
  data <- load_test_data("kruskal_wallis")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  
  # Test Kruskal-Wallis test
  if (exists("kruskal_wallis_analysis")) {
    result <- kruskal_wallis_analysis(data, names(data)[1], names(data)[2], alpha = 0.05)
    expect_true(is.list(result))
    expect_true("test_statistic" %in% names(result))
    expect_true("p_value" %in% names(result))
  }
  
  # Test Mann-Whitney U test
  if (exists("mann_whitney_analysis")) {
    result <- mann_whitney_analysis(data, names(data)[1], names(data)[2], alpha = 0.05)
    expect_true(is.list(result))
    expect_true("test_statistic" %in% names(result))
    expect_true("p_value" %in% names(result))
  }
  
  # Test Wilcoxon signed-rank test
  if (exists("wilcoxon_analysis")) {
    result <- wilcoxon_analysis(data, names(data)[1], names(data)[2], alpha = 0.05)
    expect_true(is.list(result))
    expect_true("test_statistic" %in% names(result))
    expect_true("p_value" %in% names(result))
  }
  
  # Test Friedman test
  if (exists("friedman_analysis")) {
    result <- friedman_analysis(data, names(data)[1], names(data)[2], alpha = 0.05)
    expect_true(is.list(result))
    expect_true("test_statistic" %in% names(result))
    expect_true("p_value" %in% names(result))
  }
  
  cat("  ✓ Nonparametric Tests: Kruskal-Wallis, Mann-Whitney, Wilcoxon, Friedman tested\n")
})

# Test Categorical Tests
test_that("Categorical Test Modules", {
  # Load test data
  data <- load_test_data("chi_square")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  
  # Test Chi-square test
  if (exists("chi_square_analysis")) {
    result <- chi_square_analysis(data, names(data)[1], names(data)[2], alpha = 0.05)
    expect_true(is.list(result))
    expect_true("test_statistic" %in% names(result))
    expect_true("p_value" %in% names(result))
    expect_true("expected_frequencies" %in% names(result))
  }
  
  # Test McNemar's test
  if (exists("mcnemar_analysis")) {
    result <- mcnemar_analysis(data, names(data)[1], names(data)[2], alpha = 0.05)
    expect_true(is.list(result))
    expect_true("test_statistic" %in% names(result))
    expect_true("p_value" %in% names(result))
  }
  
  # Test Cochran's Q test
  if (exists("cochrans_q_analysis")) {
    result <- cochrans_q_analysis(data, names(data)[1], names(data)[2:min(4, ncol(data))], alpha = 0.05)
    expect_true(is.list(result))
    expect_true("test_statistic" %in% names(result))
    expect_true("p_value" %in% names(result))
  }
  
  cat("  ✓ Categorical Tests: Chi-square, McNemar, Cochran's Q tested\n")
})

# Test Proportion Tests
test_that("Proportion Tests Module", {
  # Load test data
  data <- load_test_data("proportion_tests")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  
  # Test proportion tests
  if (exists("proportion_tests_analysis")) {
    result <- proportion_tests_analysis(data, names(data)[1], p0 = 0.5, alpha = 0.05)
    expect_true(is.list(result))
    expect_true("test_statistic" %in% names(result))
    expect_true("p_value" %in% names(result))
    expect_true("confidence_interval" %in% names(result))
  }
  
  cat("  ✓ Proportion Tests: One-sample proportion test tested\n")
})

# Test Levene's Test
test_that("Levene's Test Module", {
  # Load test data
  data <- load_test_data("levene")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  
  # Test Levene's test
  if (exists("levene_analysis")) {
    result <- levene_analysis(data, names(data)[1], names(data)[2], alpha = 0.05)
    expect_true(is.list(result))
    expect_true("test_statistic" %in% names(result))
    expect_true("p_value" %in% names(result))
  }
  
  cat("  ✓ Levene's Test: Homogeneity of variance test tested\n")
})

# Test Post Hoc Tests
test_that("Post Hoc Tests Module", {
  # Load test data
  data <- load_test_data("post_hoc")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  
  # Test post hoc tests
  if (exists("post_hoc_analysis")) {
    result <- post_hoc_analysis(data, names(data)[1], names(data)[2], method = "tukey", alpha = 0.05)
    expect_true(is.list(result))
    expect_true("pairwise_comparisons" %in% names(result))
    expect_true("adjusted_p_values" %in% names(result))
  }
  
  cat("  ✓ Post Hoc Tests: Multiple comparison tests tested\n")
})

# Test Bayesian Tests
test_that("Bayesian Test Modules", {
  # Load test data
  data <- load_test_data("bayesian")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  
  # Test Bayesian t-test
  if (exists("bayesian_analysis")) {
    result <- bayesian_analysis(data, names(data)[1], names(data)[2], test_type = "Bayesian t-test")
    expect_true(is.list(result))
    expect_true("bayes_factor" %in% names(result))
    expect_true("posterior_probabilities" %in% names(result))
  }
  
  # Test Bayesian model comparison
  if (exists("bayesian_model_comparison_analysis")) {
    result <- bayesian_model_comparison_analysis(data, names(data)[1], names(data)[2:min(4, ncol(data))])
    expect_true(is.list(result))
    expect_true("comparison_table" %in% names(result))
    expect_true("model_weights" %in% names(result))
  }
  
  cat("  ✓ Bayesian Tests: Bayesian t-test and model comparison tested\n")
})

# Test Custom Test Module
test_that("Custom Test Module", {
  # Load test data
  data <- load_test_data("custom_test")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  
  # Test custom test
  if (exists("custom_test_analysis")) {
    result <- custom_test_analysis(data, names(data)[1], names(data)[2], formula_str = "y ~ x", test_type = "regression")
    expect_true(is.list(result))
    expect_true("test_statistics" %in% names(result))
    expect_true("model_summary" %in% names(result))
  }
  
  cat("  ✓ Custom Test: Custom statistical test tested\n")
})

# Test Power Analysis Module
test_that("Power Analysis Module", {
  # Load test data
  data <- load_test_data("power_analysis")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  
  # Test power analysis
  if (exists("power_analysis_analysis")) {
    result <- power_analysis_analysis(test_type = "t.test", n = 30, effect_size = 0.5, alpha = 0.05)
    expect_true(is.list(result))
    expect_true("power" %in% names(result))
    expect_true("effect_size" %in% names(result))
  }
  
  cat("  ✓ Power Analysis: Statistical power calculations tested\n")
})

cat("Inference Test Modules: All tests completed\n") 