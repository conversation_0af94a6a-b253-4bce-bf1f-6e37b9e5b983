# Paired t-test Server
source("modules/calculations/paired_t_test.R")
PairedTTestServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    pairedTUploadData <- eventReactive(input$pairedTUserData, {
      handle_file_upload(input$pairedTUserData)
    })
    observeEvent(pairedTUploadData(), {
      data <- pairedTUploadData()
      updateSelectizeInput(session, 'pairedTX', choices = names(data), server = TRUE)
      updateSelectizeInput(session, 'pairedTY', choices = names(data), server = TRUE)
      output$pairedTResults <- renderUI({
        if (!is.null(data) && is.data.frame(data)) {
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('pairedTPreviewTable'))
          )
        } else NULL
      })
      output$pairedTPreviewTable <- DT::renderDT({
        head(data, 20)
      })
    })
    pairedTValidationErrors <- reactive({
      errors <- c()
      data <- pairedTUploadData()
      xvar <- input$pairedTX
      yvar <- input$pairedTY
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(xvar) || !(xvar %in% names(data))) {
        errors <- c(errors, "Please select a valid first measurement column.")
      } else if (!is.numeric(data[[xvar]])) {
        errors <- c(errors, sprintf("Column '%s' must be numeric.", xvar))
      }
      if (is.null(yvar) || !(yvar %in% names(data))) {
        errors <- c(errors, "Please select a valid second measurement column.")
      } else if (!is.numeric(data[[yvar]])) {
        errors <- c(errors, sprintf("Column '%s' must be numeric.", yvar))
      }
      if (!is.null(xvar) && !is.null(yvar) && length(data[[xvar]]) != length(data[[yvar]])) {
        errors <- c(errors, "Both columns must have the same number of observations.")
      }
      errors
    })
    observeEvent(input$goPairedT, {
      output$pairedTResults <- renderUI({
        errors <- pairedTValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Paired t-test", errors = errors)
        } else {
          data <- pairedTUploadData()
          x <- data[[input$pairedTX]]
          y <- data[[input$pairedTY]]
          res <- calc_paired_t_test(x, y)
          
          tagList(
            tabsetPanel(
              id = ns("pairedTTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("pairedTAnalysis"),
                title = "Analysis",
                titlePanel("Paired t-test Results"),
                br(),
                withMathJax(tagList(
                  h4('Hypotheses'),
                  p('$H_0: \\mu_D = 0$'),
                  p('$H_A: \\mu_D \\neq 0$'),
                  h4('Test Statistic'),
                  p('$t = \\frac{\\bar{d}}{s_d/\\sqrt{n}}$'),
                  p('$t = $', signif(res$statistic, 4)),
                  h4('P-value Method'),
                  p('$P = $', signif(res$p.value, 4)),
                  h4('Critical Value Method'),
                  p('$t^* = $', signif(qt(0.975, df = res$parameter), 4)),
                  h4('Conclusion'),
                  if (res$p.value < 0.05) {
                    p('Since $P < 0.05$, reject $H_0$.')
                  } else {
                    p('Since $P \\geq 0.05$, do not reject $H_0$.')
                  }
                )),
                br(),
                h4("Effect Size"),
                uiOutput(ns('pairedTEffectSize')),
                br(),
                h4("Assumptions Check"),
                uiOutput(ns('pairedTAssumptions'))
              ),
              tabPanel(
                id = ns("pairedTDataSummary"),
                title = "Data Summary",
                h4("Descriptive Statistics"),
                tableOutput(ns('pairedTDescriptive')),
                br(),
                h4("Differences (X - Y)"),
                tableOutput(ns('pairedTDifferences')),
                br(),
                h4("Normality Test of Differences"),
                tableOutput(ns('pairedTNormality'))
              ),
              tabPanel(
                id = ns("pairedTUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('pairedTDataTable'))
              )
            )
          )
        }
      })
    })
    
    # Additional outputs for the enhanced tabs
    output$pairedTEffectSize <- renderUI({
      data <- pairedTUploadData()
      x <- data[[input$pairedTX]]
      y <- data[[input$pairedTY]]
      res <- calc_paired_t_test(x, y)
      
      # Calculate Cohen's d effect size
      d <- mean(x - y) / sd(x - y)
      
      tagList(
        p("Cohen's d effect size: ", round(d, 4)),
        p("Effect size interpretation:"),
        if (abs(d) < 0.2) {
          p("Small effect size")
        } else if (abs(d) < 0.5) {
          p("Medium effect size")
        } else {
          p("Large effect size")
        }
      )
    })
    
    output$pairedTAssumptions <- renderUI({
      tagList(
        h5("Key Assumptions:"),
        p("1. Paired observations"),
        p("2. Normal distribution of differences (approximately)"),
        p("3. Independent pairs"),
        br(),
        p("Note: These assumptions should be checked with your data.")
      )
    })
    
    output$pairedTDescriptive <- renderTable({
      data <- pairedTUploadData()
      x <- data[[input$pairedTX]]
      y <- data[[input$pairedTY]]
      
      desc <- data.frame(
        Variable = c("X", "Y", "Difference (X-Y)"),
        Mean = c(mean(x), mean(y), mean(x - y)),
        SD = c(sd(x), sd(y), sd(x - y)),
        N = c(length(x), length(y), length(x))
      )
      desc
    }, digits = 4)
    
    output$pairedTDifferences <- renderTable({
      data <- pairedTUploadData()
      x <- data[[input$pairedTX]]
      y <- data[[input$pairedTY]]
      diff <- x - y
      
      diff_stats <- data.frame(
        Statistic = c("Mean Difference", "SD of Differences", "SE of Mean Difference", "95% CI Lower", "95% CI Upper"),
        Value = c(
          mean(diff),
          sd(diff),
          sd(diff) / sqrt(length(diff)),
          mean(diff) - qt(0.975, df = length(diff) - 1) * sd(diff) / sqrt(length(diff)),
          mean(diff) + qt(0.975, df = length(diff) - 1) * sd(diff) / sqrt(length(diff))
        )
      )
      diff_stats
    }, digits = 4)
    
    output$pairedTNormality <- renderTable({
      # Placeholder for normality test results
      data.frame(
        Test = c("Shapiro-Wilk"),
        Statistic = c("N/A"),
        P_value = c("N/A"),
        stringsAsFactors = FALSE
      )
    })
    
    output$pairedTDataTable <- DT::renderDT({
      req(pairedTUploadData())
      DT::datatable(pairedTUploadData(),
        options = list(pageLength = 25,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(pairedTUploadData())))))
    })
  })
} 