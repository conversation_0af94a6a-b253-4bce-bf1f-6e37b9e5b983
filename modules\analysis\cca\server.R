CCAServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    ccaData <- eventReactive(input$ccaUserData, {
      handle_file_upload(input$ccaUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(ccaData(), {
      data <- ccaData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'ccaXVars', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'ccaYVars', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    ccaValidationErrors <- reactive({
      errors <- c()
      data <- ccaData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$ccaXVars) || length(input$ccaXVars) < 2) {
        errors <- c(errors, "Select at least two X variables.")
      }
      if (is.null(input$ccaYVars) || length(input$ccaYVars) < 2) {
        errors <- c(errors, "Select at least two Y variables.")
      }
      
      # Check if selected variables are numeric
      all_vars <- c(input$ccaXVars, input$ccaYVars)
      for (var in all_vars) {
        if (!is.numeric(data[[var]])) {
          errors <- c(errors, sprintf("Variable '%s' must be numeric.", var))
        }
      }
      
      # Check for sufficient observations
      if (nrow(data) < 10) {
        errors <- c(errors, "CCA requires at least 10 observations.")
      }
      
      errors
    })
    
    # CCA analysis reactive
    ccaResult <- eventReactive(input$goCCA, {
      data <- ccaData()
      req(data, input$ccaXVars, input$ccaYVars)
      
      # Remove rows with missing values
      complete_data <- data[complete.cases(data[, c(input$ccaXVars, input$ccaYVars)]), ]
      
      if (nrow(complete_data) < 10) {
        stop("Insufficient complete cases for CCA analysis.")
      }
      
      # Perform CCA analysis
      cca_analysis(complete_data, input$ccaXVars, input$ccaYVars)
    })
    
    # Error handling
    output$ccaError <- renderUI({
      errors <- ccaValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          ccaResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "CCA Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$ccaModelSummary <- renderUI({
      req(ccaResult())
      res <- ccaResult()
      
      tagList(
        h4("Canonical Correlation Analysis Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Metric = c("Number of X Variables", "Number of Y Variables", "Observations", "Number of Canonical Variates"),
            Value = c(
              res$n_x_vars,
              res$n_y_vars,
              res$n_obs,
              res$n_canonical_variates
            )
          )
        }),
        h4("Canonical Correlations"),
        renderTable({
          data.frame(
            Variate = paste("CV", 1:length(res$canonical_correlations)),
            Correlation = round(res$canonical_correlations, 4),
            Squared_Correlation = round(res$canonical_correlations^2, 4),
            Cumulative_Variance = round(cumsum(res$canonical_correlations^2), 4)
          )
        }),
        h4("Significance Tests"),
        renderTable({
          res$significance_tests
        })
      )
    })
    
    output$ccaPlot <- renderPlot({
      req(ccaResult())
      res <- ccaResult()
      
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # Canonical correlations plot
      plot(res$canonical_correlations, type = "b", main = "Canonical Correlations",
           xlab = "Canonical Variate", ylab = "Correlation", pch = 19, col = "blue")
      abline(h = 0.3, col = "red", lty = 2)
      
      # Biplot
      if (!is.null(res$fit)) {
        biplot(res$fit, main = "CCA Biplot")
      }
      
      # Structure coefficients for X variables
      if (!is.null(res$x_structure_coefficients)) {
        heatmap(res$x_structure_coefficients, main = "X Variables Structure Coefficients",
                Rowv = NA, Colv = NA, scale = "none")
      }
      
      # Structure coefficients for Y variables
      if (!is.null(res$y_structure_coefficients)) {
        heatmap(res$y_structure_coefficients, main = "Y Variables Structure Coefficients",
                Rowv = NA, Colv = NA, scale = "none")
      }
      
      par(mfrow = c(1, 1))
    })
    
    output$ccaDiagnostics <- renderUI({
      req(ccaResult())
      res <- ccaResult()
      
      tagList(
        h4("CCA Diagnostics"),
        renderTable({
          data.frame(
            Metric = c("Bartlett's Test Statistic", "Bartlett's Test DF", "Bartlett's Test P-value", "Wilks' Lambda"),
            Value = c(
              round(res$bartlett_statistic, 3),
              res$bartlett_df,
              round(res$bartlett_p_value, 4),
              round(res$wilks_lambda, 4)
            )
          )
        }),
        h4("Canonical Coefficients (X Variables)"),
        renderTable({
          res$x_canonical_coefficients
        }),
        h4("Canonical Coefficients (Y Variables)"),
        renderTable({
          res$y_canonical_coefficients
        })
      )
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$ccaDataSummary <- renderUI({
      req(ccaData(), input$ccaXVars, input$ccaYVars)
      data <- ccaData()
      x_vars <- input$ccaXVars
      y_vars <- input$ccaYVars
      
      tagList(
        h4("Dataset Summary"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "X Variables", "Y Variables", "Complete Cases"),
            Value = c(
              nrow(data),
              ncol(data),
              length(x_vars),
              length(y_vars),
              sum(complete.cases(data[, c(x_vars, y_vars)]))
            )
          )
        }),
        h4("Variable Summary"),
        renderTable({
          all_vars <- c(x_vars, y_vars)
          data.frame(
            Variable = all_vars,
            Set = c(rep("X", length(x_vars)), rep("Y", length(y_vars))),
            Mean = sapply(all_vars, function(v) round(mean(data[[v]], na.rm = TRUE), 4)),
            SD = sapply(all_vars, function(v) round(sd(data[[v]], na.rm = TRUE), 4)),
            Missing = sapply(all_vars, function(v) sum(is.na(data[[v]]))),
            stringsAsFactors = FALSE
          )
        })
      )
    })
    
    output$ccaAssumptions <- renderUI({
      req(ccaResult())
      res <- ccaResult()
      
      tagList(
        h4("CCA Assumptions Check"),
        renderTable({
          data.frame(
            Assumption = c("Multivariate Normality", "Linear Relationships", "No Multicollinearity", "Adequate Sample Size"),
            Status = c(
              ifelse(res$multivariate_normality_test > 0.05, "Pass", "Fail"),
              "Pass",
              ifelse(res$vif_max < 10, "Pass", "Fail"),
              ifelse(res$n_obs >= 10, "Pass", "Fail")
            ),
            Description = c(
              "Variables follow multivariate normal distribution",
              "Linear relationships between variable sets",
              "No severe multicollinearity within sets",
              "Sufficient observations for stable estimates"
            )
          )
        }),
        h4("Model Interpretation Guidelines"),
        renderTable({
          data.frame(
            Correlation_Level = c("0.9 - 1.0", "0.7 - 0.9", "0.5 - 0.7", "0.3 - 0.5", "< 0.3"),
            Interpretation = c(
              "Very strong relationship",
              "Strong relationship",
              "Moderate relationship",
              "Weak relationship",
              "Very weak relationship"
            )
          )
        })
      )
    })
    
    output$ccaDiagnosticPlots <- renderPlot({
      req(ccaResult())
      res <- ccaResult()
      
      par(mfrow = c(2, 2))
      
      # Scree plot of canonical correlations
      plot(res$canonical_correlations^2, type = "b", main = "Scree Plot of Squared Canonical Correlations",
           xlab = "Canonical Variate", ylab = "Squared Correlation", pch = 19, col = "red")
      
      # Cumulative variance explained
      cum_var <- cumsum(res$canonical_correlations^2)
      plot(cum_var, type = "b", main = "Cumulative Variance Explained",
           xlab = "Canonical Variate", ylab = "Cumulative Variance", pch = 19, col = "green")
      abline(h = 0.8, col = "red", lty = 2)
      
      # Q-Q plot of canonical variates
      if (!is.null(res$canonical_variates)) {
        qqnorm(res$canonical_variates[, 1], main = "Q-Q Plot of First Canonical Variate")
        qqline(res$canonical_variates[, 1], col = "red")
      }
      
      # Correlation matrix heatmap
      if (!is.null(res$correlation_matrix)) {
        heatmap(res$correlation_matrix, main = "Correlation Matrix",
                Rowv = NA, Colv = NA, scale = "none")
      }
      
      par(mfrow = c(1, 1))
    })
    
    # Uploaded Data Tab Outputs
    output$ccaDataTable <- renderDT({
      req(ccaData())
      data <- ccaData()
      
      DT::datatable(
        data,
        options = list(
          pageLength = 10,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
        ),
        extensions = 'Buttons',
        filter = 'top',
        rownames = FALSE
      )
    })
    
    output$ccaDataInfo <- renderUI({
      req(ccaData())
      data <- ccaData()
      
      tagList(
        h4("Dataset Information"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "File Name"),
            Value = c(nrow(data), ncol(data), 
                     ifelse(!is.null(input$ccaUserData), input$ccaUserData$name, "Unknown"))
          )
        }),
        h4("Variable Information"),
        renderTable({
          data.frame(
            Variable = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Unique = sapply(data, function(x) length(unique(x)))
          )
        })
      )
    })
  })
} 