MANOVAServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    manovaData <- eventReactive(input$manovaUserData, {
      handle_file_upload(input$manovaUserData)
    })
    
    observeEvent(manovaData(), {
      data <- manovaData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'manovaResponses', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'manovaGroup', choices = names(data), server = TRUE)
      }
    })
    
    manovaValidationErrors <- reactive({
      errors <- c()
      data <- manovaData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$manovaResponses) || length(input$manovaResponses) < 2) {
        errors <- c(errors, "Select at least two response variables.")
      }
      if (is.null(input$manovaGroup)) {
        errors <- c(errors, "Select a group variable.")
      }
      for (var in input$manovaResponses) {
        if (!is.numeric(data[[var]])) {
          errors <- c(errors, sprintf("Response variable '%s' must be numeric.", var))
        }
      }
      errors
    })
    
    manovaResult <- eventReactive(input$goMANOVA, {
      data <- manovaData()
      req(data, input$manovaResponses, input$manovaGroup)
      
      manova_analysis(data, input$manovaResponses, input$manovaGroup)
    })
    
    observeEvent(input$goMANOVA, {
      output$manovaResultsUI <- renderUI({
        errors <- manovaValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in MANOVA", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("manovaTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("manovaAnalysis"),
                title = "Analysis",
                titlePanel("MANOVA Results"),
                br(),
                h4("Multivariate Test Results"),
                tableOutput(ns('manovaMultivariate')),
                h4("Univariate Test Results"),
                tableOutput(ns('manovaUnivariate')),
                h4("Descriptive Statistics"),
                tableOutput(ns('manovaDescriptive')),
                h4("Effect Sizes"),
                tableOutput(ns('manovaEffectSizes')),
                h4("Box's M Test"),
                textOutput(ns('manovaBoxM')),
                h4("Levene's Tests"),
                tableOutput(ns('manovaLevene')),
                h4("Fit Statistics"),
                tableOutput(ns('manovaFitStats')),
                h4("Number of Groups"),
                textOutput(ns('manovaGroups')),
                h4("Number of Observations"),
                textOutput(ns('manovaObservations')),
                h4("Number of Response Variables"),
                textOutput(ns('manovaResponses')),
                h4("Effect Size Interpretation"),
                textOutput(ns('manovaEffectSizeInterpretation')),
                h4("Univariate Effect Sizes"),
                textOutput(ns('manovaUnivariateEffectSizes'))
              ),
              tabPanel(
                id = ns("manovaDiagnostics"),
                title = "Model Diagnostics",
                h4("Profile Plot (Means by Group)"),
                plotOutput(ns('manovaProfilePlot'), height = "400px")
              ),
              tabPanel(
                id = ns("manovaUploadedData"),
                title = "Uploaded Data",
                h4("Raw Uploaded Data"),
                DT::DTOutput(ns('manovaRawDataTable'))
              )
            )
          )
        }
      })
    })
    output$manovaRawDataTable <- DT::renderDT({
      req(manovaData())
      DT::datatable(manovaData(), options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), rownames = FALSE)
    })
  })
} 