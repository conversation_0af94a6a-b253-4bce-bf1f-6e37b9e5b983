# ROC Analysis calculation and output helpers

roc_analysis_uploadData_func <- function(rocUserData, response_var, predictor_var) {
  tryCatch(
    {
      if (is.null(rocUserData) || is.null(response_var) || is.null(predictor_var)) {
        return(NULL)
      }
      
      # Read data based on file type
      if (grepl("\\.csv$", rocUserData$name, ignore.case = TRUE)) {
        df <- read.csv(rocUserData$datapath, stringsAsFactors = TRUE)
      } else if (grepl("\\.tsv$", rocUserData$name, ignore.case = TRUE)) {
        df <- read.tsv(rocUserData$datapath, stringsAsFactors = TRUE)
      } else if (grepl("\\.xlsx$", rocUserData$name, ignore.case = TRUE)) {
        df <- readxl::read_excel(rocUserData$datapath)
      } else {
        stop("Unsupported file type. Please upload a CSV, TSV, or XLSX file.")
      }
      
      # Check for required columns
      required_cols <- c(response_var, predictor_var)
      if (!all(required_cols %in% names(df))) {
        missing_cols <- required_cols[!required_cols %in% names(df)]
        stop(paste("The following columns were not found in the uploaded file:", paste(missing_cols, collapse = ", ")))
      }
      
      df[[response_var]] <- as.factor(df[[response_var]])
      
      return(df)
    },
    error = function(e) {
      return(list(error = paste("Error reading data:", e$message)))
    }
  )
}

roc_analysis_results_func <- function(data, response_var, predictor_var) {
  tryCatch({
    if (is.null(data) || nrow(data) == 0) {
      stop("No data provided for analysis.")
    }
    
    if (!requireNamespace("pROC", quietly = TRUE)) {
      stop("Package 'pROC' is required for ROC analysis.")
    }
    
    response <- data[[response_var]]
    predictor <- data[[predictor_var]]
    
    roc_obj <- pROC::roc(response, predictor, quiet = TRUE)
    
    list(
      roc_obj = roc_obj,
      auc = pROC::auc(roc_obj),
      ci = pROC::ci.auc(roc_obj),
      coords = pROC::coords(roc_obj, "best", ret = c("threshold", "specificity", "sensitivity")),
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during ROC analysis:", e$message))
  })
}

roc_analysis_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  tagList(
    h4("ROC Analysis Results"),
    p(paste("AUC:", round(results$auc, 3)))
  )
}

roc_analysis_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  
  summary_df <- data.frame(
    Metric = c("AUC", "95% CI Lower", "95% CI Upper", "Optimal Threshold", "Sensitivity", "Specificity"),
    Value = c(
      round(results$auc, 4),
      round(results$ci[1], 4),
      round(results$ci[3], 4),
      round(results$coords$threshold, 4),
      round(results$coords$sensitivity, 4),
      round(results$coords$specificity, 4)
    )
  )
  
  renderTable(summary_df)
}

roc_analysis_plot <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  
  plot(results$roc_obj, print.auc = TRUE, print.thres = "best")
}