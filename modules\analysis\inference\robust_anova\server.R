robustAnovaServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactives
    raUploadData <- eventReactive(input$raUserData, {
      handle_file_upload(input$raUserData)
    })
    
    raStackedIsValid <- eventReactive({input$raResponse; input$raFactors}, {
      raStackedIsValid_func(input$raResponse, input$raFactors)
    })
    
    raResults <- reactive({
      data <- raUploadData()
      # Only run if the selected columns exist in the data
      valid_multi <- is.null(input$raMultiColumns) || all(input$raMultiColumns %in% names(data))
      valid_factors <- is.null(input$raFactors) || input$raFactors %in% names(data)
      valid_response <- is.null(input$raResponse) || input$raResponse %in% names(data)
      if (is.null(data) || (!valid_multi && !valid_factors && !valid_response)) {
        return(NULL)
      }
      raResults_func(
        TRUE,
        input$raFormat,
        input$raMultiColumns,
        data,
        input$raFactors,
        input$raResponse,
        input$raMethod
      )
    })
    
    # Validation errors
    raValidationErrors <- reactive({
      errors <- c()
      data <- raUploadData()
      
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (input$raFormat == "Multiple") {
        if (is.null(input$raMultiColumns) || length(input$raMultiColumns) < 2) {
          errors <- c(errors, "Please select at least two columns for multi-column analysis.")
        } else {
          for (col in input$raMultiColumns) {
            if (length(unique(data[[col]])) < 2) {
              errors <- c(errors, sprintf("Column '%s' must have at least two unique values.", col))
            }
            if (all(data[[col]] == data[[col]][1])) {
              errors <- c(errors, sprintf("All values in column '%s' are the same. There must be variance.", col))
            }
          }
        }
      } else if (input$raFormat == "Stacked") {
        if (is.null(input$raResponse) || input$raResponse == "") {
          errors <- c(errors, "Please select a response variable.")
        }
        if (is.null(input$raFactors) || input$raFactors == "") {
          errors <- c(errors, "Please select a factor variable.")
        }
        if (!is.null(input$raResponse) && !is.null(input$raFactors) && input$raResponse == input$raFactors) {
          errors <- c(errors, "Response and factor variables must be different.")
        }
        if (!is.null(input$raResponse) && !is.null(input$raFactors) &&
            length(unique(data[[input$raResponse]])) < 2) {
          errors <- c(errors, sprintf("Response variable '%s' must have at least two unique values.", input$raResponse))
        }
        if (!is.null(input$raFactors) && length(unique(data[[input$raFactors]])) < 2) {
          errors <- c(errors, sprintf("Factor variable '%s' must have at least two unique values.", input$raFactors))
        }
      }
      
      errors
    })
    
    # Outputs
    output$raResults <- renderUI({
      results <- raResults()
      if (is.null(results)) return(NULL)
      robustAnovaResults(raResults, reactive({input$raSigLvl}))
    })
    
    output$robustAnovaPlot <- renderPlot({
      results <- raResults()
      if (is.null(results)) return(NULL)
      robustAnovaPlot(raResults, reactive({input$raSigLvl}))
    })
    
    output$raConclusion <- renderUI({
      results <- raResults()
      if (is.null(results)) return(NULL)
      robustAnovaConclusion(raResults, reactive({input$raSigLvl}))
    })
    
    output$raDescriptiveOutput <- renderUI({
      results <- raResults()
      if (is.null(results)) return(NULL)
      robustAnovaDescriptive(raResults, reactive({input$raSigLvl}))
    })
    
    output$raDescriptiveTable <- DT::renderDT({
      results <- raResults()
      if (is.null(results) || is.null(results$descriptive)) return(NULL)
      
      DT::datatable(results$descriptive,
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(results$descriptive)))))
    })
    
    output$renderRAData <- renderUI({
      req(raUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("raInitialUploadTable")), style = "width: 75%"),
        br(),
        br()
      )
    })
    
    output$raInitialUploadTable <- DT::renderDT({
      req(raUploadData())
      DT::datatable(raUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(raUploadData())))))
    })
    
    # Update selectizeInput choices after file upload
    observeEvent(raUploadData(), {
      data <- raUploadData()
      
      # Clear selectizeInputs and main panel
      updateSelectizeInput(session, 'raResponse', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'raFactors', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'raMultiColumns', choices = character(0), selected = NULL, server = TRUE)
      output$robustAnovaResults <- renderUI({ NULL })
      
      # If data is valid, update choices and show preview
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'raResponse', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'raFactors', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'raMultiColumns', choices = names(data), server = TRUE)
        
        output$robustAnovaResults <- renderUI({
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('raPreviewTable'))
          )
        })
        
        output$raPreviewTable <- DT::renderDT({
          head(data, 20)
        })
      }
    })
    
    # Show main results or error screen when Calculate is pressed
    observeEvent(input$goRobustAnova, {
      output$robustAnovaResults <- renderUI({
        errors <- raValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Robust ANOVA", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("raTabset"),
              selected = "Analysis",
              tabPanel(
                id    = ns("ra"),
                title = "Analysis",
                titlePanel("Robust ANOVA Results"),
                br(),
                uiOutput(ns('raResults')),
                br(),
                plotOutput(ns('robustAnovaPlot'), width = "100%", height = "500px"),
                br(),
                uiOutput(ns('raConclusion'))
              ),
              tabPanel(
                id    = ns("raStats"),
                title = "Descriptive Statistics",
                DTOutput(ns("raDescriptiveTable")),
                uiOutput(ns("raDescriptiveOutput"))
              ),
              tabPanel(
                id    = ns("raData"),
                title = "Uploaded Data",
                uiOutput(ns("renderRAData"))
              )
            )
          )
        }
      })
    })
  })
} 