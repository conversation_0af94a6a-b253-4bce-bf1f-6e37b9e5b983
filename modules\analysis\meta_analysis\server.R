MetaAnalysisServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    metaData <- eventReactive(input$metaUserData, {
      handle_file_upload(input$metaUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(metaData(), {
      data <- metaData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'metaEffectSize', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'metaVariance', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'metaStudyID', choices = c("None", names(data)), server = TRUE)
      }
    })
    
    # Meta-analysis reactive
    metaAnalysis <- reactive({
      req(metaData(), input$metaEffectSize, input$metaVariance)
      data <- metaData()
      
      # Remove rows with missing values
      complete_data <- data[complete.cases(data[, c(input$metaEffectSize, input$metaVariance)]), ]
      
      if (nrow(complete_data) < 2) return(NULL)
      
      # Check if metafor package is available
      if (!requireNamespace("metafor", quietly = TRUE)) {
        return(NULL)
      }
      
      # Perform meta-analysis
      tryCatch({
        yi <- complete_data[[input$metaEffectSize]]
        vi <- complete_data[[input$metaVariance]]
        
        # Use REML method for random effects model
        res <- metafor::rma(yi = yi, vi = vi, method = "REML")
        
        # Calculate additional statistics
        summary_stats <- summary(res)
        
        # Heterogeneity statistics
        q_stat <- res$QE
        df_q <- res$k - 1
        p_q <- 1 - pchisq(q_stat, df_q)
        i2 <- res$I2
        tau2 <- res$tau2
        
        # Publication bias tests
        egger_test <- tryCatch({
          metafor::regtest(res)
        }, error = function(e) NULL)
        
        # Funnel plot data
        funnel_data <- data.frame(
          yi = yi,
          vi = vi,
          sei = sqrt(vi),
          study_id = if (!is.null(input$metaStudyID) && input$metaStudyID != "None") {
            complete_data[[input$metaStudyID]]
          } else {
            paste("Study", 1:length(yi))
          }
        )
        
        list(
          data = complete_data,
          fit = res,
          summary = summary_stats,
          heterogeneity = list(
            Q = q_stat,
            df = df_q,
            p_value = p_q,
            I2 = i2,
            tau2 = tau2
          ),
          egger_test = egger_test,
          funnel_data = funnel_data,
          effect_size_var = input$metaEffectSize,
          variance_var = input$metaVariance,
          study_id_var = if (!is.null(input$metaStudyID) && input$metaStudyID != "None") input$metaStudyID else NULL
        )
      }, error = function(e) {
        NULL
      })
    })
    
    # Validation errors reactive
    metaValidationErrors <- reactive({
      errors <- c()
      data <- metaData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$metaEffectSize) || input$metaEffectSize == "") {
        errors <- c(errors, "Please select an effect size variable.")
      }
      
      if (is.null(input$metaVariance) || input$metaVariance == "") {
        errors <- c(errors, "Please select a variance variable.")
      }
      
      if (!is.null(input$metaEffectSize) && !is.null(input$metaVariance) && 
          input$metaEffectSize != "" && input$metaVariance != "") {
        
        # Check if effect size and variance variables are different
        if (input$metaEffectSize == input$metaVariance) {
          errors <- c(errors, "Effect size and variance variables must be different.")
        }
        
        # Check if variables are numeric
        effect_size_var <- data[[input$metaEffectSize]]
        variance_var <- data[[input$metaVariance]]
        
        if (!is.numeric(effect_size_var)) {
          errors <- c(errors, "Effect size variable must be numeric.")
        }
        
        if (!is.numeric(variance_var)) {
          errors <- c(errors, "Variance variable must be numeric.")
        }
        
        # Check if variance values are positive
        if (any(variance_var <= 0, na.rm = TRUE)) {
          errors <- c(errors, "Variance values must be positive.")
        }
        
        # Check for sufficient studies
        n_studies <- nrow(data[complete.cases(data[, c(input$metaEffectSize, input$metaVariance)]), ])
        if (n_studies < 2) {
          errors <- c(errors, "At least 2 studies are required for meta-analysis.")
        }
      }
      
      errors
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goMeta, {
      output$metaResults <- renderUI({
        errors <- metaValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Meta-Analysis", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("metaTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("metaAnalysis"),
                title = "Analysis",
                titlePanel("Meta-Analysis Results"),
                br(),
                h4("Model Summary"),
                verbatimTextOutput(ns('metaModelSummary')),
                br(),
                h4("Effect Size Estimates"),
                tableOutput(ns('metaEffectEstimates')),
                br(),
                h4("Heterogeneity Analysis"),
                tableOutput(ns('metaHeterogeneity')),
                br(),
                h4("Publication Bias Test"),
                uiOutput(ns('metaPublicationBias'))
              ),
              tabPanel(
                id = ns("metaDiagnostics"),
                title = "Data Summary/Diagnostics",
                h4("Study Characteristics"),
                tableOutput(ns('metaStudySummary')),
                br(),
                h4("Effect Size Distribution"),
                tableOutput(ns('metaEffectSummary')),
                br(),
                h4("Assumptions Check"),
                uiOutput(ns('metaAssumptions')),
                br(),
                h4("Forest Plot"),
                plotOutput(ns('metaForestPlot'), height = "500px"),
                br(),
                h4("Funnel Plot"),
                plotOutput(ns('metaFunnelPlot'), height = "400px")
              ),
              tabPanel(
                id = ns("metaUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('metaDataTable'))
              )
            )
          )
        }
      })
    })
    
    # Model summary
    output$metaModelSummary <- renderPrint({
      req(metaAnalysis())
      analysis <- metaAnalysis()
      if (!is.null(analysis)) {
        print(analysis$summary)
      }
    })
    
    # Effect size estimates
    output$metaEffectEstimates <- renderTable({
      req(metaAnalysis())
      analysis <- metaAnalysis()
      if (!is.null(analysis)) {
        fit <- analysis$fit
        
        result <- data.frame(
          Statistic = c("Overall Effect Size", "Standard Error", "95% CI Lower", "95% CI Upper", "Z-value", "P-value"),
          Value = c(
            round(fit$b[1], 4),
            round(fit$se, 4),
            round(fit$ci.lb, 4),
            round(fit$ci.ub, 4),
            round(fit$zval, 4),
            round(fit$pval, 4)
          ),
          stringsAsFactors = FALSE
        )
        result
      }
    }, digits = 4)
    
    # Heterogeneity analysis
    output$metaHeterogeneity <- renderTable({
      req(metaAnalysis())
      analysis <- metaAnalysis()
      if (!is.null(analysis)) {
        het <- analysis$heterogeneity
        
        result <- data.frame(
          Statistic = c("Q-statistic", "Degrees of Freedom", "P-value", "I²", "τ²"),
          Value = c(
            round(het$Q, 4),
            het$df,
            round(het$p_value, 4),
            round(het$I2, 2),
            round(het$tau2, 4)
          ),
          stringsAsFactors = FALSE
        )
        result
      }
    }, digits = 4)
    
    # Publication bias test
    output$metaPublicationBias <- renderUI({
      req(metaAnalysis())
      analysis <- metaAnalysis()
      
      if (!is.null(analysis) && !is.null(analysis$egger_test)) {
        test <- analysis$egger_test
        
        tagList(
          h5("Egger's Test for Publication Bias:"),
          p(strong("Intercept: "), round(test$zval, 4)),
          p(strong("P-value: "), round(test$pval, 4)),
          p(strong("Interpretation: "), 
            if (test$pval < 0.05) "Significant publication bias detected" else "No significant publication bias detected")
        )
      } else {
        tagList(
          h5("Publication Bias Test:"),
          p("Egger's test could not be performed.")
        )
      }
    })
    
    # Study characteristics
    output$metaStudySummary <- renderTable({
      req(metaAnalysis())
      analysis <- metaAnalysis()
      if (!is.null(analysis)) {
        data <- analysis$data
        
        summary_stats <- data.frame(
          Statistic = c("Number of Studies", "Total Sample Size", "Mean Effect Size", "SD Effect Size", "Min Effect Size", "Max Effect Size"),
          Value = c(
            nrow(data),
            sum(data$n, na.rm = TRUE) %||% "N/A",
            round(mean(data[[analysis$effect_size_var]], na.rm = TRUE), 4),
            round(sd(data[[analysis$effect_size_var]], na.rm = TRUE), 4),
            round(min(data[[analysis$effect_size_var]], na.rm = TRUE), 4),
            round(max(data[[analysis$effect_size_var]], na.rm = TRUE), 4)
          ),
          stringsAsFactors = FALSE
        )
        summary_stats
      }
    }, digits = 4)
    
    # Effect size distribution
    output$metaEffectSummary <- renderTable({
      req(metaAnalysis())
      analysis <- metaAnalysis()
      if (!is.null(analysis)) {
        data <- analysis$data
        effect_sizes <- data[[analysis$effect_size_var]]
        
        # Create effect size categories
        effect_categories <- cut(effect_sizes, 
                               breaks = c(-Inf, -0.5, -0.2, 0.2, 0.5, Inf),
                               labels = c("Large Negative", "Medium Negative", "Small", "Medium Positive", "Large Positive"))
        
        category_table <- table(effect_categories)
        
        result <- data.frame(
          Category = names(category_table),
          Count = as.numeric(category_table),
          Percentage = round(as.numeric(category_table) / length(effect_sizes) * 100, 2),
          stringsAsFactors = FALSE
        )
        result
      }
    }, digits = 4)
    
    # Assumptions check
    output$metaAssumptions <- renderUI({
      req(metaAnalysis())
      analysis <- metaAnalysis()
      
      if (!is.null(analysis)) {
        data <- analysis$data
        effect_sizes <- data[[analysis$effect_size_var]]
        variances <- data[[analysis$variance_var]]
        
        # Check assumptions
        n_studies <- nrow(data)
        effect_range <- max(effect_sizes) - min(effect_sizes)
        variance_range <- max(variances) - min(variances)
        
        # Check for outliers using IQR method
        q1 <- quantile(effect_sizes, 0.25)
        q3 <- quantile(effect_sizes, 0.75)
        iqr <- q3 - q1
        outliers <- sum(effect_sizes < (q1 - 1.5 * iqr) | effect_sizes > (q3 + 1.5 * iqr))
        
        tagList(
          h5("Key Assumptions:"),
          p("1. Independent studies"),
          p("2. Similar study designs"),
          p("3. Comparable outcome measures"),
          p("4. No publication bias"),
          br(),
          p(strong("Number of studies: "), n_studies),
          p(strong("Effect size range: "), round(effect_range, 4)),
          p(strong("Variance range: "), round(variance_range, 4)),
          p(strong("Outliers detected: "), outliers),
          br(),
          p("Note: High heterogeneity (I² > 75%) suggests studies may not be comparable.")
        )
      }
    })
    
    # Forest plot
    output$metaForestPlot <- renderPlot({
      req(metaAnalysis())
      analysis <- metaAnalysis()
      
      if (!is.null(analysis)) {
        # Create forest plot
        metafor::forest(analysis$fit, 
                       main = "Forest Plot",
                       xlab = "Effect Size",
                       slab = analysis$funnel_data$study_id)
      }
    })
    
    # Funnel plot
    output$metaFunnelPlot <- renderPlot({
      req(metaAnalysis())
      analysis <- metaAnalysis()
      
      if (!is.null(analysis)) {
        # Create funnel plot
        metafor::funnel(analysis$fit, 
                       main = "Funnel Plot",
                       xlab = "Effect Size",
                       ylab = "Standard Error")
      }
    })
    
    # Data table
    output$metaDataTable <- DT::renderDT({
      req(metaData())
      DT::datatable(metaData(),
        options = list(pageLength = 25,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(metaData())))))
    })
  })
} 