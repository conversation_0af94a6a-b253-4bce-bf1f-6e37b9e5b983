# Gradient Boosting calculation and output helpers

gradient_boosting_uploadData_func <- function(gbUserData) {
  ext <- tools::file_ext(gbUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(gbUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(gbUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(gbUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(gbUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

gradient_boosting_results_func <- function(data, response_var, predictor_vars, nrounds = 100, eta = 0.1, objective = "binary:logistic") {
  tryCatch({
    if (!requireNamespace("xgboost", quietly = TRUE)) {
      stop("Package 'xgboost' is required for Gradient Boosting analysis.")
    }
    
    X <- as.matrix(data[, predictor_vars])
    y <- as.numeric(data[[response_var]])
    dtrain <- xgboost::xgb.DMatrix(data = X, label = y)
    
    model <- xgboost::xgb.train(
      data = dtrain,
      nrounds = nrounds,
      eta = eta,
      objective = objective,
      verbose = 0
    )
    
    list(
      model = model,
      data = data,
      predictor_vars = predictor_vars,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Gradient Boosting calculation:", e$message))
  })
}

gradient_boosting_ht_html <- function(results) {
  # No single hypothesis test, summary is more informative
  tagList(
    h4("Gradient Boosting Model"),
    p("See summary table for model diagnostics and feature importance plot.")
  )
}

gradient_boosting_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  imp <- xgboost::xgb.importance(model = results$model)
  out <- list(
    h4("Feature Importance"),
    renderDataTable({
      DT::datatable(imp, options = list(pageLength = 10, searching = FALSE))
    })
  )
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

gradient_boosting_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  xgboost::xgb.plot.importance(xgboost::xgb.importance(model = results$model), main = 'Feature Importance')
}