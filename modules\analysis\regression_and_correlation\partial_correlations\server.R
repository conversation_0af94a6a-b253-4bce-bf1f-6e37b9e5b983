# Partial and Semi-partial Correlations Server
# Correlations with control variables

source("modules/calculations/partial_correlations.R")

PartialCorrelationsServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactive values
    partial_data <- reactiveVal(NULL)
    partial_results <- reactiveVal(NULL)
    
    # File upload reactive
    partialUploadData <- eventReactive(input$partialUserData, {
      handle_file_upload(input$partialUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(partialUploadData(), {
      data <- partialUploadData()
      # Clear selectizeInputs and main panel
      updateSelectizeInput(session, 'partialVariable1', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'partialVariable2', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'partialControlVariables', choices = character(0), selected = NULL, server = TRUE)
      output$partialResults <- renderUI({ NULL })
      
      # If data is valid, update choices
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'partialVariable1', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'partialVariable2', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'partialControlVariables', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    partialValidationErrors <- reactive({
      errors <- c()
      
      if (input$partialDataMethod == "Manual Entry") {
        # Manual entry validation
        if (is.null(input$partialManualData) || input$partialManualData == "") {
          errors <- c(errors, "Manual data entry is required.")
        } else {
          # Basic validation for manual entry
          tryCatch({
            data <- parse_partial_data(input$partialManualData)
            if (nrow(data) < 10) {
              errors <- c(errors, "At least 10 observations are required for partial correlation analysis.")
            }
            if (ncol(data) < 2) {
              errors <- c(errors, "At least 2 variables are required for correlation analysis.")
            }
          }, error = function(e) {
            errors <- c(errors, "Invalid manual data format. Please check your input.")
          })
        }
      } else {
        # File upload validation
        data <- partialUploadData()
        if (is.null(data) || !is.data.frame(data)) {
          errors <- c(errors, "No data uploaded or file could not be read.")
          return(errors)
        }
        if (is.null(input$partialVariable1) || input$partialVariable1 == "") {
          errors <- c(errors, "Please select the first variable.")
        } else {
          var_data <- data[[input$partialVariable1]]
          if (!is.numeric(var_data)) {
            errors <- c(errors, "First variable must be numeric.")
          }
        }
        if (is.null(input$partialVariable2) || input$partialVariable2 == "") {
          errors <- c(errors, "Please select the second variable.")
        } else {
          var_data <- data[[input$partialVariable2]]
          if (!is.numeric(var_data)) {
            errors <- c(errors, "Second variable must be numeric.")
          }
        }
        if (length(na.omit(data[c(input$partialVariable1, input$partialVariable2)])) < 10) {
          errors <- c(errors, "At least 10 non-missing observations are required.")
        }
      }
      
      errors
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goPartial, {
      output$partialResults <- renderUI({
        errors <- partialValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Partial Correlation Analysis", errors = errors)
        } else {
          tryCatch({
            # Prepare data
            if (input$partialDataMethod == "Manual Entry") {
              # Parse manual entry data
              data <- parse_partial_data(input$partialManualData)
            } else {
              # Use uploaded data
              req(partialUploadData(), input$partialVariable1, input$partialVariable2)
              
              data <- partialUploadData()
              var1 <- input$partialVariable1
              var2 <- input$partialVariable2
              control_vars <- input$partialControlVariables
              
              # Select relevant columns
              if (is.null(control_vars) || length(control_vars) == 0) {
                data <- data[c(var1, var2)]
                names(data) <- c("Var1", "Var2")
              } else {
                data <- data[c(var1, var2, control_vars)]
                names(data) <- c("Var1", "Var2", paste0("Control", 1:length(control_vars)))
              }
            }
            
            # Remove rows with missing values
            complete_cases <- complete.cases(data)
            if (sum(complete_cases) < nrow(data)) {
              warning("Removing rows with missing values")
              data <- data[complete_cases, ]
            }
            
            # Check data structure
            if (nrow(data) < 10) {
              stop("At least 10 observations are required")
            }
            
            # Perform partial correlation analysis
            results <- perform_partial_correlation_analysis(data, 
                                                          partial_correlation = input$partialCorrelation,
                                                          semi_partial = input$partialSemiPartial,
                                                          significance_testing = input$partialSignificance,
                                                          effect_size = input$partialEffectSize,
                                                          conf_level = input$partialConfLevel)
            
            # Store results
            partial_results(results)
            
            # Display results
            tagList(
              h3("Partial and Semi-partial Correlation Results"),
              
              # Data summary
              h4("Data Summary"),
              renderTable({
                summary_table <- data.frame(
                  Statistic = c("Number of Variables", "Number of Observations", "Missing Values"),
                  Value = c(results$n_variables, results$n_observations, results$n_missing)
                )
                summary_table
              }, rownames = FALSE),
              
              br(),
              
              # Zero-order correlations
              h4("Zero-Order Correlations"),
              renderDataTable({
                zero_order_table <- results$zero_order_correlations
                datatable(zero_order_table, 
                         options = list(pageLength = 10, scrollX = TRUE),
                         rownames = FALSE) %>%
                  formatRound(columns = c("Correlation", "p_value", "CI_Lower", "CI_Upper"), digits = 4)
              }),
              
              br(),
              
              # Partial correlations
              if (!is.null(results$partial_correlations)) tagList(
                h4("Partial Correlations"),
                renderDataTable({
                  partial_table <- results$partial_correlations
                  datatable(partial_table, 
                           options = list(pageLength = 10, scrollX = TRUE),
                           rownames = FALSE) %>%
                    formatRound(columns = c("Partial_Correlation", "p_value", "CI_Lower", "CI_Upper"), digits = 4)
                }),
                br()
              ),
              
              # Semi-partial correlations
              if (!is.null(results$semi_partial_correlations)) tagList(
                h4("Semi-partial Correlations"),
                renderDataTable({
                  semi_partial_table <- results$semi_partial_correlations
                  datatable(semi_partial_table, 
                           options = list(pageLength = 10, scrollX = TRUE),
                           rownames = FALSE) %>%
                    formatRound(columns = c("Correlation", "p_value", "CI_Lower", "CI_Upper"), digits = 4)
                }),
                br()
              ),
              
              # Comparison table
              h4("Correlation Comparison"),
              renderDataTable({
                comparison_table <- results$correlation_comparison
                datatable(comparison_table, 
                         options = list(pageLength = 10, scrollX = TRUE),
                         rownames = FALSE) %>%
                  formatRound(columns = c("Zero_Order", "Partial", "Semi_Partial", "Difference"), digits = 4)
              }),
              
              br(),
              
              # Visualization
              h4("Correlation Visualization"),
              fluidRow(
                column(6,
                  renderPlot({
                    # Correlation comparison plot
                    if (!is.null(results$correlation_comparison)) {
                      comparison_data <- results$correlation_comparison
                      comparison_long <- tidyr::gather(comparison_data, Type, Correlation, Zero_Order, Partial, Semi_Partial)
                      
                      ggplot(comparison_long, aes(x = Type, y = Correlation, fill = Type)) +
                        geom_bar(stat = "identity") +
                        facet_wrap(~Variables) +
                        labs(title = "Correlation Comparison",
                             x = "Correlation Type", y = "Correlation Coefficient") +
                        theme_minimal() +
                        theme(axis.text.x = element_text(angle = 45, hjust = 1))
                    }
                  })
                ),
                column(6,
                  renderPlot({
                    # Scatter plot with control variables
                    if (ncol(results$data) > 2) {
                      # Create partial regression plot
                      var1 <- results$data$Var1
                      var2 <- results$data$Var2
                      control_data <- results$data[, 3:ncol(results$data), drop = FALSE]
                      
                      # Residuals from controlling for other variables
                      residuals1 <- residuals(lm(var1 ~ ., data = control_data))
                      residuals2 <- residuals(lm(var2 ~ ., data = control_data))
                      
                      ggplot(data.frame(Residuals1 = residuals1, Residuals2 = residuals2), 
                             aes(x = Residuals1, y = Residuals2)) +
                        geom_point(alpha = 0.7) +
                        geom_smooth(method = "lm", se = TRUE) +
                        labs(title = "Partial Regression Plot",
                             x = "Residuals of Var1", y = "Residuals of Var2") +
                        theme_minimal()
                    }
                  })
                )
              ),
              
              br(),
              
              # Educational content
              h4("Interpretation"),
              p(strong("Zero-Order Correlation:"), "Simple correlation between two variables without controlling for others."),
              p(strong("Partial Correlation:"), "Correlation between two variables after controlling for the effects of other variables."),
              p(strong("Semi-partial Correlation:"), "Correlation between one variable and the residuals of another after controlling for other variables."),
              p(strong("Control Variables:"), "Variables whose effects are removed to examine the unique relationship between the main variables.")
            )
          }, error = function(e) {
            errorScreenUI(title = "Error in Partial Correlation Analysis", errors = e$message)
          })
        }
      })
    })
    
    # Helper function to parse numeric input
    parse_partial_data <- function(input_text) {
      if (is.null(input_text) || input_text == "") {
        stop("Please enter data")
      }
      
      # Parse the input text into a data frame
      lines <- strsplit(input_text, "\n")[[1]]
      lines <- lines[lines != ""]
      
      if (length(lines) < 2) {
        stop("At least 2 lines required (header + data)")
      }
      
      # Parse header
      header <- strsplit(lines[1], ",")[[1]]
      header <- trimws(header)
      
      # Parse data
      data_lines <- lines[-1]
      data_matrix <- matrix(NA, nrow = length(data_lines), ncol = length(header))
      
      for (i in 1:length(data_lines)) {
        values <- strsplit(data_lines[i], ",")[[1]]
        values <- trimws(values)
        values <- as.numeric(values)
        
        if (length(values) != length(header)) {
          stop(paste("Line", i + 1, "has incorrect number of values"))
        }
        
        data_matrix[i, ] <- values
      }
      
      data.frame(data_matrix, stringsAsFactors = FALSE)
    }
  })
} 