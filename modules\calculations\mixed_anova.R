# Mixed ANOVA calculation and output helpers

mixed_anova_uploadData_func <- function(mixedAnovaUserData) {
  ext <- tools::file_ext(mixedAnovaUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(mixedAnovaUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(mixedAnovaUserData$datapath),
         xlsx = readxl::read_xlsx(mixedAnovaUserData$datapath),
         txt = readr::read_tsv(mixedAnovaUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

mixed_anova_results_func <- function(data, subject_var, dv_var, between_var, within_var) {
  tryCatch({
    if (!requireNamespace("afex", quietly = TRUE)) {
      stop("Package 'afex' needed for mixed ANOVA.")
    }
    
    data[[subject_var]] <- as.factor(data[[subject_var]])
    data[[between_var]] <- as.factor(data[[between_var]])
    data[[within_var]] <- as.factor(data[[within_var]])
    
    model <- afex::aov_ez(
      id = subject_var,
      dv = dv_var,
      data = data,
      between = between_var,
      within = within_var
    )
    
    list(
      model = model,
      data = data,
      subject_var = subject_var,
      dv_var = dv_var,
      between_var = between_var,
      within_var = within_var,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Mixed ANOVA calculation:", e$message))
  })
}

mixed_anova_ht_html <- function(results) {
  # No single hypothesis test, summary table is more informative
  tagList(
    h4("Mixed ANOVA Model"),
    p("See summary table for main effects and interactions.")
  )
}

mixed_anova_summary_html <- function(results) {
  anova_table <- anova(results$model)
  
  # Post-hoc tests if significant interactions
  post_hoc <- NULL
  if (any(anova_table$`Pr(>F)` < 0.05, na.rm = TRUE)) {
    if (requireNamespace("emmeans", quietly = TRUE)) {
      post_hoc <- emmeans::emmeans(results$model, specs = as.formula(paste("~", results$between_var, "|", results$within_var)))
    }
  }
  
  tagList(
    h4("ANOVA Table"),
    renderPrint(anova_table),
    if (!is.null(post_hoc)) {
      tagList(
        h4("Post-Hoc Tests (Estimated Marginal Means)"),
        renderPrint(summary(post_hoc, adjust = "tukey"))
      )
    }
  )
}

mixed_anova_plot <- function(results) {
  if (requireNamespace("emmeans", quietly = TRUE)) {
    emmeans::emmip(results$model, as.formula(paste("~", results$between_var, "|", results$within_var)))
  }
}