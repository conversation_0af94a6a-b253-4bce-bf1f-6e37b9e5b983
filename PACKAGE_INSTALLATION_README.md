# CougarStats Package Installation Guide

This guide will help you install all necessary R packages for the CougarStats application.

## Quick Start

### Windows Users
1. Double-click `install_packages.bat` or run it from Command Prompt
2. The script will automatically install all required packages

### Unix/Linux/Mac Users
1. Open Terminal
2. Navigate to the CougarStats directory
3. Run: `./install_packages.sh` or `bash install_packages.sh`

### Manual Installation
1. Open R or RStudio
2. Run: `source("install_all_packages.R")`

## What the Installation Script Does

The `install_all_packages.R` script will:

1. **Install Core Packages** (Shiny, visualization, data manipulation)
   - shiny, shinyjs, shinyWidgets, DT, plotly, leaflet
   - dplyr, readr, ggplot2, ggpubr, and more

2. **Install Analysis Packages** (Statistical analysis)
   - survival, psych, metafor, mice, mgcv, glmnet
   - brms, BayesFactor, mediation, MatchIt, and more

3. **Install Specialized Packages** (Text mining, spatial analysis)
   - tm, quanteda, wordcloud (NLP)
   - sp, gstat, spatstat (Spatial)
   - cmprsk (Competing risks - with fallback methods)

4. **Handle Problematic Packages**
   - Automatically tries multiple sources (CRAN, Bioconductor, GitHub)
   - Provides specific troubleshooting for failed packages
   - Gracefully handles missing packages

5. **Test Application Startup**
   - Verifies core packages load correctly
   - Tests if global.R can be sourced
   - Provides clear feedback on what works and what doesn't

## Package Categories

### Core Packages (CRAN)
Essential packages for the Shiny application interface and basic functionality:
- **Shiny ecosystem**: shiny, shinyjs, shinyWidgets, shinyMatrix, shinyvalidate
- **Visualization**: ggplot2, plotly, leaflet, ggpubr, ggResidpanel
- **Data handling**: dplyr, readr, readxl, writexl, DT
- **UI components**: bslib, shinythemes, colourpicker, datamods

### Analysis Packages (CRAN)
Statistical analysis and modeling packages:
- **Survival analysis**: survival, cmprsk
- **Regression**: MASS, car, robustbase, pscl
- **Machine learning**: randomForest, gbm, xgboost, caret
- **Bayesian**: brms, BayesFactor, bayestestR, rstanarm
- **Advanced stats**: psych, metafor, mice, mgcv, glmnet

### Specialized Packages
- **Text mining**: tm, quanteda, wordcloud, topicmodels
- **Spatial analysis**: sp, sf, gstat, spatstat
- **Time series**: forecast, seasonal
- **Network analysis**: igraph, networkD3, visNetwork

## Troubleshooting

### Common Issues

#### 1. Package Installation Fails
If a package fails to install:
- Check your internet connection
- Update R to the latest version
- Try installing manually: `install.packages("package_name")`

#### 2. cmprsk Package Issues
The `cmprsk` package is not available on CRAN. The script will try:
1. Bioconductor: `BiocManager::install('cmprsk')`
2. GitHub: `remotes::install_github('cran/cmprsk')`
3. Manual download from CRAN archive

If all fail, see `CMPRSK_INSTALLATION.md` for detailed instructions.

#### 3. R Not Found in PATH
**Windows**: 
- Reinstall R and check "Add R to PATH" during installation
- Or manually add R to your system PATH

**Unix/Linux/Mac**:
- Install R from your package manager or CRAN
- Ensure R is in your PATH: `which R`

#### 4. Permission Errors
**Windows**: Run Command Prompt as Administrator
**Unix/Linux/Mac**: Use `sudo` or install packages to user library

### Manual Package Installation

If the script fails, you can install packages manually:

```r
# Core packages
install.packages(c("shiny", "dplyr", "ggplot2", "plotly"))

# Analysis packages  
install.packages(c("survival", "psych", "metafor"))

# Special packages
if (!requireNamespace("BiocManager", quietly = TRUE))
    install.packages("BiocManager")
BiocManager::install("cmprsk")

# GitHub packages
if (!requireNamespace("remotes", quietly = TRUE))
    install.packages("remotes")
remotes::install_github("deepanshu88/shinyDarkmode")
remotes::install_github("rsquaredacademy/olsrr")
```

## Verification

After installation, verify everything works:

```r
# Test core functionality
library(shiny)
library(ggplot2)
library(dplyr)

# Test survival analysis
library(survival)
if (requireNamespace("cmprsk", quietly = TRUE)) {
    library(cmprsk)
    cat("Competing risks analysis available\n")
} else {
    cat("Competing risks analysis not available\n")
}

# Test application startup
source("global.R")
```

## Running CougarStats

Once all packages are installed:

1. **From R/RStudio**:
   ```r
   setwd("path/to/CougarStats")
   shiny::runApp()
   ```

2. **From Command Line**:
   ```bash
   Rscript -e "shiny::runApp()"
   ```

3. **From RStudio**: Open `server.R` and click "Run App"

## Package Dependencies

The script installs approximately 80+ packages. Key dependencies include:

- **R version**: 4.0.0 or higher recommended
- **System libraries**: May require additional system packages on Linux
- **Memory**: At least 4GB RAM recommended for large datasets
- **Disk space**: ~2GB for all packages

## Support

If you encounter issues:

1. Check the error messages in the installation output
2. Refer to `ERROR_HANDLING_README.md` for general troubleshooting
3. Check `CMPRSK_INSTALLATION.md` for competing risks specific issues
4. Ensure your R version is up to date
5. Try installing packages individually to isolate issues

## Package Sources

The script tries packages from multiple sources in this order:
1. **CRAN** (Comprehensive R Archive Network) - primary source
2. **Bioconductor** - for bioinformatics packages like cmprsk
3. **GitHub** - for packages not on CRAN (shinyDarkmode, olsrr)

This ensures maximum compatibility and availability of all required packages. 