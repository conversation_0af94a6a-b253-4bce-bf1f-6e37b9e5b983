# Discriminant Analysis calculation and output helpers

discriminant_uploadData_func <- function(daUserData) {
  ext <- tools::file_ext(daUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(daUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(daUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(daUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(daUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

discriminant_results_func <- function(data, group_var, predictor_vars, method = "lda") {
  tryCatch({
    if (!requireNamespace("MASS", quietly = TRUE)) {
      stop("Package 'MASS' is required for discriminant analysis.")
    }
    
    formula <- as.formula(paste(group_var, "~", paste(predictor_vars, collapse = " + ")))
    
    if (method == "lda") {
      fit <- MASS::lda(formula, data = data)
    } else if (method == "qda") {
      fit <- MASS::qda(formula, data = data)
    } else {
      stop("Unknown method.")
    }
    
    list(
      fit = fit,
      data = data,
      group_var = group_var,
      predictor_vars = predictor_vars,
      method = method,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Discriminant Analysis calculation:", e$message))
  })
}

discriminant_ht_html <- function(results) {
  # No formal hypothesis test for discriminant analysis, summary is more informative
  tagList(
    h4("Discriminant Analysis"),
    p("See summary table for model details.")
  )
}

discriminant_summary_html <- function(results) {
  pred <- predict(results$fit)$class
  cm <- table(True = results$data[[results$group_var]], Pred = pred)
  
  tagList(
    h4("Model Summary"),
    renderPrint(results$fit),
    h4("Confusion Matrix"),
    renderTable(cm)
  )
}

discriminant_plot <- function(results) {
  plot(results$fit)
}