SEMServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    semData <- eventReactive(input$semUserData, {
      handle_file_upload(input$semUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(semData(), {
      data <- semData()
      if (!is.null(data) && is.data.frame(data)) {
        # Update model specification with available variables
        updateTextAreaInput(session, 'semModel', 
                           value = paste("# Available variables:", paste(names(data), collapse = ", "), 
                                       "\n# Example model specification:",
                                       "\n# latent variable =~ indicator1 + indicator2 + indicator3",
                                       "\n# dependent ~ independent1 + independent2",
                                       "\n# mediator ~ independent",
                                       "\n# dependent ~ independent + mediator", sep = "\n"))
      }
    })
    
    # Validation errors reactive
    semValidationErrors <- reactive({
      errors <- c()
      data <- semData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$semModel) || input$semModel == "") {
        errors <- c(errors, "Please provide a model specification.")
      }
      if (nrow(data) < 50) {
        errors <- c(errors, "SEM requires at least 50 observations.")
      }
      if (ncol(data) < 3) {
        errors <- c(errors, "SEM requires at least 3 variables.")
      }
      errors
    })
    
    # SEM analysis reactive
    semResult <- eventReactive(input$goSEM, {
      data <- semData()
      req(data, input$semModel)
      
      # Check if lavaan package is available
      if (!requireNamespace("lavaan", quietly = TRUE)) {
        stop("Package 'lavaan' is required for SEM analysis.")
      }
      
      # Remove rows with missing values
      complete_data <- data[complete.cases(data), ]
      
      if (nrow(complete_data) < 50) {
        stop("Insufficient complete cases for SEM analysis.")
      }
      
      # Perform SEM analysis
      sem_model(complete_data, input$semModel)
    })
    
    # Error handling
    output$semError <- renderUI({
      errors <- semValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          semResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "SEM Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$semModelSummary <- renderUI({
      req(semResult())
      res <- semResult()
      
      tagList(
        h4("SEM Model Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Metric = c("Model Type", "Observations", "Parameters", "Degrees of Freedom", "Chi-square"),
            Value = c(
              "Structural Equation Model",
              res$n_obs,
              res$n_par,
              res$df,
              round(res$chi_square, 3)
            )
          )
        }),
        h4("Model Fit Indices"),
        renderTable({
          res$fit_indices
        }),
        h4("Parameter Estimates"),
        renderTable({
          res$parameter_estimates
        })
      )
    })
    
    output$semPlot <- renderPlot({
      req(semResult())
      res <- semResult()
      
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # Path diagram
      if (requireNamespace("semPlot", quietly = TRUE)) {
        semPlot::semPaths(res$fit, what = "std", layout = "tree", 
                         edge.label.cex = 1.2, main = "Standardized Path Diagram")
      }
      
      # Residual plot
      if (!is.null(res$residuals)) {
        plot(res$fitted, res$residuals, main = "Residuals vs Fitted",
             xlab = "Fitted Values", ylab = "Residuals")
        abline(h = 0, col = "red", lty = 2)
      }
      
      # Q-Q plot of residuals
      if (!is.null(res$residuals)) {
        qqnorm(res$residuals, main = "Q-Q Plot of Residuals")
        qqline(res$residuals, col = "red")
      }
      
      # Modification indices
      if (!is.null(res$modification_indices)) {
        top_mi <- head(res$modification_indices[order(-res$modification_indices$mi), ], 10)
        barplot(top_mi$mi, names.arg = top_mi$lhs, main = "Top Modification Indices",
                las = 2, cex.names = 0.7)
      }
      
      par(mfrow = c(1, 1))
    })
    
    output$semDiagnostics <- renderUI({
      req(semResult())
      res <- semResult()
      
      tagList(
        h4("Model Diagnostics"),
        renderTable({
          data.frame(
            Metric = c("RMSEA", "CFI", "TLI", "SRMR", "AIC", "BIC"),
            Value = c(
              round(res$rmsea, 4),
              round(res$cfi, 4),
              round(res$tli, 4),
              round(res$srmr, 4),
              round(res$aic, 3),
              round(res$bic, 3)
            ),
            Threshold = c("< 0.08", "> 0.90", "> 0.90", "< 0.08", "Lower", "Lower")
          )
        }),
        h4("Modification Indices"),
        renderTable({
          head(res$modification_indices[order(-res$modification_indices$mi), ], 10)
        })
      )
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$semDataSummary <- renderUI({
      req(semData())
      data <- semData()
      
      tagList(
        h4("Dataset Summary"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "Complete Cases", "Missing Cases", "Missing Percentage"),
            Value = c(
              nrow(data),
              ncol(data),
              sum(complete.cases(data)),
              sum(!complete.cases(data)),
              round(sum(!complete.cases(data))/nrow(data) * 100, 2)
            )
          )
        }),
        h4("Variable Summary"),
        renderTable({
          data.frame(
            Variable = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Mean = sapply(data, function(x) if(is.numeric(x)) round(mean(x, na.rm = TRUE), 3) else "N/A"),
            SD = sapply(data, function(x) if(is.numeric(x)) round(sd(x, na.rm = TRUE), 3) else "N/A")
          )
        })
      )
    })
    
    output$semAssumptions <- renderUI({
      req(semResult())
      res <- semResult()
      
      tagList(
        h4("SEM Assumptions Check"),
        renderTable({
          data.frame(
            Assumption = c("Multivariate Normality", "Linearity", "No Multicollinearity", "No Outliers"),
            Test_Statistic = c(
              round(res$multivariate_normality, 4),
              "N/A",
              round(res$vif_max, 4),
              res$n_outliers
            ),
            P_Value = c(
              round(res$normality_p, 4),
              "N/A",
              "N/A",
              "N/A"
            ),
            Decision = c(
              ifelse(res$normality_p > 0.05, "Pass", "Fail"),
              "Pass",
              ifelse(res$vif_max < 10, "Pass", "Fail"),
              ifelse(res$n_outliers == 0, "Pass", "Fail")
            )
          )
        }),
        h4("Correlation Matrix"),
        renderTable({
          res$correlation_matrix
        })
      )
    })
    
    output$semDiagnosticPlots <- renderPlot({
      req(semResult())
      res <- semResult()
      
      par(mfrow = c(2, 2))
      
      # Residuals vs Fitted
      if (!is.null(res$residuals)) {
        plot(res$fitted, res$residuals, main = "Residuals vs Fitted",
             xlab = "Fitted Values", ylab = "Residuals")
        abline(h = 0, col = "red", lty = 2)
      }
      
      # Histogram of residuals
      if (!is.null(res$residuals)) {
        hist(res$residuals, main = "Histogram of Residuals", 
             xlab = "Residuals", freq = FALSE)
        curve(dnorm(x, mean = mean(res$residuals, na.rm = TRUE), 
                    sd = sd(res$residuals, na.rm = TRUE)), 
              add = TRUE, col = "red")
      }
      
      # Q-Q plot
      if (!is.null(res$residuals)) {
        qqnorm(res$residuals, main = "Q-Q Plot of Residuals")
        qqline(res$residuals, col = "red")
      }
      
      # Leverage plot
      if (!is.null(res$leverage)) {
        plot(res$leverage, main = "Leverage Plot",
             xlab = "Observation", ylab = "Leverage")
        abline(h = 2 * mean(res$leverage, na.rm = TRUE), col = "red", lty = 2)
      }
      
      par(mfrow = c(1, 1))
    })
    
    # Uploaded Data Tab Outputs
    output$semDataTable <- renderDT({
      req(semData())
      data <- semData()
      
      DT::datatable(
        data,
        options = list(
          pageLength = 10,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
        ),
        extensions = 'Buttons',
        filter = 'top',
        rownames = FALSE
      )
    })
    
    output$semDataInfo <- renderUI({
      req(semData())
      data <- semData()
      
      tagList(
        h4("Dataset Information"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "File Name"),
            Value = c(nrow(data), ncol(data), 
                     ifelse(!is.null(input$semUserData), input$semUserData$name, "Unknown"))
          )
        }),
        h4("Variable Information"),
        renderTable({
          data.frame(
            Variable = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Unique = sapply(data, function(x) length(unique(x)))
          )
        })
      )
    })
  })
} 