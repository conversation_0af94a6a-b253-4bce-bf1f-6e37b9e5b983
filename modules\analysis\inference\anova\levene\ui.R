# Lev<PERSON>'s/<PERSON>'s Test UI
LeveneTestSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("leveneUserData"), "Upload your Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
    selectizeInput(ns("leveneResponse"), "Response Variable", choices = NULL),
    selectizeInput(ns("leveneGroup"), "Group/Factor Column", choices = NULL),
    radioButtons(ns("leveneTestType"), "Test Type", choices = c("Levene's Test", "Bartlett's Test"), selected = "<PERSON><PERSON>'s Test", inline = TRUE),
    radioButtons(ns("leveneSigLvl"), "Significance Level (alpha)", choices = c("10%", "5%", "1%"), selected = "5%", inline = TRUE),
    br(),
    actionButton(ns("goLevene"), label = "Calculate", class = "act-btn")
  )
}

LeveneTestMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('leveneResults'))
  )
}

LeveneTestUI <- function(id) {
  ns <- NS(id)
  tagList(
    LeveneTestSidebarUI(id),
    LeveneTestMainUI(id)
  )
} 