# Jonckheere-Terpstra Test calculation and output helpers

jonckheere_terpstra_uploadData_func <- function(jtUserData) {
  ext <- tools::file_ext(jtUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(jtUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(jtUserData$datapath),
         xlsx = readxl::read_xlsx(jtUserData$datapath),
         txt = readr::read_tsv(jtUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

jonckheere_terpstra_results_func <- function(data, response_var, factor_var, alternative = "two.sided") {
  tryCatch({
    response <- data[[response_var]]
    groups <- as.factor(data[[factor_var]])
    
    # Using the implementation from 'clinfun' package as it's more robust
    if (!requireNamespace("clinfun", quietly = TRUE)) {
      stop("Package 'clinfun' needed for Jonckheere-Terpstra test.")
    }
    
    test_result <- clinfun::jonckheere.test(response, groups, alternative = alternative)
    
    desc_stats <- data %>%
      dplyr::group_by_at(factor_var) %>%
      dplyr::summarise(
        N = n(),
        Mean = mean(.data[[response_var]]),
        SD = sd(.data[[response_var]]),
        Median = median(.data[[response_var]]),
        .groups = 'drop'
      )
      
    list(
      test = test_result,
      desc_stats = desc_stats,
      data = data,
      response_var = response_var,
      factor_var = factor_var,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Jonckheere-Terpstra test calculation:", e$message))
  })
}

jonckheere_terpstra_ht_html <- function(results, sigLvl) {
  test <- results$test
  p_value <- test$p.value
  
  conclusion <- if (p_value < sigLvl) {
    "Reject H0. There is a significant ordered difference among the group medians."
  } else {
    "Do not reject H0. There is no significant ordered difference among the group medians."
  }
  
  withMathJax(tagList(
    h4("Jonckheere-Terpstra Test for Ordered Alternatives"),
    p("$H_0$: The medians of the groups are equal."),
    p(sprintf("$H_A$: The medians of the groups are ordered (%s).", test$alternative)),
    p(sprintf("Test Statistic (JT): %.4f", test$statistic)),
    p(sprintf("P-value: %.4f", p_value)),
    p(strong("Conclusion: "), conclusion)
  ))
}

jonckheere_terpstra_summary_html <- function(results) {
  tagList(
    h4("Descriptive Statistics by Group"),
    renderTable(results$desc_stats, digits = 4)
  )
}

jonckheere_terpstra_plot <- function(results) {
  ggplot(results$data, aes(x = as.factor(.data[[results$factor_var]]), y = .data[[results$response_var]], fill = as.factor(.data[[results$factor_var]]))) +
    geom_boxplot(alpha = 0.7) +
    labs(title = "Boxplot of Response by Group",
         x = results$factor_var,
         y = results$response_var) +
    theme_minimal() +
    theme(legend.position = "none")
}