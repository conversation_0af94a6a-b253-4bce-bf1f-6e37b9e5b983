augmentedDickeyFullerServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactives
    adfUploadData <- eventReactive(input$adfUserData, {
      handle_file_upload(input$adfUserData)
    })
    
    adfResults <- reactive({
      data <- adfUploadData()
      if (is.null(data) || is.null(input$adfVariable) || input$adfVariable == "") {
        return(NULL)
      }
      
      values <- data[[input$adfVariable]]
      values <- values[!is.na(values)]
      
      if (length(values) < 4) return(NULL)
      
      list(values = values, n = length(values), type = input$adfType, lag = input$adfLag)
    })
    
    # Validation errors
    adfValidationErrors <- reactive({
      errors <- c()
      data <- adfUploadData()
      
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$adfVariable) || input$adfVariable == "") {
        errors <- c(errors, "Please select a variable.")
        return(errors)
      }
      
      values <- data[[input$adfVariable]]
      if (length(na.omit(values)) < 4) {
        errors <- c(errors, "At least 4 non-missing values are required.")
      }
      
      if (input$adfLag >= length(na.omit(values)) - 1) {
        errors <- c(errors, "Number of lags must be less than (n-1).")
      }
      
      errors
    })
    
    # Outputs
    output$adfHT <- renderUI({
      results <- adfResults()
      if (is.null(results)) return(NULL)
      augmentedDickeyFullerHT(adfResults, reactive({input$adfSigLvl}))
    })
    
    output$augmentedDickeyFullerPlot <- renderPlot({
      results <- adfResults()
      if (is.null(results)) return(NULL)
      augmentedDickeyFullerPlot(adfResults)
    })
    
    output$adfConclusionOutput <- renderUI({
      results <- adfResults()
      if (is.null(results)) return(NULL)
      adfConclusion(adfResults, reactive({input$adfSigLvl}))
    })
    
    output$renderADFData <- renderUI({
      req(adfUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("adfInitialUploadTable")), style = "width: 75%"),
        br(),
        br()
      )
    })
    
    output$adfInitialUploadTable <- DT::renderDT({
      req(adfUploadData())
      DT::datatable(adfUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(adfUploadData())))))
    })
    
    # Update selectizeInput choices after file upload
    observeEvent(adfUploadData(), {
      data <- adfUploadData()
      updateSelectizeInput(session, 'adfVariable', choices = character(0), selected = NULL, server = TRUE)
      output$augmentedDickeyFullerResults <- renderUI({ NULL })
      
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'adfVariable', choices = names(data), server = TRUE)
        output$augmentedDickeyFullerResults <- renderUI({
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('adfPreviewTable'))
          )
        })
        output$adfPreviewTable <- DT::renderDT({
          head(data, 20)
        })
      }
    })
    
    # Show main results or error screen when Calculate is pressed
    observeEvent(input$goInference, {
      output$augmentedDickeyFullerResults <- renderUI({
        errors <- adfValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Augmented Dickey-Fuller Test", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("adfTabset"),
              selected = "Analysis",
              tabPanel(
                id    = ns("adf"),
                title = "Analysis",
                titlePanel("Augmented Dickey-Fuller Test for Stationarity"),
                br(),
                uiOutput(ns('adfHT')),
                br(),
                plotOutput(ns('augmentedDickeyFullerPlot'), width = "50%", height = "400px"),
                br(),
                uiOutput(ns('adfConclusionOutput'))
              ),
              tabPanel(
                id    = ns("adfData"),
                title = "Uploaded Data",
                uiOutput(ns("renderADFData"))
              )
            )
          )
        }
      })
    })
  })
} 