# Automated Reporting logic for CougarStats

automatedReportsResults_func <- function(data, report_title = "CougarStats Report") {
  if (!requireNamespace("rmarkdown", quietly = TRUE)) {
    stop("Package 'rmarkdown' is required for automated reporting.")
  }
  # Create a temporary Rmd file
  rmd_file <- tempfile(fileext = ".Rmd")
  html_file <- tempfile(fileext = ".html")
  rmd_content <- paste0(
    "---\n",
    "title: '", report_title, "'\n",
    "output: html_document\n",
    "---\n\n",
    "## Data Preview\n\n",
    "```{r}\n",
    "head(data)\n",
    "```\n\n",
    "## Summary Statistics\n\n",
    "```{r}\n",
    "summary(data)\n",
    "```\n"
  )
  writeLines(rmd_content, rmd_file)
  # Render the report
  rmarkdown::render(rmd_file, output_file = html_file, params = list(data = data), envir = new.env(parent = globalenv()))
  list(
    report_path = html_file,
    title = report_title
  )
} 