BayesianHierarchicalUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("bhUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("bhGroup"), "Grouping Variable", choices = NULL),
        selectizeInput(ns("bhResponse"), "Response Variable", choices = NULL),
        selectizeInput(ns("bhPredictors"), "Predictor Variables", choices = NULL, multiple = TRUE),
        selectInput(ns("bhModelType"), "Model Type", choices = c("Random Intercept", "Random Slope", "Custom")),
        textInput(ns("bhPriors"), "Priors (optional)", value = ""),
        br(),
        actionButton(ns("goBH"), label = "Run Model", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("bhError")),
        tableOutput(ns("bhSummary")),
        plotOutput(ns("bhPlot"))
      )
    )
  )
} 