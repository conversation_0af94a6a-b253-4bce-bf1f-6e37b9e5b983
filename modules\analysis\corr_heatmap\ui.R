CorrHeatmapUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("chmUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("chmVars"), "Variables", choices = NULL, multiple = TRUE),
        br(),
        actionButton(ns("goCHM"), label = "Generate Heatmap", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("chmError")),
        plotOutput(ns("chmPlot"))
      )
    )
  )
} 