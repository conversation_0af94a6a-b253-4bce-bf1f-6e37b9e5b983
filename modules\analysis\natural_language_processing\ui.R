NaturalLanguageProcessingUI <- function(id) {
  ns <- NS(id)
  
  sidebarLayout(
    sidebarPanel(
      fileInput(ns("data_file"), "Upload Data (CSV)", accept = ".csv"),
      selectInput(ns("text_column"), "Text Column", choices = NULL),
      selectInput(ns("analysis_type"), "Analysis Type",
                 choices = c("Text Preprocessing" = "preprocessing",
                           "Sentiment Analysis" = "sentiment",
                           "Topic Modeling" = "topic",
                           "Text Classification" = "classification",
                           "Named Entity Recognition" = "ner",
                           "Text Summarization" = "summarization",
                           "Word Embeddings" = "embeddings")),
      conditionalPanel(
        condition = paste0("input['", ns("analysis_type"), "'] == 'sentiment'"),
        selectInput(ns("sentiment_method"), "Sentiment Method",
                   choices = c("VADER" = "vader", "TextBlob" = "textblob", 
                             "Custom Lexicon" = "custom", "Machine Learning" = "ml"))
      ),
      conditionalPanel(
        condition = paste0("input['", ns("analysis_type"), "'] == 'topic'"),
        selectInput(ns("topic_method"), "Topic Modeling Method",
                   choices = c("Latent Dirichlet Allocation (LDA)" = "lda",
                             "Non-negative Matrix Factorization (NMF)" = "nmf",
                             "Latent Semantic Analysis (LSA)" = "lsa")),
        numericInput(ns("n_topics"), "Number of Topics", value = 5, min = 2, max = 20),
        numericInput(ns("max_iter"), "Maximum Iterations", value = 100, min = 10, max = 1000)
      ),
      conditionalPanel(
        condition = paste0("input['", ns("analysis_type"), "'] == 'classification'"),
        selectInput(ns("target_column"), "Target Column", choices = NULL),
        selectInput(ns("classification_method"), "Classification Method",
                   choices = c("Naive Bayes" = "naive_bayes",
                             "Support Vector Machine" = "svm",
                             "Random Forest" = "random_forest",
                             "Neural Network" = "neural_network"))
      ),
      conditionalPanel(
        condition = paste0("input['", ns("analysis_type"), "'] == 'embeddings'"),
        selectInput(ns("embedding_method"), "Embedding Method",
                   choices = c("Word2Vec" = "word2vec",
                             "GloVe" = "glove",
                             "FastText" = "fasttext",
                             "BERT" = "bert")),
        numericInput(ns("embedding_dim"), "Embedding Dimension", value = 100, min = 50, max = 300)
      ),
      checkboxInput(ns("remove_punctuation"), "Remove Punctuation", value = TRUE),
      checkboxInput(ns("remove_numbers"), "Remove Numbers", value = FALSE),
      checkboxInput(ns("remove_stopwords"), "Remove Stop Words", value = TRUE),
      checkboxInput(ns("lemmatize"), "Lemmatize", value = TRUE),
      checkboxInput(ns("stem"), "Stemming", value = FALSE),
      selectInput(ns("language"), "Language",
                 choices = c("English" = "en", "Spanish" = "es", "French" = "fr", 
                           "German" = "de", "Italian" = "it")),
      numericInput(ns("min_word_length"), "Minimum Word Length", value = 3, min = 1, max = 10),
      numericInput(ns("max_word_length"), "Maximum Word Length", value = 15, min = 5, max = 50),
      numericInput(ns("max_features"), "Maximum Features", value = 1000, min = 100, max = 10000),
      numericInput(ns("ngram_range_min"), "N-gram Range (Min)", value = 1, min = 1, max = 5),
      numericInput(ns("ngram_range_max"), "N-gram Range (Max)", value = 2, min = 1, max = 5),
      selectInput(ns("vectorization_method"), "Vectorization Method",
                 choices = c("TF-IDF" = "tfidf", "Count Vectorizer" = "count", 
                           "Hashing Vectorizer" = "hash")),
      numericInput(ns("test_size"), "Test Size Proportion", value = 0.2, min = 0.1, max = 0.5, step = 0.05),
      actionButton(ns("run_analysis"), "Run NLP Analysis", class = "btn-primary"),
      downloadButton(ns("download_results"), "Download Results"),
      downloadButton(ns("download_model"), "Download Model")
    ),
    mainPanel(
      h4("Results"),
      tableOutput(ns("text_statistics")),
      plotOutput(ns("word_frequency_plot")),
      plotOutput(ns("text_length_distribution")),
      conditionalPanel(
        condition = paste0("input['", ns("analysis_type"), "'] == 'sentiment'"),
        plotOutput(ns("sentiment_distribution")),
        plotOutput(ns("sentiment_over_time")),
        tableOutput(ns("sentiment_summary")),
        plotOutput(ns("sentiment_wordcloud"))
      ),
      conditionalPanel(
        condition = paste0("input['", ns("analysis_type"), "'] == 'topic'"),
        plotOutput(ns("topic_visualization")),
        plotOutput(ns("topic_coherence")),
        tableOutput(ns("topic_keywords")),
        plotOutput(ns("topic_distribution"))
      ),
      conditionalPanel(
        condition = paste0("input['", ns("analysis_type"), "'] == 'classification'"),
        plotOutput(ns("classification_performance")),
        tableOutput(ns("classification_metrics")),
        plotOutput(ns("confusion_matrix")),
        plotOutput(ns("feature_importance"))
      ),
      conditionalPanel(
        condition = paste0("input['", ns("analysis_type"), "'] == 'ner'"),
        plotOutput(ns("entity_distribution")),
        tableOutput(ns("entity_summary")),
        plotOutput(ns("entity_network"))
      ),
      conditionalPanel(
        condition = paste0("input['", ns("analysis_type"), "'] == 'summarization'"),
        tableOutput(ns("summary_metrics")),
        plotOutput(ns("summary_comparison")),
        verbatimTextOutput(ns("generated_summary"))
      ),
      conditionalPanel(
        condition = paste0("input['", ns("analysis_type"), "'] == 'embeddings'"),
        plotOutput(ns("embedding_visualization")),
        plotOutput(ns("similarity_matrix")),
        plotOutput(ns("word_analogies")),
        tableOutput(ns("embedding_metrics"))
      ),
      plotOutput(ns("wordcloud")),
      plotOutput(ns("bigram_network")),
      tableOutput(ns("keyword_extraction")),
      plotOutput(ns("text_complexity"))
    )
  )
} 