bartlettsTestServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactives
    btUploadData <- eventReactive(input$btUserData, {
      handle_file_upload(input$btUserData)
    })
    
    btResults <- reactive({
      data <- btUploadData()
      if (is.null(data)) return(NULL)
      
      if (input$btFormat == "Multiple") {
        if (is.null(input$btMultiColumns) || length(input$btMultiColumns) < 2) {
          return(NULL)
        }
        groups <- list()
        for (col in input$btMultiColumns) {
          groups[[col]] <- data[[col]][!is.na(data[[col]])]
        }
      } else {
        if (is.null(input$btResponse) || input$btResponse == "" ||
            is.null(input$btFactors) || input$btFactors == "") {
          return(NULL)
        }
        response <- data[[input$btResponse]]
        factor <- data[[input$btFactors]]
        groups <- split(response, factor)
        groups <- lapply(groups, function(x) x[!is.na(x)])
      }
      
      if (length(groups) < 2) return(NULL)
      
      list(groups = groups, n_groups = length(groups))
    })
    
    # Validation errors
    btValidationErrors <- reactive({
      errors <- c()
      data <- btUploadData()
      
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (input$btFormat == "Multiple") {
        if (is.null(input$btMultiColumns) || length(input$btMultiColumns) < 2) {
          errors <- c(errors, "Please select at least two columns for analysis.")
        } else {
          for (col in input$btMultiColumns) {
            if (length(na.omit(data[[col]])) < 2) {
              errors <- c(errors, sprintf("Column '%s' must have at least two non-missing values.", col))
            }
          }
        }
      } else {
        if (is.null(input$btResponse) || input$btResponse == "") {
          errors <- c(errors, "Please select a response variable.")
        }
        if (is.null(input$btFactors) || input$btFactors == "") {
          errors <- c(errors, "Please select a factor variable.")
        }
        if (!is.null(input$btResponse) && !is.null(input$btFactors) && 
            input$btResponse == input$btFactors) {
          errors <- c(errors, "Response and factor variables must be different.")
        }
      }
      
      errors
    })
    
    # Outputs
    output$btHT <- renderUI({
      results <- btResults()
      if (is.null(results)) return(NULL)
      bartlettsTestHT(btResults, reactive({input$btSigLvl}))
    })
    
    output$bartlettsTestPlot <- renderPlot({
      results <- btResults()
      if (is.null(results)) return(NULL)
      bartlettsTestPlot(btResults)
    })
    
    output$btConclusionOutput <- renderUI({
      results <- btResults()
      if (is.null(results)) return(NULL)
      btConclusion(btResults, reactive({input$btSigLvl}))
    })
    
    output$renderBTData <- renderUI({
      req(btUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("btInitialUploadTable")), style = "width: 75%"),
        br(),
        br()
      )
    })
    
    output$btInitialUploadTable <- DT::renderDT({
      req(btUploadData())
      DT::datatable(btUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(btUploadData())))))
    })
    
    # Update selectizeInput choices after file upload
    observeEvent(btUploadData(), {
      data <- btUploadData()
      updateSelectizeInput(session, 'btResponse', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'btFactors', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'btMultiColumns', choices = character(0), selected = NULL, server = TRUE)
      output$bartlettsTestResults <- renderUI({ NULL })
      
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'btResponse', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'btFactors', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'btMultiColumns', choices = names(data), server = TRUE)
        output$bartlettsTestResults <- renderUI({
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('btPreviewTable'))
          )
        })
        output$btPreviewTable <- DT::renderDT({
          head(data, 20)
        })
      }
    })
    
    # Show main results or error screen when Calculate is pressed
    observeEvent(input$goInference, {
      output$bartlettsTestResults <- renderUI({
        errors <- btValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Bartlett's Test", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("btTabset"),
              selected = "Analysis",
              tabPanel(
                id    = ns("bt"),
                title = "Analysis",
                titlePanel("Bartlett's Test for Homogeneity of Variances"),
                br(),
                uiOutput(ns('btHT')),
                br(),
                plotOutput(ns('bartlettsTestPlot'), width = "50%", height = "400px"),
                br(),
                uiOutput(ns('btConclusionOutput'))
              ),
              tabPanel(
                id    = ns("btData"),
                title = "Uploaded Data",
                uiOutput(ns("renderBTData"))
              )
            )
          )
        }
      })
    })
  })
} 