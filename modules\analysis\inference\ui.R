statisticalInferenceUI <- function(id) {
  ns <- NS(id)
  tabsetPanel(
    tabPanel("Parametric Tests",
      tabsetPanel(
        tabPanel("One Sample",
          sidebarLayout(
            sidebarPanel(OneSampleInferenceSidebarUI(ns("one_sample"))),
            mainPanel(OneSampleInferenceMainUI(ns("one_sample")))
          )
        ),
        tabPanel("Two Sample",
          sidebarLayout(
            sidebarPanel(TwoSampleInferenceSidebarUI(ns("two_sample"))),
            mainPanel(TwoSampleInferenceMainUI(ns("two_sample")))
          )
        ),
        tabPanel("Paired t-test",
          sidebarLayout(
            sidebarPanel(PairedTTestSidebarUI(ns("paired_t_test"))),
            mainPanel(PairedTTestMainUI(ns("paired_t_test")))
          )
        ),
        tabPanel("Proportion Tests",
          sidebarLayout(
            sidebarPanel(ProportionTestSidebarUI(ns("proportion_tests"))),
            mainPanel(ProportionTestMainUI(ns("proportion_tests")))
          )
        )
      )
    ),
    tabPanel("ANOVA & Related",
      tabsetPanel(
        tabPanel("One-way ANOVA",
          sidebarLayout(
            sidebarPanel(AnovaInferenceSidebarUI(ns("anova"))),
            mainPanel(AnovaInferenceMainUI(ns("anova")))
          )
        ),
        tabPanel("Two-way ANOVA",
          sidebarLayout(
            sidebarPanel(TwoWayAnovaSidebarUI(ns("two_way"))),
            mainPanel(TwoWayAnovaMainUI(ns("two_way")))
          )
        ),
        tabPanel("Three-way ANOVA",
          sidebarLayout(
            sidebarPanel(ThreeWayAnovaSidebarUI(ns("three_way"))),
            mainPanel(ThreeWayAnovaMainUI(ns("three_way")))
          )
        ),
        tabPanel("ANCOVA",
          sidebarLayout(
            sidebarPanel(AncovaSidebarUI(ns("ancova"))),
            mainPanel(AncovaMainUI(ns("ancova")))
          )
        ),
        tabPanel("Repeated Measures ANOVA",
          sidebarLayout(
            sidebarPanel(RepeatedMeasuresAnovaSidebarUI(ns("repeated_measures"))),
            mainPanel(RepeatedMeasuresAnovaMainUI(ns("repeated_measures")))
          )
        ),
        tabPanel("Mixed ANOVA",
          sidebarLayout(
            sidebarPanel(MixedAnovaSidebarUI(ns("mixed_anova"))),
            mainPanel(MixedAnovaMainUI(ns("mixed_anova")))
          )
        ),
        tabPanel("Post-hoc Tests",
          sidebarLayout(
            sidebarPanel(PostHocTestSidebarUI(ns("post_hoc"))),
            mainPanel(PostHocTestMainUI(ns("post_hoc")))
          )
        )
      )
    ),
    tabPanel("Nonparametric Tests",
      tabsetPanel(
        tabPanel("Kruskal-Wallis",
          sidebarLayout(
            sidebarPanel(kruskalWallisSidebarUI(ns("kruskal_wallis"))),
            mainPanel(kruskalWallisMainUI(ns("kruskal_wallis")))
          )
        ),
        tabPanel("Mann-Whitney Test",
          sidebarLayout(
            sidebarPanel(MannWhitneySidebarUI(ns("mann_whitney"))),
            mainPanel(MannWhitneyMainUI(ns("mann_whitney")))
          )
        ),
        tabPanel("Wilcoxon Test",
          sidebarLayout(
            sidebarPanel(WilcoxonSidebarUI(ns("wilcoxon"))),
            mainPanel(WilcoxonMainUI(ns("wilcoxon")))
          )
        ),
        tabPanel("Friedman Test",
          sidebarLayout(
            sidebarPanel(FriedmanTestSidebarUI(ns("friedman"))),
            mainPanel(FriedmanTestMainUI(ns("friedman")))
          )
        ),
        tabPanel("Sign Test",
          sidebarLayout(
            sidebarPanel(SignTestSidebarUI(ns("sign_test"))),
            mainPanel(SignTestMainUI(ns("sign_test")))
          )
        ),
        tabPanel("Jonckheere-Terpstra Test",
          sidebarLayout(
            sidebarPanel(JonckheereTerpstraSidebarUI(ns("jonckheere_terpstra"))),
            mainPanel(JonckheereTerpstraMainUI(ns("jonckheere_terpstra")))
          )
        ),
        tabPanel("Mann-Kendall Trend Test",
          sidebarLayout(
            sidebarPanel(mannKendallSidebarUI(ns("mann_kendall"))),
            mainPanel(mannKendallMainUI(ns("mann_kendall")))
          )
        ),
        tabPanel("Runs Test (Wald-Wolfowitz)",
          sidebarLayout(
            sidebarPanel(runsTestSidebarUI(ns("runs_test"))),
            mainPanel(runsTestMainUI(ns("runs_test")))
          )
        ),
        tabPanel("Robust ANOVA",
          sidebarLayout(
            sidebarPanel(robustAnovaSidebarUI(ns("robust_anova"))),
            mainPanel(robustAnovaMainUI(ns("robust_anova")))
          )
        )
      )
    ),
    tabPanel("Categorical Tests",
      tabsetPanel(
        tabPanel("Chi-Square",
          sidebarLayout(
            sidebarPanel(chiSquareSidebarUI(ns("chi_square"))),
            mainPanel(chiSquareMainUI(ns("chi_square")))
          )
        ),
        tabPanel("McNemar's Test",
          sidebarLayout(
            sidebarPanel(McNemarTestSidebarUI(ns("mcnemar"))),
            mainPanel(McNemarTestMainUI(ns("mcnemar")))
          )
        ),
        tabPanel("Cochran's Q Test",
          sidebarLayout(
            sidebarPanel(CochransQTestSidebarUI(ns("cochrans_q"))),
            mainPanel(CochransQTestMainUI(ns("cochrans_q")))
          )
        ),
        tabPanel("Cochran-Mantel-Haenszel Test",
          sidebarLayout(
            sidebarPanel(CochranMantelHaenszelSidebarUI(ns("cochran_mantel_haenszel"))),
            mainPanel(CochranMantelHaenszelMainUI(ns("cochran_mantel_haenszel")))
          )
        )
      )
    ),
    tabPanel("Goodness-of-Fit Tests",
      tabsetPanel(
        tabPanel("Anderson-Darling Test",
          sidebarLayout(
            sidebarPanel(andersonDarlingSidebarUI(ns("anderson_darling"))),
            mainPanel(andersonDarlingMainUI(ns("anderson_darling")))
          )
        ),
        tabPanel("Shapiro-Wilk Test",
          sidebarLayout(
            sidebarPanel(shapiroWilkSidebarUI(ns("shapiro_wilk"))),
            mainPanel(shapiroWilkMainUI(ns("shapiro_wilk")))
          )
        ),
        tabPanel("Jarque-Bera Test",
          sidebarLayout(
            sidebarPanel(jarqueBeraSidebarUI(ns("jarque_bera"))),
            mainPanel(jarqueBeraMainUI(ns("jarque_bera")))
          )
        ),
        tabPanel("Bartlett's Test",
          sidebarLayout(
            sidebarPanel(bartlettsTestSidebarUI(ns("bartletts_test"))),
            mainPanel(bartlettsTestMainUI(ns("bartletts_test")))
          )
        )
      )
    ),
    tabPanel("Bayesian Methods",
      tabsetPanel(
        tabPanel("Bayesian Tests",
          sidebarLayout(
            sidebarPanel(BayesianTestSidebarUI(ns("bayesian_tests"))),
            mainPanel(BayesianTestMainUI(ns("bayesian_tests")))
          )
        ),
        tabPanel("Bayesian Regression/ANOVA",
          sidebarLayout(
            sidebarPanel(BayesianRegressionUI(ns("bayesreg"))),
            mainPanel()
          )
        ),
        tabPanel("Bayesian Model Comparison",
          sidebarLayout(
            sidebarPanel(BayesianModelComparisonUI(ns("bmc"))),
            mainPanel()
          )
        )
      )
    ),
    tabPanel("Resampling Methods",
      tabsetPanel(
        tabPanel("Permutation Tests",
          sidebarLayout(
            sidebarPanel(PermutationTestsUI(ns("perm"))),
            mainPanel()
          )
        ),
        tabPanel("Bootstrap Analysis",
          sidebarLayout(
            sidebarPanel(BootstrapUI(ns("boot"))),
            mainPanel()
          )
        ),
        tabPanel("Simulation",
          sidebarLayout(
            sidebarPanel(SimulationUI(ns("sim"))),
            mainPanel()
          )
        )
      )
    ),
    tabPanel("Other Tests",
      sidebarLayout(
        sidebarPanel(CustomTestSidebarUI(ns("custom_test"))),
        mainPanel(CustomTestMainUI(ns("custom_test")))
      )
    )
  )
}