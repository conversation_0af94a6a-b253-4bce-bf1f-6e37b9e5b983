augmentedDickeyFullerUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(
      fileInput(
        inputId = ns("adfUserData"),
        label   = strong("Upload your Data (.csv or .xls or .xlsx or .txt)"),
        accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
      ),
      selectizeInput(
        inputId = ns("adfVariable"),
        label = strong("Select Variable"),
        choices = c(""),
        selected = NULL,
        options = list(placeholder = 'Select a column', onInitialize = I('function() { this.setValue(\"\"); }'))
      ),
      radioButtons(
        inputId = ns("adfType"),
        label = strong("Test Type"),
        choices = c("none", "drift", "trend"),
        selected = "drift"
      ),
      numericInput(
        inputId = ns("adfLag"),
        label = strong("Number of Lags"),
        value = 1,
        min = 0,
        max = 20,
        step = 1
      ),
      radioButtons(
        inputId = ns("adfSigLvl"),
        label = strong("Significance Level (alpha)"),
        choices = c("10%", "5%", "1%"),
        selected = "5%",
        inline = TRUE
      ),
      br(),
      actionButton(ns("goInference"), label = "Calculate", class = "act-btn")
    ),
    mainPanel(
      textOutput(ns('adfError')),
      tableOutput(ns('adfResults')),
      plotOutput(ns('adfPlot'))
    )
  )
} 