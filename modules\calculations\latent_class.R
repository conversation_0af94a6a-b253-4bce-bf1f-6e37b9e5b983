# Latent Class Analysis calculation and output helpers

latent_class_uploadData_func <- function(lcUserData) {
  ext <- tools::file_ext(lcUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(lcUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(lcUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(lcUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(lcUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

latent_class_results_func <- function(data, manifest_vars, n_classes = 2) {
  tryCatch({
    if (!requireNamespace("poLCA", quietly = TRUE)) {
      stop("Package 'poLCA' is required for Latent Class Analysis.")
    }
    
    f <- as.formula(paste("cbind(", paste(manifest_vars, collapse = ","), ") ~ 1"))
    fit <- poLCA::poLCA(f, data = data, nclass = n_classes, verbose = FALSE)
    
    list(
      fit = fit,
      data = data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Latent Class Analysis calculation:", e$message))
  })
}

latent_class_ht_html <- function(results) {
  # No single hypothesis test, summary is more informative
  tagList(
    h4("Latent Class Analysis (LCA)"),
    p("See summary table for model fit and class probabilities.")
  )
}

latent_class_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  out <- list(
    h4("Model Summary"),
    renderPrint(summary(results$fit)),
    h4("Class Probabilities"),
    renderPrint(results$fit$probs)
  )
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

latent_class_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  plot(results$fit, main = 'Latent Class Probabilities')
}