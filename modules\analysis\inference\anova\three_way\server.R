# Three-Way ANOVA Server
# Factorial design with three factors

source("modules/calculations/three_way_anova.R")

ThreeWayAnovaServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    threeWayUploadData <- eventReactive(input$threeWayUserData, {
      handle_file_upload(input$threeWayUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(threeWayUploadData(), {
      data <- threeWayUploadData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'threeWayResponseVariable', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'threeWayFactor1', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'threeWayFactor2', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'threeWayFactor3', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    threeWayValidationErrors <- reactive({
      errors <- c()
      
      if (input$threeWayDataMethod == "Manual Entry") {
        # Manual entry validation
        if (is.null(input$threeWayData) || input$threeWayData == "") {
          errors <- c(errors, "Response values are required for manual entry.")
        } else {
          data_vals <- createNumLst(input$threeWayData)
          if (length(data_vals) < 8) {
            errors <- c(errors, "At least 8 observations are required for three-way ANOVA.")
          }
        }
        
        if (is.null(input$threeWayFactor1Data) || input$threeWayFactor1Data == "") {
          errors <- c(errors, "Factor 1 values are required for manual entry.")
        }
        if (is.null(input$threeWayFactor2Data) || input$threeWayFactor2Data == "") {
          errors <- c(errors, "Factor 2 values are required for manual entry.")
        }
        if (is.null(input$threeWayFactor3Data) || input$threeWayFactor3Data == "") {
          errors <- c(errors, "Factor 3 values are required for manual entry.")
        }
      } else {
        # File upload validation
        data <- threeWayUploadData()
        if (is.null(data) || !is.data.frame(data)) {
          errors <- c(errors, "No data uploaded or file could not be read.")
          return(errors)
        }
        if (is.null(input$threeWayResponseVariable) || input$threeWayResponseVariable == "") {
          errors <- c(errors, "Please select a response variable.")
        } else {
          var_data <- data[[input$threeWayResponseVariable]]
          if (!is.numeric(var_data)) {
            errors <- c(errors, "Response variable must be numeric.")
          }
          if (length(na.omit(var_data)) < 8) {
            errors <- c(errors, "At least 8 non-missing observations are required.")
          }
        }
        if (is.null(input$threeWayFactor1) || input$threeWayFactor1 == "") {
          errors <- c(errors, "Please select Factor 1.")
        }
        if (is.null(input$threeWayFactor2) || input$threeWayFactor2 == "") {
          errors <- c(errors, "Please select Factor 2.")
        }
        if (is.null(input$threeWayFactor3) || input$threeWayFactor3 == "") {
          errors <- c(errors, "Please select Factor 3.")
        }
      }
      
      errors
    })
    
    # Three-way ANOVA analysis reactive
    threeWayResult <- reactive({
      req(input$threeWayDataMethod)
      
      if (input$threeWayDataMethod == "Manual Entry") {
        data_vals <- createNumLst(input$threeWayData)
        factor1_vals <- createNumLst(input$threeWayFactor1Data)
        factor2_vals <- createNumLst(input$threeWayFactor2Data)
        factor3_vals <- createNumLst(input$threeWayFactor3Data)
      } else {
        data <- threeWayUploadData()
        req(data, input$threeWayResponseVariable, input$threeWayFactor1, 
            input$threeWayFactor2, input$threeWayFactor3)
        data_vals <- data[[input$threeWayResponseVariable]]
        factor1_vals <- data[[input$threeWayFactor1]]
        factor2_vals <- data[[input$threeWayFactor2]]
        factor3_vals <- data[[input$threeWayFactor3]]
      }
      
      conf_level <- input$threeWayConfLevel
      
      # Perform three-way ANOVA
      result <- perform_three_way_anova(data_vals, factor1_vals, factor2_vals, factor3_vals, conf_level)
      
      result
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goThreeWay, {
      output$threeWayResults <- renderUI({
        errors <- threeWayValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Three-Way ANOVA", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("threeWayTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("threeWayAnalysis"),
                title = "Analysis",
                titlePanel("Three-Way ANOVA Results"),
                br(),
                uiOutput(ns('threeWaySummary')),
                br(),
                h4("ANOVA Table"),
                tableOutput(ns('threeWayAnovaTable')),
                br(),
                h4("Effect Sizes"),
                tableOutput(ns('threeWayEffectSizes')),
                br(),
                h4("Assumptions and Interpretation"),
                uiOutput(ns('threeWayAssumptions'))
              ),
              tabPanel(
                id = ns("threeWayDataSummary"),
                title = "Data Summary",
                h4("Descriptive Statistics by Factor Combinations"),
                tableOutput(ns('threeWayDescriptive')),
                br(),
                h4("Cell Means"),
                tableOutput(ns('threeWayCellMeans')),
                br(),
                h4("Data Distribution"),
                plotOutput(ns('threeWayBoxplot'), height = "500px")
              ),
              tabPanel(
                id = ns("threeWayPostHoc"),
                title = "Post-Hoc Tests",
                h4("Main Effects Post-Hoc Tests"),
                uiOutput(ns('threeWayMainEffects')),
                br(),
                h4("Interaction Effects"),
                uiOutput(ns('threeWayInteractions'))
              ),
              tabPanel(
                id = ns("threeWayUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('threeWayViewUpload'))
              )
            )
          )
        }
      })
    })
    
    # Analysis Tab Outputs
    output$threeWaySummary <- renderUI({
      req(threeWayResult())
      result <- threeWayResult()
      
      tagList(
        h4("Three-Way ANOVA Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Statistic = c("Total Sample Size", "Factor 1 Levels", "Factor 2 Levels", "Factor 3 Levels",
                         "Total Cells", "Significance Level", "Model R²", "Adjusted R²"),
            Value = c(
              result$total_n,
              result$factor1_levels,
              result$factor2_levels,
              result$factor3_levels,
              result$total_cells,
              paste0((1 - result$conf_level) * 100, "%"),
              round(result$r_squared, 4),
              round(result$adj_r_squared, 4)
            )
          )
        }),
        br(),
        p(strong("Model:"), "Response ~ Factor1 + Factor2 + Factor3 + Factor1:Factor2 + Factor1:Factor3 + Factor2:Factor3 + Factor1:Factor2:Factor3")
      )
    })
    
    output$threeWayAnovaTable <- renderTable({
      req(threeWayResult())
      result <- threeWayResult()
      
      result$anova_table
    }, digits = 4)
    
    output$threeWayEffectSizes <- renderTable({
      req(threeWayResult())
      result <- threeWayResult()
      
      result$effect_sizes
    }, digits = 4)
    
    output$threeWayAssumptions <- renderUI({
      req(threeWayResult())
      result <- threeWayResult()
      
      tagList(
        h5("Key Assumptions:"),
        p("1. Independence: Observations are independent"),
        p("2. Normality: Residuals are normally distributed"),
        p("3. Homogeneity of variance: Equal variances across groups"),
        p("4. Linearity: Linear relationships between factors and response"),
        br(),
        h5("Assumption Tests:"),
        p("• Normality test p-value: ", round(result$normality_p, 4)),
        p("• Levene's test p-value: ", round(result$levene_p, 4)),
        br(),
        h5("Interpretation:"),
        p("• Main effects: Individual factor effects"),
        p("• Two-way interactions: Combined effects of two factors"),
        p("• Three-way interaction: Combined effects of all three factors"),
        br(),
        h5("When to Use:"),
        p("• Factorial experiments with three factors"),
        p("• Testing main effects and interactions"),
        p("• Balanced or unbalanced designs"),
        p("• Continuous response variable")
      )
    })
    
    # Data Summary Tab Outputs
    output$threeWayDescriptive <- renderTable({
      req(threeWayResult())
      result <- threeWayResult()
      
      result$descriptive_stats
    }, digits = 4)
    
    output$threeWayCellMeans <- renderTable({
      req(threeWayResult())
      result <- threeWayResult()
      
      result$cell_means
    }, digits = 4)
    
    output$threeWayBoxplot <- renderPlot({
      req(threeWayResult())
      result <- threeWayResult()
      
      # Create interaction plot
      par(mfrow = c(2, 2))
      
      # Factor 1 main effect
      boxplot(result$data ~ result$factor1, main = paste("Factor 1:", result$factor1_name),
              xlab = "Levels", ylab = "Response", col = "#4F81BD")
      
      # Factor 2 main effect
      boxplot(result$data ~ result$factor2, main = paste("Factor 2:", result$factor2_name),
              xlab = "Levels", ylab = "Response", col = "#C0504D")
      
      # Factor 3 main effect
      boxplot(result$data ~ result$factor3, main = paste("Factor 3:", result$factor3_name),
              xlab = "Levels", ylab = "Response", col = "#9BBB59")
      
      # Interaction plot (Factor 1 x Factor 2)
      interaction.plot(result$factor1, result$factor2, result$data,
                      main = "Factor 1 x Factor 2 Interaction",
                      xlab = "Factor 1", ylab = "Mean Response",
                      trace.label = "Factor 2", col = c("#4F81BD", "#C0504D", "#9BBB59"))
    })
    
    # Post-Hoc Tab Outputs
    output$threeWayMainEffects <- renderUI({
      req(threeWayResult())
      result <- threeWayResult()
      
      tagList(
        h5("Factor 1 Post-Hoc Tests:"),
        renderTable(result$factor1_posthoc, digits = 4),
        br(),
        h5("Factor 2 Post-Hoc Tests:"),
        renderTable(result$factor2_posthoc, digits = 4),
        br(),
        h5("Factor 3 Post-Hoc Tests:"),
        renderTable(result$factor3_posthoc, digits = 4)
      )
    })
    
    output$threeWayInteractions <- renderUI({
      req(threeWayResult())
      result <- threeWayResult()
      
      tagList(
        h5("Two-Way Interactions:"),
        p("Significant interactions indicate that the effect of one factor depends on the level of another factor."),
        br(),
        h5("Three-Way Interaction:"),
        p("A significant three-way interaction indicates that the two-way interaction effect depends on the level of the third factor.")
      )
    })
    
    # Uploaded Data Tab Output
    output$threeWayViewUpload <- DT::renderDT({
      req(threeWayUploadData())
      DT::datatable(threeWayUploadData(), 
                   options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), 
                   rownames = FALSE)
    })
  })
} 