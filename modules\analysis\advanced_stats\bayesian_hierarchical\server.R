BayesianHierarchicalServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    bhData <- eventReactive(input$bhUserData, {
      handle_file_upload(input$bhUserData)
    })
    
    observeEvent(bhData(), {
      data <- bhData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'bhResponse', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'bhPredictors', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'bhGroup', choices = names(data), server = TRUE)
      }
    })
    
    bhValidationErrors <- reactive({
      errors <- c()
      data <- bhData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$bhResponse)) {
        errors <- c(errors, "Select a response variable.")
      }
      if (is.null(input$bhPredictors) || length(input$bhPredictors) < 1) {
        errors <- c(errors, "Select at least one predictor variable.")
      }
      if (is.null(input$bhGroup)) {
        errors <- c(errors, "Select a group variable.")
      }
      if (!is.null(input$bhResponse) && !is.numeric(data[[input$bhResponse]])) {
        errors <- c(errors, "Response variable must be numeric.")
      }
      for (var in input$bhPredictors) {
        if (!is.numeric(data[[var]])) {
          errors <- c(errors, sprintf("Predictor variable '%s' must be numeric.", var))
        }
      }
      errors
    })
    
    bhResult <- eventReactive(input$goBH, {
      data <- bhData()
      req(data, input$bhResponse, input$bhPredictors, input$bhGroup)
      
      # Get parameters from UI
      model_type <- ifelse(is.null(input$bhModelType), "random_intercept", input$bhModelType)
      
      bayesian_hierarchical_analysis(data, input$bhResponse, input$bhPredictors, 
                                   input$bhGroup, model_type = model_type)
    })
    
    observeEvent(input$goBH, {
      output$bhResultsUI <- renderUI({
        errors <- bhValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Bayesian Hierarchical Analysis", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("bhTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("bhAnalysis"),
                title = "Analysis",
                titlePanel("Bayesian Hierarchical Model Results"),
                br(),
                h4("Fixed Effects"),
                tableOutput(ns('bhFixedEffects')),
                h4("Random Effects"),
                tableOutput(ns('bhRandomEffects')),
                h4("Fit Statistics"),
                tableOutput(ns('bhFitStats')),
                h4("Group Summary"),
                tableOutput(ns('bhGroupSummary')),
                h4("Model Type"),
                textOutput(ns('bhModelType')),
                h4("Number of Groups"),
                textOutput(ns('bhGroups')),
                h4("Number of Observations"),
                textOutput(ns('bhObservations')),
                h4("Number of Predictors"),
                textOutput(ns('bhPredictors'))
              ),
              tabPanel(
                id = ns("bhDiagnostics"),
                title = "Diagnostics",
                h4("Convergence Diagnostics"),
                tableOutput(ns('bhConvergence')),
                h4("Convergence Status"),
                textOutput(ns('bhConvergenceStatus')),
                h4("Posterior Predictive Check Plot"),
                plotOutput(ns('bhPPCheck'), height = "300px"),
                h4("Fixed Effects Plot"),
                plotOutput(ns('bhFixedEffectsPlot'), height = "300px"),
                h4("Random Effects Plot"),
                plotOutput(ns('bhRandomEffectsPlot'), height = "300px")
              ),
              tabPanel(
                id = ns("bhUploadedData"),
                title = "Uploaded Data",
                h4("Raw Uploaded Data"),
                DT::DTOutput(ns('bhRawDataTable'))
              )
            )
          )
        }
      })
    })
    output$bhRawDataTable <- DT::renderDT({
      req(bhData())
      DT::datatable(bhData(), options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), rownames = FALSE)
    })
  })
} 