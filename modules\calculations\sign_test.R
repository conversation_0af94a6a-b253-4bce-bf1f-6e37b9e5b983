# Sign Test calculation and output helpers

sign_test_uploadData_func <- function(stUserData, var) {
  tryCatch(
    {
      if (is.null(stUserData) || is.null(var)) {
        return(NULL)
      }
      
      # Read data based on file type
      if (grepl("\\.csv$", stUserData$name, ignore.case = TRUE)) {
        df <- read.csv(stUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.tsv$", stUserData$name, ignore.case = TRUE)) {
        df <- read.tsv(stUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.xlsx$", stUserData$name, ignore.case = TRUE)) {
        df <- readxl::read_excel(stUserData$datapath)
      } else {
        stop("Unsupported file type. Please upload a CSV, TSV, or XLSX file.")
      }
      
      if (!var %in% names(df)) {
        stop(paste("Variable '", var, "' not found in the uploaded file.", sep = ""))
      }
      
      # Return the selected variable as a numeric vector
      return(as.numeric(df[[var]]))
    },
    error = function(e) {
      # Return a list with an error message
      return(list(error = paste("Error reading data:", e$message)))
    }
  )
}

sign_test_results_func <- function(data, hypothesized_median = 0, alternative = "two.sided", conf_level = 0.95) {
  tryCatch({
    if (is.null(data) || length(data) < 2) {
      stop("At least 2 observations are required.")
    }
    
    if (!requireNamespace("BSDA", quietly = TRUE)) {
      stop("Package 'BSDA' needed for sign test.")
    }
    
    test_result <- BSDA::SIGN.test(data, md = hypothesized_median, alternative = alternative, conf.level = conf_level)
    
    list(
      test = test_result,
      data = data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Sign test calculation:", e$message))
  })
}

sign_test_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  
  p_value <- results$test$p.value
  conclusion <- if (p_value < 0.05) "significant" else "not significant"
  
  tagList(
    h4("Sign Test"),
    p(sprintf("The difference between the sample median and the hypothesized median is %s (p = %.3f).", conclusion, p_value))
  )
}

sign_test_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  renderPrint(results$test)
}

sign_test_plot <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  
  if (!requireNamespace("ggplot2", quietly = TRUE)) {
    stop("Package 'ggplot2' is required for plotting.")
  }
  
  plot_data <- data.frame(Value = results$data)
  
  ggplot(plot_data, aes(x = Value)) +
    geom_histogram(bins = 20, fill = "steelblue", alpha = 0.7) +
    geom_vline(xintercept = results$test$null.value, color = "red", linetype = "dashed") +
    labs(title = "Distribution of Sample Data",
         x = "Value",
         y = "Frequency") +
    theme_minimal()
}