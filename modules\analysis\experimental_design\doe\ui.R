# Placeholder for DOE UI
doeSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns("doeFactorInputs")),
    actionButton(ns("goDOE"), label = "Generate Design", class = "act-btn")
  )
}

doeMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('doeHT')),
    plotOutput(ns('doePlot'), width = "50%", height = "400px"),
    uiOutput(ns('doeConclusionOutput'))
  )
}

doeUI <- function(id) {
  ns <- NS(id)
  tagList(
    doeSidebarUI(id),
    doeMainUI(id),
    tabsetPanel(
      id = ns("doeTabset"),
      selected = "Analysis",
      tabPanel(
        id    = ns("doeAnalysis"),
        title = "Design of Experiments (DOE) Analysis",
        titlePanel("Design of Experiments (DOE) Analysis"),
        br(),
        uiOutput(ns('doeHT')),
        br(),
        plotOutput(ns('doePlot'), width = "50%", height = "400px"),
        br(),
        uiOutput(ns('doeConclusionOutput'))
      ),
      tabPanel(
        id    = ns("doeData"),
        title = "Design Inputs",
        uiOutput(ns("renderDOEData"))
      )
    )
  )
} 