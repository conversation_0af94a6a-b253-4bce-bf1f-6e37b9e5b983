PoissonRegressionSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("poisUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
    selectInput(ns("poisModelType"), "Model Type", choices = c("Poisson", "Negative Binomial")),
    selectizeInput(ns("poisResponse"), "Response Variable", choices = NULL),
    selectizeInput(ns("poisPredictors"), "Predictor Variables", choices = NULL, multiple = TRUE),
    br(),
    actionButton(ns("goPois"), label = "Fit Model", class = "act-btn")
  )
}

PoissonRegressionMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns("poisError")),
    tableOutput(ns("poisResults")),
    plotOutput(ns("poisPlot"))
  )
}

PoissonRegressionUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(PoissonRegressionSidebarUI(id)),
    mainPanel(PoissonRegressionMainUI(id))
  )
} 