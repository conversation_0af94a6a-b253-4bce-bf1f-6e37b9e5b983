StateSpaceServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    ssData <- eventReactive(input$ssUserData, {
      handle_file_upload(input$ssUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(ssData(), {
      data <- ssData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'ssSeries', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    ssValidationErrors <- reactive({
      errors <- c()
      data <- ssData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$ssSeries)) {
        errors <- c(errors, "Select a time series variable.")
      }
      if (!is.null(input$ssSeries) && !is.numeric(data[[input$ssSeries]])) {
        errors <- c(errors, "Time series variable must be numeric.")
      }
      if (!is.null(input$ssSeries) && length(na.omit(data[[input$ssSeries]])) < 10) {
        errors <- c(errors, "Time series must have at least 10 observations.")
      }
      errors
    })
    
    # State space analysis reactive
    ssResult <- eventReactive(input$goSS, {
      data <- ssData()
      req(data, input$ssSeries)
      
      # Remove missing values
      clean_data <- na.omit(data[[input$ssSeries]])
      if (length(clean_data) < 10) {
        stop("Insufficient data after removing missing values.")
      }
      
      # Create time series object
      ts_data <- ts(clean_data)
      
      # Fit state space model
      state_space_model(ts_data)
    })
    
    # Error handling
    output$ssError <- renderUI({
      errors <- ssValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          ssResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "State Space Model Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$ssModelSummary <- renderUI({
      req(ssResult())
      res <- ssResult()
      
      tagList(
        h4("State Space Model Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Metric = c("Model Type", "Observations", "AIC", "BIC", "Log-Likelihood"),
            Value = c(
              "Structural Time Series",
              length(res$data),
              round(res$aic, 3),
              round(res$bic, 3),
              round(res$loglik, 3)
            )
          )
        }),
        h4("Model Parameters"),
        renderTable({
          res$parameters
        }),
        h4("Model Diagnostics"),
        renderTable({
          res$diagnostics
        })
      )
    })
    
    output$ssPlot <- renderPlot({
      req(ssResult())
      res <- ssResult()
      
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # Original data and fitted values
      plot(res$data, main = "Original Data and Fitted Values", 
           ylab = "Value", xlab = "Time")
      lines(res$fitted, col = "red", lwd = 2)
      legend("topright", legend = c("Original", "Fitted"), 
             col = c("black", "red"), lty = c(1, 1), lwd = c(1, 2))
      
      # Residuals
      plot(res$residuals, main = "Residuals", ylab = "Residual", xlab = "Time")
      abline(h = 0, col = "red", lty = 2)
      
      # ACF of residuals
      acf(res$residuals, main = "ACF of Residuals", na.action = na.pass)
      
      # Q-Q plot of residuals
      qqnorm(res$residuals, main = "Q-Q Plot of Residuals")
      qqline(res$residuals, col = "red")
      
      par(mfrow = c(1, 1))
    })
    
    output$ssForecast <- renderPlot({
      req(ssResult())
      res <- ssResult()
      
      # Generate forecasts if available
      if (!is.null(res$forecast)) {
        plot(res$forecast, main = "State Space Model Forecasts")
      } else {
        plot.new()
        text(0.5, 0.5, "Forecasts not available", cex = 1.2)
      }
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$ssDataSummary <- renderUI({
      req(ssData(), input$ssSeries)
      data <- ssData()
      series_data <- data[[input$ssSeries]]
      
      tagList(
        h4("Time Series Summary"),
        renderTable({
          summary_stats <- summary(series_data)
          data.frame(
            Statistic = names(summary_stats),
            Value = as.numeric(summary_stats)
          )
        }),
        h4("Missing Values"),
        renderTable({
          missing_count <- sum(is.na(series_data))
          total_count <- length(series_data)
          data.frame(
            Metric = c("Total Observations", "Missing Values", "Complete Cases", "Missing Percentage"),
            Value = c(total_count, missing_count, total_count - missing_count, 
                     round(missing_count/total_count * 100, 2))
          )
        })
      )
    })
    
    output$ssAssumptions <- renderUI({
      req(ssResult())
      res <- ssResult()
      
      tagList(
        h4("Model Assumptions Check"),
        renderTable({
          # Normality test
          norm_test <- shapiro.test(res$residuals)
          # Stationarity test
          adf_test <- adf.test(res$data, alternative = "stationary")
          
          data.frame(
            Assumption = c("Residual Normality (Shapiro-Wilk)", "Stationarity (ADF Test)"),
            Test_Statistic = c(round(norm_test$statistic, 4), round(adf_test$statistic, 4)),
            P_Value = c(round(norm_test$p.value, 4), round(adf_test$p.value, 4)),
            Decision = c(
              ifelse(norm_test$p.value > 0.05, "Pass", "Fail"),
              ifelse(adf_test$p.value < 0.05, "Pass", "Fail")
            )
          )
        }),
        h4("Residual Analysis"),
        renderTable({
          data.frame(
            Metric = c("Mean", "Standard Deviation", "Skewness", "Kurtosis"),
            Value = c(
              round(mean(res$residuals, na.rm = TRUE), 4),
              round(sd(res$residuals, na.rm = TRUE), 4),
              round(skewness(res$residuals, na.rm = TRUE), 4),
              round(kurtosis(res$residuals, na.rm = TRUE), 4)
            )
          )
        })
      )
    })
    
    output$ssDiagnosticPlots <- renderPlot({
      req(ssResult())
      res <- ssResult()
      
      par(mfrow = c(2, 2))
      
      # Residuals vs Fitted
      plot(res$fitted, res$residuals, main = "Residuals vs Fitted",
           xlab = "Fitted Values", ylab = "Residuals")
      abline(h = 0, col = "red", lty = 2)
      
      # Histogram of residuals
      hist(res$residuals, main = "Histogram of Residuals", 
           xlab = "Residuals", freq = FALSE)
      curve(dnorm(x, mean = mean(res$residuals, na.rm = TRUE), 
                  sd = sd(res$residuals, na.rm = TRUE)), 
            add = TRUE, col = "red")
      
      # Box plot of residuals
      boxplot(res$residuals, main = "Box Plot of Residuals", ylab = "Residuals")
      
      # Time series of residuals
      plot(res$residuals, main = "Residuals Over Time", 
           ylab = "Residuals", xlab = "Time")
      abline(h = 0, col = "red", lty = 2)
      
      par(mfrow = c(1, 1))
    })
    
    # Uploaded Data Tab Outputs
    output$ssDataTable <- renderDT({
      req(ssData())
      data <- ssData()
      
      DT::datatable(
        data,
        options = list(
          pageLength = 10,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
        ),
        extensions = 'Buttons',
        filter = 'top',
        rownames = FALSE
      )
    })
    
    output$ssDataInfo <- renderUI({
      req(ssData())
      data <- ssData()
      
      tagList(
        h4("Dataset Information"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "File Name"),
            Value = c(nrow(data), ncol(data), 
                     ifelse(!is.null(input$ssUserData), input$ssUserData$name, "Unknown"))
          )
        }),
        h4("Variable Information"),
        renderTable({
          data.frame(
            Variable = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Unique = sapply(data, function(x) length(unique(x)))
          )
        })
      )
    })
  })
} 