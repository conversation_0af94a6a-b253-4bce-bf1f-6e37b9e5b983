jarqueBeraSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(
      inputId = ns("jbUserData"),
      label   = strong("Upload your Data (.csv or .xls or .xlsx or .txt)"),
      accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
    ),
    div(
      id = ns("jbUploadInputs"),
      selectizeInput(
        inputId = ns("jbVariable"),
        label = strong("Select Variable"),
        choices = c(""),
        selected = NULL,
        options = list(placeholder = 'Select a column', onInitialize = I('function() { this.setValue(\"\"); }'))
      ),
      radioButtons(
        inputId = ns("jbSigLvl"),
        label = strong("Significance Level (alpha)"),
        choices = c("10%", "5%", "1%"),
        selected = "5%",
        inline = TRUE
      )
    ),
    br(),
    actionButton(ns("goInference"), label = "Calculate", class = "act-btn")
  )
}

jarqueBeraMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('jarqueBeraResults'))
  )
}

jarqueBeraUI <- function(id) {
  ns <- NS(id)
  tagList(
    jarqueBeraSidebarUI(id),
    jarqueBeraMainUI(id)
  )
} 