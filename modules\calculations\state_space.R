# State Space Model calculation and output helpers

# 1. Data Upload Function
state_space_uploadData_func <- function(ssUserData) {
  ext <- tools::file_ext(ssUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(ssUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(ssUserData$datapath),
         xlsx = readxl::read_xlsx(ssUserData$datapath),
         txt = readr::read_tsv(ssUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

# 2. Main Results Function (core calculations)
state_space_results_func <- function(data, ts_col) {
  tryCatch({
    ts_data <- data[[ts_col]]
    if (!is.ts(ts_data)) ts_data <- ts(ts_data)
    fit <- stats::StructTS(ts_data)
    list(
      fitted = fit,
      summary = summary(fit),
      ts_data = ts_data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during State Space Model calculation:", e$message))
  })
}

# 3. Hypothesis Test HTML Output
state_space_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  tagList(
    h4("State Space Model (Kalman Filtering)"),
    p("See summary table for model details.")
  )
}

# 4. Summary Table HTML Output
state_space_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  out <- list(h4("State Space Model Summary"), renderPrint(results$summary))
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

# 5. Plot Output
state_space_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  plot(results$ts_data, main = 'State Space Model Fit')
  lines(fitted(results$fitted), col = 'red')
  # Residuals
  resids <- residuals(results$fitted)
  plot(resids, type = 'h', main = 'Residuals', ylab = 'Residuals')
} 