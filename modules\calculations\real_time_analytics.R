# Real-time Analytics calculation and output helpers

real_time_analytics_uploadData_func <- function(rtaUserData) {
  # This is a placeholder as real-time data would likely be streamed.
  # For demonstration, we allow a file upload.
  tryCatch(
    {
      if (is.null(rtaUserData)) {
        return(NULL)
      }
      
      # Read data based on file type
      if (grepl("\\.csv$", rtaUserData$name, ignore.case = TRUE)) {
        df <- read.csv(rtaUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.tsv$", rtaUserData$name, ignore.case = TRUE)) {
        df <- read.tsv(rtaUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.xlsx$", rtaUserData$name, ignore.case = TRUE)) {
        df <- readxl::read_excel(rtaUserData$datapath)
      } else {
        stop("Unsupported file type. Please upload a CSV, TSV, or XLSX file.")
      }
      
      return(df)
    },
    error = function(e) {
      return(list(error = paste("Error reading data:", e$message)))
    }
  )
}

real_time_analytics_results_func <- function(data, time_col = NULL, value_col = NULL, window = 10) {
  tryCatch({
    if (is.null(data) || nrow(data) == 0) {
      stop("No data provided for analysis.")
    }
    # Identify numeric columns
    num_cols <- names(data)[sapply(data, is.numeric)]
    if (length(num_cols) == 0) stop("No numeric columns found in data.")
    # Summary statistics for all numeric columns
    summary_stats <- as.data.frame(lapply(data[num_cols], function(x) c(
      Mean = mean(x, na.rm = TRUE),
      SD = sd(x, na.rm = TRUE),
      Min = min(x, na.rm = TRUE),
      Max = max(x, na.rm = TRUE),
      Median = median(x, na.rm = TRUE),
      N = sum(!is.na(x))
    )))
    summary_stats <- t(summary_stats)
    # Time series plot and rolling mean/median if time_col and value_col are provided
    ts_plot <- NULL
    rolling_plot <- NULL
    if (!is.null(time_col) && !is.null(value_col) && time_col %in% names(data) && value_col %in% names(data)) {
      library(ggplot2)
      library(zoo)
      ts_df <- data[order(data[[time_col]]), c(time_col, value_col)]
      ts_df <- ts_df[complete.cases(ts_df), ]
      ts_plot <- ggplot(ts_df, aes_string(x = time_col, y = value_col)) +
        geom_line(color = "blue") +
        labs(title = "Time Series Plot", x = time_col, y = value_col) +
        theme_minimal()
      ts_df$rolling_mean <- zoo::rollmean(ts_df[[value_col]], k = window, fill = NA, align = "right")
      ts_df$rolling_median <- zoo::rollmedian(ts_df[[value_col]], k = window, fill = NA, align = "right")
      rolling_plot <- ggplot(ts_df, aes_string(x = time_col)) +
        geom_line(aes(y = rolling_mean), color = "red", na.rm = TRUE) +
        geom_line(aes(y = rolling_median), color = "green", na.rm = TRUE) +
        labs(title = paste("Rolling Mean/Median (window =", window, ")"), y = value_col) +
        theme_minimal()
    }
    list(
      summary_stats = summary_stats,
      ts_plot = ts_plot,
      rolling_plot = rolling_plot,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Real-time Analytics:", e$message))
  })
}

real_time_analytics_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  tagList(
    h4("Real-time Analytics"),
    p("Summary statistics and time series analysis for uploaded data.")
  )
}

real_time_analytics_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  tagList(
    h4("Summary Statistics for Numeric Columns"),
    renderTable(results$summary_stats, rownames = TRUE)
  )
}

real_time_analytics_plot <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  plots <- list()
  if (!is.null(results$ts_plot)) plots[[1]] <- results$ts_plot
  if (!is.null(results$rolling_plot)) plots[[2]] <- results$rolling_plot
  if (length(plots) > 0) {
    gridExtra::grid.arrange(grobs = plots)
  } else {
    plot.new()
    title("No time series or rolling plots available.")
  }
}