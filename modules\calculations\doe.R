# DOE calculations for CougarStats

# DOE calculation and output helpers

# 1. Data Upload Function
doe_uploadData_func <- function(doeUserData) {
  ext <- tools::file_ext(doeUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(doeUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(doeUserData$datapath),
         xlsx = readxl::read_xlsx(doeUserData$datapath),
         txt = readr::read_tsv(doeUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

# 2. Main Results Function (core calculations)
doe_results_func <- function(factors, levels, resolution = 3, replications = 1) {
  tryCatch({
    results <- doeResults_func(factors, levels, resolution, replications)
    results$error <- NULL
    results
  }, error = function(e) {
    list(error = paste("An error occurred during DOE calculation:", e$message))
  })
}

# 3. Hypothesis Test HTML Output
doe_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  tagList(
    h4("Design of Experiments (DOE)"),
    p("See summary table for design details.")
  )
}

# 4. Summary Table HTML Output
doe_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  out <- list(h4("DOE Summary"), renderPrint(results$summary))
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

# 5. Plot Output
doe_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  if (!is.null(results$plot)) {
    print(results$plot)
    return()
  }
  # Main effects plot (if design available)
  if (!is.null(results$design)) {
    matplot(as.matrix(results$design), type = 'l', main = 'Main Effects', ylab = 'Level')
    legend('topright', legend = colnames(results$design), lty = 1:ncol(results$design))
    return()
  }
  plot.new(); title("No plot available.")
}

doeResults_func <- function(factors, levels, resolution = 3, replications = 1) {
  if (!requireNamespace("FrF2", quietly = TRUE)) {
    stop("Package 'FrF2' is required for DOE analysis.")
  }
  if (!requireNamespace("ggplot2", quietly = TRUE)) {
    stop("Package 'ggplot2' is required for plotting.")
  }
  # Validate input
  if (is.null(factors) || is.null(levels) || length(factors) != length(levels)) {
    stop("Factors and levels must be specified and of equal length.")
  }
  # Create design
  design <- FrF2::FrF2(nruns = 2^length(factors) * replications, nfactors = length(factors),
                       factor.names = setNames(levels, factors), resolution = resolution)
  # Main effects plot (if response is available, user can upload data and join later)
  design_df <- as.data.frame(design)
  # For demonstration, plot first two factors
  plot_obj <- NULL
  if (length(factors) >= 2) {
    plot_obj <- ggplot2::ggplot(design_df, ggplot2::aes_string(x = factors[1], fill = factors[2])) +
      ggplot2::geom_bar(position = "dodge") +
      ggplot2::labs(title = "DOE Factorial Design: First Two Factors")
  }
  list(
    design = design_df,
    summary = summary(design),
    plot = plot_obj
  )
} 