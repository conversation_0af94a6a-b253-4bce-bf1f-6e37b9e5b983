LatentClassServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    lcData <- eventReactive(input$lcUserData, {
      handle_file_upload(input$lcUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(lcData(), {
      data <- lcData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'lcVars', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    lcValidationErrors <- reactive({
      errors <- c()
      data <- lcData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$lcVars) || length(input$lcVars) < 2) {
        errors <- c(errors, "Select at least two variables for latent class analysis.")
      }
      if (is.null(input$lcClasses) || input$lcClasses < 2 || input$lcClasses > 10) {
        errors <- c(errors, "Number of classes must be between 2 and 10.")
      }
      if (nrow(data) < 50) {
        errors <- c(errors, "Latent class analysis requires at least 50 observations.")
      }
      errors
    })
    
    # Latent class analysis reactive
    lcResult <- eventReactive(input$goLC, {
      data <- lcData()
      req(data, input$lcVars, input$lcClasses)
      
      # Check if poLCA package is available
      if (!requireNamespace("poLCA", quietly = TRUE)) {
        stop("Package 'poLCA' is required for latent class analysis.")
      }
      
      # Remove rows with missing values
      complete_data <- data[complete.cases(data[, input$lcVars, drop = FALSE]), ]
      
      if (nrow(complete_data) < 50) {
        stop("Insufficient complete cases for latent class analysis.")
      }
      
      # Perform latent class analysis
      latent_class_analysis(complete_data, input$lcVars, input$lcClasses)
    })
    
    # Error handling
    output$lcError <- renderUI({
      errors <- lcValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          lcResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "Latent Class Analysis Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$lcModelSummary <- renderUI({
      req(lcResult())
      res <- lcResult()
      
      tagList(
        h4("Latent Class Analysis Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Metric = c("Number of Classes", "Observations", "Log-Likelihood", "AIC", "BIC", "Entropy"),
            Value = c(
              res$n_classes,
              res$n_obs,
              round(res$log_likelihood, 3),
              round(res$aic, 3),
              round(res$bic, 3),
              round(res$entropy, 4)
            )
          )
        }),
        h4("Class Probabilities"),
        renderTable({
          res$class_probabilities
        }),
        h4("Conditional Probabilities"),
        renderTable({
          res$conditional_probabilities
        })
      )
    })
    
    output$lcPlot <- renderPlot({
      req(lcResult())
      res <- lcResult()
      
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # Class probabilities
      if (!is.null(res$class_probabilities)) {
        barplot(res$class_probabilities$Probability, 
                names.arg = res$class_probabilities$Class,
                main = "Class Probabilities", 
                ylab = "Probability", col = "steelblue")
      }
      
      # Conditional probabilities heatmap
      if (!is.null(res$conditional_probabilities)) {
        # Create heatmap of conditional probabilities
        cp_matrix <- as.matrix(res$conditional_probabilities[, -1])
        rownames(cp_matrix) <- res$conditional_probabilities$Variable
        heatmap(cp_matrix, main = "Conditional Probabilities", 
                Rowv = NA, Colv = NA, scale = "none")
      }
      
      # Class assignment distribution
      if (!is.null(res$class_assignments)) {
        hist(res$class_assignments, main = "Class Assignment Distribution",
             xlab = "Class", ylab = "Frequency", breaks = seq(0.5, res$n_classes + 0.5, 1))
      }
      
      # Model fit comparison
      if (!is.null(res$fit_comparison)) {
        plot(res$fit_comparison$Classes, res$fit_comparison$BIC, 
             type = "b", main = "Model Fit Comparison (BIC)",
             xlab = "Number of Classes", ylab = "BIC", pch = 19)
      }
      
      par(mfrow = c(1, 1))
    })
    
    output$lcDiagnostics <- renderUI({
      req(lcResult())
      res <- lcResult()
      
      tagList(
        h4("Model Diagnostics"),
        renderTable({
          data.frame(
            Metric = c("Likelihood Ratio Chi-square", "Degrees of Freedom", "P-value", "Entropy R-squared"),
            Value = c(
              round(res$lr_chi_square, 3),
              res$df,
              round(res$p_value, 4),
              round(res$entropy_r_squared, 4)
            )
          )
        }),
        h4("Class Characteristics"),
        renderTable({
          res$class_characteristics
        })
      )
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$lcDataSummary <- renderUI({
      req(lcData(), input$lcVars)
      data <- lcData()
      vars <- input$lcVars
      
      tagList(
        h4("Dataset Summary"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "Selected Variables", "Complete Cases", "Missing Cases"),
            Value = c(
              nrow(data),
              ncol(data),
              length(vars),
              sum(complete.cases(data[, vars, drop = FALSE])),
              sum(!complete.cases(data[, vars, drop = FALSE]))
            )
          )
        }),
        h4("Variable Summary"),
        renderTable({
          data.frame(
            Variable = vars,
            Type = sapply(data[, vars, drop = FALSE], class),
            Missing = sapply(data[, vars, drop = FALSE], function(x) sum(is.na(x))),
            Categories = sapply(data[, vars, drop = FALSE], function(x) length(unique(x))),
            stringsAsFactors = FALSE
          )
        })
      )
    })
    
    output$lcAssumptions <- renderUI({
      req(lcResult())
      res <- lcResult()
      
      tagList(
        h4("Latent Class Analysis Assumptions"),
        renderTable({
          data.frame(
            Assumption = c("Local Independence", "Categorical Variables", "No Missing Data", "Adequate Sample Size"),
            Status = c(
              ifelse(res$local_independence_test > 0.05, "Pass", "Fail"),
              "Pass",
              ifelse(res$missing_data_pct < 5, "Pass", "Fail"),
              ifelse(res$n_obs >= 50, "Pass", "Fail")
            ),
            Description = c(
              "Variables are independent within classes",
              "Variables are categorical",
              "Minimal missing data",
              "Sufficient observations for stable estimates"
            )
          )
        }),
        h4("Model Selection Criteria"),
        renderTable({
          data.frame(
            Criterion = c("AIC", "BIC", "Entropy", "Likelihood Ratio"),
            Value = c(
              round(res$aic, 3),
              round(res$bic, 3),
              round(res$entropy, 4),
              round(res$lr_chi_square, 3)
            ),
            Interpretation = c(
              "Lower is better",
              "Lower is better",
              "Higher is better (>0.8)",
              "Significant improvement"
            )
          )
        })
      )
    })
    
    output$lcDiagnosticPlots <- renderPlot({
      req(lcResult())
      res <- lcResult()
      
      par(mfrow = c(2, 2))
      
      # Class size distribution
      if (!is.null(res$class_sizes)) {
        barplot(res$class_sizes, main = "Class Size Distribution",
                xlab = "Class", ylab = "Number of Observations", col = "lightblue")
      }
      
      # Posterior probabilities
      if (!is.null(res$posterior_probabilities)) {
        boxplot(res$posterior_probabilities, main = "Posterior Probabilities by Class",
                xlab = "Class", ylab = "Posterior Probability")
      }
      
      # Model fit comparison
      if (!is.null(res$fit_comparison)) {
        plot(res$fit_comparison$Classes, res$fit_comparison$AIC, 
             type = "b", main = "Model Fit Comparison (AIC)",
             xlab = "Number of Classes", ylab = "AIC", pch = 19)
      }
      
      # Entropy plot
      if (!is.null(res$entropy_by_class)) {
        plot(res$entropy_by_class$Class, res$entropy_by_class$Entropy,
             type = "b", main = "Entropy by Class",
             xlab = "Class", ylab = "Entropy", pch = 19)
        abline(h = 0.8, col = "red", lty = 2)
      }
      
      par(mfrow = c(1, 1))
    })
    
    # Uploaded Data Tab Outputs
    output$lcDataTable <- renderDT({
      req(lcData())
      data <- lcData()
      
      DT::datatable(
        data,
        options = list(
          pageLength = 10,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
        ),
        extensions = 'Buttons',
        filter = 'top',
        rownames = FALSE
      )
    })
    
    output$lcDataInfo <- renderUI({
      req(lcData())
      data <- lcData()
      
      tagList(
        h4("Dataset Information"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "File Name"),
            Value = c(nrow(data), ncol(data), 
                     ifelse(!is.null(input$lcUserData), input$lcUserData$name, "Unknown"))
          )
        }),
        h4("Variable Information"),
        renderTable({
          data.frame(
            Variable = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Unique = sapply(data, function(x) length(unique(x)))
          )
        })
      )
    })
  })
} 