# Cochran's Q Test calculation and output helpers

cochrans_q_uploadData_func <- function(cqUserData) {
  ext <- tools::file_ext(cqUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(cqUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(cqUserData$datapath),
         xlsx = readxl::read_xlsx(cqUserData$datapath),
         txt = readr::read_tsv(cqUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

cochrans_q_results_func <- function(data, binary_vars) {
  tryCatch({
    if (!requireNamespace("DescTools", quietly = TRUE)) {
      stop("Package 'DescTools' needed for Cochran's Q test.")
    }
    
    qdata <- data[, binary_vars, drop = FALSE]
    test_result <- DescTools::CochranQTest(as.matrix(qdata))
    
    desc_stats <- data.frame(
      Variable = binary_vars,
      N = colSums(!is.na(qdata)),
      Successes = colSums(qdata, na.rm = TRUE)
    )
    desc_stats$Proportion <- desc_stats$Successes / desc_stats$N
    
    list(
      test = test_result,
      desc_stats = desc_stats,
      data = qdata,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Cochran's Q test calculation:", e$message))
  })
}

cochrans_q_ht_html <- function(results, sigLvl) {
  test <- results$test
  p_value <- test$p.value
  
  conclusion <- if (p_value < sigLvl) {
    "Reject H0. There is a significant difference among the proportions of successes."
  } else {
    "Do not reject H0. There is no significant difference among the proportions of successes."
  }
  
  withMathJax(tagList(
    h4("Cochran's Q Test"),
    p("$H_0$: The proportion of successes is the same across all groups."),
    p("$H_A$: The proportion of successes is different in at least one group."),
    p(sprintf("Test Statistic (Q): %.4f", test$statistic)),
    p(sprintf("Degrees of Freedom: %d", test$parameter)),
    p(sprintf("P-value: %.4f", p_value)),
    p(strong("Conclusion: "), conclusion)
  ))
}

cochrans_q_summary_html <- function(results) {
  tagList(
    h4("Descriptive Statistics by Group"),
    renderTable(results$desc_stats, digits = 4)
  )
}

cochrans_q_plot <- function(results) {
  plot_data <- results$desc_stats
  
  ggplot(plot_data, aes(x = Variable, y = Proportion, fill = Variable)) +
    geom_bar(stat = "identity", alpha = 0.7) +
    labs(title = "Proportion of Successes by Group",
         x = "Group",
         y = "Proportion") +
    theme_minimal() +
    theme(legend.position = "none")
}