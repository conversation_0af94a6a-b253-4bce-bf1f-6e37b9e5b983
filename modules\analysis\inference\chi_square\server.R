source("modules/calculations/chi_square.R")

chiSquareServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # --- Reactives ---
    
    # Get the currently active matrix based on user's dimension selection
    active_matrix <- reactive({
      mat_input <- switch(input$chisquareDimension,
        "2 x 2" = input$chiSqInput2x2,
        "2 x 3" = input$chiSqInput2x3,
        "3 x 2" = input$chiSqInput3x2,
        "3 x 3" = input$chiSqInput3x3
      )
      # Ensure it's a numeric matrix
      mat <- apply(mat_input, 2, function(x) as.numeric(as.character(x)))
      rownames(mat) <- rownames(mat_input)
      mat
    })
    
    # Validate the input matrix
    chiSqValidationErrors <- reactive({
      errors <- c()
      mat <- tryCatch({ active_matrix() }, error = function(e) NULL)
      
      if (is.null(mat) || !is.matrix(mat)) {
        errors <- c(errors, "Input data could not be read as a valid matrix.")
        return(errors)
      }
      if (any(is.na(mat))) {
        errors <- c(errors, "Matrix contains non-numeric or missing values. Please ensure all cells are numbers.")
      }
      if (any(mat < 0)) {
        errors <- c(errors, "Matrix cannot contain negative values.")
      }
      if (any(mat %% 1 != 0)) {
        errors <- c(errors, "Matrix must contain integer values for this test.")
      }
      if (all(mat == 0)) {
        errors <- c(errors, "All cell values cannot be zero.")
      }
      errors
    })
    
    # Perform calculations
    results <- eventReactive(input$goInference, {
      if (length(chiSqValidationErrors()) > 0) return(NULL)
      
      chi_square_results_func(
        matrix_data = active_matrix(),
        yates_correction = input$chiSquareYates
      )
    })
    
    # --- Observers ---
    
    # Trigger calculations and render results
    observeEvent(input$goInference, {
      output$chiSquareResults <- renderUI({
        
        # First, check for validation errors
        errors <- chiSqValidationErrors()
        if (length(errors) > 0) {
          return(errorScreenUI(title = "Validation Error(s) in Chi-Square Test", errors = errors))
        }
        
        # If validation passes, get results
        res <- results()
        req(res)
        
        # Second, check for calculation errors
        if (!is.null(res$error)) {
          return(errorScreenUI(title = "Calculation Error", errors = res$error))
        }
        
        # If all good, render tabs
        sigLvl <- as.numeric(sub("%", "", input$chisquareSigLvl)) / 100
        
        tagList(
          tabsetPanel(
            id = ns("chiSqTabset"),
            tabPanel(
              "Analysis",
              titlePanel("Test Results"),
              chi_square_render_analysis_html(res, sigLvl)
            ),
            tabPanel(
              "Summary & Assumptions",
              titlePanel("Effect Size & Assumptions"),
              chi_square_render_summary_html(res)
            ),
            tabPanel(
              "Tables",
              titlePanel("Observed & Expected Tables"),
              chi_square_render_tables_html(res, active_matrix())
            )
          )
        )
      })
    })
  })
}