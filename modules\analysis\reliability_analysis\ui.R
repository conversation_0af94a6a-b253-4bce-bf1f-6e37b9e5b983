# Reliability Analysis UI
# <PERSON><PERSON><PERSON>'s alpha, split-half reliability, item analysis

ReliabilityAnalysisUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(
      width = 3,
      radioButtons(
        inputId = ns("reliabilityDataMethod"),
        label = "Data Input Method:",
        choices = c("Upload File" = "Upload File", "Manual Entry" = "Manual Entry"),
        selected = "Upload File"
      ),
      conditionalPanel(
        condition = "input.reliabilityDataMethod == 'Upload File'",
        ns = ns,
        fileInput(
          inputId = ns("reliabilityUserData"),
          label = "Upload Data File:",
          accept = c(".csv", ".txt", ".xlsx", ".xls"),
          buttonLabel = "Browse Files",
          placeholder = "No file selected"
        ),
        selectizeInput(
          inputId = ns("reliabilityItems"),
          label = "Select Scale Items:",
          choices = NULL,
          multiple = TRUE,
          options = list(placeholder = "Select scale items...")
        )
      ),
      conditionalPanel(
        condition = "input.reliabilityDataMethod == 'Manual Entry'",
        ns = ns,
        textAreaInput(
          inputId = ns("reliabilityManualData"),
          label = "Enter Item Scores (comma-separated):",
          placeholder = "Item1,Item2,Item3,Item4\n5,4,3,4\n4,5,4,3\n3,4,5,4",
          rows = 8
        )
      ),
      h5("Reliability Measures"),
      checkboxInput(
        inputId = ns("reliabilityCronbachAlpha"),
        label = "Cronbach's Alpha",
        value = TRUE
      ),
      checkboxInput(
        inputId = ns("reliabilitySplitHalf"),
        label = "Split-Half Reliability",
        value = TRUE
      ),
      checkboxInput(
        inputId = ns("reliabilityItemAnalysis"),
        label = "Item Analysis",
        value = TRUE
      ),
      checkboxInput(
        inputId = ns("reliabilityInterItem"),
        label = "Inter-Item Correlations",
        value = TRUE
      ),
      selectInput(
        inputId = ns("reliabilitySplitMethod"),
        label = "Split-Half Method:",
        choices = c("First Half vs Second Half" = "first_second", 
                   "Odd vs Even Items" = "odd_even",
                   "Random Split" = "random"),
        selected = "odd_even"
      ),
      numericInput(
        inputId = ns("reliabilityConfLevel"),
        label = "Confidence Level:",
        value = 0.95,
        min = 0.5,
        max = 0.99,
        step = 0.01
      ),
      actionButton(
        inputId = ns("goReliability"),
        label = "Perform Reliability Analysis",
        class = "btn-primary",
        style = "width: 100%;"
      ),
      br(),
      br(),
      helpText(
        "Cronbach's alpha measures internal consistency reliability.",
        "Split-half reliability assesses scale stability.",
        "Item analysis identifies problematic items.",
        "Inter-item correlations show item relationships."
      )
    ),
    mainPanel(
      width = 9,
      textOutput(ns("reliabilityError")),
      tableOutput(ns("reliabilitySummary")),
      plotOutput(ns("reliabilityPlot")),
      tableOutput(ns("reliabilityItemsTable")),
      tableOutput(ns("reliabilityInterItemTable")),
      textOutput(ns("reliabilityConclusion"))
    )
  )
} 