ChangePointUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("cpUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("cpSeries"), "Series Variable", choices = NULL),
        br(),
        actionButton(ns("goCP"), label = "Detect Change Points", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("cpError")),
        plotOutput(ns("cpPlot")),
        tableOutput(ns("cpSummary"))
      )
    )
  )
} 