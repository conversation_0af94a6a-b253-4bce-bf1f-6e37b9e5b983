# DOE Server
doeServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns

    # UI for specifying factors and levels
    output$doeFactorInputs <- renderUI({
      tagList(
        numericInput(ns("doeNumFactors"), "Number of Factors", 2, min = 2, max = 8),
        uiOutput(ns("doeFactorNames")),
        uiOutput(ns("doeFactorLevels")),
        numericInput(ns("doeResolution"), "Resolution", 3, min = 2, max = 5),
        numericInput(ns("doeReplications"), "Replications", 1, min = 1)
      )
    })
    output$doeFactorNames <- renderUI({
      n <- input$doeNumFactors
      if (is.null(n) || n < 2) return(NULL)
      lapply(1:n, function(i) {
        textInput(ns(paste0("doeFactor", i)), paste("Factor", i, "Name"), value = paste0("F", i))
      })
    })
    output$doeFactorLevels <- renderUI({
      n <- input$doeNumFactors
      if (is.null(n) || n < 2) return(NULL)
      lapply(1:n, function(i) {
        textInput(ns(paste0("doeLevel", i)), paste("Levels for Factor", i, "(comma-separated)"), value = "-1,1")
      })
    })

    # Run DOE analysis
    doeResults <- eventReactive(input$goDOE, {
      n <- input$doeNumFactors
      if (is.null(n) || n < 2) return(NULL)
      factors <- sapply(1:n, function(i) input[[paste0("doeFactor", i)]] )
      levels <- lapply(1:n, function(i) strsplit(input[[paste0("doeLevel", i)]], ",")[[1]])
      resolution <- input$doeResolution
      replications <- input$doeReplications
      modules::calculations::doeResults_func(
        factors = factors,
        levels = levels,
        resolution = resolution,
        replications = replications
      )
    })

    # Outputs
    output$doeHT <- renderUI({
      results <- doeResults()
      if (is.null(results)) return(NULL)
      tagList(
        verbatimTextOutput(ns("doeSummary")),
        DT::DTOutput(ns("doeDesignTable"))
      )
    })
    output$doeSummary <- renderPrint({
      results <- doeResults()
      if (is.null(results)) return(NULL)
      print(results$summary)
    })
    output$doeDesignTable <- DT::renderDT({
      results <- doeResults()
      if (is.null(results)) return(NULL)
      DT::datatable(results$design)
    })
    output$doePlot <- renderPlot({
      results <- doeResults()
      if (is.null(results) || is.null(results$plot)) return(NULL)
      results$plot
    })
    output$doeConclusionOutput <- renderUI({
      results <- doeResults()
      if (is.null(results)) return(NULL)
      tags$p("DOE design generated. See summary, design matrix, and plot above.")
    })
    output$renderDOEData <- renderUI({
      tagList(
        titlePanel("DOE Design Inputs"),
        br(),
        br(),
        uiOutput(ns("doeFactorInputs"))
      )
    })
  })
} 
} 