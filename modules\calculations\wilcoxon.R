# Wilcoxon signed-rank test calculation and output helpers

wilcoxon_uploadData_func <- function(wilcoxonUserData) {
  ext <- tools::file_ext(wilcoxonUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(wilcoxonUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(wilcoxonUserData$datapath),
         xlsx = readxl::read_xlsx(wilcoxonUserData$datapath),
         txt = readr::read_tsv(wilcoxonUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

wilcoxon_results_func <- function(data, x_var, y_var, conf_level, alternative) {
  tryCatch({
    x <- data[[x_var]]
    y <- data[[y_var]]
    
    # Core test
    test <- wilcox.test(x, y, paired = TRUE, conf.int = TRUE, conf.level = conf_level, alternative = alternative, exact = FALSE)
    
    # Effect size (r = Z/sqrt(N))
    n_total <- length(x)
    z_score <- qnorm(test$p.value / 2, lower.tail = FALSE)
    effect_size <- abs(z_score) / sqrt(n_total)
    
    # Descriptive statistics
    desc_stats <- data.frame(
      Variable = c(x_var, y_var),
      N = c(length(x), length(y)),
      Mean = c(mean(x, na.rm = TRUE), mean(y, na.rm = TRUE)),
      Median = c(median(x, na.rm = TRUE), median(y, na.rm = TRUE)),
      SD = c(sd(x, na.rm = TRUE), sd(y, na.rm = TRUE)),
      Min = c(min(x, na.rm = TRUE), min(y, na.rm = TRUE)),
      Max = c(max(x, na.rm = TRUE), max(y, na.rm = TRUE))
    )
    
    # Difference summary
    diff <- x - y
    diff_stats <- data.frame(
      Statistic = c("Mean Difference", "Median Difference", "SD of Differences", "Min Difference", "Max Difference"),
      Value = c(mean(diff, na.rm = TRUE), median(diff, na.rm = TRUE), 
               sd(diff, na.rm = TRUE), min(diff, na.rm = TRUE), max(diff, na.rm = TRUE))
    )
    
    # Return all results in a list
    list(
      test = test,
      effect_size = effect_size,
      desc_stats = desc_stats,
      diff_stats = diff_stats,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Wilcoxon signed-rank test calculation:", e$message))
  })
}

wilcoxon_ht_html <- function(results, conf_level) {
  test <- results$test
  effect_size <- results$effect_size
  
  # Effect size interpretation
  effect_interp <- if (effect_size < 0.1) {
    "Negligible effect size"
  } else if (effect_size < 0.3) {
    "Small effect size"
  } else if (effect_size < 0.5) {
    "Medium effect size"
  } else {
    "Large effect size"
  }
  
  # Conclusion
  conclusion <- if (test$p.value < (1 - conf_level)) {
    sprintf("Since P (%.4f) < %.2f, reject H0. There is a significant difference between the paired observations.", test$p.value, (1 - conf_level))
  } else {
    sprintf("Since P (%.4f) >= %.2f, do not reject H0. There is not a significant difference between the paired observations.", test$p.value, (1 - conf_level))
  }
  
  withMathJax(tagList(
    h4('Hypotheses'),
    p('$H_0$: The median difference is zero'),
    p('$H_A$: The median difference is not zero'),
    br(),
    h4('Test Statistic'),
    p(sprintf('$W = %.4f$', test$statistic)),
    br(),
    h4('P-value'),
    p(sprintf('$P = %.4f$', test$p.value)),
    br(),
    h4('Confidence Interval'),
    if (!is.null(test$conf.int)) {
      p(sprintf('CI = (%.4f, %.4f)', test$conf.int[1], test$conf.int[2]))
    },
    br(),
    h4("Effect Size"),
    p(sprintf("Effect size (r): %.4f", effect_size)),
    p(effect_interp),
    br(),
    h4("Assumptions Check"),
    p("1. Paired observations"),
    p("2. Ordinal or continuous data"),
    p("3. Symmetric distribution of differences"),
    br(),
    h4('Conclusion'),
    p(conclusion)
  ))
}

wilcoxon_summary_html <- function(results) {
  tagList(
    h4("Descriptive Statistics"),
    renderTable(results$desc_stats, digits = 4),
    br(),
    h4("Difference Summary"),
    renderTable(results$diff_stats, digits = 4)
  )
}

wilcoxon_plot <- function(data, x_var, y_var) {
  plot_data <- data.frame(
    Value = c(data[[x_var]], data[[y_var]]),
    Variable = rep(c(x_var, y_var), each = nrow(data))
  )
  
  ggplot(plot_data, aes(x = Variable, y = Value, fill = Variable)) +
    geom_boxplot(alpha = 0.7) +
    labs(title = "Boxplot of Paired Variables",
         x = "Variable",
         y = "Value") +
    theme_minimal() +
    theme(legend.position = "none")
}