# Partial and Semi-partial Correlations calculation and output helpers

partial_correlations_uploadData_func <- function(pcUserData) {
  ext <- tools::file_ext(pcUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(pcUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(pcUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(pcUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(pcUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

partial_correlations_results_func <- function(data, vars, control_vars) {
  tryCatch({
    if (!requireNamespace("ppcor", quietly = TRUE)) {
      stop("Package 'ppcor' is required for Partial Correlations.")
    }
    
    # Partial correlation
    pcor_result <- ppcor::pcor(data[, c(vars, control_vars)])
    
    # Semi-partial correlation
    spcor_result <- ppcor::spcor(data[, c(vars, control_vars)])
    
    list(
      pcor = pcor_result,
      spcor = spcor_result,
      data = data,
      vars = vars,
      control_vars = control_vars,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Partial Correlation calculation:", e$message))
  })
}

partial_correlations_ht_html <- function(results) {
  tagList(
    h4("Partial and Semi-Partial Correlations"),
    p("The tables below show the partial and semi-partial correlation coefficients, p-values, and other statistics.")
  )
}

partial_correlations_summary_html <- function(results) {
  tagList(
    h4("Partial Correlations"),
    renderTable(results$pcor$estimate, rownames = TRUE),
    h4("Partial Correlations p-values"),
    renderTable(results$pcor$p.value, rownames = TRUE),
    h4("Semi-Partial Correlations"),
    renderTable(results$spcor$estimate, rownames = TRUE),
    h4("Semi-Partial Correlations p-values"),
    renderTable(results$spcor$p.value, rownames = TRUE)
  )
}

partial_correlations_plot <- function(results) {
  # No standard plot for this, summary table is the main output
  p("No plot available for partial correlations. Please refer to the summary tables.")
}