# Random Forest Server
randomForestServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Upload and parse data
    rfUploadData <- reactive({
      req(input$rfUserData)
      ext <- tools::file_ext(input$rfUserData$name)
      if (ext %in% c("csv", "txt")) {
        read.csv(input$rfUserData$datapath)
      } else if (ext %in% c("xls", "xlsx")) {
        if (!requireNamespace("readxl", quietly = TRUE)) stop("Install 'readxl' for Excel support.")
        readxl::read_excel(input$rfUserData$datapath)
      } else {
        stop("Unsupported file type.")
      }
    })

    # UI for selecting columns and RF params
    output$rfColSelectors <- renderUI({
      df <- rfUploadData()
      req(df)
      tagList(
        selectInput(ns("rfResponseCol"), "Response Variable", choices = names(df)),
        selectInput(ns("rfPredictors"), "Predictors", choices = names(df), multiple = TRUE),
        numericInput(ns("rfNtree"), "Number of Trees (ntree)", 500, min = 10),
        numericInput(ns("rfMtry"), "Number of Variables at Split (mtry)", NA, min = 1)
      )
    })

    # Run Random Forest analysis
    rfResults <- eventReactive(input$goRF, {
      df <- rfUploadData()
      req(input$rfResponseCol, input$rfPredictors)
      mtry_val <- if (is.na(input$rfMtry)) NULL else input$rfMtry
      modules::calculations::randomForestResults_func(
        data = df,
        response = input$rfResponseCol,
        predictors = input$rfPredictors,
        ntree = input$rfNtree,
        mtry = mtry_val
      )
    })

    # Outputs
    output$rfHT <- renderUI({
      results <- rfResults()
      if (is.null(results)) return(NULL)
      tagList(
        verbatimTextOutput(ns("rfSummary")),
        tags$p(paste("Accuracy:", round(results$accuracy, 4)))
      )
    })
    output$rfSummary <- renderPrint({
      results <- rfResults()
      if (is.null(results)) return(NULL)
      print(results$summary)
    })
    output$rfPlot <- renderPlot({
      results <- rfResults()
      if (is.null(results) || is.null(results$plot)) return(NULL)
      results$plot
    })
    output$rfConclusionOutput <- renderUI({
      results <- rfResults()
      if (is.null(results)) return(NULL)
      tags$p("Random Forest model fitted. See summary and variable importance plot above.")
    })
    output$renderRFData <- renderUI({
      req(rfUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("rfInitialUploadTable")), style = "width: 75%"),
        br(),
        br(),
        uiOutput(ns("rfColSelectors"))
      )
    })
    output$rfInitialUploadTable <- DT::renderDT({
      req(rfUploadData())
      DT::datatable(rfUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(rfUploadData())))))
    })
  })
} 