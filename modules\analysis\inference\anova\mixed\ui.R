# Mixed ANOVA UI
# Between and within-subjects factors

MixedAnovaSidebarUI <- function(id) {
  ns <- NS(id)
  
  tagList(
    # Data Input
    radioButtons(
      inputId = ns("mixedDataMethod"),
      label = "Data Input Method:",
      choices = c("Upload File" = "Upload File", "Manual Entry" = "Manual Entry"),
      selected = "Upload File"
    ),
    # File Upload
    conditionalPanel(
      condition = "input.mixedDataMethod == 'Upload File'",
      ns = ns,
      fileInput(
        inputId = ns("mixedUserData"),
        label = "Upload Data File:",
        accept = c(".csv", ".txt", ".xlsx", ".xls"),
        buttonLabel = "Browse Files",
        placeholder = "No file selected"
      ),
      selectizeInput(
        inputId = ns("mixedDependentVariable"),
        label = "Select Dependent Variable:",
        choices = NULL,
        options = list(placeholder = "Select dependent variable...")
      ),
      selectizeInput(
        inputId = ns("mixedBetweenFactor"),
        label = "Select Between-Subjects Factor:",
        choices = NULL,
        options = list(placeholder = "Select between-subjects factor...")
      ),
      selectizeInput(
        inputId = ns("mixedWithinFactor"),
        label = "Select Within-Subjects Factor:",
        choices = NULL,
        options = list(placeholder = "Select within-subjects factor...")
      ),
      selectizeInput(
        inputId = ns("mixedSubjectID"),
        label = "Select Subject ID Variable:",
        choices = NULL,
        options = list(placeholder = "Select subject ID variable...")
      )
    ),
    # Manual Entry
    conditionalPanel(
      condition = "input.mixedDataMethod == 'Manual Entry'",
      ns = ns,
      textAreaInput(
        inputId = ns("mixedManualData"),
        label = "Enter Data (comma-separated):",
        placeholder = "Subject,Between,Within,Dependent\n1,A,Time1,10\n1,A,Time2,12\n2,B,Time1,8\n2,B,Time2,11",
        rows = 8
      )
    ),
    # Analysis Options
    h5("Analysis Options"),
    checkboxInput(
      inputId = ns("mixedSphericityTest"),
      label = "Test Sphericity Assumption",
      value = TRUE
    ),
    checkboxInput(
      inputId = ns("mixedGreenhouseGeisser"),
      label = "Use Greenhouse-Geisser Correction",
      value = TRUE
    ),
    checkboxInput(
      inputId = ns("mixedHuynhFeldt"),
      label = "Use Huynh-Feldt Correction",
      value = TRUE
    ),
    checkboxInput(
      inputId = ns("mixedEffectSize"),
      label = "Calculate Effect Sizes",
      value = TRUE
    ),
    numericInput(
      inputId = ns("mixedConfLevel"),
      label = "Confidence Level:",
      value = 0.95,
      min = 0.5,
      max = 0.99,
      step = 0.01
    ),
    actionButton(
      inputId = ns("goMixedAnova"),
      label = "Perform Mixed ANOVA",
      class = "btn-primary",
      style = "width: 100%;"
    ),
    br(),
    br(),
    # Help text
    helpText(
      "Mixed ANOVA combines between-subjects and within-subjects factors.",
      "Sphericity assumption applies to within-subjects factors.",
      "Greenhouse-Geisser and Huynh-Feldt correct for violations.",
      "Effect sizes include partial eta-squared and Cohen's d."
    )
  )
}

MixedAnovaMainUI <- function(id) {
  ns <- NS(id)
  
  tagList(
    uiOutput(ns("mixedResults"))
  )
}

MixedAnovaUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(
      MixedAnovaSidebarUI(id)
    ),
    mainPanel(
      MixedAnovaMainUI(id)
    )
  )
} 