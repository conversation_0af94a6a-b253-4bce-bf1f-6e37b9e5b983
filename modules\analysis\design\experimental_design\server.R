experimentalDesignServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactives
    edResults <- reactive({
      edResults_func(
        TRUE,
        input$edDesignType,
        input$edCRDTreatments,
        input$edCRDReplicates,
        input$edRBDTreatments,
        input$edRBDBlocks,
        input$edLSDSize,
        input$edFactorialFactors,
        input$edFactorialLevels,
        input$edFactorialReplicates,
        input$edRSDFactors,
        input$edRSDType
      )
    })
    
    # Validation errors
    edValidationErrors <- reactive({
      errors <- c()
      
      if (input$edDesignType == "Completely Randomized Design (CRD)") {
        if (input$edCRDTreatments < 2) {
          errors <- c(errors, "Number of treatments must be at least 2.")
        }
        if (input$edCRDReplicates < 2) {
          errors <- c(errors, "Number of replicates must be at least 2.")
        }
      } else if (input$edDesignType == "Randomized Block Design (RBD)") {
        if (input$edRBDTreatments < 2) {
          errors <- c(errors, "Number of treatments must be at least 2.")
        }
        if (input$edRBDBlocks < 2) {
          errors <- c(errors, "Number of blocks must be at least 2.")
        }
      } else if (input$edDesignType == "Latin Square Design (LSD)") {
        if (input$edLSDSize < 3) {
          errors <- c(errors, "Square size must be at least 3.")
        }
        if (input$edLSDSize > 10) {
          errors <- c(errors, "Square size cannot exceed 10 for practical purposes.")
        }
      } else if (input$edDesignType == "Factorial Design") {
        if (input$edFactorialFactors < 2) {
          errors <- c(errors, "Number of factors must be at least 2.")
        }
        if (input$edFactorialFactors > 5) {
          errors <- c(errors, "Number of factors cannot exceed 5 for practical purposes.")
        }
        if (input$edFactorialLevels < 2) {
          errors <- c(errors, "Number of levels must be at least 2.")
        }
        if (input$edFactorialLevels > 5) {
          errors <- c(errors, "Number of levels cannot exceed 5 for practical purposes.")
        }
      } else if (input$edDesignType == "Response Surface Design") {
        if (input$edRSDFactors < 2) {
          errors <- c(errors, "Number of factors must be at least 2.")
        }
        if (input$edRSDFactors > 5) {
          errors <- c(errors, "Number of factors cannot exceed 5 for practical purposes.")
        }
      }
      
      errors
    })
    
    # Outputs
    output$edResults <- renderUI({
      results <- edResults()
      if (is.null(results)) return(NULL)
      experimentalDesignResults(edResults, reactive({input$edSigLvl}))
    })
    
    output$experimentalDesignPlot <- renderPlot({
      results <- edResults()
      if (is.null(results)) return(NULL)
      experimentalDesignPlot(edResults, reactive({input$edSigLvl}))
    })
    
    output$edInstructions <- renderUI({
      results <- edResults()
      if (is.null(results)) return(NULL)
      experimentalDesignInstructions(edResults, reactive({input$edSigLvl}))
    })
    
    output$edLayoutOutput <- renderUI({
      results <- edResults()
      if (is.null(results)) return(NULL)
      experimentalDesignLayout(edResults, reactive({input$edSigLvl}))
    })
    
    output$edLayoutTable <- DT::renderDT({
      results <- edResults()
      if (is.null(results) || is.null(results$layout)) return(NULL)
      
      layout_df <- results$layout
      DT::datatable(layout_df,
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(layout_df)))))
    })
    
    output$edAnalysisPlan <- renderUI({
      results <- edResults()
      if (is.null(results)) return(NULL)
      experimentalDesignAnalysisPlan(edResults, reactive({input$edSigLvl}))
    })
    
    # Show main results or error screen when Calculate is pressed
    observeEvent(input$goExperimentalDesign, {
      output$experimentalDesignResults <- renderUI({
        errors <- edValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Experimental Design", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("edTabset"),
              selected = "Design",
              tabPanel(
                id    = ns("ed"),
                title = "Design",
                titlePanel("Experimental Design"),
                br(),
                uiOutput(ns('edResults')),
                br(),
                plotOutput(ns('experimentalDesignPlot'), width = "100%", height = "500px"),
                br(),
                uiOutput(ns('edInstructions'))
              ),
              tabPanel(
                id    = ns("edLayout"),
                title = "Design Layout",
                DTOutput(ns("edLayoutTable")),
                uiOutput(ns("edLayoutOutput"))
              ),
              tabPanel(
                id    = ns("edAnalysis"),
                title = "Analysis Plan",
                uiOutput(ns("edAnalysisPlan"))
              )
            )
          )
        }
      })
    })
  })
} 