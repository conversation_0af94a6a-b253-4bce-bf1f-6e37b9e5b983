TimeSeriesServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactive values to store analysis results
    rv <- reactiveValues(
      ts_data = NULL,
      analysis_results = NULL,
      current_analysis = NULL
    )
    
    # File upload handling
    observeEvent(input$tsUserData, {
      req(input$tsUserData)
      
      # Reset analysis results when new data is uploaded
      rv$analysis_results <- NULL
      rv$current_analysis <- NULL
      
      # Process uploaded file
      result <- time_series_uploadData_func(
        uploaded_file = input$tsUserData
      )
      
      if (!is.null(result$error)) {
        showNotification(result$error, type = "error")
        return()
      }
      
      # Store the processed data
      rv$ts_data <- result
      
      # Update variable selection
      updateSelectizeInput(
        session, 
        "tsVar", 
        choices = names(rv$ts_data$data),
        selected = if (length(names(rv$ts_data$data)) > 1) names(rv$ts_data$data)[2] else names(rv$ts_data$data)[1]
      )
    })
    
    # Update frequency input based on data
    observe({
      req(rv$ts_data, input$tsVar)
      
      # If we have date information, try to determine frequency
      if (!is.null(rv$ts_data$date_col) && !is.null(rv$ts_data$frequency)) {
        updateNumericInput(
          session, 
          "tsFrequency", 
          value = rv$ts_data$frequency
        )
      }
    })
    
    # Main analysis function
    observeEvent(input$goTS, {
      req(rv$ts_data, input$tsVar, input$tsFrequency > 0)
      
      # Show loading indicator
      show_modal_progress_line(text = "Analyzing time series...")
      
      tryCatch({
        # Get the time series data
        ts_values <- rv$ts_data$data[[input$tsVar]]
        
        # Remove NAs
        ts_values <- stats::na.omit(ts_values)
        
        # Convert to time series object
        ts_obj <- stats::ts(
          ts_values,
          frequency = input$tsFrequency
        )
        
        # Perform time series analysis
        rv$analysis_results <- list(
          # Basic decomposition
          decomposition = time_series_results_func(
            ts_data = ts_obj,
            analysis_type = "decomposition"
          ),
          
          # Stationarity analysis
          stationarity = time_series_results_func(
            ts_data = ts_obj,
            analysis_type = "stationarity"
          ),
          
          # ARIMA modeling
          arima = time_series_results_func(
            ts_data = ts_obj,
            analysis_type = "arima",
            model_type = "auto"
          ),
          
          # Forecasting
          forecast = time_series_results_func(
            ts_data = ts_obj,
            analysis_type = "forecast",
            h = ifelse(input$tsFrequency > 1, input$tsFrequency * 2, 10)
          ),
          
          # Seasonality analysis
          seasonality = if (input$tsFrequency > 1) {
            time_series_results_func(
              ts_data = ts_obj,
              analysis_type = "seasonality"
            )
          } else {
            list(error = "Seasonality analysis requires frequency > 1")
          },
          
          # Store metadata
          metadata = list(
            variable = input$tsVar,
            frequency = input$tsFrequency,
            n = length(ts_obj)
          )
        )
        
        # Set current analysis to decomposition by default
        rv$current_analysis <- "decomposition"
        
      }, error = function(e) {
        showNotification(
          paste("Error in time series analysis:", e$message),
          type = "error"
        )
      }, finally = {
        # Remove loading indicator
        remove_modal_progress()
      })
    })
    
    # Render error messages if any
    output$tsError <- renderUI({
      req(rv$analysis_results)
      
      errors <- list()
      
      # Check each analysis for errors
      for (analysis_name in names(rv$analysis_results)) {
        analysis <- rv$analysis_results[[analysis_name]]
        if (!is.null(analysis$error)) {
          errors[[length(errors) + 1]] <- tags$div(
            class = "alert alert-warning",
            tags$strong(tools::toTitleCase(analysis_name), " Analysis:"),
            tags$br(),
            analysis$error
          )
        }
      }
      
      if (length(errors) > 0) {
        return(do.call(tagList, errors))
      } else {
        return(NULL)
      }
    })
    # Render time series decomposition plot
    output$tsDecomp <- renderPlot({
      req(rv$analysis_results$decomposition)
      
      # Get the decomposition results
      decomp <- rv$analysis_results$decomposition
      
      # Set up plot layout
      old_par <- par(no.readonly = TRUE)
      on.exit(par(old_par))
      
      # Create decomposition plot
      if (!is.null(decomp$decomposition)) {
        plot(decomp$decomposition, 
             col = "blue", 
             main = "Time Series Decomposition")
      } else {
        # Fallback if decomposition failed
        plot(decomp$ts_data, 
             main = "Time Series Plot",
             ylab = input$tsVar,
             col = "blue",
             lwd = 2)
        grid()
      }
    })
    
    # Render ACF plot
    output$tsACF <- renderPlot({
      req(rv$analysis_results$stationarity)
      
      # Get the stationarity results
      stationarity <- rv$analysis_results$stationarity
      
      # Create ACF plot
      acf_plot <- recordPlot()
      acf(stationarity$ts_data, 
          main = "Autocorrelation Function (ACF)",
          na.action = na.pass)
      grid()
      
      return(acf_plot)
    })
    
    # Render PACF plot
    output$tsPACF <- renderPlot({
      req(rv$analysis_results$stationarity)
      
      # Get the stationarity results
      stationarity <- rv$analysis_results$stationarity
      
      # Create PACF plot
      pacf_plot <- recordPlot()
      pacf(stationarity$ts_data, 
           main = "Partial Autocorrelation Function (PACF)",
           na.action = na.pass)
      grid()
      
      return(pacf_plot)
    })
    
    # Render forecast plot
    output$tsForecast <- renderPlot({
      req(rv$analysis_results$forecast)
      
      # Get the forecast results
      fcst <- rv$analysis_results$forecast
      
      # Create forecast plot
      if (!is.null(fcst$forecast) && requireNamespace("forecast", quietly = TRUE)) {
        forecast::autoplot(fcst$forecast) +
          ggplot2::labs(
            title = paste("Forecast for", input$tsVar),
            x = "Time",
            y = input$tsVar
          ) +
          ggplot2::theme_minimal()
      } else {
        # Fallback if forecast package is not available
        plot(1, type = "n", 
             xlim = c(1, length(fcst$ts_data) + 10), 
             ylim = range(fcst$ts_data, na.rm = TRUE),
             main = "Forecast",
             xlab = "Time",
             ylab = input$tsVar)
        lines(fcst$ts_data, col = "blue", lwd = 2)
        grid()
      }
    })
    
    # Render summary statistics
    output$tsSummary <- renderUI({
      req(rv$analysis_results$decomposition)
      
      # Get the decomposition results for summary stats
      decomp <- rv$analysis_results$decomposition
      
      # Create a nice summary table
      stats_df <- data.frame(
        Statistic = c(
          "Observations", "Start", "End", "Frequency",
          "Mean", "Standard Deviation", "Minimum", "Maximum"
        ),
        Value = c(
          decomp$n,
          paste(decomp$start, collapse = " "),
          paste(decomp$end, collapse = " "),
          decomp$frequency,
          sprintf("%.4f", decomp$statistics$mean),
          sprintf("%.4f", decomp$statistics$sd),
          sprintf("%.4f", decomp$statistics$min),
          sprintf("%.4f", decomp$statistics$max)
        )
      )
      
      # Convert to kable for nice formatting
      stats_table <- knitr::kable(stats_df, format = "html", align = c("l", "r")) %>%
        kableExtra::kable_styling(
          bootstrap_options = c("striped", "hover"),
          full_width = FALSE
        )
      
      # Add stationarity information
      if (!is.null(rv$analysis_results$stationarity$is_stationary)) {
        stationarity_text <- if (is.na(rv$analysis_results$stationarity$is_stationary)) {
          "Stationarity: Could not determine (check if tseries package is installed)"
        } else if (rv$analysis_results$stationarity$is_stationary) {
          "Stationarity: Time series appears to be stationary"
        } else {
          "Stationarity: Time series appears to be non-stationary"
        }
      } else {
        stationarity_text <- ""
      }
      
      # Combine all elements
      tagList(
        h4("Summary Statistics"),
        HTML(stats_table),
        p(stationarity_text, style = "margin-top: 15px;")
      )
    })
    # Render ARIMA model summary
    output$arimaSummary <- renderPrint({
      req(rv$analysis_results$arima)
      
      # Get the ARIMA results
      arima_results <- rv$analysis_results$arima
      
      if (!is.null(arima_results$error)) {
        cat("Error in ARIMA modeling:\n")
        cat(arima_results$error, "\n")
        return()
      }
      
      if (is.null(arima_results$arima_model)) {
        cat("No ARIMA model was fitted.\n")
        return()
      }
      
      # Print model summary
      cat("ARIMA Model Summary\n")
      cat("==================\n\n")
      
      # Model specification
      cat("Model: ", arima_results$arima_model$method, "\n\n")
      
      # Coefficients
      cat("Coefficients:\n")
      coef_table <- data.frame(
        Estimate = arima_results$arima_model$coef,
        `Std. Error` = sqrt(diag(arima_results$arima_model$var.coef)),
        `z value` = arima_results$arima_model$coef / sqrt(diag(arima_results$arima_model$var.coef)),
        `Pr(>|z|)` = 2 * (1 - pnorm(abs(arima_results$arima_model$coef / sqrt(diag(arima_results$arima_model$var.coef))))),
        check.names = FALSE
      )
      
      # Format p-values
      coef_table$`Pr(>|z|)` <- ifelse(
        coef_table$`Pr(>|z|)` < 2e-16,
        "<2e-16",
        formatC(coef_table$`Pr(>|z|)`, format = "e", digits = 2)
      )
      
      print(coef_table)
      
      # Model diagnostics
      cat("\nModel Diagnostics:\n")
      cat("AIC =", arima_results$arima_model$aic, "\n")
      cat("AICc =", arima_results$arima_model$aicc, "\n")
      cat("BIC =", arima_results$arima_model$bic, "\n\n")
      
      # Ljung-Box test on residuals
      if (!is.null(arima_results$arima_diagnostics$ljung_box)) {
        lb_test <- arima_results$arima_diagnostics$ljung_box
        cat("Ljung-Box test on residuals:\n")
        cat("  X-squared =", lb_test$statistic, 
            ", df =", lb_test$parameter, 
            ", p-value =", lb_test$p.value, "\n")
      }
      
      # Accuracy measures
      if (!is.null(arima_results$arima_accuracy)) {
        cat("\nAccuracy measures:\n")
        print(arima_results$arima_accuracy)
      }
    })
    
    # Download handler for results
    output$downloadResults <- downloadHandler(
      filename = function() {
        paste0("time_series_analysis_", format(Sys.time(), "%Y%m%d_%H%M%S"), ".html")
      },
      content = function(file) {
        # Create a temporary directory for the report
        temp_report <- file.path(tempdir(), "report.Rmd")
        
        # Create the R Markdown report
        writeLines(c(
          "---",
          "title: \"Time Series Analysis Report\"",
          "output: html_document",
          "date: '" %+% format(Sys.time(), "%Y-%m-%d") %+% "'",
          "---",
          "",
          "```{r setup, include=FALSE}",
          "knitr::opts_chunk$set(echo = FALSE, warning = FALSE, message = FALSE)",
          "library(forecast)",
          "library(ggplot2)",
          "library(knitr)",
          "library(kableExtra)",
          "```",
          "",
          "# Time Series Analysis Report",
          "",
          "## Data Summary",
          "",
          "```{r summary}",
          "if (exists('analysis_results')) {",
          "  cat('Variable:', analysis_results$metadata$variable, '\\n')",
          "  cat('Frequency:', analysis_results$metadata$frequency, '\\n')",
          "  cat('Observations:', analysis_results$metadata$n, '\\n')",
          "}",
          "```",
          "",
          "## Time Series Plot",
          "",
          "```{r ts-plot, fig.width=10, fig.height=5}",
          "if (exists('analysis_results')) {",
          "  plot(analysis_results$decomposition$ts_data, ",
          "       main = 'Time Series Plot',",
          "       ylab = analysis_results$metadata$variable,",
          "       col = 'blue', lwd = 2)",
          "  grid()",
          "}",
          "```",
          "",
          "## Decomposition",
          "",
          "```{r decomp-plot, fig.width=10, fig.height=8}",
          "if (exists('analysis_results') && !is.null(analysis_results$decomposition$decomposition)) {",
          "  plot(analysis_results$decomposition$decomposition, col = 'blue')",
          "}",
          "```",
          "",
          "## ACF and PACF Plots",
          "",
          "```{r acf-plots, fig.width=10, fig.height=8}",
          "if (exists('analysis_results')) {",
          "  par(mfrow = c(2, 1))",
          "  acf(analysis_results$decomposition$ts_data, main = 'Autocorrelation Function (ACF)')",
          "  pacf(analysis_results$decomposition$ts_data, main = 'Partial Autocorrelation Function (PACF)')",
          "  par(mfrow = c(1, 1))",
          "}",
          "```",
          "",
          "## ARIMA Model",
          "",
          "```{r arima-summary}",
          "if (exists('analysis_results') && !is.null(analysis_results$arima$arima_model)) {",
          "  cat('### Model Specification\\n\\n')",
          "  print(analysis_results$arima$arima_model)",
          "  ",
          "  if (!is.null(analysis_results$arima$arima_accuracy)) {",
          "    cat('\\n### Accuracy Measures\\n\\n')",
          "    print(analysis_results$arima$arima_accuracy)",
          "  }",
          "}",
          "```",
          "",
          "## Forecast",
          "",
          "```{r forecast-plot, fig.width=10, fig.height=5}",
          "if (exists('analysis_results') && !is.null(analysis_results$forecast$forecast)) {",
          "  plot(analysis_results$forecast$forecast, ",
          "       main = paste('Forecast for', analysis_results$metadata$variable),",
          "       xlab = 'Time', ylab = analysis_results$metadata$variable)",
          "}",
          "```"
        ), temp_report)
        
        # Set up parameters to pass to Rmd document
        params <- list(analysis_results = rv$analysis_results)
        
        # Knit the document, passing in the `params` list, and eval it in a
        # child of the global environment (this isolates the code in the document
        # from the code in this app).
        rmarkdown::render(
          temp_report,
          output_file = file,
          params = params,
          envir = new.env(parent = globalenv())
        )
      }
    )
    
    # Clean up when the session ends
    onStop(function() {
      # Add any cleanup code here if needed
    })
  })
}
