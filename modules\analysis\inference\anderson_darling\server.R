andersonDarlingServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactives
    adUploadData <- eventReactive(input$adUserData, {
      handle_file_upload(input$adUserData)
    })
    
    adResults <- reactive({
      data <- adUploadData()
      if (is.null(data) || is.null(input$adVariable) || input$adVariable == "") {
        return(NULL)
      }
      
      values <- data[[input$adVariable]]
      values <- values[!is.na(values)]
      
      if (length(values) < 3) return(NULL)
      
      list(values = values, n = length(values), distribution = input$adDistribution)
    })
    
    # Validation errors
    adValidationErrors <- reactive({
      errors <- c()
      data <- adUploadData()
      
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$adVariable) || input$adVariable == "") {
        errors <- c(errors, "Please select a variable.")
        return(errors)
      }
      
      values <- data[[input$adVariable]]
      if (length(na.omit(values)) < 3) {
        errors <- c(errors, "At least 3 non-missing values are required.")
      }
      
      errors
    })
    
    # Outputs
    output$adHT <- renderUI({
      results <- adResults()
      if (is.null(results)) return(NULL)
      andersonDarlingHT(adResults, reactive({input$adSigLvl}))
    })
    
    output$andersonDarlingPlot <- renderPlot({
      results <- adResults()
      if (is.null(results)) return(NULL)
      andersonDarlingPlot(adResults)
    })
    
    output$adConclusionOutput <- renderUI({
      results <- adResults()
      if (is.null(results)) return(NULL)
      adConclusion(adResults, reactive({input$adSigLvl}))
    })
    
    output$renderADData <- renderUI({
      req(adUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("adInitialUploadTable")), style = "width: 75%"),
        br(),
        br()
      )
    })
    
    output$adInitialUploadTable <- DT::renderDT({
      req(adUploadData())
      DT::datatable(adUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(adUploadData())))))
    })
    
    # Update selectizeInput choices after file upload
    observeEvent(adUploadData(), {
      data <- adUploadData()
      updateSelectizeInput(session, 'adVariable', choices = character(0), selected = NULL, server = TRUE)
      output$andersonDarlingResults <- renderUI({ NULL })
      
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'adVariable', choices = names(data), server = TRUE)
        output$andersonDarlingResults <- renderUI({
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('adPreviewTable'))
          )
        })
        output$adPreviewTable <- DT::renderDT({
          head(data, 20)
        })
      }
    })
    
    # Show main results or error screen when Calculate is pressed
    observeEvent(input$goInference, {
      output$andersonDarlingResults <- renderUI({
        errors <- adValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Anderson-Darling Test", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("adTabset"),
              selected = "Analysis",
              tabPanel(
                id    = ns("ad"),
                title = "Analysis",
                titlePanel("Anderson-Darling Goodness-of-Fit Test"),
                br(),
                uiOutput(ns('adHT')),
                br(),
                plotOutput(ns('andersonDarlingPlot'), width = "50%", height = "400px"),
                br(),
                uiOutput(ns('adConclusionOutput'))
              ),
              tabPanel(
                id    = ns("adData"),
                title = "Uploaded Data",
                uiOutput(ns("renderADData"))
              )
            )
          )
        }
      })
    })
  })
} 