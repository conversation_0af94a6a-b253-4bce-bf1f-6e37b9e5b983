# Descriptive statistics calculation and output helpers

desc_stats_uploadData_func <- function(dsUserData) {
  ext <- tools::file_ext(dsUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(dsUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(dsUserData$datapath),
         xlsx = readxl::read_xlsx(dsUserData$datapath),
         txt = readr::read_tsv(dsUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

desc_stats_results_func <- function(data, selected_vars = NULL) {
  tryCatch({
    if (is.null(data) || !is.data.frame(data)) {
      return(list(error = "Invalid data provided"))
    }
    
    # If no variables selected, use all numeric variables
    if (is.null(selected_vars)) {
      numeric_cols <- sapply(data, is.numeric)
      selected_vars <- names(data)[numeric_cols]
    }
    
    if (length(selected_vars) == 0) {
      return(list(error = "No numeric variables found for analysis"))
    }
    
    # Calculate descriptive statistics for each selected variable
    results_list <- list()
    for (var in selected_vars) {
      if (var %in% names(data)) {
        dat <- na.omit(data[[var]])
        if (length(dat) >= 2) {
          results_list[[var]] <- createDSColumn(dat)
        }
      }
    }
    
    list(
      data = data,
      selected_vars = selected_vars,
      statistics = results_list,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("Error in descriptive statistics calculation:", e$message))
  })
}

desc_stats_ht_html <- function(results) {
  renderUI({
    if (!is.null(results$error)) {
      return(tagList(p("Error:", results$error)))
    }
    
    req(results$statistics)
    stats <- results$statistics
    
    if (length(stats) == 0) {
      return(tagList(p("No statistics calculated.")))
    }
    
    # Create a comprehensive statistics table
    stat_names <- c("Observations", "Sum", "Sum of Squares", "Mean", "Mode", 
                   "Mode Frequency", "Minimum", "First Quartile (Q1)", 
                   "Second Quartile or Median (Q2)", "Third Quartile (Q3)", 
                   "Maximum", "IQR", "Lower Fence", "Upper Fence", 
                   "Potential Outliers", "Outlier Values", "Range", 
                   "Sample Standard Deviation", "Sample Variance", 
                   "Standard Error of the Mean", "Coefficient of Variation", 
                   "Skewness", "Kurtosis")
    
    # Create data frame for display
    df <- data.frame(row.names = stat_names)
    for (var in names(stats)) {
      df[[var]] <- stats[[var]]$Value
    }
    
    withMathJax(
      tagList(
        h4("Descriptive Statistics"),
        DT::renderDT({
          DT::datatable(df, 
                       options = list(
                         pageLength = -1,
                         dom = 't',
                         ordering = FALSE,
                         searching = FALSE,
                         paging = FALSE,
                         autoWidth = TRUE,
                         scrollX = TRUE
                       ),
                       rownames = TRUE,
                       escape = FALSE)
        })
      )
    )
  })
}

desc_stats_summary_html <- function(results) {
  renderUI({
    if (!is.null(results$error)) {
      return(NULL)
    }
    
    req(results$statistics)
    stats <- results$statistics
    
    if (length(stats) == 0) {
      return(tagList(p("No summary available.")))
    }
    
    # Create summary table with key statistics
    summary_data <- do.call(rbind, lapply(names(stats), function(var) {
      stat_values <- stats[[var]]$Value
      data.frame(
        Variable = var,
        N = stat_values[1],  # Observations
        Mean = stat_values[4],  # Mean
        SD = stat_values[18],  # Sample Standard Deviation
        Min = stat_values[7],  # Minimum
        Q1 = stat_values[8],  # First Quartile
        Median = stat_values[9],  # Median
        Q3 = stat_values[10],  # Third Quartile
        Max = stat_values[11]  # Maximum
      )
    }))
    
    tagList(
      h4("Summary Statistics"),
      DT::renderDT({
        DT::datatable(summary_data,
                     options = list(
                       pageLength = 10,
                       autoWidth = TRUE,
                       scrollX = TRUE
                     ),
                     rownames = FALSE)
      })
    )
  })
}

desc_stats_plot <- function(results) {
  renderPlot({
    if (!is.null(results$error)) {
      return(NULL)
    }
    
    req(results$data, results$selected_vars)
    data <- results$data
    selected_vars <- results$selected_vars
    
    if (length(selected_vars) == 0) {
      return(NULL)
    }
    
    # Create plots for the first variable (or all if multiple)
    var <- selected_vars[1]
    if (var %in% names(data)) {
      dat <- na.omit(data[[var]])
      
      if (length(dat) > 1) {
        par(mfrow = c(2, 2))
        
        # Histogram
        hist(dat, main = paste("Histogram of", var), 
             xlab = var, col = "steelblue", breaks = 20)
        
        # Boxplot
        boxplot(dat, main = paste("Boxplot of", var), 
                ylab = var, col = "lightblue")
        
        # Q-Q plot
        qqnorm(dat, main = paste("Q-Q Plot of", var))
        qqline(dat, col = "red")
        
        # Density plot
        plot(density(dat), main = paste("Density Plot of", var),
             xlab = var, col = "darkblue", lwd = 2)
        polygon(density(dat), col = rgb(0, 0, 1, 0.3))
      }
    }
  })
}
