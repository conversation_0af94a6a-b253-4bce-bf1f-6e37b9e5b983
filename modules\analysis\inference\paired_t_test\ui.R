# Paired t-test UI
PairedTTestSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("pairedTUserData"), "Upload your Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
    selectizeInput(ns("pairedTX"), "First Measurement (Before)", choices = NULL),
    selectizeInput(ns("pairedTY"), "Second Measurement (After)", choices = NULL),
    radioButtons(ns("pairedTSigLvl"), "Significance Level (alpha)", choices = c("10%", "5%", "1%"), selected = "5%", inline = TRUE),
    br(),
    actionButton(ns("goPairedT"), label = "Calculate", class = "act-btn")
  )
}

PairedTTestMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('pairedTResults'))
  )
}

PairedTTestUI <- function(id) {
  ns <- NS(id)
  tagList(
    PairedTTestSidebarUI(id),
    PairedTTestMainUI(id)
  )
} 