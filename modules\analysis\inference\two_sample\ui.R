# Two Sample Inference UI
# Extracted and modularized from statInfr.R

TwoSampleInferenceSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    radioButtons(
      inputId      = ns("popuParameters"),
      label        = strong("Parameter of Interest"),
      choiceValues = list("Independent Population Means", "Dependent Population Means", "Population Proportions", "Two Population Variances"),
      choiceNames  = list(HTML("Two Independent Populations (\\( \\mu_{1} - \\mu_{2} \\))"), HTML("Dependent (Paired) Populations (\\( \\mu_{d} \\))"), HTML("Two Population Proportions (\\( p_{1} - p_{2}\\))"), HTML("Two Population Variances (\\( \\sigma_{1}^2/\\sigma_{2}^2 \\))")),
      selected     = "Independent Population Means",
      inline       = FALSE
    ),
    conditionalPanel(
      ns = ns,
      condition = "input.popuParameters == 'Independent Population Means'",
      radioButtons(
        inputId      = ns("dataAvailability2"),
        label        = strong("Data Availability"),
        choiceValues = list("Summarized Data", "Enter Raw Data", "Upload Data"),
        choiceNames  = list("Summarized Data", "Enter Raw Data", "Upload Data"),
        selected     = "Summarized Data",
        inline       = TRUE
      ),
      conditionalPanel(
        ns = ns,
        condition = "input.dataAvailability2 == 'Summarized Data'",
        numericInput(ns("sampleSize1"), strong("Sample Size 1 (n1)"), 21, min = 2, step = 1),
        numericInput(ns("sampleMean1"), strong("Sample Mean 1 (x̄1)"), 29.6, step = 0.00001),
        numericInput(ns("sampleSize2"), strong("Sample Size 2 (n2)"), 21, min = 2, step = 1),
        numericInput(ns("sampleMean2"), strong("Sample Mean 2 (x̄2)"), 33.9, step = 0.00001),
        radioButtons(ns("bothsigmaKnown"), strong("Are Population Standard Deviations (sigma1 and sigma2) known?"),
          choiceValues = list("bothKnown", "bothUnknown"),
          choiceNames = list("Both Known", "Both Unknown"),
          selected = "bothKnown", inline = TRUE
        ),
        conditionalPanel(
          ns = ns,
          condition = "input.bothsigmaKnown == 'bothKnown'",
          numericInput(ns("popuSD1"), strong("Population Standard Deviation 1 (sigma1) Value"), 5.36, min = 0.00001, step = 0.00001),
          numericInput(ns("popuSD2"), strong("Population Standard Deviation 2 (sigma2) Value"), 5.97, min = 0.00001, step = 0.00001)
        ),
        conditionalPanel(
          ns = ns,
          condition = "input.bothsigmaKnown == 'bothUnknown'",
          radioButtons(ns("bothsigmaEqual"), strong("Assume Population Variances are equal (sigma1^2 = sigma2^2)?"),
            choiceValues = list("TRUE", "FALSE"),
            choiceNames = list("Yes (Pooled)", "No (Welch-Satterthwaite df)"),
            selected = "TRUE", inline = TRUE
          ),
          numericInput(ns("sampSD1"), strong("Sample Standard Deviation 1 (s1) Value"), 5.24, min = 0.00001, step = 0.00001),
          numericInput(ns("sampSD2"), strong("Sample Standard Deviation 2 (s2) Value"), 5.85, min = 0.00001, step = 0.00001)
        )
      )
      # Raw Data, Upload Data, etc. (omitted for brevity, but should be included in full implementation)
    )
    # TODO: Add Dependent Means, Proportions, Variances, and all other UI as in statInfr.R
  )
}

TwoSampleInferenceMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('twoSampleResults'))
  )
} 