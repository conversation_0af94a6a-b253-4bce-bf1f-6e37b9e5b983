TimeSeriesUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("tsUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("tsVar"), "Time Series Variable", choices = NULL),
        numericInput(ns("tsFrequency"), "Frequency (e.g., 12 for monthly)", value = 12, min = 1),
        br(),
        actionButton(ns("goTS"), label = "Analyze Time Series", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("tsError")),
        plotOutput(ns("tsDecomp")),
        plotOutput(ns("tsACF")),
        plotOutput(ns("tsPACF")),
        plotOutput(ns("tsForecast"))
      )
    )
  )
} 