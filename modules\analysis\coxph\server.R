CoxPHServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    coxData <- eventReactive(input$coxUserData, {
      handle_file_upload(input$coxUserData)
    })
    observeEvent(coxData(), {
      data <- coxData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'coxTime', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'coxEvent', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'coxCovariates', choices = names(data), server = TRUE)
      }
    })
    coxValidationErrors <- reactive({
      errors <- c()
      data <- coxData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$coxTime) || input$coxTime == "") {
        errors <- c(errors, "Select a time variable.")
      }
      if (is.null(input$coxEvent) || input$coxEvent == "") {
        errors <- c(errors, "Select an event variable.")
      }
      if (is.null(input$coxCovariates) || length(input$coxCovariates) == 0) {
        errors <- c(errors, "Select at least one covariate.")
      }
      errors
    })
    coxResult <- eventReactive(input$goCox, {
      data <- coxData()
      req(data, input$coxTime, input$coxEvent, input$coxCovariates)
      coxph_fit(data, input$coxTime, input$coxEvent, input$coxCovariates)
    })
    output$coxError <- renderUI({
      tryCatch({ coxResult(); NULL }, error = function(e) errorScreenUI(title = "Cox PH Error", errors = e$message))
    })
    output$coxSummary <- renderPrint({
      res <- coxResult()
      if (is.null(res)) return(NULL)
      print(res$summary)
    })
    output$coxSurvPlot <- renderPlot({
      res <- coxResult()
      if (is.null(res)) return(NULL)
      if (!requireNamespace("survminer", quietly = TRUE)) return(NULL)
      survminer::ggsurvplot(survival::survfit(res$fit), data = res$fit$data)
    })
    output$coxDiagPlot <- renderPlot({
      res <- coxResult()
      if (is.null(res)) return(NULL)
      plot(res$fit, which = 1:2)
    })
  })
} 