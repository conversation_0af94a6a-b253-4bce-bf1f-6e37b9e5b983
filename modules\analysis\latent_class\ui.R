LatentClassUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("lcUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("lcVars"), "Variables", choices = NULL, multiple = TRUE),
        numericInput(ns("lcClasses"), "Number of Classes", value = 2, min = 2, max = 10),
        br(),
        actionButton(ns("goLC"), label = "Run Latent Class Analysis", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("lcError")),
        tableOutput(ns("lcResults")),
        plotOutput(ns("lcPlot"))
      )
    )
  )
} 