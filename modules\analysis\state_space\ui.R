StateSpaceUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("ssUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("ssSeries"), "Series Variable", choices = NULL),
        br(),
        actionButton(ns("goSS"), label = "Run State Space Model", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("ssError")),
        plotOutput(ns("ssPlot")),
        tableOutput(ns("ssSummary"))
      )
    )
  )
} 