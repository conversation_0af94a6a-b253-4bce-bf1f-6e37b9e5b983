PermutationTestsServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Data upload reactive
    permData <- eventReactive(input$permUserData, {
      handle_file_upload(input$permUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(permData(), {
      data <- permData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'permVars', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    permValidationErrors <- reactive({
      errors <- c()
      
      data <- permData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$permVars) || length(input$permVars) == 0) {
        errors <- c(errors, "Please select at least one variable for permutation test.")
      }
      
      if (!is.null(input$permVars) && length(input$permVars) > 0) {
        for (var in input$permVars) {
          if (!is.numeric(data[[var]])) {
            errors <- c(errors, sprintf("Variable '%s' must be numeric.", var))
          }
        }
      }
      
      errors
    })
    
    # Permutation test result reactive
    permResult <- eventReactive(input$goPerm, {
      data <- permData()
      req(data, input$permVars, input$permTestType)
      
      # Perform permutation test
      permutation_test(data, input$permVars, input$permTestType)
    })
    
    observeEvent(input$goPerm, {
      output$permError <- renderUI({
        tryCatch({ 
          res <- permResult()
          if (is.null(res)) {
            errorScreenUI(title = "Permutation Test Error", errors = "No results available")
          } else {
            NULL
          }
        }, error = function(e) {
          errorScreenUI(title = "Permutation Test Error", errors = e$message)
        })
      })
      
      output$permResults <- renderUI({
        errors <- permValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Permutation Test", errors = errors)
        } else {
          res <- permResult()
          if (is.null(res)) return(NULL)
          
          tagList(
            tabsetPanel(
              id = ns("permTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("permAnalysis"),
                title = "Analysis",
                titlePanel("Permutation Test Results"),
                br(),
                h4("Test Summary"),
                tableOutput(ns('permSummary')),
                br(),
                h4("Test Results"),
                tableOutput(ns('permTestResults')),
                br(),
                h4("Permutation Distribution"),
                tableOutput(ns('permDistribution')),
                br(),
                h4("Interpretation"),
                uiOutput(ns('permInterpretation'))
              ),
              tabPanel(
                id = ns("permDiagnostics"),
                title = "Diagnostics",
                h4("Permutation Distribution Plot"),
                plotOutput(ns('permDistributionPlot'), height = "400px"),
                br(),
                h4("Permutation Histogram"),
                plotOutput(ns('permHistogram'), height = "400px"),
                br(),
                h4("Diagnostic Statistics"),
                tableOutput(ns('permDiagnosticStats'))
              ),
              tabPanel(
                id = ns("permUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('permDataTable'))
              )
            )
          )
        }
      })
    })
    
    # Additional outputs for the enhanced tabs
    output$permSummary <- renderTable({
      res <- permResult()
      if (is.null(res)) return(NULL)
      
      data.frame(
        Statistic = c("Test Type", "Variables", "Original Statistic", "Permutations", "P-value", "Significance"),
        Value = c(
          input$permTestType,
          paste(input$permVars, collapse = ", "),
          ifelse(!is.null(res$original_statistic), round(res$original_statistic, 4), "N/A"),
          ifelse(!is.null(res$n_permutations), res$n_permutations, "N/A"),
          ifelse(!is.null(res$p_value), round(res$p_value, 4), "N/A"),
          ifelse(!is.null(res$p_value), ifelse(res$p_value < 0.05, "Significant", "Not Significant"), "N/A")
        ),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    output$permTestResults <- renderTable({
      res <- permResult()
      if (is.null(res)) return(NULL)
      
      data.frame(
        Statistic = c("Original Statistic", "Mean of Permutations", "SD of Permutations", "P-value", "95% CI Lower", "95% CI Upper"),
        Value = c(
          ifelse(!is.null(res$original_statistic), round(res$original_statistic, 4), "N/A"),
          ifelse(!is.null(res$perm_mean), round(res$perm_mean, 4), "N/A"),
          ifelse(!is.null(res$perm_sd), round(res$perm_sd, 4), "N/A"),
          ifelse(!is.null(res$p_value), round(res$p_value, 4), "N/A"),
          ifelse(!is.null(res$ci_lower), round(res$ci_lower, 4), "N/A"),
          ifelse(!is.null(res$ci_upper), round(res$ci_upper, 4), "N/A")
        ),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    output$permDistribution <- renderTable({
      res <- permResult()
      if (is.null(res) || is.null(res$permutation_stats)) return(NULL)
      
      res$permutation_stats
    }, digits = 4)
    
    output$permInterpretation <- renderUI({
      res <- permResult()
      if (is.null(res)) return(NULL)
      
      p_value <- res$p_value
      interpretation <- if (is.null(p_value)) {
        "P-value not available"
      } else if (p_value < 0.001) {
        "Highly significant (p < 0.001)"
      } else if (p_value < 0.01) {
        "Very significant (p < 0.01)"
      } else if (p_value < 0.05) {
        "Significant (p < 0.05)"
      } else {
        "Not significant (p >= 0.05)"
      }
      
      tagList(
        h5("Permutation Test Interpretation:"),
        p("Permutation tests provide a non-parametric alternative to traditional parametric tests."),
        p("The test works by randomly shuffling the data and recalculating the test statistic."),
        p("The p-value represents the proportion of permutations that produced a test statistic as extreme as or more extreme than the observed value."),
        p("Interpretation: ", interpretation),
        p("Note: Permutation tests make fewer assumptions than parametric tests and are robust to violations of normality.")
      )
    })
    
    output$permDistributionPlot <- renderPlot({
      res <- permResult()
      if (is.null(res) || is.null(res$permutation_values)) return(NULL)
      
      # Create permutation distribution plot
      hist(res$permutation_values, 
           main = "Permutation Distribution", 
           xlab = "Test Statistic", 
           ylab = "Frequency",
           col = "lightcoral",
           border = "black")
      abline(v = res$original_statistic, col = "red", lwd = 2, lty = 2)
      legend("topright", legend = "Original Statistic", col = "red", lwd = 2, lty = 2)
    })
    
    output$permHistogram <- renderPlot({
      res <- permResult()
      if (is.null(res) || is.null(res$permutation_values)) return(NULL)
      
      # Create histogram with density curve
      hist(res$permutation_values, 
           main = "Permutation Histogram with Density", 
           xlab = "Test Statistic", 
           ylab = "Density",
           col = "lightyellow",
           border = "black",
           freq = FALSE)
      lines(density(res$permutation_values), col = "blue", lwd = 2)
    })
    
    output$permDiagnosticStats <- renderTable({
      res <- permResult()
      if (is.null(res)) return(NULL)
      
      data.frame(
        Statistic = c("Permutations", "Convergence", "Permutation SE", "Permutation Skewness", "Permutation Kurtosis"),
        Value = c(
          ifelse(!is.null(res$n_permutations), res$n_permutations, "N/A"),
          "Converged",
          ifelse(!is.null(res$perm_sd), round(res$perm_sd, 4), "N/A"),
          ifelse(!is.null(res$perm_skewness), round(res$perm_skewness, 4), "N/A"),
          ifelse(!is.null(res$perm_kurtosis), round(res$perm_kurtosis, 4), "N/A")
        ),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    output$permDataTable <- DT::renderDT({
      req(permData())
      DT::datatable(permData(),
        options = list(pageLength = 25,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(permData())))))
    })
  })
} 