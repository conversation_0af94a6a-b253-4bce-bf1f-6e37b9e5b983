# Ridge Regression Server
# Regularization

source("modules/calculations/ridge_regression.R")

RidgeRegressionServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    ridgeUploadData <- eventReactive(input$ridgeUserData, {
      handle_file_upload(input$ridgeUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(ridgeUploadData(), {
      data <- ridgeUploadData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'ridgeResponseVariable', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'ridgePredictorVariables', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    ridgeValidationErrors <- reactive({
      errors <- c()
      
      data <- ridgeUploadData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$ridgeResponseVariable) || input$ridgeResponseVariable == "") {
        errors <- c(errors, "Please select a response variable.")
      } else {
        var_data <- data[[input$ridgeResponseVariable]]
        if (!is.numeric(var_data)) {
          errors <- c(errors, "Response variable must be numeric.")
        }
        if (length(na.omit(var_data)) < 10) {
          errors <- c(errors, "At least 10 non-missing observations are required.")
        }
      }
      if (is.null(input$ridgePredictorVariables) || length(input$ridgePredictorVariables) < 2) {
        errors <- c(errors, "Please select at least 2 predictor variables.")
      } else {
        for (var in input$ridgePredictorVariables) {
          pred_data <- data[[var]]
          if (!is.numeric(pred_data)) {
            errors <- c(errors, paste("Predictor variable", var, "must be numeric."))
          }
        }
      }
      
      errors
    })
    
    # Ridge regression analysis reactive
    ridgeResult <- reactive({
      req(ridgeUploadData(), input$ridgeResponseVariable, input$ridgePredictorVariables)
      
      data <- ridgeUploadData()
      response_var <- input$ridgeResponseVariable
      predictor_vars <- input$ridgePredictorVariables
      lambda <- input$ridgeLambda
      conf_level <- input$ridgeConfLevel
      
      # Perform ridge regression
      result <- perform_ridge_regression(data, response_var, predictor_vars, lambda, conf_level)
      
      result
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goRidge, {
      output$ridgeResults <- renderUI({
        errors <- ridgeValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Ridge Regression", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("ridgeTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("ridgeAnalysis"),
                title = "Analysis",
                titlePanel("Ridge Regression Results"),
                br(),
                uiOutput(ns('ridgeSummary')),
                br(),
                h4("Model Summary"),
                tableOutput(ns('ridgeModelSummary')),
                br(),
                h4("Coefficients"),
                tableOutput(ns('ridgeCoefficients')),
                br(),
                h4("Model Comparison"),
                tableOutput(ns('ridgeModelComparison')),
                br(),
                h4("Assumptions and Interpretation"),
                uiOutput(ns('ridgeAssumptions'))
              ),
              tabPanel(
                id = ns("ridgeDataSummary"),
                title = "Data Summary",
                h4("Descriptive Statistics"),
                tableOutput(ns('ridgeDescriptive')),
                br(),
                h4("Correlation Matrix"),
                tableOutput(ns('ridgeCorrelation')),
                br(),
                h4("Coefficient Path"),
                plotOutput(ns('ridgePath'), height = "400px")
              ),
              tabPanel(
                id = ns("ridgeDiagnostics"),
                title = "Diagnostics",
                h4("Residual Analysis"),
                plotOutput(ns('ridgeResiduals'), height = "600px"),
                br(),
                h4("Influence Analysis"),
                tableOutput(ns('ridgeInfluence'))
              ),
              tabPanel(
                id = ns("ridgeUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('ridgeViewUpload'))
              )
            )
          )
        }
      })
    })
    
    # Analysis Tab Outputs
    output$ridgeSummary <- renderUI({
      req(ridgeResult())
      result <- ridgeResult()
      
      tagList(
        h4("Ridge Regression Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Statistic = c("Sample Size", "Number of Predictors", "Lambda", "Significance Level", 
                         "R²", "Adjusted R²", "RMSE", "MAE"),
            Value = c(
              result$n,
              result$num_predictors,
              result$lambda,
              paste0((1 - result$conf_level) * 100, "%"),
              round(result$r_squared, 4),
              round(result$adj_r_squared, 4),
              round(result$rmse, 4),
              round(result$mae, 4)
            )
          )
        }),
        br(),
        p(strong("Model:"), "Y = β₀ + β₁X₁ + β₂X₂ + ... + βₖXₖ + λ∑βᵢ²")
      )
    })
    
    output$ridgeModelSummary <- renderTable({
      req(ridgeResult())
      result <- ridgeResult()
      
      result$model_summary
    }, digits = 4)
    
    output$ridgeCoefficients <- renderTable({
      req(ridgeResult())
      result <- ridgeResult()
      
      result$coefficients
    }, digits = 4)
    
    output$ridgeModelComparison <- renderTable({
      req(ridgeResult())
      result <- ridgeResult()
      
      result$model_comparison
    }, digits = 4)
    
    output$ridgeAssumptions <- renderUI({
      req(ridgeResult())
      result <- ridgeResult()
      
      tagList(
        h5("Key Assumptions:"),
        p("1. Independence: Observations are independent"),
        p("2. Normality: Residuals are normally distributed"),
        p("3. Homoscedasticity: Constant variance of residuals"),
        p("4. Linearity: Linear relationships between predictors and response"),
        br(),
        h5("Advantages:"),
        p("• Handles multicollinearity"),
        p("• Reduces overfitting"),
        p("• More stable coefficients"),
        p("• Works with high-dimensional data"),
        br(),
        h5("Disadvantages:"),
        p("• Biased estimates"),
        p("• Requires lambda selection"),
        p("• Less interpretable coefficients"),
        br(),
        h5("When to Use:"),
        p("• Multicollinear predictors"),
        p("• High-dimensional data"),
        p("• Overfitting concerns"),
        p("• Need for stable predictions")
      )
    })
    
    # Data Summary Tab Outputs
    output$ridgeDescriptive <- renderTable({
      req(ridgeResult())
      result <- ridgeResult()
      
      result$descriptive_stats
    }, digits = 4)
    
    output$ridgeCorrelation <- renderTable({
      req(ridgeResult())
      result <- ridgeResult()
      
      result$correlation_matrix
    }, digits = 4)
    
    output$ridgePath <- renderPlot({
      req(ridgeResult())
      result <- ridgeResult()
      
      # Create coefficient path plot
      path_data <- result$coefficient_path
      matplot(path_data$Lambda, path_data[, -1], type = "l", 
              main = "Ridge Regression Coefficient Path",
              xlab = "Lambda", ylab = "Coefficients",
              col = 1:result$num_predictors, lwd = 2)
      legend("topright", legend = names(path_data)[-1], 
             col = 1:result$num_predictors, lwd = 2)
    })
    
    # Diagnostics Tab Outputs
    output$ridgeResiduals <- renderPlot({
      req(ridgeResult())
      result <- ridgeResult()
      
      # Create diagnostic plots
      par(mfrow = c(2, 2))
      
      # Residuals vs Fitted
      plot(result$fitted_values, result$residuals,
           main = "Residuals vs Fitted",
           xlab = "Fitted Values", ylab = "Residuals",
           pch = 16, col = "#4F81BD")
      abline(h = 0, col = "red", lty = 2)
      
      # Normal Q-Q plot
      qqnorm(result$residuals, main = "Normal Q-Q Plot",
             pch = 16, col = "#4F81BD")
      qqline(result$residuals, col = "red")
      
      # Scale-Location plot
      plot(result$fitted_values, sqrt(abs(result$residuals)),
           main = "Scale-Location Plot",
           xlab = "Fitted Values", ylab = "√|Residuals|",
           pch = 16, col = "#4F81BD")
      
      # Residuals vs Index
      plot(1:length(result$residuals), result$residuals,
           main = "Residuals vs Index",
           xlab = "Index", ylab = "Residuals",
           pch = 16, col = "#4F81BD")
      abline(h = 0, col = "red", lty = 2)
    })
    
    output$ridgeInfluence <- renderTable({
      req(ridgeResult())
      result <- ridgeResult()
      
      result$influence_analysis
    }, digits = 4)
    
    # Uploaded Data Tab Output
    output$ridgeViewUpload <- DT::renderDT({
      req(ridgeUploadData())
      DT::datatable(ridgeUploadData(), 
                   options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), 
                   rownames = FALSE)
    })
  })
} 