BayesianModelComparisonServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    bmcData <- eventReactive(input$bmcUserData, {
      handle_file_upload(input$bmcUserData)
    })
    
    observeEvent(bmcData(), {
      data <- bmcData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'bmcResponse', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'bmcPredictors', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'bmcModels', choices = names(data), server = TRUE)
      }
    })
    
    bmcValidationErrors <- reactive({
      errors <- c()
      data <- bmcData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$bmcResponse)) {
        errors <- c(errors, "Select a response variable.")
      }
      if (is.null(input$bmcPredictors) || length(input$bmcPredictors) < 1) {
        errors <- c(errors, "Select at least one predictor variable.")
      }
      if (!is.null(input$bmcResponse) && !is.numeric(data[[input$bmcResponse]])) {
        errors <- c(errors, "Response variable must be numeric.")
      }
      for (var in input$bmcPredictors) {
        if (!is.numeric(data[[var]])) {
          errors <- c(errors, sprintf("Predictor variable '%s' must be numeric.", var))
        }
      }
      errors
    })
    
    bmcResult <- eventReactive(input$goBMC, {
      data <- bmcData()
      req(data, input$bmcResponse, input$bmcPredictors)
      
      # Get parameters from UI
      comparison_method <- ifelse(is.null(input$bmcMethod), "LOO", input$bmcMethod)
      model_types <- ifelse(is.null(input$bmcModelTypes), c("linear", "polynomial"), input$bmcModelTypes)
      
      bayesian_model_comparison_analysis(data, input$bmcResponse, input$bmcPredictors, 
                                       comparison_method = comparison_method, model_types = model_types)
    })
    
    observeEvent(input$goBMC, {
      output$bmcResultsUI <- renderUI({
        errors <- bmcValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Bayesian Model Comparison", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("bmcTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("bmcAnalysis"),
                title = "Analysis",
                titlePanel("Bayesian Model Comparison Results"),
                br(),
                h4("Comparison Method"),
                textOutput(ns('bmcMethod')),
                h4("Model Comparison Table"),
                tableOutput(ns('bmcComparisonTable')),
                h4("Model Weights"),
                tableOutput(ns('bmcWeights')),
                h4("Best Model"),
                textOutput(ns('bmcBestModel')),
                h4("Model Rankings"),
                tableOutput(ns('bmcRankings')),
                h4("Evidence Ratios"),
                tableOutput(ns('bmcEvidenceRatios')),
                h4("Model Complexity"),
                tableOutput(ns('bmcComplexity')),
                h4("Number of Models"),
                textOutput(ns('bmcNumModels')),
                h4("Number of Observations"),
                textOutput(ns('bmcObservations')),
                h4("Interpretation"),
                textOutput(ns('bmcInterpretation'))
              ),
              tabPanel(
                id = ns("bmcDiagnostics"),
                title = "Diagnostics",
                h4("Model Comparison Plot"),
                plotOutput(ns('bmcComparisonPlot'), height = "300px"),
                h4("Model Weights Plot"),
                plotOutput(ns('bmcWeightsPlot'), height = "300px"),
                h4("Diagnostic Summary"),
                textOutput(ns('bmcDiagnostics'))
              ),
              tabPanel(
                id = ns("bmcUploadedData"),
                title = "Uploaded Data",
                h4("Raw Uploaded Data"),
                DT::DTOutput(ns('bmcRawDataTable'))
              )
            )
          )
        }
      })
    })
    
    # Comparison method
    output$bmcMethod <- renderText({
      res <- bmcResult()
      if (is.null(res)) return(NULL)
      paste("Comparison method:", res$comparison_method)
    })
    
    # Model comparison table
    output$bmcComparisonTable <- renderTable({
      res <- bmcResult()
      if (is.null(res)) return(NULL)
      res$comparison_table
    }, rownames = FALSE, digits = 4)
    
    # Model weights
    output$bmcWeights <- renderTable({
      res <- bmcResult()
      if (is.null(res)) return(NULL)
      res$model_weights
    }, rownames = FALSE, digits = 4)
    
    # Best model
    output$bmcBestModel <- renderText({
      res <- bmcResult()
      if (is.null(res)) return(NULL)
      paste("Best model:", res$best_model)
    })
    
    # Model rankings
    output$bmcRankings <- renderTable({
      res <- bmcResult()
      if (is.null(res)) return(NULL)
      res$model_rankings
    }, rownames = FALSE, digits = 4)
    
    # Evidence ratios
    output$bmcEvidenceRatios <- renderTable({
      res <- bmcResult()
      if (is.null(res)) return(NULL)
      res$evidence_ratios
    }, rownames = FALSE, digits = 4)
    
    # Model complexity
    output$bmcComplexity <- renderTable({
      res <- bmcResult()
      if (is.null(res)) return(NULL)
      res$model_complexity
    }, rownames = FALSE, digits = 4)
    
    # Number of models
    output$bmcNumModels <- renderText({
      res <- bmcResult()
      if (is.null(res)) return(NULL)
      paste("Number of models compared:", res$n_models)
    })
    
    # Number of observations
    output$bmcObservations <- renderText({
      res <- bmcResult()
      if (is.null(res)) return(NULL)
      paste("Number of observations:", res$n_observations)
    })
    
    # Interpretation
    output$bmcInterpretation <- renderText({
      res <- bmcResult()
      if (is.null(res)) return(NULL)
      res$interpretation
    })
    
    # Model comparison plot
    output$bmcComparisonPlot <- renderPlot({
      res <- bmcResult()
      if (is.null(res)) return(NULL)
      
      # Create comparison plot
      comparison_data <- res$comparison_table
      if (!is.null(comparison_data)) {
        metric_col <- if ("LOOIC" %in% names(comparison_data)) "LOOIC" else names(comparison_data)[2]
        barplot(comparison_data[[metric_col]], 
                names.arg = comparison_data$Model,
                main = "Model Comparison",
                ylab = metric_col,
                col = "steelblue")
      }
    })
    
    # Model weights plot
    output$bmcWeightsPlot <- renderPlot({
      res <- bmcResult()
      if (is.null(res)) return(NULL)
      
      weights_data <- res$model_weights
      if (!is.null(weights_data)) {
        pie(weights_data$Weight, 
            labels = weights_data$Model,
            main = "Model Weights",
            col = rainbow(length(weights_data$Weight)))
      }
    })
    
    # Diagnostics summary
    output$bmcDiagnostics <- renderText({
      res <- bmcResult()
      if (is.null(res)) return(NULL)
      
      paste("Comparison method: ", res$comparison_method,
            "\nBest model: ", res$best_model,
            "\nModel weights range: ", round(min(res$model_weights$Weight), 4), " to ", round(max(res$model_weights$Weight), 4),
            "\nEvidence strength: ", res$evidence_strength)
    })
    
    # Raw data table
    output$bmcRawDataTable <- DT::renderDT({
      req(bmcData())
      DT::datatable(bmcData(), options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), rownames = FALSE)
    })
  })
} 