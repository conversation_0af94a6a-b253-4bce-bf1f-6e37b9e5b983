source('modules/calculations/desc_stats.R')
source('modules/calculations/shared/desc_stats.R')

descStatsServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns

    # Data upload reactive
    dsUploadData <- eventReactive(input$dsUserData, {
      desc_stats_uploadData_func(input$dsUserData)
    })

    # Raw data reactive for manual entry
    dsRawData <- reactive({
      dat <- as.numeric(unlist(strsplit(input$descriptiveStat, ",|\\s+")))
      dat <- dat[!is.na(dat)]
      return(dat)
    })

    # Main results reactive
    dsResults <- reactive({
      if (input$dataInput == 'Upload Data') {
        data <- dsUploadData()
        if (is.null(data)) return(NULL)
        desc_stats_results_func(data, input$dsUploadVars)
      } else if (input$dataInput == 'Enter Raw Data') {
        dat <- dsRawData()
        if (length(dat) < 2) return(NULL)
        # Create a temporary data frame for raw data
        temp_data <- data.frame(Value = dat)
        desc_stats_results_func(temp_data, "Value")
      } else {
        return(NULL)
      }
    })

    # Legacy function for backward compatibility with existing UI
    getDsDataframe <- reactive({
      results <- dsResults()
      if (is.null(results) || !is.null(results$error)) return(NULL)

      if(input$dataInput == 'Upload Data' && !is.null(input$dsUploadVars) && !is.null(dsUploadData())) {
        df <- data.frame()
        for(x in input$dsUploadVars) {
          dat <- na.omit(as.data.frame(dsUploadData())[, x])
          newCol <- createDSColumn(dat)
          df[[x]] <- newCol$Value
        }
        rownames(df) <- c("Observations", "Sum", "Sum of Squares", "Mean", "Mode", "Mode Frequency", "Minimum", "First Quartile (Q1)", "Second Quartile or Median (Q2)", "Third Quartile (Q3)", "Maximum", "IQR", "Lower Fence", "Upper Fence", "Potential Outliers", "Outlier Values", "Range", "Sample Standard Deviation", "Sample Variance", "Standard Error of the Mean", "Coefficient of Variation", "Skewness", "Kurtosis")
        return(df)
      } else if(input$dataInput == 'Enter Raw Data') {
        dat <- dsRawData()
        newCol <- createDSColumn(dat)
        df <- data.frame(Value = newCol$Value)
        rownames(df) <- c("Observations", "Sum", "Sum of Squares", "Mean", "Mode", "Mode Frequency", "Minimum", "First Quartile (Q1)", "Second Quartile or Median (Q2)", "Third Quartile (Q3)", "Maximum", "IQR", "Lower Fence", "Upper Fence", "Potential Outliers", "Outlier Values", "Range", "Sample Standard Deviation", "Sample Variance", "Standard Error of the Mean", "Coefficient of Variation", "Skewness", "Kurtosis")
        return(df)
      } else {
        return(NULL)
      }
    })
    # Validation errors reactive
    dsValidationErrors <- reactive({
      errors <- c()

      if (input$dataInput == 'Enter Raw Data') {
        dat <- dsRawData()
        if (length(dat) < 2) {
          errors <- c(errors, "At least 2 observations are required for descriptive statistics.")
        }
        if (length(dat) > 0 && all(dat == dat[1])) {
          errors <- c(errors, "All values are the same. There must be variance in the data.")
        }
      } else if (input$dataInput == 'Upload Data') {
        data <- dsUploadData()
        if (is.null(data) || !is.data.frame(data)) {
          errors <- c(errors, "No data uploaded or file could not be read.")
        } else if (is.null(input$dsUploadVars) || length(input$dsUploadVars) == 0) {
          errors <- c(errors, "Please select at least one variable for analysis.")
        } else {
          for (var in input$dsUploadVars) {
            if (!var %in% names(data)) {
              errors <- c(errors, sprintf("Variable '%s' not found in uploaded data.", var))
            } else {
              dat <- na.omit(data[[var]])
              if (length(dat) < 2) {
                errors <- c(errors, sprintf("Variable '%s' must have at least 2 non-missing observations.", var))
              }
              if (length(dat) > 0 && all(dat == dat[1])) {
                errors <- c(errors, sprintf("All values in variable '%s' are the same. There must be variance.", var))
              }
            }
          }
        }
      }

      errors
    })
    # Uploaded data table (this can stay outside, as it's only for preview)
    output$renderDSData <- renderUI({
      req(dsUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("dsUploadTable")), style = "width: 75%"),
        br(),
        br()
      )
    })
    output$dsUploadTable <- DT::renderDT({
      req(dsUploadData())
      DT::datatable(dsUploadData(), options = list(pageLength = 25, lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")), columnDefs = list(list(className = 'dt-center', targets = 0:ncol(dsUploadData())))), rownames = FALSE)
    })
    # Update variable choices when data is uploaded
    observeEvent(dsUploadData(), {
      data <- dsUploadData()
      updateSelectInput(session, 'dsUploadVars', choices = character(0), selected = NULL)
      if (!is.null(data) && is.data.frame(data)) {
        # Only show numeric variables
        numeric_vars <- names(data)[sapply(data, is.numeric)]
        updateSelectInput(session, 'dsUploadVars', choices = numeric_vars, selected = NULL)
      }
    })

    # UI outputs using calculation functions
    output$dsHT <- renderUI({
      results <- dsResults()
      if (is.null(results)) return(NULL)
      desc_stats_ht_html(results)
    })

    output$dsSummary <- renderUI({
      results <- dsResults()
      if (is.null(results)) return(NULL)
      desc_stats_summary_html(results)
    })

    output$dsPlot <- renderPlot({
      results <- dsResults()
      if (is.null(results)) return(NULL)
      desc_stats_plot(results)
    })

    output$dsDataTable <- DT::renderDT({
      data <- dsUploadData()
      if (is.null(data)) return(NULL)
      DT::datatable(data)
    })
    # Show main results or error screen in main panel when Calculate is pressed
    observeEvent(input$goDescpStats, {
      shinyjs::show("descriptiveStatsMP")
      # Main results UI
      output$renderDescrStats <- renderUI({
        errors <- dsValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Descriptive Statistics", errors = errors)
        } else {
          results <- dsResults()
          if (is.null(results)) {
            errorScreenUI(title = "Descriptive Statistics Error", errors = "No results available")
          } else if (!is.null(results$error)) {
            errorScreenUI(title = "Descriptive Statistics Error", errors = results$error)
          } else {
            tagList(
              tabsetPanel(
                id = ns("dsTabset"),
                selected = "Analysis",
                tabPanel(
                  id = ns("dsAnalysis"),
                  title = "Analysis",
                  titlePanel("Descriptive Statistics"),
                  br(),
                  uiOutput(ns('dsHT')),
                  br(),
                  plotOutput(ns('dsPlot'), width = "100%", height = "600px"),
                  br(),
                  uiOutput(ns('dsSummary'))
                ),
                tabPanel(
                  id = ns("dsLegacy"),
                  title = "Detailed Statistics",
                  withMathJax(),
                  conditionalPanel(
                    condition = sprintf("input['%s'] == ''", ns("dsTableFilters")),
                    br(),
                    p("Select one or more items from the Statistics menu to see more information.")
                  ),
                  conditionalPanel(
                    condition = sprintf("input['%s'] != ''", ns("dsTableFilters")),
                    uiOutput(ns("dsTableData"))
                  ),
                  br(),
                  conditionalPanel(
                    condition = sprintf("input['%s'].indexOf('First Quartile (Q1)') > -1 | input['%s'].indexOf('Third Quartile (Q3)') > -1 | input['%s'].indexOf('IQR') > -1 | input['%s'].indexOf('Potential Outliers') > -1", ns("dsTableFilters"), ns("dsTableFilters"), ns("dsTableFilters"), ns("dsTableFilters")),
                    helpText("* Note: Quartiles are calculated by excluding the median on both sides.")),
                  br(),
                  br(),
                  conditionalPanel(
                    condition = sprintf("input['%s'].indexOf('Mean') > -1 | input['%s'].indexOf('Sample Standard Deviation') > -1", ns("dsTableFilters"), ns("dsTableFilters")),
                    fluidRow(
                      column(
                        width = 4,
                        br(),
                        uiOutput(ns("sampleDataTable")),
                        br(),
                        br()),
                      column(
                        width = 8,
                        conditionalPanel(
                          condition = sprintf("input['%s'].indexOf('Mean') > -1", ns("dsTableFilters")),
                          withMathJax(),
                          titlePanel(tags$u("Sample Mean")),
                          br(),
                          uiOutput(ns("dsMeanCalc")),
                          br()),
                        conditionalPanel(
                          condition = sprintf("input['%s'].indexOf('Sample Standard Deviation') > -1", ns("dsTableFilters")),
                          withMathJax(),
                          titlePanel(tags$u("Sample Standard Deviation")),
                          br(),
                          uiOutput(ns("dsSDCalc")),
                          br(),
                          br(),
                          br()))
                    )
                  )
                ),
                tabPanel(
                  id    = ns("dsGraphs"),
                  title = "Graphs",
                  value = "Graphs",
                  uiOutput(ns("renderDSBoxplot")),
                  uiOutput(ns("renderDSHistogram")),
                  verbatimTextOutput(ns("dsStemLeaf"))
                ),
                tabPanel(
                  id    = ns("dsUploadData"),
                  title = "Uploaded Data",
                  value = "Uploaded Data",
                  uiOutput(ns("renderDSData"))
                )
              )
            )
          )
        }
      })
      # Only render these after Calculate is pressed
      output$dsTableData <- renderUI({
        req(getDsDataframe())
        df <- getDsDataframe()
        if(input$dataInput == 'Upload Data') {
          DT::DTOutput(ns("dsTableDataInner"))
        } else {
          DT::DTOutput(ns("dsTableDataInner"))
        }
      })
      
      output$dsTableDataInner <- DT::renderDT({
        req(getDsDataframe())
        df <- getDsDataframe()
        if(input$dataInput == 'Upload Data') {
          DT::datatable(df[input$dsTableFilters, , drop = FALSE], options = list(dom = 't', pageLength = -1, ordering = FALSE, searching = FALSE, paging = FALSE, autoWidth = TRUE, scrollX = TRUE), rownames = TRUE, escape = FALSE)
        } else {
          DT::datatable(df[input$dsTableFilters, , drop = FALSE, drop = FALSE], options = list(dom = 't', pageLength = -1, ordering = FALSE, searching = FALSE, paging = FALSE, autoWidth = TRUE, scrollX = TRUE), rownames = TRUE, escape = FALSE)
        }
      })
      
      output$sampleDataTable <- renderUI({
        DT::DTOutput(ns("sampleDataTableInner"))
      })
      
      output$sampleDataTableInner <- DT::renderDT({
        dat <- if(input$dataInput == 'Upload Data' && !is.null(input$dsUploadVars)) na.omit(as.data.frame(dsUploadData())[, input$dsUploadVars[1]]) else dsRawData()
        sample_df <- data.frame(x = dat, x2 = dat^2)
        names(sample_df) <- c("x", "x<sup>2</sup>")
        dfTotaled <- rbind(sample_df, Totals = colSums(sample_df))
        DT::datatable(round(dfTotaled, digits = 3), options = list(dom = 't', pageLength = -1, autoWidth = TRUE, scrollX = TRUE), rownames = FALSE, escape = FALSE)
      })
      output$dsMeanCalc <- renderUI({
        df <- getDsDataframe()
        if(is.null(df)) return(NULL)
        n <- as.numeric(df["Observations", 1])
        sumx <- as.numeric(df["Sum", 1])
        meanx <- as.numeric(df["Mean", 1])
        tagList(
          withMathJax(),
          sprintf("\\( \\bar{x} = \\dfrac{\\sum x}{n} = \\dfrac{%s}{%s} = %s \\)", sumx, n, meanx),
          br(), br(), br()
        )
      })
      output$dsSDCalc <- renderUI({
        df <- getDsDataframe()
        if(is.null(df)) return(NULL)
        n <- as.numeric(df["Observations", 1])
        sumx <- as.numeric(df["Sum", 1])
        sumx2 <- as.numeric(df["Sum of Squares", 1])
        sdval <- as.numeric(df["Sample Standard Deviation", 1])
        tagList(
          withMathJax(),
          sprintf("\\( s = \\sqrt{ \\dfrac{\\sum x^{2} - \\dfrac{(\\sum x)^{2}}{n} }{n - 1} } \\)"),
          sprintf("\\( = \\sqrt{ \\dfrac{%s - \\dfrac{(%s)^{2}}{%s} }{%s - 1} } = %s \\)", sumx2, sumx, n, n, sdval)
        )
      })
      output$renderDSBoxplot <- renderUI({
        dat <- if(input$dataInput == 'Upload Data' && !is.null(input$dsUploadVars)) na.omit(as.data.frame(dsUploadData())[, input$dsUploadVars[1]]) else dsRawData()
        if(length(dat) < 2) return(NULL)
        boxplot(dat, main = "Boxplot", ylab = "Value", col = "#4F81BD")
      })
      output$renderDSHistogram <- renderUI({
        dat <- if(input$dataInput == 'Upload Data' && !is.null(input$dsUploadVars)) na.omit(as.data.frame(dsUploadData())[, input$dsUploadVars[1]]) else dsRawData()
        if(length(dat) < 2) return(NULL)
        hist(dat, main = "Histogram", xlab = "Value", col = "#4F81BD")
      })
      output$dsStemLeaf <- renderPrint({
        dat <- if(input$dataInput == 'Upload Data' && !is.null(input$dsUploadVars)) na.omit(as.data.frame(dsUploadData())[, input$dsUploadVars[1]]) else dsRawData()
        if(length(dat) < 2) return(NULL)
        utils::stem(dat)
      })
    })
    # Reset functionality
    observeEvent(input$resetAll, {
      updateTextAreaInput(session, "descriptiveStat", value = "2.14,   2.09,   2.65,   3.56,   5.55,   5.00,   5.55,   8.09,   10.79")
      updatePickerInput(session, "dsTableFilters", selected = c("Observations", "Mean", "Minimum", "First Quartile (Q1)", "Second Quartile or Median (Q2)", "Third Quartile (Q3)", "Maximum", "Sample Standard Deviation"))
      updateSelectizeInput(session, "dsGraphOptions", selected = c("Boxplot"))
      output$renderDescrStats <- renderUI({ NULL })
      output$dsTableData <- renderUI({ NULL })
      output$sampleDataTable <- renderUI({ NULL })
      output$dsMeanCalc <- renderUI({ NULL })
      output$dsSDCalc <- renderUI({ NULL })
      output$renderDSBoxplot <- renderUI({ NULL })
      output$renderDSHistogram <- renderUI({ NULL })
      output$dsStemLeaf <- renderPrint({ NULL })
      shinyjs::hide("descriptiveStatsMP")
    })
  })
} 