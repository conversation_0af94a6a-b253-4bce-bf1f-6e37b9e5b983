UnsupervisedMLUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("umlUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("umlVars"), "Variables", choices = NULL, multiple = TRUE),
        selectInput(ns("umlMethod"), "Method", choices = c("k-means", "Hierarchical", "GMM", "Autoencoder")),
        numericInput(ns("umlClusters"), "Number of Clusters", value = 3, min = 2, max = 20),
        br(),
        actionButton(ns("goUML"), label = "Run Clustering", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("umlError")),
        tableOutput(ns("umlSummary")),
        plotOutput(ns("umlPlot"))
      )
    )
  )
} 