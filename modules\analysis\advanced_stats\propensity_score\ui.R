PropensityScoreUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("psUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("psTreatment"), "Treatment Variable", choices = NULL),
        selectizeInput(ns("psCovariates"), "Covariates", choices = NULL, multiple = TRUE),
        selectInput(ns("psMethod"), "Method", choices = c("Matching", "Weighting")),
        br(),
        actionButton(ns("goPS"), label = "Run Propensity Score Analysis", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("psError")),
        tableOutput(ns("psSummary")),
        plotOutput(ns("psPlot"))
      )
    )
  )
} 