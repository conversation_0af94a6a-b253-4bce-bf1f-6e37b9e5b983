SupervisedMLServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    mlData <- eventReactive(input$mlUserData, {
      handle_file_upload(input$mlUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(mlData(), {
      data <- mlData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, "mlResponse", choices = names(data), server = TRUE)
        updateSelectizeInput(session, "mlPredictors", choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    mlValidationErrors <- reactive({
      errors <- c()
      data <- mlData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$mlResponse) || input$mlResponse == "") {
        errors <- c(errors, "Please select a response variable.")
      }
      
      if (is.null(input$mlPredictors) || length(input$mlPredictors) == 0) {
        errors <- c(errors, "Please select at least one predictor variable.")
      }
      
      if (!is.null(input$mlResponse) && !is.null(input$mlPredictors) && 
          input$mlResponse != "" && length(input$mlPredictors) > 0) {
        
        # Check if response variable is in predictors
        if (input$mlResponse %in% input$mlPredictors) {
          errors <- c(errors, "Response variable cannot be included in predictors.")
        }
        
        # Check for sufficient observations
        n_obs <- nrow(data)
        n_vars <- length(input$mlPredictors) + 1  # +1 for response
        
        if (n_obs <= n_vars) {
          errors <- c(errors, sprintf("Number of observations (%d) must be greater than number of variables (%d).", n_obs, n_vars))
        }
        
        # Check for variance in response variable
        response_var <- data[[input$mlResponse]]
        if (sd(response_var, na.rm = TRUE) == 0) {
          errors <- c(errors, "Response variable must have variance (not all the same).")
        }
        
        # Check for variance in predictor variables
        for (var in input$mlPredictors) {
          if (sd(data[[var]], na.rm = TRUE) == 0) {
            errors <- c(errors, sprintf("Predictor variable '%s' must have variance (not all the same).", var))
          }
        }
      }
      
      # Check cross-validation folds
      if (!is.null(input$mlCVFolds) && (input$mlCVFolds < 2 || input$mlCVFolds > 20)) {
        errors <- c(errors, "Cross-validation folds must be between 2 and 20.")
      }
      
      errors
    })
    
    # Supervised ML analysis reactive
    mlResult <- eventReactive(input$goML, {
      data <- mlData()
      req(data, input$mlResponse, input$mlPredictors, input$mlModelType)
      
      # Remove rows with missing values
      complete_data <- data[complete.cases(data[, c(input$mlResponse, input$mlPredictors)]), ]
      
      if (nrow(complete_data) < 10) {
        stop("Insufficient complete cases for machine learning analysis.")
      }
      
      # Perform supervised machine learning
      tryCatch({
        if (requireNamespace("caret", quietly = TRUE)) {
          # Use caret for model training
          x <- complete_data[, input$mlPredictors, drop = FALSE]
          y <- complete_data[[input$mlResponse]]
          
          # Set up cross-validation
          cv_folds <- ifelse(is.null(input$mlCVFolds), 5, input$mlCVFolds)
          ctrl <- caret::trainControl(method = "cv", number = cv_folds)
          
          # Train model based on type
          model_type <- tolower(input$mlModelType)
          if (model_type == "random forest") {
            if (!requireNamespace("randomForest", quietly = TRUE)) {
              stop("Package 'randomForest' is required for Random Forest.")
            }
            model <- caret::train(x, y, method = "rf", trControl = ctrl)
          } else if (model_type == "svm") {
            if (!requireNamespace("e1071", quietly = TRUE)) {
              stop("Package 'e1071' is required for SVM.")
            }
            model <- caret::train(x, y, method = "svmRadial", trControl = ctrl)
          } else if (model_type == "gbm") {
            if (!requireNamespace("gbm", quietly = TRUE)) {
              stop("Package 'gbm' is required for Gradient Boosting.")
            }
            model <- caret::train(x, y, method = "gbm", trControl = ctrl)
          } else if (model_type == "knn") {
            model <- caret::train(x, y, method = "knn", trControl = ctrl)
          } else if (model_type == "neural network") {
            if (!requireNamespace("nnet", quietly = TRUE)) {
              stop("Package 'nnet' is required for Neural Network.")
            }
            model <- caret::train(x, y, method = "nnet", trControl = ctrl)
          } else {
            stop("Unsupported model type.")
          }
          
          # Get predictions
          predictions <- predict(model, x)
          residuals <- y - predictions
          
          # Calculate performance metrics
          mse <- mean(residuals^2)
          rmse <- sqrt(mse)
          mae <- mean(abs(residuals))
          r_squared <- 1 - (sum(residuals^2) / sum((y - mean(y))^2))
          
          # Variable importance (if available)
          var_importance <- NULL
          if (model_type == "random forest" && !is.null(model$finalModel)) {
            var_importance <- randomForest::importance(model$finalModel)
          }
          
          list(
            model = model,
            model_type = input$mlModelType,
            predictions = predictions,
            residuals = residuals,
            actual = y,
            mse = mse,
            rmse = rmse,
            mae = mae,
            r_squared = r_squared,
            cv_folds = cv_folds,
            n_observations = nrow(complete_data),
            n_predictors = length(input$mlPredictors),
            variable_importance = var_importance,
            response_variable = input$mlResponse,
            predictor_variables = input$mlPredictors
          )
        } else {
          stop("Package 'caret' is required for supervised machine learning.")
        }
      }, error = function(e) {
        stop(e$message)
      })
    })
    
    # Error handling
    output$mlError <- renderUI({
      errors <- mlValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          mlResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "Supervised ML Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$mlModelSummary <- renderUI({
      req(mlResult())
      res <- mlResult()
      
      tagList(
        h4("Supervised Machine Learning Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Metric = c("Model Type", "Response Variable", "Number of Predictors", "Number of Observations", "Cross-Validation Folds"),
            Value = c(
              res$model_type,
              res$response_variable,
              res$n_predictors,
              res$n_observations,
              res$cv_folds
            )
          )
        }),
        h4("Model Performance"),
        renderTable({
          data.frame(
            Metric = c("Mean Squared Error (MSE)", "Root Mean Squared Error (RMSE)", "Mean Absolute Error (MAE)", "R-squared"),
            Value = c(
              round(res$mse, 4),
              round(res$rmse, 4),
              round(res$mae, 4),
              round(res$r_squared, 4)
            )
          )
        })
      )
    })
    
    output$mlPlot <- renderPlot({
      req(mlResult())
      res <- mlResult()
      
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # Actual vs Predicted
      plot(res$actual, res$predictions, main = "Actual vs Predicted",
           xlab = "Actual Values", ylab = "Predicted Values", pch = 19, col = "blue")
      abline(a = 0, b = 1, col = "red", lty = 2)
      
      # Residuals plot
      plot(res$predictions, res$residuals, main = "Residuals vs Predicted",
           xlab = "Predicted Values", ylab = "Residuals", pch = 19, col = "green")
      abline(h = 0, col = "red", lty = 2)
      
      # Q-Q plot of residuals
      qqnorm(res$residuals, main = "Q-Q Plot of Residuals")
      qqline(res$residuals, col = "red")
      
      # Variable importance (if available)
      if (!is.null(res$variable_importance)) {
        barplot(res$variable_importance[, 1], main = "Variable Importance",
                ylab = "Importance", col = "orange", las = 2)
      } else {
        # Alternative: histogram of residuals
        hist(res$residuals, main = "Distribution of Residuals",
             xlab = "Residuals", ylab = "Frequency", col = "lightblue")
      }
      
      par(mfrow = c(1, 1))
    })
    
    output$mlDiagnostics <- renderUI({
      req(mlResult())
      res <- mlResult()
      
      tagList(
        h4("Model Diagnostics"),
        renderTable({
          data.frame(
            Metric = c("Model Complexity", "Training Method", "Cross-Validation Method", "Residual Standard Error"),
            Value = c(
              res$n_predictors,
              "Supervised Learning",
              paste(res$cv_folds, "-fold CV"),
              round(sd(res$residuals), 4)
            )
          )
        }),
        if (!is.null(res$variable_importance)) {
          tagList(
            h4("Variable Importance"),
            renderTable({
              data.frame(
                Variable = rownames(res$variable_importance),
                Importance = round(res$variable_importance[, 1], 4)
              )
            })
          )
        }
      )
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$mlDataSummary <- renderUI({
      req(mlData(), input$mlResponse, input$mlPredictors)
      data <- mlData()
      response <- input$mlResponse
      predictors <- input$mlPredictors
      
      tagList(
        h4("Dataset Summary"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "Response Variable", "Number of Predictors", "Complete Cases"),
            Value = c(
              nrow(data),
              ncol(data),
              response,
              length(predictors),
              sum(complete.cases(data[, c(response, predictors)]))
            )
          )
        }),
        h4("Response Variable Summary"),
        renderTable({
          if (!is.null(response) && response != "") {
            response_data <- data[[response]]
            data.frame(
              Metric = c("Mean", "Median", "SD", "Min", "Max", "Missing"),
              Value = c(
                round(mean(response_data, na.rm = TRUE), 4),
                round(median(response_data, na.rm = TRUE), 4),
                round(sd(response_data, na.rm = TRUE), 4),
                round(min(response_data, na.rm = TRUE), 4),
                round(max(response_data, na.rm = TRUE), 4),
                sum(is.na(response_data))
              )
            )
          } else {
            data.frame(Metric = "N/A", Value = "N/A")
          }
        })
      )
    })
    
    output$mlAssumptions <- renderUI({
      req(mlResult())
      res <- mlResult()
      
      tagList(
        h4("Machine Learning Assumptions Check"),
        renderTable({
          data.frame(
            Assumption = c("Adequate Sample Size", "Variable Selection", "Cross-Validation", "Model Complexity"),
            Status = c(
              ifelse(res$n_observations >= 30, "Pass", "Fail"),
              "Pass",
              "Pass",
              ifelse(res$n_predictors < res$n_observations/10, "Pass", "Fail")
            ),
            Description = c(
              "Sufficient observations for reliable model training",
              "Appropriate variables selected for prediction",
              "Cross-validation used to prevent overfitting",
              "Model complexity appropriate for sample size"
            )
          )
        }),
        h4("Model Selection Guidelines"),
        renderTable({
          data.frame(
            Model_Type = c("Random Forest", "SVM", "GBM", "kNN", "Neural Network"),
            Use_When = c(
              "Non-linear relationships, feature importance needed",
              "High-dimensional data, clear decision boundaries",
              "Complex non-linear patterns, ensemble learning",
              "Simple relationships, interpretable predictions",
              "Complex non-linear patterns, large datasets"
            ),
            Pros = c(
              "Robust, handles non-linearity, feature importance",
              "Effective in high dimensions, good generalization",
              "High accuracy, handles complex patterns",
              "Simple, interpretable, no assumptions",
              "Can capture complex patterns, flexible"
            )
          )
        })
      )
    })
    
    output$mlDiagnosticPlots <- renderPlot({
      req(mlResult())
      res <- mlResult()
      
      par(mfrow = c(2, 2))
      
      # Residuals vs fitted
      plot(res$predictions, res$residuals, main = "Residuals vs Predicted",
           xlab = "Predicted Values", ylab = "Residuals", pch = 19, col = "blue")
      abline(h = 0, col = "red", lty = 2)
      
      # Q-Q plot of residuals
      qqnorm(res$residuals, main = "Q-Q Plot of Residuals")
      qqline(res$residuals, col = "red")
      
      # Scale-location plot
      sqrt_abs_resid <- sqrt(abs(res$residuals))
      plot(res$predictions, sqrt_abs_resid, main = "Scale-Location Plot",
           xlab = "Predicted Values", ylab = "sqrt(|Residuals|)", pch = 19, col = "green")
      
      # Residuals histogram
      hist(res$residuals, main = "Distribution of Residuals",
           xlab = "Residuals", ylab = "Frequency", col = "lightblue")
      
      par(mfrow = c(1, 1))
    })
    
    # Uploaded Data Tab Outputs
    output$mlDataTable <- renderDT({
      req(mlData())
      data <- mlData()
      
      DT::datatable(
        data,
        options = list(
          pageLength = 10,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
        ),
        extensions = 'Buttons',
        filter = 'top',
        rownames = FALSE
      )
    })
    
    output$mlDataInfo <- renderUI({
      req(mlData())
      data <- mlData()
      
      tagList(
        h4("Dataset Information"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "File Name"),
            Value = c(nrow(data), ncol(data), 
                     ifelse(!is.null(input$mlUserData), input$mlUserData$name, "Unknown"))
          )
        }),
        h4("Variable Information"),
        renderTable({
          data.frame(
            Variable = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Unique = sapply(data, function(x) length(unique(x)))
          )
        })
      )
    })
    
    # Legacy outputs for backward compatibility
    output$mlSummary <- renderTable({
      req(mlResult())
      res <- mlResult()
      
      data.frame(
        Metric = c("Model Type", "R-squared", "RMSE", "MAE", "CV Folds"),
        Value = c(
          res$model_type,
          round(res$r_squared, 4),
          round(res$rmse, 4),
          round(res$mae, 4),
          res$cv_folds
        )
      )
    })
  })
} 