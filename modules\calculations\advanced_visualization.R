# Advanced Visualization Calculation Functions

#' Create 3D scatter plot with interactive features
#' @param data Input dataset
#' @param x_var X variable name
#' @param y_var Y variable name
#' @param z_var Z variable name
#' @param color_var Color variable (optional)
#' @param size_var Size variable (optional)
#' @param group_var Group variable (optional)
#' @param title Plot title
#' @param xlab X axis label
#' @param ylab Y axis label
#' @param interactive Whether to create interactive plot
#' @return Plot object
create_3d_scatter_plot_analysis <- function(data, x_var, y_var, z_var, color_var = NULL, 
                                           size_var = NULL, group_var = NULL, title = "",
                                           xlab = "", ylab = "", interactive = TRUE) {
  
  # Load required packages
  if (!require(plotly, quietly = TRUE)) {
    stop("plotly package is required for interactive 3D plots")
  }
  
  # Prepare data
  plot_data <- data.frame(
    x = data[[x_var]],
    y = data[[y_var]],
    z = data[[z_var]]
  )
  
  if (!is.null(color_var)) {
    plot_data$color <- data[[color_var]]
  }
  if (!is.null(size_var)) {
    plot_data$size <- data[[size_var]]
  }
  if (!is.null(group_var)) {
    plot_data$group <- data[[group_var]]
  }
  
  if (interactive) {
    p <- plot_ly(plot_data, x = ~x, y = ~y, z = ~z, type = "scatter3d", mode = "markers")
    
    if (!is.null(color_var)) {
      p <- p %>% add_markers(color = ~color)
    }
    if (!is.null(size_var)) {
      p <- p %>% add_markers(size = ~size)
    }
    if (!is.null(group_var)) {
      p <- p %>% add_markers(color = ~group)
    }
    
    p <- p %>% layout(
      title = title,
      scene = list(
        xaxis = list(title = xlab),
        yaxis = list(title = ylab),
        zaxis = list(title = "Z")
      )
    )
  } else {
    # Static 3D plot
    if (!require(scatterplot3d, quietly = TRUE)) {
      stop("scatterplot3d package is required for static 3D plots")
    }
    
    p <- scatterplot3d(plot_data$x, plot_data$y, plot_data$z,
                       main = title,
                       xlab = xlab,
                       ylab = ylab,
                       zlab = "Z")
  }
  
  return(p)
}

#' Create geographic map visualization
#' @param data Input dataset
#' @param lat_var Latitude variable name
#' @param lon_var Longitude variable name
#' @param color_var Color variable (optional)
#' @param size_var Size variable (optional)
#' @param map_type Map type
#' @param title Plot title
#' @return Leaflet map object
create_geographic_map_analysis <- function(data, lat_var, lon_var, color_var = NULL, 
                                          size_var = NULL, map_type = "osm", title = "") {
  
  # Load required packages
  if (!require(leaflet, quietly = TRUE)) {
    stop("leaflet package is required for geographic maps")
  }
  
  # Prepare data
  plot_data <- data.frame(
    lat = data[[lat_var]],
    lon = data[[lon_var]]
  )
  
  if (!is.null(color_var)) {
    plot_data$color <- data[[color_var]]
  }
  if (!is.null(size_var)) {
    plot_data$size <- data[[size_var]]
  }
  
  # Create map
  m <- leaflet(plot_data) %>%
    addTiles() %>%
    addCircleMarkers(
      lng = ~lon, lat = ~lat,
      radius = if(!is.null(size_var)) ~size else 5,
      color = if(!is.null(color_var)) ~color else "red",
      popup = ~paste("Lat:", lat, "Lon:", lon)
    )
  
  return(m)
}

#' Create interactive network visualization
#' @param data Input dataset
#' @param from_var From node variable
#' @param to_var To node variable
#' @param weight_var Weight variable (optional)
#' @param group_var Group variable (optional)
#' @param layout Layout algorithm
#' @param title Plot title
#' @param interactive Whether to create interactive plot
#' @return Network plot object
create_network_plot_analysis <- function(data, from_var, to_var, weight_var = NULL, 
                                        group_var = NULL, layout = 1, title = "", interactive = TRUE) {
  
  # Load required packages
  if (!require(igraph, quietly = TRUE)) {
    stop("igraph package is required for network analysis")
  }
  
  # Prepare edges
  edges <- data.frame(
    from = data[[from_var]],
    to = data[[to_var]]
  )
  
  if (!is.null(weight_var)) {
    edges$weight <- data[[weight_var]]
  }
  
  # Create network object
  net <- graph_from_data_frame(edges, directed = FALSE)
  
  if (interactive) {
    if (!require(visNetwork, quietly = TRUE)) {
      stop("visNetwork package is required for interactive network plots")
    }
    
    # Create nodes
    nodes <- data.frame(id = unique(c(edges$from, edges$to)))
    if (!is.null(group_var)) {
      nodes$group <- data[[group_var]][match(nodes$id, data[[from_var]])]
    }
    
    p <- visNetwork(nodes, edges) %>%
      visPhysics(stabilization = FALSE) %>%
      visLayout(randomSeed = 123) %>%
      visOptions(highlightNearest = TRUE, nodesIdSelection = TRUE)
  } else {
    # Static network plot
    p <- plot(net, 
              vertex.size = 5,
              vertex.label.cex = 0.8,
              edge.width = if(!is.null(weight_var)) edges$weight else 1,
              main = title)
  }
  
  return(p)
}

#' Create heatmap matrix visualization
#' @param data Input dataset
#' @param vars Variables for correlation matrix
#' @param method Correlation method
#' @param title Plot title
#' @param interactive Whether to create interactive plot
#' @return Heatmap object
create_heatmap_matrix_analysis <- function(data, vars, method = "pearson", title = "", interactive = TRUE) {
  
  # Select numeric variables only
  numeric_vars <- vars[sapply(data[vars], is.numeric)]
  
  if (length(numeric_vars) < 2) {
    stop("Need at least 2 numeric variables for correlation matrix")
  }
  
  # Calculate correlation matrix
  cor_matrix <- cor(data[numeric_vars], method = method, use = "complete.obs")
  
  if (interactive) {
    if (!require(plotly, quietly = TRUE)) {
      stop("plotly package is required for interactive heatmaps")
    }
    
    p <- plot_ly(z = cor_matrix, 
                 x = colnames(cor_matrix), 
                 y = rownames(cor_matrix),
                 type = "heatmap",
                 colorscale = "Viridis") %>%
      layout(title = title)
  } else {
    # Static heatmap
    p <- heatmap(cor_matrix, 
                 main = title,
                 col = viridis(100))
  }
  
  return(p)
}

#' Create parallel coordinates plot
#' @param data Input dataset
#' @param vars Variables for parallel coordinates
#' @param group_var Group variable (optional)
#' @param title Plot title
#' @param interactive Whether to create interactive plot
#' @return Parallel coordinates plot object
create_parallel_coordinates_analysis <- function(data, vars, group_var = NULL, title = "", interactive = TRUE) {
  
  # Select numeric variables only
  numeric_vars <- vars[sapply(data[vars], is.numeric)]
  
  if (length(numeric_vars) < 2) {
    stop("Need at least 2 numeric variables for parallel coordinates")
  }
  
  plot_data <- data[numeric_vars]
  
  if (!is.null(group_var)) {
    plot_data$group <- data[[group_var]]
  }
  
  if (interactive) {
    if (!require(plotly, quietly = TRUE)) {
      stop("plotly package is required for interactive parallel coordinates")
    }
    
    p <- plot_ly(plot_data, type = "parcoords",
                 line = list(color = if(!is.null(group_var)) ~group else "blue",
                            colorscale = "Viridis")) %>%
      layout(title = title)
  } else {
    if (!require(GGally, quietly = TRUE)) {
      stop("GGally package is required for static parallel coordinates")
    }
    
    p <- ggparcoord(plot_data, columns = 1:length(numeric_vars),
                    groupColumn = if(!is.null(group_var)) "group" else NULL,
                    alphaLines = 0.7) +
      labs(title = title) +
      theme_minimal()
  }
  
  return(p)
}

#' Create radar/spider chart
#' @param data Input dataset
#' @param vars Variables for radar chart
#' @param group_var Group variable
#' @param title Plot title
#' @param interactive Whether to create interactive plot
#' @return Radar chart object
create_radar_chart_analysis <- function(data, vars, group_var, title = "", interactive = TRUE) {
  
  # Select numeric variables only
  numeric_vars <- vars[sapply(data[vars], is.numeric)]
  
  if (length(numeric_vars) < 3) {
    stop("Need at least 3 numeric variables for radar chart")
  }
  
  # Calculate means by group
  plot_data <- aggregate(data[numeric_vars], 
                        by = list(group = data[[group_var]]), 
                        FUN = mean, na.rm = TRUE)
  
  if (interactive) {
    if (!require(plotly, quietly = TRUE)) {
      stop("plotly package is required for interactive radar charts")
    }
    
    p <- plot_ly(type = "scatterpolar", mode = "lines+markers")
    
    for (i in 1:nrow(plot_data)) {
      values <- as.numeric(plot_data[i, numeric_vars])
      p <- p %>% add_trace(
        r = values,
        theta = numeric_vars,
        name = plot_data$group[i],
        fill = "toself"
      )
    }
    
    p <- p %>% layout(
      polar = list(radialaxis = list(visible = TRUE, range = c(0, max(plot_data[numeric_vars], na.rm = TRUE)))),
      title = title
    )
  } else {
    if (!require(fmsb, quietly = TRUE)) {
      stop("fmsb package is required for static radar charts")
    }
    
    p <- fmsb::radarchart(plot_data[numeric_vars], 
                          axistype = 1,
                          title = title)
  }
  
  return(p)
}

#' Create treemap visualization
#' @param data Input dataset
#' @param group_var Group variable
#' @param size_var Size variable
#' @param color_var Color variable (optional)
#' @param title Plot title
#' @param interactive Whether to create interactive plot
#' @return Treemap object
create_treemap_analysis <- function(data, group_var, size_var, color_var = NULL, title = "", interactive = TRUE) {
  
  # Aggregate data by group
  plot_data <- aggregate(data[[size_var]], 
                        by = list(group = data[[group_var]]), 
                        FUN = sum, na.rm = TRUE)
  
  if (!is.null(color_var)) {
    color_data <- aggregate(data[[color_var]], 
                           by = list(group = data[[group_var]]), 
                           FUN = mean, na.rm = TRUE)
    plot_data$color <- color_data$x
  }
  
  if (interactive) {
    if (!require(plotly, quietly = TRUE)) {
      stop("plotly package is required for interactive treemaps")
    }
    
    p <- plot_ly(
      type = "treemap",
      labels = plot_data$group,
      parents = "",
      values = plot_data$x,
      textinfo = "label+value"
    ) %>%
      layout(title = title)
  } else {
    if (!require(treemap, quietly = TRUE)) {
      stop("treemap package is required for static treemaps")
    }
    
    p <- treemap(plot_data,
                 index = "group",
                 vSize = "x",
                 vColor = if(!is.null(color_var)) "color" else NULL,
                 title = title)
  }
  
  return(p)
}

#' Create Sankey diagram
#' @param data Input dataset
#' @param from_var From variable
#' @param to_var To variable
#' @param value_var Value variable
#' @param title Plot title
#' @param interactive Whether to create interactive plot
#' @return Sankey diagram object
create_sankey_diagram_analysis <- function(data, from_var, to_var, value_var, title = "", interactive = TRUE) {
  
  # Aggregate flows
  plot_data <- aggregate(data[[value_var]], 
                        by = list(from = data[[from_var]], 
                                 to = data[[to_var]]), 
                        FUN = sum, na.rm = TRUE)
  
  if (interactive) {
    if (!require(plotly, quietly = TRUE)) {
      stop("plotly package is required for interactive Sankey diagrams")
    }
    
    p <- plot_ly(
      type = "sankey",
      orientation = "h",
      node = list(
        label = unique(c(plot_data$from, plot_data$to)),
        color = "blue"
      ),
      link = list(
        source = match(plot_data$from, unique(c(plot_data$from, plot_data$to))) - 1,
        target = match(plot_data$to, unique(c(plot_data$from, plot_data$to))) - 1,
        value = plot_data$x
      )
    ) %>%
      layout(title = title)
  } else {
    if (!require(networkD3, quietly = TRUE)) {
      stop("networkD3 package is required for static Sankey diagrams")
    }
    
    p <- networkD3::sankeyNetwork(
      Links = plot_data,
      Nodes = data.frame(name = unique(c(plot_data$from, plot_data$to))),
      Source = "from",
      Target = "to",
      Value = "x",
      NodeID = "name"
    )
  }
  
  return(p)
}

#' Generate comprehensive visualization summary
#' @param plot_object Plot object
#' @param plot_type Type of visualization
#' @param data_summary Data summary
#' @return Summary information
generate_visualization_summary <- function(plot_object, plot_type, data_summary = NULL) {
  
  summary_info <- list(
    type = plot_type,
    timestamp = Sys.time(),
    data_summary = data_summary
  )
  
  return(summary_info)
}

advanced_visualization_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  out <- list(h4("Advanced Visualization Summary"), renderPrint(results))
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

advanced_visualization_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  if (!is.null(results$plot)) {
    print(results$plot)
    return()
  }
  plot.new(); title("No plot available.")
} 