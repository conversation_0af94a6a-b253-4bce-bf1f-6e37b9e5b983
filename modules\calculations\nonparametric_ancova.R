# Non-parametric ANCOVA calculation and output helpers

nonparametric_ancova_uploadData_func <- function(npaUserData) {
  ext <- tools::file_ext(npaUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(npaUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(npaUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(npaUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(npaUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

nonparametric_ancova_results_func <- function(data, dependent_var, group_var, covariate_var, method = "quade") {
  tryCatch({
    
    required_cols <- c(dependent_var, group_var, covariate_var)
    if (!all(required_cols %in% names(data))) {
      stop("One or more specified columns are not in the dataset.")
    }
    
    # Prepare data for analysis functions
    analysis_data <- data.frame(
        Dependent = data[[dependent_var]],
        Group = as.factor(data[[group_var]]),
        Covariate = data[[covariate_var]]
    )
    
    results <- perform_nonparametric_ancova(analysis_data, method = method)
    
    list(
      results = results,
      data = data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Non-parametric ANCOVA calculation:", e$message))
  })
}

nonparametric_ancova_ht_html <- function(results) {
  res <- results$results$test_results
  main_test <- res[1,] # First test is the primary one
  
  tagList(
    h4(paste("Non-parametric ANCOVA:", main_test$Test)),
    p(sprintf("Test Statistic: %.4f, p-value: %.4f", main_test$Statistic, main_test$p_value)),
    p(ifelse(main_test$p_value < 0.05, "There is a significant difference between groups after adjusting for the covariate.", "There is no significant difference between groups after adjusting for the covariate."))
  )
}

nonparametric_ancova_summary_html <- function(results) {
  tagList(
    h4("Descriptive Statistics"),
    renderTable(results$results$descriptive_stats),
    h4("Adjusted Means (Rank-based)"),
    renderTable(results$results$adjusted_means)
  )
}

nonparametric_ancova_plot <- function(results) {
  generate_np_ancova_plots(results$results)
}

# --- Helper functions from original file ---

perform_nonparametric_ancova <- function(data, method = "quade", conf_level = 0.95) {
  
  data <- data[complete.cases(data), ]
  
  desc_stats <- data %>%
    dplyr::group_by(Group) %>%
    dplyr::summarise(
      Mean = mean(Dependent, na.rm = TRUE),
      SD = sd(Dependent, na.rm = TRUE),
      n = dplyr::n(),
      .groups = 'drop'
    )
  
  data$Rank_Dependent <- rank(data$Dependent)
  data$Rank_Covariate <- rank(data$Covariate)
  
  test_results <- switch(method,
    "quade" = perform_quade_test(data),
    "rank_residuals" = perform_rank_residuals_test(data),
    "robust" = perform_robust_ancova(data),
    stop("Invalid method specified.")
  )
  
  adjusted_means <- calculate_rank_adjusted_means(data, conf_level)
  
  list(
    test_results = test_results,
    descriptive_stats = desc_stats,
    adjusted_means = adjusted_means,
    data = data
  )
}

perform_quade_test <- function(data) {
  if (!requireNamespace("coin", quietly = TRUE)) stop("Package 'coin' required for Quade test.")
  
  quade_test <- coin::quade_test(Dependent ~ Group | Covariate, data = data)
  
  data.frame(
    Test = "Quade's Test",
    Statistic = coin::statistic(quade_test),
    p_value = coin::pvalue(quade_test),
    stringsAsFactors = FALSE
  )
}

perform_rank_residuals_test <- function(data) {
    covariate_model <- lm(Rank_Dependent ~ Rank_Covariate, data = data)
    data$Rank_Residuals <- residuals(covariate_model)
    kw_test <- kruskal.test(Rank_Residuals ~ Group, data = data)
    
    data.frame(
        Test = "Kruskal-Wallis on Rank Residuals",
        Statistic = kw_test$statistic,
        p_value = kw_test$p.value,
        stringsAsFactors = FALSE
    )
}

perform_robust_ancova <- function(data) {
  if (!requireNamespace("WRS2", quietly = TRUE)) stop("Package 'WRS2' required for robust ANCOVA.")
  
  robust_model <- WRS2::ancova(Dependent ~ Group * Covariate, data = data)
  
  data.frame(
    Test = "Robust ANCOVA (WRS2)",
    Statistic = NA, # WRS2 doesn't provide a single statistic in the same way
    p_value = robust_model$pv[1], # p-value for the main effect of Group
    stringsAsFactors = FALSE
  )
}

calculate_rank_adjusted_means <- function(data, conf_level) {
  model <- lm(Rank_Dependent ~ Group + Rank_Covariate, data = data)
  
  if (!requireNamespace("emmeans", quietly = TRUE)) stop("Package 'emmeans' required for adjusted means.")
  
  adj_means <- emmeans::emmeans(model, "Group")
  
  as.data.frame(adj_means)
}

generate_np_ancova_plots <- function(results) {
  ggplot(results$data, aes(x = Covariate, y = Dependent, color = Group)) +
    geom_point(alpha = 0.7) +
    geom_smooth(method = "lm", se = FALSE) +
    labs(title = "Scatter Plot with Regression Lines",
         x = "Covariate", y = "Dependent Variable",
         color = "Group") +
    theme_minimal()
}