# ANCOVA Server
# Analysis of Covariance

source("modules/calculations/ancova.R")

AncovaServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactive values
    ancova_data <- reactiveVal(NULL)
    ancova_results <- reactiveVal(NULL)
    
    # File upload reactive
    ancovaUploadData <- eventReactive(input$ancovaUserData, {
      handle_file_upload(input$ancovaUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(ancovaUploadData(), {
      data <- ancovaUploadData()
      # Clear selectizeInputs and main panel
      updateSelectizeInput(session, 'ancovaResponseVariable', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'ancovaFactorVariable', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'ancovaCovariateVariable', choices = character(0), selected = NULL, server = TRUE)
      output$ancovaResults <- renderUI({ NULL })
      
      # If data is valid, update choices
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'ancovaResponseVariable', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'ancovaFactorVariable', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'ancovaCovariateVariable', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    ancovaValidationErrors <- reactive({
      errors <- c()
      
      if (input$ancovaDataMethod == "Manual Entry") {
        # Manual entry validation
        if (is.null(input$ancovaData) || input$ancovaData == "") {
          errors <- c(errors, "Response values are required for manual entry.")
        } else {
          data_vals <- createNumLst(input$ancovaData)
          if (length(data_vals) < 6) {
            errors <- c(errors, "At least 6 observations are required for ANCOVA.")
          }
        }
        
        if (is.null(input$ancovaFactorData) || input$ancovaFactorData == "") {
          errors <- c(errors, "Factor values are required for manual entry.")
        }
        if (is.null(input$ancovaCovariateData) || input$ancovaCovariateData == "") {
          errors <- c(errors, "Covariate values are required for manual entry.")
        }
      } else {
        # File upload validation
        data <- ancovaUploadData()
        if (is.null(data) || !is.data.frame(data)) {
          errors <- c(errors, "No data uploaded or file could not be read.")
          return(errors)
        }
        if (is.null(input$ancovaResponseVariable) || input$ancovaResponseVariable == "") {
          errors <- c(errors, "Please select a response variable.")
        } else {
          var_data <- data[[input$ancovaResponseVariable]]
          if (!is.numeric(var_data)) {
            errors <- c(errors, "Response variable must be numeric.")
          }
          if (length(na.omit(var_data)) < 6) {
            errors <- c(errors, "At least 6 non-missing observations are required.")
          }
        }
        if (is.null(input$ancovaFactorVariable) || input$ancovaFactorVariable == "") {
          errors <- c(errors, "Please select a factor variable.")
        }
        if (is.null(input$ancovaCovariateVariable) || input$ancovaCovariateVariable == "") {
          errors <- c(errors, "Please select a covariate variable.")
        } else {
          cov_data <- data[[input$ancovaCovariateVariable]]
          if (!is.numeric(cov_data)) {
            errors <- c(errors, "Covariate variable must be numeric.")
          }
        }
      }
      
      errors
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goAncova, {
      output$ancovaResults <- renderUI({
        errors <- ancovaValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in ANCOVA", errors = errors)
        } else {
          tryCatch({
            # Prepare data
            if (input$ancovaDataMethod == "Manual Entry") {
              data_vals <- createNumLst(input$ancovaData)
              factor_vals <- createNumLst(input$ancovaFactorData)
              covariate_vals <- createNumLst(input$ancovaCovariateData)
            } else {
              data <- ancovaUploadData()
              req(data, input$ancovaResponseVariable, input$ancovaFactorVariable, input$ancovaCovariateVariable)
              data_vals <- data[[input$ancovaResponseVariable]]
              factor_vals <- data[[input$ancovaFactorVariable]]
              covariate_vals <- data[[input$ancovaCovariateVariable]]
            }
            
            conf_level <- input$ancovaConfLevel
            
            # Perform ANCOVA
            result <- perform_ancova(data_vals, factor_vals, covariate_vals, conf_level)
            
            # Store results
            ancova_results(result)
            
            # Display results
            tagList(
              tabsetPanel(
                id = ns("ancovaTabset"),
                selected = "Analysis",
                tabPanel(
                  id = ns("ancovaAnalysis"),
                  title = "Analysis",
                  titlePanel("ANCOVA Results"),
                  br(),
                  uiOutput(ns('ancovaSummary')),
                  br(),
                  h4("ANCOVA Table"),
                  tableOutput(ns('ancovaTable')),
                  br(),
                  h4("Effect Sizes"),
                  tableOutput(ns('ancovaEffectSizes')),
                  br(),
                  h4("Assumptions and Interpretation"),
                  uiOutput(ns('ancovaAssumptions'))
                ),
                tabPanel(
                  id = ns("ancovaDataSummary"),
                  title = "Data Summary",
                  h4("Descriptive Statistics by Group"),
                  tableOutput(ns('ancovaDescriptive')),
                  br(),
                  h4("Covariate Statistics"),
                  tableOutput(ns('ancovaCovariateStats')),
                  br(),
                  h4("Data Visualization"),
                  plotOutput(ns('ancovaScatterplot'), height = "400px")
                ),
                tabPanel(
                  id = ns("ancovaPostHoc"),
                  title = "Post-Hoc Tests",
                  h4("Adjusted Means"),
                  tableOutput(ns('ancovaAdjustedMeans')),
                  br(),
                  h4("Pairwise Comparisons"),
                  tableOutput(ns('ancovaPairwise'))
                ),
                tabPanel(
                  id = ns("ancovaUploadedData"),
                  title = "Uploaded Data",
                  h4("Raw Data"),
                  DT::DTOutput(ns('ancovaViewUpload'))
                )
              )
            )
          }, error = function(e) {
            errorScreenUI(title = "Error in ANCOVA Analysis", errors = e$message)
          })
        }
      })
    })
    
    # Analysis Tab Outputs
    output$ancovaSummary <- renderUI({
      req(ancova_results())
      result <- ancova_results()
      
      tagList(
        h4("ANCOVA Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Statistic = c("Total Sample Size", "Number of Groups", "Significance Level", 
                         "Model R²", "Adjusted R²", "Covariate Effect", "Factor Effect"),
            Value = c(
              result$total_n,
              result$num_groups,
              paste0((1 - result$conf_level) * 100, "%"),
              round(result$r_squared, 4),
              round(result$adj_r_squared, 4),
              ifelse(result$covariate_sig, "Significant", "Not Significant"),
              ifelse(result$factor_sig, "Significant", "Not Significant")
            )
          )
        }),
        br(),
        p(strong("Model:"), "Response ~ Factor + Covariate")
      )
    })
    
    output$ancovaTable <- renderTable({
      req(ancova_results())
      result <- ancova_results()
      
      result$anova_table
    }, digits = 4)
    
    output$ancovaEffectSizes <- renderTable({
      req(ancova_results())
      result <- ancova_results()
      
      result$effect_sizes
    }, digits = 4)
    
    output$ancovaAssumptions <- renderUI({
      req(ancova_results())
      result <- ancova_results()
      
      tagList(
        h5("Model Assumptions:"),
        p(strong("Normality:"), "Residuals should be normally distributed."),
        p(strong("Homoscedasticity:"), "Residuals should have constant variance."),
        p(strong("Independence:"), "Observations should be independent."),
        p(strong("Linearity:"), "Relationship between covariate and response should be linear."),
        br(),
        h5("Interpretation:"),
        p(strong("Factor Effect:"), "Tests whether group means differ after controlling for the covariate."),
        p(strong("Covariate Effect:"), "Tests whether the covariate significantly predicts the response."),
        p(strong("Interaction:"), "Tests whether the relationship between covariate and response differs by group.")
      )
    })
    
    # Data Summary Tab Outputs
    output$ancovaDescriptive <- renderTable({
      req(ancova_results())
      result <- ancova_results()
      
      result$descriptive_stats
    }, digits = 3)
    
    output$ancovaCovariateStats <- renderTable({
      req(ancova_results())
      result <- ancova_results()
      
      result$covariate_stats
    }, digits = 3)
    
    output$ancovaScatterplot <- renderPlot({
      req(ancova_results())
      result <- ancova_results()
      
      # Create scatter plot with regression lines by group
      if (input$ancovaDataMethod == "Upload File") {
        data <- ancovaUploadData()
        response_var <- input$ancovaResponseVariable
        factor_var <- input$ancovaFactorVariable
        covariate_var <- input$ancovaCovariateVariable
        
        ggplot(data, aes_string(x = covariate_var, y = response_var, color = factor_var)) +
          geom_point(alpha = 0.7) +
          geom_smooth(method = "lm", se = TRUE) +
          labs(title = "ANCOVA: Response vs Covariate by Group",
               x = "Covariate", y = "Response",
               color = "Group") +
          theme_minimal()
      } else {
        # For manual entry, create a simple plot
        plot(1, 1, type = "n", xlab = "Covariate", ylab = "Response", 
             main = "ANCOVA: Response vs Covariate by Group")
        legend("topright", legend = unique(result$factor_levels), 
               col = 1:length(unique(result$factor_levels)), pch = 1)
      }
    })
    
    # Post-Hoc Tab Outputs
    output$ancovaAdjustedMeans <- renderTable({
      req(ancova_results())
      result <- ancova_results()
      
      result$adjusted_means
    }, digits = 3)
    
    output$ancovaPairwise <- renderTable({
      req(ancova_results())
      result <- ancova_results()
      
      result$pairwise_comparisons
    }, digits = 4)
    
    # Uploaded Data Tab Output
    output$ancovaViewUpload <- DT::renderDT({
      req(ancovaUploadData())
      DT::datatable(ancovaUploadData(),
        options = list(pageLength = 10,
                       lengthMenu = list(c(10, 25, 50, 100), c("10", "25", "50", "100"))))
    })
  })
} 