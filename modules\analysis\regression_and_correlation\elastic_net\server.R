# Elastic Net Regression Server
# Combines L1 and L2 penalties for variable selection and regularization

source("modules/calculations/elastic_net_regression.R")

ElasticNetRegressionServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactive values
    elastic_net_data <- reactiveVal(NULL)
    elastic_net_results <- reactiveVal(NULL)
    
    # File upload reactive
    elasticNetUploadData <- eventReactive(input$elasticNetUserData, {
      handle_file_upload(input$elasticNetUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(elasticNetUploadData(), {
      data <- elasticNetUploadData()
      # Clear selectizeInputs and main panel
      updateSelectizeInput(session, 'elasticNetResponseVariable', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'elasticNetPredictorVariables', choices = character(0), selected = NULL, server = TRUE)
      output$elasticNetResults <- renderUI({ NULL })
      
      # If data is valid, update choices
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'elasticNetResponseVariable', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'elasticNetPredictorVariables', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    elasticNetValidationErrors <- reactive({
      errors <- c()
      
      if (input$elasticNetDataMethod == "Manual Entry") {
        # Manual entry validation
        if (is.null(input$elasticNetResponseData) || input$elasticNetResponseData == "") {
          errors <- c(errors, "Response data is required for manual entry.")
        } else {
          tryCatch({
            response_data <- parse_numeric_input(input$elasticNetResponseData)
            if (length(response_data) < 10) {
              errors <- c(errors, "At least 10 observations are required for elastic net regression.")
            }
          }, error = function(e) {
            errors <- c(errors, "Invalid response data format. Please check your input.")
          })
        }
        if (is.null(input$elasticNetPredictorData) || input$elasticNetPredictorData == "") {
          errors <- c(errors, "Predictor data is required for manual entry.")
        }
      } else {
        # File upload validation
        data <- elasticNetUploadData()
        if (is.null(data) || !is.data.frame(data)) {
          errors <- c(errors, "No data uploaded or file could not be read.")
          return(errors)
        }
        if (is.null(input$elasticNetResponseVariable) || input$elasticNetResponseVariable == "") {
          errors <- c(errors, "Please select a response variable.")
        } else {
          var_data <- data[[input$elasticNetResponseVariable]]
          if (!is.numeric(var_data)) {
            errors <- c(errors, "Response variable must be numeric.")
          }
          if (length(na.omit(var_data)) < 10) {
            errors <- c(errors, "At least 10 non-missing observations are required.")
          }
        }
        if (is.null(input$elasticNetPredictorVariables) || length(input$elasticNetPredictorVariables) == 0) {
          errors <- c(errors, "Please select at least one predictor variable.")
        } else {
          for (var in input$elasticNetPredictorVariables) {
            var_data <- data[[var]]
            if (!is.numeric(var_data)) {
              errors <- c(errors, paste("Predictor variable", var, "must be numeric."))
            }
          }
        }
      }
      
      errors
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goElasticNet, {
      output$elasticNetResults <- renderUI({
        errors <- elasticNetValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Elastic Net Regression", errors = errors)
        } else {
          tryCatch({
            # Prepare data
            if (input$elasticNetDataMethod == "Manual Entry") {
              # Parse manual entry data
              response_data <- parse_numeric_input(input$elasticNetResponseData)
              predictor_data <- parse_numeric_input(input$elasticNetPredictorData)
              
              if (length(response_data) != length(predictor_data)) {
                stop("Response and predictor data must have the same length")
              }
              
              data <- data.frame(
                Response = response_data,
                Predictor = predictor_data
              )
            } else {
              # Use uploaded data
              req(elasticNetUploadData(), input$elasticNetResponseVariable, input$elasticNetPredictorVariables)
              
              data <- elasticNetUploadData()
              response_var <- input$elasticNetResponseVariable
              predictor_vars <- input$elasticNetPredictorVariables
              
              # Select relevant columns
              data <- data[c(response_var, predictor_vars)]
              names(data)[1] <- "Response"
            }
            
            # Remove rows with missing values
            complete_cases <- complete.cases(data)
            if (sum(complete_cases) < nrow(data)) {
              warning("Removing rows with missing values")
              data <- data[complete_cases, ]
            }
            
            if (nrow(data) < 10) {
              stop("At least 10 observations are required for elastic net regression")
            }
            
            # Perform elastic net regression
            results <- perform_elastic_net_regression(data, 
                                                    cross_validation = input$elasticNetCrossValidation,
                                                    cv_folds = input$elasticNetCVFolds,
                                                    alpha_grid = input$elasticNetAlphaGrid,
                                                    alpha = input$elasticNetAlpha,
                                                    lambda = input$elasticNetLambda,
                                                    conf_level = input$elasticNetConfLevel)
            
            # Store results
            elastic_net_results(results)
            
            # Display results
            tagList(
              h3("Elastic Net Regression Results"),
              
              # Model information
              h4("Model Information"),
              renderTable({
                info_table <- data.frame(
                  Statistic = c("Optimal Alpha", "Optimal Lambda", "Variables Selected", "Total Variables"),
                  Value = c(
                    round(results$best_alpha, 4),
                    round(results$best_lambda, 4),
                    results$n_selected,
                    results$n_total
                  )
                )
                info_table
              }, rownames = FALSE),
              
              br(),
              
              # Performance metrics
              h4("Model Performance"),
              renderTable({
                perf_table <- data.frame(
                  Metric = c("R-squared", "Adjusted R-squared", "RMSE", "MAE"),
                  Value = c(
                    round(results$r_squared, 4),
                    round(results$adj_r_squared, 4),
                    round(results$rmse, 4),
                    round(results$mae, 4)
                  )
                )
                perf_table
              }, rownames = FALSE),
              
              br(),
              
              # Coefficient table
              h4("Coefficients"),
              renderDataTable({
                coef_table <- results$coefficients
                datatable(coef_table, 
                         options = list(pageLength = 10, scrollX = TRUE),
                         rownames = FALSE) %>%
                  formatRound(columns = c("Coefficient", "CI_Lower", "CI_Upper"), digits = 4) %>%
                  formatStyle("Selected", 
                             backgroundColor = styleEqual(c(TRUE, FALSE), c("#d4edda", "#f8d7da")))
              }),
              
              br(),
              
              # Cross-validation results
              if (!is.null(results$cv_results)) tagList(
                h4("Cross-Validation Results"),
                renderPlot({
                  # Plot CV results for different alpha values
                  cv_data <- results$cv_results
                  ggplot(cv_data, aes(x = Alpha, y = CV_Error)) +
                    geom_line() +
                    geom_point() +
                    geom_vline(xintercept = results$best_alpha, color = "red", linetype = "dashed") +
                    labs(title = "Cross-Validation Error by Alpha",
                         x = "Alpha", y = "Cross-Validation Error") +
                    theme_minimal()
                }),
                br()
              ),
              
              # Coefficient path plot
              h4("Coefficient Paths"),
              renderPlot({
                # Create coefficient path plot
                path_data <- results$coefficient_paths
                ggplot(path_data, aes(x = Lambda, y = Coefficient, color = Variable)) +
                  geom_line() +
                  geom_vline(xintercept = results$best_lambda, color = "red", linetype = "dashed") +
                  scale_x_log10() +
                  labs(title = "Coefficient Paths",
                       x = "Lambda (log scale)", y = "Coefficient") +
                  theme_minimal() +
                  theme(legend.position = "bottom")
              }),
              
              br(),
              
              # Model diagnostics
              h4("Model Diagnostics"),
              fluidRow(
                column(6,
                  renderPlot({
                    # Residuals vs fitted
                    ggplot(data.frame(Fitted = results$predictions, Residuals = results$residuals), 
                           aes(x = Fitted, y = Residuals)) +
                      geom_point(alpha = 0.7) +
                      geom_hline(yintercept = 0, color = "red", linetype = "dashed") +
                      labs(title = "Residuals vs Fitted",
                           x = "Fitted Values", y = "Residuals") +
                      theme_minimal()
                  })
                ),
                column(6,
                  renderPlot({
                    # Q-Q plot
                    qqnorm(results$residuals, main = "Q-Q Plot of Residuals")
                    qqline(results$residuals, col = "red")
                  })
                )
              ),
              
              br(),
              
              # Variable importance
              h4("Variable Importance"),
              renderDataTable({
                importance_table <- results$variable_importance
                datatable(importance_table, 
                         options = list(pageLength = 10, scrollX = TRUE),
                         rownames = FALSE) %>%
                  formatRound(columns = c("Importance", "Relative_Importance"), digits = 4)
              }),
              
              br(),
              
              # Educational content
              h4("Interpretation"),
              p(strong("Elastic Net:"), "Combines L1 (Lasso) and L2 (Ridge) penalties for variable selection and regularization."),
              p(strong("Alpha:"), "Controls the balance between L1 and L2 penalties (0 = Ridge, 1 = Lasso)."),
              p(strong("Lambda:"), "Controls the strength of regularization. Larger values result in more shrinkage."),
              p(strong("Variable Selection:"), "Variables with non-zero coefficients are considered important predictors.")
            )
          }, error = function(e) {
            errorScreenUI(title = "Error in Elastic Net Regression Analysis", errors = e$message)
          })
        }
      })
    })
    
    # Helper function to parse numeric input
    parse_numeric_input <- function(input_text) {
      if (is.null(input_text) || input_text == "") {
        stop("Please enter data")
      }
      
      # Parse comma, space, or newline separated values
      values <- unlist(strsplit(input_text, "[,\\s\\n]+"))
      values <- values[values != ""]
      
      # Convert to numeric
      numeric_values <- as.numeric(values)
      
      if (any(is.na(numeric_values))) {
        stop("All values must be numeric")
      }
      
      numeric_values
    }
  })
} 