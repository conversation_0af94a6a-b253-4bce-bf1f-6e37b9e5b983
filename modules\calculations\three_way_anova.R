# Three-Way ANOVA Calculations
# Factorial design with three factors

perform_three_way_anova <- function(data, factor1, factor2, factor3, conf_level = 0.95) {
  # Remove NA values
  complete_cases <- complete.cases(data, factor1, factor2, factor3)
  data <- data[complete_cases]
  factor1 <- factor1[complete_cases]
  factor2 <- factor2[complete_cases]
  factor3 <- factor3[complete_cases]
  
  # Convert factors to proper factors
  factor1 <- as.factor(factor1)
  factor2 <- as.factor(factor2)
  factor3 <- as.factor(factor3)
  
  # Get factor levels
  factor1_levels <- length(levels(factor1))
  factor2_levels <- length(levels(factor2))
  factor3_levels <- length(levels(factor3))
  total_cells <- factor1_levels * factor2_levels * factor3_levels
  
  total_n <- length(data)
  
  if (total_n < 8) {
    stop("At least 8 observations are required for three-way ANOVA")
  }
  
  # Fit the three-way ANOVA model
  model <- aov(data ~ factor1 * factor2 * factor3)
  
  # Get ANOVA table
  anova_summary <- summary(model)
  anova_table <- as.data.frame(anova_summary[[1]])
  
  # Add effect sizes (partial eta-squared)
  ss_total <- sum(anova_table$`Sum Sq`)
  anova_table$`Partial_Eta_Squared` <- anova_table$`Sum Sq` / (anova_table$`Sum Sq` + anova_table$`Residuals`)
  
  # Rename columns for clarity
  colnames(anova_table) <- c("Sum of Squares", "df", "Mean Square", "F-value", "P-value", "Partial_Eta_Squared")
  
  # Calculate R-squared values
  r_squared <- 1 - (anova_table$`Sum of Squares`[nrow(anova_table)] / ss_total)
  adj_r_squared <- 1 - ((anova_table$`Sum of Squares`[nrow(anova_table)] / anova_table$df[nrow(anova_table)]) / 
                        (ss_total / (total_n - 1)))
  
  # Calculate descriptive statistics
  descriptive_stats <- data.frame(
    Factor = c("Factor 1", "Factor 2", "Factor 3"),
    Levels = c(factor1_levels, factor2_levels, factor3_levels),
    Mean = c(mean(data), mean(data), mean(data)),
    SD = c(sd(data), sd(data), sd(data)),
    Min = c(min(data), min(data), min(data)),
    Max = c(max(data), max(data), max(data))
  )
  
  # Calculate cell means
  cell_means <- aggregate(data, by = list(factor1, factor2, factor3), FUN = mean)
  colnames(cell_means) <- c("Factor1", "Factor2", "Factor3", "Mean")
  
  # Calculate effect sizes for each effect
  effect_sizes <- data.frame(
    Effect = rownames(anova_table)[-nrow(anova_table)],
    Partial_Eta_Squared = anova_table$`Partial_Eta_Squared`[-nrow(anova_table)],
    stringsAsFactors = FALSE
  )
  
  # Perform post-hoc tests for main effects
  factor1_posthoc <- tryCatch({
    if (factor1_levels > 2) {
      tukey_result <- TukeyHSD(model, "factor1", conf.level = conf_level)
      as.data.frame(tukey_result$factor1)
    } else {
      data.frame(Comparison = "Only 2 levels", diff = NA, lwr = NA, upr = NA, p_adj = NA)
    }
  }, error = function(e) {
    data.frame(Comparison = "Error in calculation", diff = NA, lwr = NA, upr = NA, p_adj = NA)
  })
  
  factor2_posthoc <- tryCatch({
    if (factor2_levels > 2) {
      tukey_result <- TukeyHSD(model, "factor2", conf.level = conf_level)
      as.data.frame(tukey_result$factor2)
    } else {
      data.frame(Comparison = "Only 2 levels", diff = NA, lwr = NA, upr = NA, p_adj = NA)
    }
  }, error = function(e) {
    data.frame(Comparison = "Error in calculation", diff = NA, lwr = NA, upr = NA, p_adj = NA)
  })
  
  factor3_posthoc <- tryCatch({
    if (factor3_levels > 2) {
      tukey_result <- TukeyHSD(model, "factor3", conf.level = conf_level)
      as.data.frame(tukey_result$factor3)
    } else {
      data.frame(Comparison = "Only 2 levels", diff = NA, lwr = NA, upr = NA, p_adj = NA)
    }
  }, error = function(e) {
    data.frame(Comparison = "Error in calculation", diff = NA, lwr = NA, upr = NA, p_adj = NA)
  })
  
  # Test assumptions
  residuals <- residuals(model)
  
  # Normality test
  normality_test <- shapiro.test(residuals)
  normality_p <- normality_test$p.value
  
  # Homogeneity of variance test (Levene's test)
  levene_test <- tryCatch({
    car::leveneTest(data ~ factor1 * factor2 * factor3)
  }, error = function(e) {
    list(`Pr(>F)` = c(NA, NA))
  })
  levene_p <- levene_test$`Pr(>F)`[1]
  
  # Return results
  result <- list(
    data = data,
    factor1 = factor1,
    factor2 = factor2,
    factor3 = factor3,
    factor1_name = "Factor 1",
    factor2_name = "Factor 2",
    factor3_name = "Factor 3",
    total_n = total_n,
    factor1_levels = factor1_levels,
    factor2_levels = factor2_levels,
    factor3_levels = factor3_levels,
    total_cells = total_cells,
    conf_level = conf_level,
    model = model,
    anova_table = anova_table,
    r_squared = r_squared,
    adj_r_squared = adj_r_squared,
    descriptive_stats = descriptive_stats,
    cell_means = cell_means,
    effect_sizes = effect_sizes,
    factor1_posthoc = factor1_posthoc,
    factor2_posthoc = factor2_posthoc,
    factor3_posthoc = factor3_posthoc,
    normality_p = normality_p,
    levene_p = levene_p
  )
  
  return(result)
}

threeWayUploadData_func <- function(threeWayUserData) {
  ext <- tools::file_ext(threeWayUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(threeWayUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(threeWayUserData$datapath),
         xlsx = readxl::read_xlsx(threeWayUserData$datapath),
         txt = readr::read_tsv(threeWayUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

threeWayResults_func <- function(si_iv_is_valid, threeWayFormat, threeWayMultiColumns, threeWayUploadData_output, threeWayResponseVariable, threeWayFactor1, threeWayFactor2, threeWayFactor3, threeWayData, threeWayFactor1Data, threeWayFactor2Data, threeWayFactor3Data) {
  req(si_iv_is_valid)
  results <- list()
  
  if (threeWayFormat == "Multiple") {
    req(threeWayMultiColumns)
    data <- stack(threeWayUploadData_output[, threeWayMultiColumns])
    factor1 <- data$ind
    factor2 <- rep(1, nrow(data))  # Default factor2
    factor3 <- rep(1, nrow(data))  # Default factor3
    response <- data$values
  } else if (threeWayFormat == "Upload File") {
    req(threeWayResponseVariable, threeWayFactor1, threeWayFactor2, threeWayFactor3)
    data <- threeWayUploadData_output
    response <- data[[threeWayResponseVariable]]
    factor1 <- data[[threeWayFactor1]]
    factor2 <- data[[threeWayFactor2]]
    factor3 <- data[[threeWayFactor3]]
  } else {
    # Manual entry
    req(threeWayData, threeWayFactor1Data, threeWayFactor2Data, threeWayFactor3Data)
    response <- createNumLst(threeWayData)
    factor1 <- createNumLst(threeWayFactor1Data)
    factor2 <- createNumLst(threeWayFactor2Data)
    factor3 <- createNumLst(threeWayFactor3Data)
  }
  
  # Remove NA values
  complete_cases <- complete.cases(response, factor1, factor2, factor3)
  response <- response[complete_cases]
  factor1 <- factor1[complete_cases]
  factor2 <- factor2[complete_cases]
  factor3 <- factor3[complete_cases]
  
  if (length(response) < 8) {
    stop("At least 8 observations are required for three-way ANOVA")
  }
  
  results$response <- response
  results$factor1 <- factor1
  results$factor2 <- factor2
  results$factor3 <- factor3
  results$n <- length(response)
  return(results)
}

threeWayAnovaTable <- function(threeWayResults_output) {
  renderTable({
    req(threeWayResults_output())
    results <- threeWayResults_output()
    
    # Perform three-way ANOVA
    test_result <- perform_three_way_anova(
      results$response,
      results$factor1,
      results$factor2,
      results$factor3
    )
    
    # Format ANOVA table
    anova_table <- test_result$anova_table
    anova_table$`Sum of Squares` <- round(anova_table$`Sum of Squares`, 4)
    anova_table$`Mean Square` <- round(anova_table$`Mean Square`, 4)
    anova_table$`F-value` <- round(anova_table$`F-value`, 4)
    anova_table$`P-value` <- ifelse(anova_table$`P-value` < 0.0001, "< 0.0001", round(anova_table$`P-value`, 4))
    anova_table$`Partial_Eta_Squared` <- round(anova_table$`Partial_Eta_Squared`, 4)
    
    anova_table
  }, rownames = TRUE)
}

threeWayEffectSizes <- function(threeWayResults_output) {
  renderTable({
    req(threeWayResults_output())
    results <- threeWayResults_output()
    
    # Perform three-way ANOVA
    test_result <- perform_three_way_anova(
      results$response,
      results$factor1,
      results$factor2,
      results$factor3
    )
    
    # Format effect sizes table
    effect_sizes <- test_result$effect_sizes
    effect_sizes$Partial_Eta_Squared <- round(effect_sizes$Partial_Eta_Squared, 4)
    
    effect_sizes
  }, rownames = FALSE)
}

threeWayDescriptive <- function(threeWayResults_output) {
  renderTable({
    req(threeWayResults_output())
    results <- threeWayResults_output()
    
    # Calculate descriptive statistics by factor combinations
    data_df <- data.frame(
      Response = results$response,
      Factor1 = results$factor1,
      Factor2 = results$factor2,
      Factor3 = results$factor3
    )
    
    desc_stats <- data_df %>%
      group_by(Factor1, Factor2, Factor3) %>%
      summarise(
        n = n(),
        Mean = mean(Response),
        SD = sd(Response),
        SE = SD / sqrt(n),
        .groups = 'drop'
      ) %>%
      mutate(
        Mean = round(Mean, 4),
        SD = round(SD, 4),
        SE = round(SE, 4)
      )
    
    desc_stats
  }, rownames = FALSE)
}

threeWayCellMeans <- function(threeWayResults_output) {
  renderTable({
    req(threeWayResults_output())
    results <- threeWayResults_output()
    
    # Perform three-way ANOVA
    test_result <- perform_three_way_anova(
      results$response,
      results$factor1,
      results$factor2,
      results$factor3
    )
    
    # Format cell means table
    cell_means <- test_result$cell_means
    cell_means$Mean <- round(cell_means$Mean, 4)
    
    cell_means
  }, rownames = FALSE)
}

threeWayBoxplot <- function(threeWayResults_output) {
  renderPlot({
    req(threeWayResults_output())
    results <- threeWayResults_output()
    
    # Create data frame for plotting
    plot_data <- data.frame(
      Response = results$response,
      Factor1 = results$factor1,
      Factor2 = results$factor2,
      Factor3 = results$factor3
    )
    
    # Create interaction plot
    ggplot(plot_data, aes(x = interaction(Factor1, Factor2), y = Response, fill = Factor3)) +
      geom_boxplot(alpha = 0.7) +
      labs(title = "Three-Way ANOVA: Response by Factor Interactions",
           x = "Factor1 × Factor2", y = "Response", fill = "Factor3") +
      theme_minimal() +
      theme(axis.text.x = element_text(angle = 45, hjust = 1))
  })
}

threeWayMainEffects <- function(threeWayResults_output) {
  renderUI({
    req(threeWayResults_output())
    results <- threeWayResults_output()
    
    # Perform three-way ANOVA
    test_result <- perform_three_way_anova(
      results$response,
      results$factor1,
      results$factor2,
      results$factor3
    )
    
    tagList(
      h5("Factor 1 Post-hoc Tests:"),
      renderTable({
        test_result$factor1_posthoc
      }, rownames = FALSE),
      br(),
      h5("Factor 2 Post-hoc Tests:"),
      renderTable({
        test_result$factor2_posthoc
      }, rownames = FALSE),
      br(),
      h5("Factor 3 Post-hoc Tests:"),
      renderTable({
        test_result$factor3_posthoc
      }, rownames = FALSE)
    )
  })
}

threeWayInteractions <- function(threeWayResults_output) {
  renderUI({
    req(threeWayResults_output())
    results <- threeWayResults_output()
    
    # Perform three-way ANOVA
    test_result <- perform_three_way_anova(
      results$response,
      results$factor1,
      results$factor2,
      results$factor3
    )
    
    # Check for significant interactions
    anova_table <- test_result$anova_table
    interactions <- anova_table[grepl(":", rownames(anova_table)), ]
    
    if (nrow(interactions) > 0) {
      tagList(
        h5("Significant Interactions:"),
        renderTable({
          interactions
        }, rownames = TRUE),
        p("Note: Post-hoc tests for interactions require specific analysis of simple effects.")
      )
    } else {
      p("No significant interactions found.")
    }
  })
}

threeWayAssumptions <- function(threeWayResults_output) {
  renderUI({
    req(threeWayResults_output())
    results <- threeWayResults_output()
    
    # Perform three-way ANOVA
    test_result <- perform_three_way_anova(
      results$response,
      results$factor1,
      results$factor2,
      results$factor3
    )
    
    tagList(
      h5("Normality Test (Shapiro-Wilk):"),
      p(sprintf("W = %.4f, p = %s", 
                shapiro.test(residuals(test_result$model))$statistic,
                ifelse(test_result$normality_p < 0.0001, "< 0.0001", round(test_result$normality_p, 4)))),
      br(),
      h5("Homogeneity of Variance (Levene's Test):"),
      p(sprintf("F = %.4f, p = %s", 
                car::leveneTest(test_result$data ~ test_result$factor1 * test_result$factor2 * test_result$factor3)$`F value`[1],
                ifelse(test_result$levene_p < 0.0001, "< 0.0001", round(test_result$levene_p, 4)))),
      br(),
      h5("Interpretation:"),
      p("• Normality: p > 0.05 suggests residuals are normally distributed"),
      p("• Homogeneity: p > 0.05 suggests equal variances across groups"),
      p("• If assumptions are violated, consider non-parametric alternatives or data transformation.")
    )
  })
} 