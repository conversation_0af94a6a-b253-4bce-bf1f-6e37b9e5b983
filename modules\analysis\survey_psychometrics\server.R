SurveyPsychometricsServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    surveyData <- eventReactive(input$surveyUserData, {
      handle_file_upload(input$surveyUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(surveyData(), {
      data <- surveyData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'surveyVars', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    surveyValidationErrors <- reactive({
      errors <- c()
      data <- surveyData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$surveyVars) || length(input$surveyVars) < 2) {
        errors <- c(errors, "Select at least two variables (items).")
      }
      
      # Check if selected variables are numeric
      if (!is.null(input$surveyVars) && length(input$surveyVars) >= 2) {
        for (var in input$surveyVars) {
          if (!is.numeric(data[[var]])) {
            errors <- c(errors, sprintf("Variable '%s' must be numeric.", var))
          }
        }
      }
      
      errors
    })
    
    # Survey psychometrics analysis reactive
    surveyAnalysis <- eventReactive(input$goSurvey, {
      data <- surveyData()
      req(data, input$surveyVars)
      
      # Check if psych package is available
      if (!requireNamespace("psych", quietly = TRUE)) {
        return(NULL)
      }
      
      # Perform survey psychometrics analysis
      tryCatch({
        # Select only the chosen variables
        survey_subset <- data[, input$surveyVars, drop = FALSE]
        
        # Remove rows with missing values
        complete_data <- survey_subset[complete.cases(survey_subset), ]
        
        if (nrow(complete_data) < 10) {
          return(NULL)
        }
        
        # Calculate Cronbach's alpha
        alpha_result <- psych::alpha(complete_data)
        
        # Calculate McDonald's omega if possible
        omega_result <- tryCatch({
          psych::omega(complete_data, plot = FALSE)
        }, error = function(e) {
          NULL
        })
        
        # Item analysis
        item_stats <- alpha_result$item.stats
        
        # Inter-item correlation matrix
        inter_item_cor <- cor(complete_data, use = "complete.obs")
        
        # Scale statistics
        scale_mean <- mean(rowMeans(complete_data, na.rm = TRUE), na.rm = TRUE)
        scale_sd <- sd(rowMeans(complete_data, na.rm = TRUE), na.rm = TRUE)
        
        # Reliability if item deleted
        alpha_if_deleted <- sapply(1:ncol(complete_data), function(i) {
          temp_data <- complete_data[, -i, drop = FALSE]
          psych::alpha(temp_data)$total$raw_alpha
        })
        
        list(
          alpha = alpha_result$total$raw_alpha,
          omega = if (!is.null(omega_result)) omega_result$omega.tot else NA,
          mean_interitem_correlation = alpha_result$total$average_r,
          item_stats = item_stats,
          inter_item_correlation = inter_item_cor,
          scale_mean = scale_mean,
          scale_sd = scale_sd,
          alpha_if_deleted = alpha_if_deleted,
          n_items = ncol(complete_data),
          n_respondents = nrow(complete_data),
          data = complete_data
        )
      }, error = function(e) {
        NULL
      })
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goSurvey, {
      output$surveyError <- renderUI({
        tryCatch({ 
          res <- surveyAnalysis()
          if (is.null(res)) {
            errorScreenUI(title = "Survey Psychometrics Error", errors = "Analysis failed. Check your data and ensure at least 2 numeric variables are selected.")
          } else {
            NULL
          }
        }, error = function(e) {
          errorScreenUI(title = "Survey Psychometrics Error", errors = e$message)
        })
      })
      
      output$surveyResults <- renderUI({
        res <- surveyAnalysis()
        if (is.null(res)) return(NULL)
        
        tagList(
          tabsetPanel(
            id = ns("surveyTabset"),
            selected = "Analysis",
            tabPanel(
              id = ns("surveyAnalysis"),
              title = "Analysis",
              titlePanel("Survey Psychometrics Results"),
              br(),
              h4("Reliability Coefficients"),
              tableOutput(ns('surveyReliability')),
              br(),
              h4("Scale Statistics"),
              tableOutput(ns('surveyScaleStats')),
              br(),
              h4("Reliability Interpretation"),
              uiOutput(ns('surveyInterpretation'))
            ),
            tabPanel(
              id = ns("surveyItemAnalysis"),
              title = "Item Analysis",
              h4("Item Statistics"),
              tableOutput(ns('surveyItemStats')),
              br(),
              h4("Alpha if Item Deleted"),
              tableOutput(ns('surveyAlphaIfDeleted')),
              br(),
              h4("Inter-Item Correlations"),
              plotOutput(ns('surveyCorrelationPlot'), height = "500px")
            ),
            tabPanel(
              id = ns("surveyDiagnostics"),
              title = "Diagnostics",
              h4("Reliability Guidelines"),
              uiOutput(ns('surveyGuidelines')),
              br(),
              h4("Data Quality Checks"),
              tableOutput(ns('surveyDataQuality')),
              br(),
              h4("Assumptions"),
              uiOutput(ns('surveyAssumptions'))
            ),
            tabPanel(
              id = ns("surveyUploadedData"),
              title = "Uploaded Data",
              h4("Raw Data"),
              uiOutput(ns('surveyDataTable'))
            )
          )
        )
      })
    })
    
    # Reliability coefficients table
    output$surveyReliability <- renderTable({
      res <- surveyAnalysis()
      if (is.null(res)) return(NULL)
      
      data.frame(
        Coefficient = c("Cronbach's Alpha", "McDonald's Omega", "Mean Inter-item Correlation"),
        Value = c(res$alpha, res$omega, res$mean_interitem_correlation),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    # Scale statistics table
    output$surveyScaleStats <- renderTable({
      res <- surveyAnalysis()
      if (is.null(res)) return(NULL)
      
      data.frame(
        Statistic = c("Number of Items", "Number of Respondents", "Scale Mean", "Scale SD"),
        Value = c(res$n_items, res$n_respondents, res$scale_mean, res$scale_sd),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    # Reliability interpretation
    output$surveyInterpretation <- renderUI({
      res <- surveyAnalysis()
      if (is.null(res)) return(NULL)
      
      alpha <- res$alpha
      interpretation <- if (alpha < 0.5) {
        "Unacceptable reliability"
      } else if (alpha < 0.6) {
        "Poor reliability"
      } else if (alpha < 0.7) {
        "Questionable reliability"
      } else if (alpha < 0.8) {
        "Acceptable reliability"
      } else if (alpha < 0.9) {
        "Good reliability"
      } else {
        "Excellent reliability"
      }
      
      tagList(
        p(strong("Cronbach's Alpha: "), round(alpha, 4)),
        p(strong("Interpretation: "), interpretation),
        br(),
        p("Reliability guidelines:"),
        p("- Below 0.5: Unacceptable"),
        p("- 0.5 to 0.6: Poor"),
        p("- 0.6 to 0.7: Questionable"),
        p("- 0.7 to 0.8: Acceptable"),
        p("- 0.8 to 0.9: Good"),
        p("- 0.9 and above: Excellent")
      )
    })
    
    # Item statistics table
    output$surveyItemStats <- renderTable({
      res <- surveyAnalysis()
      if (is.null(res)) return(NULL)
      
      item_stats <- res$item_stats
      result <- data.frame(
        Item = rownames(item_stats),
        Mean = item_stats$mean,
        SD = item_stats$sd,
        Item_Total_Correlation = item_stats$r.drop,
        Alpha_if_Deleted = res$alpha_if_deleted,
        stringsAsFactors = FALSE
      )
      result
    }, digits = 4)
    
    # Alpha if item deleted table
    output$surveyAlphaIfDeleted <- renderTable({
      res <- surveyAnalysis()
      if (is.null(res)) return(NULL)
      
      data.frame(
        Item = names(res$alpha_if_deleted),
        Alpha_if_Deleted = res$alpha_if_deleted,
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    # Inter-item correlation plot
    output$surveyCorrelationPlot <- renderPlot({
      res <- surveyAnalysis()
      if (is.null(res)) return(NULL)
      
      cor_matrix <- res$inter_item_correlation
      
      # Create correlation heatmap
      corrplot::corrplot(cor_matrix, 
                        method = "color",
                        type = "upper",
                        order = "hclust",
                        tl.col = "black",
                        tl.srt = 45,
                        addCoef.col = "black",
                        number.cex = 0.7)
    })
    
    # Reliability guidelines
    output$surveyGuidelines <- renderUI({
      tagList(
        h5("Reliability Guidelines:"),
        p("• Cronbach's Alpha ≥ 0.9: Excellent"),
        p("• Cronbach's Alpha ≥ 0.8: Good"),
        p("• Cronbach's Alpha ≥ 0.7: Acceptable"),
        p("• Cronbach's Alpha ≥ 0.6: Questionable"),
        p("• Cronbach's Alpha ≥ 0.5: Poor"),
        p("• Cronbach's Alpha < 0.5: Unacceptable"),
        br(),
        p("Note: Higher reliability indicates more consistent measurement.")
      )
    })
    
    # Data quality checks
    output$surveyDataQuality <- renderTable({
      res <- surveyAnalysis()
      if (is.null(res)) return(NULL)
      
      data <- res$data
      
      # Calculate missing data percentage
      missing_pct <- round(sum(is.na(data)) / (nrow(data) * ncol(data)) * 100, 2)
      
      # Check for floor/ceiling effects
      min_vals <- apply(data, 2, min, na.rm = TRUE)
      max_vals <- apply(data, 2, max, na.rm = TRUE)
      
      data.frame(
        Check = c("Missing Data (%)", "Minimum Value", "Maximum Value", "Number of Items", "Number of Respondents"),
        Value = c(missing_pct, min(min_vals), max(max_vals), ncol(data), nrow(data)),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    # Assumptions
    output$surveyAssumptions <- renderUI({
      tagList(
        h5("Key Assumptions:"),
        p("1. Items measure the same underlying construct"),
        p("2. Items are linearly related"),
        p("3. Errors are uncorrelated"),
        p("4. Items have similar variances"),
        br(),
        p("Note: Cronbach's alpha assumes unidimensionality and tau-equivalence.")
      )
    })
    
    # Uploaded data table
    output$surveyDataTable <- renderUI({
      req(surveyData())
      DT::DTOutput(ns("surveyDataTableInner"))
    })
    
    output$surveyDataTableInner <- DT::renderDT({
      req(surveyData())
      DT::datatable(surveyData(),
        options = list(pageLength = 25,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(surveyData())))))
    })
  })
} 