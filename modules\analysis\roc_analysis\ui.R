ROCAnalysisUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("rocUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("rocResponse"), "Response Variable (binary)", choices = NULL),
        selectizeInput(ns("rocPredictor"), "Predictor Variable (numeric)", choices = NULL),
        br(),
        actionButton(ns("goROC"), label = "Run ROC Analysis", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("rocError")),
        plotOutput(ns("rocCurve")),
        verbatimTextOutput(ns("rocAUC")),
        tableOutput(ns("rocCutoffs"))
      )
    )
  )
} 