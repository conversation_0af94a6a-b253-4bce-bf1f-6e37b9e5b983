StratifiedKMServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    skmData <- eventReactive(input$skmUserData, {
      handle_file_upload(input$skmUserData)
    })
    observeEvent(skmData(), {
      data <- skmData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'skmTime', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'skmEvent', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'skmStrata', choices = names(data), server = TRUE)
      }
    })
    skmValidationErrors <- reactive({
      errors <- c()
      data <- skmData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$skmTime) || input$skmTime == "") {
        errors <- c(errors, "Select a time variable.")
      }
      if (is.null(input$skmEvent) || input$skmEvent == "") {
        errors <- c(errors, "Select an event variable.")
      }
      if (is.null(input$skmStrata) || input$skmStrata == "") {
        errors <- c(errors, "Select a strata variable.")
      }
      errors
    })
    skmResult <- eventReactive(input$goSKM, {
      data <- skmData()
      req(data, input$skmTime, input$skmEvent, input$skmStrata)
      stratified_km(data, input$skmTime, input$skmEvent, input$skmStrata)
    })
    output$skmError <- renderUI({
      tryCatch({ skmResult(); NULL }, error = function(e) errorScreenUI(title = "Stratified KM Error", errors = e$message))
    })
    output$skmSummary <- renderPrint({
      res <- skmResult()
      if (is.null(res)) return(NULL)
      print(res$summary)
    })
    output$skmPlot <- renderPlot({
      res <- skmResult()
      if (is.null(res)) return(NULL)
      if (!requireNamespace("survminer", quietly = TRUE)) return(NULL)
      survminer::ggsurvplot(res$fit, data = res$fit$data)
    })
  })
} 