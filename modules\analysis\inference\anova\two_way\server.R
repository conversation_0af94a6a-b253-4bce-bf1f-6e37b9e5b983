# Two-Way ANOVA Server
source("modules/calculations/two_way_anova.R")
TwoWayAnovaServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    twAnovaUploadData <- eventReactive(input$twAnovaUserData, {
      handle_file_upload(input$twAnovaUserData)
    })
    observeEvent(twAnovaUploadData(), {
      data <- twAnovaUploadData()
      updateSelectizeInput(session, 'twAnovaResponse', choices = names(data), server = TRUE)
      updateSelectizeInput(session, 'twAnovaFactorA', choices = names(data), server = TRUE)
      updateSelectizeInput(session, 'twAnovaFactorB', choices = names(data), server = TRUE)
      output$twoWayAnovaResults <- renderUI({
        if (!is.null(data) && is.data.frame(data)) {
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('twAnovaPreviewTable'))
          )
        } else NULL
      })
      output$twAnovaPreviewTable <- DT::renderDT({
        head(data, 20)
      })
    })
    twAnovaValidationErrors <- reactive({
      errors <- c()
      data <- twAnovaUploadData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$twAnovaResponse) || input$twAnovaResponse == "") {
        errors <- c(errors, "Please select a response variable.")
      }
      if (is.null(input$twAnovaFactorA) || input$twAnovaFactorA == "") {
        errors <- c(errors, "Please select Factor A.")
      }
      if (is.null(input$twAnovaFactorB) || input$twAnovaFactorB == "") {
        errors <- c(errors, "Please select Factor B.")
      }
      if (!is.null(input$twAnovaResponse) && !is.null(input$twAnovaFactorA) && input$twAnovaResponse == input$twAnovaFactorA) {
        errors <- c(errors, "Response and Factor A must be different.")
      }
      if (!is.null(input$twAnovaResponse) && !is.null(input$twAnovaFactorB) && input$twAnovaResponse == input$twAnovaFactorB) {
        errors <- c(errors, "Response and Factor B must be different.")
      }
      if (!is.null(input$twAnovaFactorA) && !is.null(input$twAnovaFactorB) && input$twAnovaFactorA == input$twAnovaFactorB) {
        errors <- c(errors, "Factor A and Factor B must be different.")
      }
      errors
    })
    observeEvent(input$goTwoWayAnova, {
      output$twoWayAnovaResults <- renderUI({
        errors <- twAnovaValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Two-Way ANOVA", errors = errors)
        } else {
          data <- twAnovaUploadData()
          res <- calc_two_way_anova(
            data,
            response = input$twAnovaResponse,
            factor_a = input$twAnovaFactorA,
            factor_b = input$twAnovaFactorB,
            interaction = isTRUE(input$twAnovaInteraction)
          )
          tagList(
            tabsetPanel(
              id = ns("twAnovaTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("twAnovaAnalysis"),
                title = "Analysis",
                titlePanel("Two-Way ANOVA Results"),
                br(),
                h4('ANOVA Table'),
                verbatimTextOutput(ns('twAnovaTable')),
                br(),
                h4('Model Summary'),
                verbatimTextOutput(ns('twAnovaModelSummary')),
                br(),
                h4("Effect Size"),
                uiOutput(ns('twAnovaEffectSize')),
                br(),
                h4("Assumptions Check"),
                uiOutput(ns('twAnovaAssumptions'))
              ),
              tabPanel(
                id = ns("twAnovaDataSummary"),
                title = "Data Summary",
                h4("Descriptive Statistics by Factor"),
                tableOutput(ns('twAnovaDescriptive')),
                br(),
                h4("Interaction Summary"),
                tableOutput(ns('twAnovaInteraction'))
              ),
              tabPanel(
                id = ns("twAnovaUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('twAnovaDataTable'))
              )
            )
          )
        }
      })
    })
    
    # Additional outputs for the enhanced tabs
    output$twAnovaEffectSize <- renderUI({
      tagList(
        p("Effect size interpretation:"),
        p("- 0.0 to 0.01: Negligible"),
        p("- 0.01 to 0.06: Small"),
        p("- 0.06 to 0.14: Medium"),
        p("- 0.14 and above: Large"),
        br(),
        p("Note: Effect sizes are based on partial eta-squared values.")
      )
    })
    
    output$twAnovaAssumptions <- renderUI({
      tagList(
        h5("Key Assumptions:"),
        p("1. Independent observations"),
        p("2. Normal distribution of residuals"),
        p("3. Homogeneity of variances"),
        p("4. Random sampling"),
        p("5. Additivity of effects (no interaction)"),
        br(),
        p("Note: Two-way ANOVA is robust to moderate violations of normality and homogeneity.")
      )
    })
    
    output$twAnovaDescriptive <- renderTable({
      data <- twAnovaUploadData()
      if (is.null(data)) return(NULL)
      
      response_var <- data[[input$twAnovaResponse]]
      factor_a <- data[[input$twAnovaFactorA]]
      factor_b <- data[[input$twAnovaFactorB]]
      
      # Descriptive statistics by Factor A
      desc_a <- data.frame(
        Factor = paste("Factor A:", levels(as.factor(factor_a))),
        N = tapply(response_var, factor_a, length),
        Mean = tapply(response_var, factor_a, mean),
        SD = tapply(response_var, factor_a, sd),
        Min = tapply(response_var, factor_a, min),
        Max = tapply(response_var, factor_a, max)
      )
      
      # Descriptive statistics by Factor B
      desc_b <- data.frame(
        Factor = paste("Factor B:", levels(as.factor(factor_b))),
        N = tapply(response_var, factor_b, length),
        Mean = tapply(response_var, factor_b, mean),
        SD = tapply(response_var, factor_b, sd),
        Min = tapply(response_var, factor_b, min),
        Max = tapply(response_var, factor_b, max)
      )
      
      rbind(desc_a, desc_b)
    }, digits = 4)
    
    output$twAnovaInteraction <- renderTable({
      data <- twAnovaUploadData()
      if (is.null(data)) return(NULL)
      
      response_var <- data[[input$twAnovaResponse]]
      factor_a <- data[[input$twAnovaFactorA]]
      factor_b <- data[[input$twAnovaFactorB]]
      
      # Interaction means
      interaction_means <- tapply(response_var, list(factor_a, factor_b), mean)
      interaction_df <- data.frame(
        Factor_A = rep(rownames(interaction_means), each = ncol(interaction_means)),
        Factor_B = rep(colnames(interaction_means), times = nrow(interaction_means)),
        Mean = as.vector(interaction_means),
        N = as.vector(tapply(response_var, list(factor_a, factor_b), length))
      )
      interaction_df
    }, digits = 4)
    
    output$twAnovaDataTable <- DT::renderDT({
      req(twAnovaUploadData())
      DT::datatable(twAnovaUploadData(),
        options = list(pageLength = 25,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(twAnovaUploadData())))))
    })
  })
} 