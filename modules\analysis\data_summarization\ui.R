DataSummarizationUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("dsUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        br(),
        actionButton(ns("goDS"), label = "Summarize Data", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("dsError")),
        tableOutput(ns("dsSummary")),
        plotOutput(ns("dsPlot"))
      )
    )
  )
} 