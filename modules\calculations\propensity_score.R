# Propensity Score Analysis calculation and output helpers

propensity_score_uploadData_func <- function(psUserData, treatment, covariates) {
  tryCatch(
    {
      if (is.null(psUserData) || is.null(treatment) || is.null(covariates)) {
        return(NULL)
      }
      
      # Read data based on file type
      if (grepl("\\.csv$", psUserData$name, ignore.case = TRUE)) {
        df <- read.csv(psUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.tsv$", psUserData$name, ignore.case = TRUE)) {
        df <- read.tsv(psUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.xlsx$", psUserData$name, ignore.case = TRUE)) {
        df <- readxl::read_excel(psUserData$datapath)
      } else {
        stop("Unsupported file type. Please upload a CSV, TSV, or XLSX file.")
      }
      
      # Check for required columns
      required_cols <- c(treatment, covariates)
      if (!all(required_cols %in% names(df))) {
        missing_cols <- required_cols[!required_cols %in% names(df)]
        stop(paste("The following columns were not found in the uploaded file:", paste(missing_cols, collapse = ", ")))
      }
      
      return(df)
    },
    error = function(e) {
      return(list(error = paste("Error reading data:", e$message)))
    }
  )
}

propensity_score_results_func <- function(data, treatment, covariates, method = "nearest") {
  tryCatch({
    if (is.null(data) || nrow(data) == 0) {
      stop("No data provided for analysis.")
    }
    
    if (!requireNamespace("MatchIt", quietly = TRUE)) {
      stop("Package 'MatchIt' is required for propensity score analysis.")
    }
    
    # Create the formula for the propensity score model
    formula <- as.formula(paste(treatment, "~", paste(covariates, collapse = " + ")))
    
    # Perform propensity score matching
    match_obj <- MatchIt::matchit(formula, data = data, method = method)
    
    list(
      match_obj = match_obj,
      summary = summary(match_obj),
      matched_data = MatchIt::match.data(match_obj),
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during propensity score analysis:", e$message))
  })
}

propensity_score_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(
      h4("Error"),
      p(results$error)
    ))
  }
  
  tagList(
    h4("Propensity Score Analysis")
  )
}

propensity_score_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  
  renderPrint({
    results$summary
  })
}

propensity_score_plot <- function(results) {
  if (!is.null(results$error) || is.null(results$match_obj)) {
    return(NULL)
  }
  
  # Generate diagnostic plots
  plot(results$match_obj, type = "jitter", interactive = FALSE)
  plot(results$match_obj, type = "hist")
}