# Post-hoc Tests Server
source("modules/calculations/post_hoc.R")
PostHocTestServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    postHocUploadData <- eventReactive(input$postHocUserData, {
      handle_file_upload(input$postHocUserData)
    })
    observeEvent(postHocUploadData(), {
      data <- postHocUploadData()
      updateSelectizeInput(session, 'postHocResponse', choices = names(data), server = TRUE)
      updateSelectizeInput(session, 'postHocGroup', choices = names(data), server = TRUE)
      output$postHocResults <- renderUI({
        if (!is.null(data) && is.data.frame(data)) {
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('postHocPreviewTable'))
          )
        } else NULL
      })
      output$postHocPreviewTable <- DT::renderDT({
        head(data, 20)
      })
    })
    postHocValidationErrors <- reactive({
      errors <- c()
      data <- postHocUploadData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$postHocResponse) || input$postHocResponse == "") {
        errors <- c(errors, "Please select a response variable.")
      }
      if (is.null(input$postHocGroup) || input$postHocGroup == "") {
        errors <- c(errors, "Please select a group/factor column.")
      }
      errors
    })
    observeEvent(input$goPostHoc, {
      output$postHocResults <- renderUI({
        errors <- postHocValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Post-hoc Test", errors = errors)
        } else {
          data <- postHocUploadData()
          res <- calc_post_hoc(
            data,
            response = input$postHocResponse,
            group = input$postHocGroup,
            test_type = input$postHocTestType
          )
          
          tagList(
            tabsetPanel(
              id = ns("postHocTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("postHocAnalysis"),
                title = "Analysis",
                titlePanel("Post-hoc Test Results"),
                br(),
                h4('Test Results'),
                verbatimTextOutput(ns('postHocResultText')),
                br(),
                h4("Significant Differences"),
                uiOutput(ns('postHocSignificant')),
                br(),
                h4("Effect Size"),
                uiOutput(ns('postHocEffectSize')),
                br(),
                h4("Assumptions Check"),
                uiOutput(ns('postHocAssumptions'))
              ),
              tabPanel(
                id = ns("postHocDataSummary"),
                title = "Data Summary",
                h4("Descriptive Statistics by Group"),
                tableOutput(ns('postHocDescriptive')),
                br(),
                h4("Pairwise Comparisons"),
                tableOutput(ns('postHocPairwise'))
              ),
              tabPanel(
                id = ns("postHocUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('postHocDataTable'))
              )
            )
          )
        }
      })
      output$postHocResultText <- renderPrint({
        data <- postHocUploadData()
        res <- calc_post_hoc(
          data,
          response = input$postHocResponse,
          group = input$postHocGroup,
          test_type = input$postHocTestType
        )
        print(res)
      })
    })
    
    # Additional outputs for the enhanced tabs
    output$postHocSignificant <- renderUI({
      tagList(
        p("Significant differences will be highlighted here based on the test results.")
      )
    })
    
    output$postHocEffectSize <- renderUI({
      tagList(
        p("Effect size interpretation:"),
        p("- 0.0 to 0.1: Negligible"),
        p("- 0.1 to 0.3: Small"),
        p("- 0.3 to 0.5: Medium"),
        p("- 0.5 to 1.0: Large")
      )
    })
    
    output$postHocAssumptions <- renderUI({
      tagList(
        h5("Key Assumptions:"),
        p("1. Independent observations"),
        p("2. Normal distribution within groups"),
        p("3. Homogeneity of variances"),
        p("4. Random sampling"),
        br(),
        p("Note: Post-hoc tests should only be used after a significant ANOVA result.")
      )
    })
    
    output$postHocDescriptive <- renderTable({
      data <- postHocUploadData()
      if (is.null(data)) return(NULL)
      
      response_var <- data[[input$postHocResponse]]
      group_var <- data[[input$postHocGroup]]
      
      desc_stats <- data.frame(
        Group = levels(as.factor(group_var)),
        N = tapply(response_var, group_var, length),
        Mean = tapply(response_var, group_var, mean),
        SD = tapply(response_var, group_var, sd),
        SE = tapply(response_var, group_var, function(x) sd(x)/sqrt(length(x))),
        Min = tapply(response_var, group_var, min),
        Max = tapply(response_var, group_var, max)
      )
      desc_stats
    }, digits = 4)
    
    output$postHocPairwise <- renderTable({
      # Placeholder for pairwise comparisons
      data.frame(
        Comparison = c("Group1 vs Group2", "Group1 vs Group3", "Group2 vs Group3"),
        Difference = c("N/A", "N/A", "N/A"),
        P_value = c("N/A", "N/A", "N/A"),
        Significant = c("N/A", "N/A", "N/A"),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    output$postHocDataTable <- DT::renderDT({
      req(postHocUploadData())
      DT::datatable(postHocUploadData(),
        options = list(pageLength = 25,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(postHocUploadData())))))
    })
  })
} 