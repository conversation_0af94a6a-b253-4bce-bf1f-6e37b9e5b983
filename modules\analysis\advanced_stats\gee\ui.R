# Placeholder for GEE UI
geeSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("geeUserData"), "Upload your Data (.csv or .xls or .xlsx or .txt)", accept = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")),
    uiOutput(ns("geeColSelectors")),
    actionButton(ns("goGEE"), label = "Calculate", class = "act-btn")
  )
}

geeMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('geeHT')),
    plotOutput(ns('geePlot'), width = "50%", height = "400px"),
    uiOutput(ns('geeConclusionOutput'))
  )
}

geeUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(geeSidebarUI(id)),
    mainPanel(geeMainUI(id))
  )
} 