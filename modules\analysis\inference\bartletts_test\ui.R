bartlettsTestSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(
      inputId = ns("btUserData"),
      label   = strong("Upload your Data (.csv or .xls or .xlsx or .txt)"),
      accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
    ),
    div(
      id = ns("btUploadInputs"),
      radioButtons(
        inputId = ns("btFormat"),
        label   = strong("Data Format"),
        choiceNames = c("Values in multiple columns", "Responses and factors stacked in two columns"),
        choiceValues = c("Multiple", "Stacked")
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Multiple'", ns("btFormat")),
        selectizeInput(
          inputId = ns("btMultiColumns"),
          label = strong("Choose columns to conduct analysis"),
          choices = c(""),
          multiple = TRUE,
          selected = NULL,
          options = list(hideSelected = FALSE, placeholder = 'Select two or more columns', onInitialize = I('function() { this.setValue(\"\"); }'))
        )
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Stacked'", ns("btFormat")),
        selectizeInput(
          inputId = ns("btResponse"),
          label = strong("Response Variable"),
          choices = c(""),
          selected = NULL,
          options = list(placeholder = 'Select a variable', onInitialize = I('function() { this.setValue(\"\"); }'))
        ),
        selectizeInput(
          inputId = ns("btFactors"),
          label = strong("Factors"),
          choices = c(""),
          selected = NULL,
          options = list(placeholder = 'Select a factor', onInitialize = I('function() { this.setValue(\"\"); }'))
        )
      )
    ),
    radioButtons(
      inputId = ns("btSigLvl"),
      label = strong("Significance Level (alpha)"),
      choices  = c("10%", "5%", "1%"),
      selected = "5%",
      inline   = TRUE
    ),
    br(),
    actionButton(ns("goInference"), label = "Calculate", class = "act-btn")
  )
}

bartlettsTestMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('bartlettsTestResults'))
  )
}

bartlettsTestUI <- function(id) {
  ns <- NS(id)
  tagList(
    bartlettsTestSidebarUI(id),
    bartlettsTestMainUI(id)
  )
} 