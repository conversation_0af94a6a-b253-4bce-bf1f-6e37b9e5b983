experimentalDesignServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactives
    edResults <- reactive({
      if (is.null(input$edTool)) return(NULL)
      
      edResults_func(
        input$edTool,
        input$edTestType,
        input$edEffectSize,
        input$edPower,
        input$edAlpha,
        input$edSampleSize,
        input$edEffectSizePower,
        input$edAlphaPower,
        input$edFactors,
        input$edLevels,
        input$edRuns,
        input$edTotalSubjects,
        input$edGroups,
        input$edStratified,
        input$edBlocks,
        input$edTreatments,
        input$edBlockType
      )
    })
    
    # Validation errors reactive
    edValidationErrors <- reactive({
      errors <- c()
      
      if (is.null(input$edTool)) {
        errors <- c(errors, "Please select an experimental design tool.")
        return(errors)
      }
      
      if (input$edTool == "Sample Size Calculator") {
        if (is.null(input$edTestType)) {
          errors <- c(errors, "Please select a test type.")
        }
        if (input$edEffectSize <= 0) {
          errors <- c(errors, "Effect size must be positive.")
        }
        if (input$edPower <= 0 || input$edPower >= 1) {
          errors <- c(errors, "Power must be between 0 and 1.")
        }
        if (input$edAlpha <= 0 || input$edAlpha >= 1) {
          errors <- c(errors, "Alpha must be between 0 and 1.")
        }
      }
      
      if (input$edTool == "Power Analysis") {
        if (input$edSampleSize <= 0) {
          errors <- c(errors, "Sample size must be positive.")
        }
        if (input$edEffectSizePower <= 0) {
          errors <- c(errors, "Effect size must be positive.")
        }
        if (input$edAlphaPower <= 0 || input$edAlphaPower >= 1) {
          errors <- c(errors, "Alpha must be between 0 and 1.")
        }
      }
      
      if (input$edTool == "Design Efficiency") {
        if (input$edFactors <= 0) {
          errors <- c(errors, "Number of factors must be positive.")
        }
        if (input$edLevels <= 0) {
          errors <- c(errors, "Levels per factor must be positive.")
        }
        if (input$edRuns <= 0) {
          errors <- c(errors, "Number of runs must be positive.")
        }
      }
      
      if (input$edTool == "Randomization") {
        if (input$edTotalSubjects <= 0) {
          errors <- c(errors, "Total number of subjects must be positive.")
        }
        if (input$edGroups <= 0) {
          errors <- c(errors, "Number of groups must be positive.")
        }
        if (input$edTotalSubjects < input$edGroups) {
          errors <- c(errors, "Total subjects must be greater than or equal to number of groups.")
        }
      }
      
      if (input$edTool == "Blocking Design") {
        if (input$edBlocks <= 0) {
          errors <- c(errors, "Number of blocks must be positive.")
        }
        if (input$edTreatments <= 0) {
          errors <- c(errors, "Number of treatments must be positive.")
        }
      }
      
      errors
    })
    
    # Outputs
    output$edResults <- renderUI({
      results <- edResults()
      if (is.null(results)) return(NULL)
      edResultsOutput(results)
    })
    
    output$edPlots <- renderUI({
      results <- edResults()
      if (is.null(results)) return(NULL)
      edPlotsOutput(results)
    })
    
    # Show main results or error screen when Calculate is pressed
    observeEvent(input$goExperimentalDesign, {
      output$experimentalDesignResults <- renderUI({
        errors <- edValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Experimental Design", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("edTabset"),
              selected = "Results",
              tabPanel(
                id = ns("edResults"),
                title = "Results",
                titlePanel("Experimental Design Results"),
                br(),
                uiOutput(ns('edResults'))
              ),
              tabPanel(
                id = ns("edPlots"),
                title = "Plots",
                titlePanel("Design Plots"),
                br(),
                uiOutput(ns('edPlots'))
              )
            )
          )
        }
      })
    })
  })
} 