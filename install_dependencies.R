# CougarStats Dependency Installation Script
# This script installs all required packages for the CougarStats application

# Function to install packages with error handling
install_package_safely <- function(package_name, repo = "https://cran.rstudio.com/") {
  if (!requireNamespace(package_name, quietly = TRUE)) {
    tryCatch({
      install.packages(package_name, repos = repo, dependencies = TRUE)
      cat("✓ Successfully installed:", package_name, "\n")
    }, error = function(e) {
      cat("✗ Failed to install:", package_name, "-", e$message, "\n")
    })
  } else {
    cat("✓ Already installed:", package_name, "\n")
  }
}

# Function to install packages from GitHub
install_github_safely <- function(repo_name) {
  if (!requireNamespace("remotes", quietly = TRUE)) {
    install.packages("remotes", repos = "https://cran.rstudio.com/")
  }
  
  tryCatch({
    remotes::install_github(repo_name)
    cat("✓ Successfully installed from GitHub:", repo_name, "\n")
  }, error = function(e) {
    cat("✗ Failed to install from GitHub:", repo_name, "-", e$message, "\n")
  })
}

# Function to install packages from Bioconductor
install_bioc_safely <- function(package_name) {
  if (!requireNamespace("BiocManager", quietly = TRUE)) {
    install.packages("BiocManager", repos = "https://cran.rstudio.com/")
  }
  
  tryCatch({
    BiocManager::install(package_name, update = FALSE, ask = FALSE)
    cat("✓ Successfully installed from Bioconductor:", package_name, "\n")
  }, error = function(e) {
    cat("✗ Failed to install from Bioconductor:", package_name, "-", e$message, "\n")
  })
}

# Core Shiny and UI packages
cat("Installing core Shiny and UI packages...\n")
core_packages <- c(
  "aplpack", "bslib", "car", "colourpicker", "DescTools", "dplyr", "DT", 
  "generics", "ggplot2", "plotly", "ggpubr", "ggsci", "e1071", "markdown", 
  "nortest", "readr", "readxl", "rstatix", "shiny", "shinyDarkmode", 
  "shinythemes", "shinyjs", "shinyMatrix", "shinyvalidate", "shinyWidgets", 
  "tinytex", "writexl", "xtable", "MASS", "latex2exp", "thematic", "datamods", 
  "magrittr", "olsrr", "ggResidpanel", "leaflet", "htmlwidgets", "sf", 
  "spdep", "lme4", "nlme", "randomForest", "gbm", "xgboost", "caret", 
  "lavaan", "forecast", "igraph", "visNetwork", "GGally", "fmsb", "treemap", 
  "networkD3", "scatterplot3d", "shinydashboard"
)

for (pkg in core_packages) {
  install_package_safely(pkg)
}

# Additional packages for new modules (excluding cmprsk which will be handled separately)
cat("\nInstalling additional packages for new modules...\n")
additional_packages <- c(
  "survival", "psych", "metafor", "mice", "VIM", "skimr", 
  "poLCA", "mirt", "mgcv", "glmnet", "robustbase", "pscl", "pwr", "FSA", 
  "polycor", "changepoint", "boot", "brms", "loo", "BayesFactor", 
  "bayestestR", "rstanarm", "mediation", "MatchIt", "heplots", "ipred", 
  "cluster", "fpc", "dbscan", "fields", "spgwr", "seasonal", "Rtsne", 
  "umap", "pryr", "performance", "sjPlot"
)

for (pkg in additional_packages) {
  install_package_safely(pkg)
}

# Text mining and NLP packages
cat("\nInstalling text mining and NLP packages...\n")
nlp_packages <- c("tm", "quanteda", "wordcloud", "topicmodels", "lda")

for (pkg in nlp_packages) {
  install_package_safely(pkg)
}

# Spatial analysis packages
cat("\nInstalling spatial analysis packages...\n")
spatial_packages <- c("sp", "gstat", "spatstat")

for (pkg in spatial_packages) {
  install_package_safely(pkg)
}

# Install cmprsk package (not on CRAN)
cat("\nInstalling cmprsk package (not available on CRAN)...\n")
if (!requireNamespace("cmprsk", quietly = TRUE)) {
  tryCatch({
    # Try to install from CRAN first (in case it's available)
    install.packages("cmprsk", repos = "https://cran.rstudio.com/")
    cat("✓ Successfully installed cmprsk from CRAN\n")
  }, error = function(e) {
    cat("⚠️  cmprsk not available on CRAN, trying alternative sources...\n")
    
    # Try to install from Bioconductor
    tryCatch({
      if (!requireNamespace("BiocManager", quietly = TRUE)) {
        install.packages("BiocManager", repos = "https://cran.rstudio.com/")
      }
      BiocManager::install("cmprsk", update = FALSE, ask = FALSE)
      cat("✓ Successfully installed cmprsk from Bioconductor\n")
    }, error = function(e2) {
      cat("✗ Failed to install cmprsk from Bioconductor\n")
      
      # Try to install from GitHub
      tryCatch({
        if (!requireNamespace("remotes", quietly = TRUE)) {
          install.packages("remotes", repos = "https://cran.rstudio.com/")
        }
        remotes::install_github("cran/cmprsk")
        cat("✓ Successfully installed cmprsk from GitHub\n")
      }, error = function(e3) {
        cat("✗ Failed to install cmprsk from all sources\n")
        cat("⚠️  cmprsk package is required for competing risks analysis\n")
        cat("   You may need to install it manually or skip competing risks features\n")
      })
    })
  })
} else {
  cat("✓ cmprsk already installed\n")
}

# Install packages from GitHub
cat("\nInstalling packages from GitHub...\n")
github_packages <- c(
  "deepanshu88/shinyDarkmode",
  "rsquaredacademy/olsrr"
)

for (repo in github_packages) {
  install_github_safely(repo)
}

# Check for any missing packages
cat("\nChecking for missing packages...\n")
all_packages <- c(core_packages, additional_packages, nlp_packages, spatial_packages, "cmprsk")
missing_packages <- all_packages[!sapply(all_packages, requireNamespace, quietly = TRUE)]

if (length(missing_packages) > 0) {
  cat("\n⚠️  The following packages could not be installed:\n")
  for (pkg in missing_packages) {
    cat("  -", pkg, "\n")
  }
  cat("\nYou may need to install these manually or check your internet connection.\n")
  
  # Special handling for cmprsk
  if ("cmprsk" %in% missing_packages) {
    cat("\n📋 For cmprsk package, try these manual installation methods:\n")
    cat("1. From Bioconductor: BiocManager::install('cmprsk')\n")
    cat("2. From GitHub: remotes::install_github('cran/cmprsk')\n")
    cat("3. Download from: https://cran.r-project.org/src/contrib/Archive/cmprsk/\n")
    cat("4. Skip competing risks features if installation fails\n")
  }
} else {
  cat("\n✅ All packages successfully installed!\n")
}

# Create a summary report
cat("\n" , rep("=", 50), "\n")
cat("COUGARSTATS DEPENDENCY INSTALLATION SUMMARY\n")
cat(rep("=", 50), "\n")
cat("Total packages checked:", length(all_packages), "\n")
cat("Successfully installed:", length(all_packages) - length(missing_packages), "\n")
if (length(missing_packages) > 0) {
  cat("Failed to install:", length(missing_packages), "\n")
}
cat(rep("=", 50), "\n")

# Instructions for users
cat("\n📋 NEXT STEPS:\n")
cat("1. If all packages installed successfully, you can run the CougarStats application\n")
cat("2. If some packages failed to install, try running this script again\n")
cat("3. For persistent issues, you may need to update R or install system dependencies\n")
cat("4. Some packages may require additional system libraries (especially on Linux)\n")
cat("5. If cmprsk installation fails, competing risks analysis will not be available\n")

# Optional: Test if the application can start
cat("\n🧪 Testing if CougarStats can start...\n")
tryCatch({
  # Test loading key packages
  library(shiny)
  library(ggplot2)
  library(dplyr)
  cat("✅ Core packages loaded successfully\n")
  
  # Test loading survival package
  library(survival)
  cat("✅ Survival analysis package loaded successfully\n")
  
  # Test loading cmprsk if available
  if (requireNamespace("cmprsk", quietly = TRUE)) {
    library(cmprsk)
    cat("✅ Competing risks package loaded successfully\n")
  } else {
    cat("⚠️  cmprsk package not available - competing risks analysis disabled\n")
  }
  
  # Test if global.R can be sourced
  if (file.exists("global.R")) {
    source("global.R", local = TRUE)
    cat("✅ global.R loaded successfully\n")
  } else {
    cat("⚠️  global.R not found in current directory\n")
  }
  
  cat("🎉 CougarStats should be ready to run!\n")
  
}, error = function(e) {
  cat("❌ Error during testing:", e$message, "\n")
  cat("Please check the error messages above and try again.\n")
}) 