# ANOVA Inference UI
# Extracted and modularized from statInfr.R

AnovaInferenceSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(
      ns("anovaUserData"),
      strong("Upload your Data (.csv or .xls or .xlsx or .txt)"),
      accept = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
    ),
    radioButtons(
      ns("anovaFormat"),
      strong("Data Format"),
      choiceNames = c("Values in multiple columns", "Responses and factors stacked in two columns"),
      choiceValues = c("Multiple", "Stacked")
    ),
    conditionalPanel(
      ns = ns,
      condition = "input.anovaFormat == 'Multiple'",
      selectizeInput(
        ns("anovaMultiColumns"),
        strong("Choose columns to conduct analysis"),
        choices = c(""),
        multiple = TRUE,
        options = list(hideSelected = FALSE, placeholder = 'Select two or more columns', onInitialize = I('function() { this.setValue(\"\"); }'))
      )
    ),
    conditionalPanel(
      ns = ns,
      condition = "input.anovaFormat == 'Stacked'",
      selectizeInput(
        ns("anovaResponse"),
        strong("Response Variable"),
        choices = c(""),
        options = list(placeholder = 'Select a variable', onInitialize = I('function() { this.setValue(\"\"); }'))
      ),
      selectizeInput(
        ns("anovaFactors"),
        strong("Factors"),
        choices = c(""),
        options = list(placeholder = 'Select a factor', onInitialize = I('function() { this.setValue(\"\"); }'))
      )
    ),
    radioButtons(
      ns("anovaSigLvl"),
      strong("Significance Level (\u03B1)"),
      choices = c("10%", "5%", "1%"),
      selected = "5%",
      inline = TRUE
    ),
    checkboxGroupInput(
      ns("anovaOptions"),
      p(strong("Options")),
      choiceNames = c("Include post hoc tests"),
      choiceValues = c("posthoc")
    ),
    selectizeInput(
      ns("anovaGraphs"),
      strong("Graphs"),
      choices = c("Side-by-side Boxplot", "Histogram of Residuals", "QQ Plot of Residuals", "Plot Group Means"),
      multiple = TRUE,
      selected = c("Side-by-side Boxplot", "Plot Group Means"),
      options = list(hideSelected = FALSE, placeholder = 'Select graph(s) to display')
    ),
    br(),
    actionButton(ns("goInference"), label = "Calculate", class = "act-btn")
  )
}

AnovaInferenceMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('anovaResults'))
  )
} 