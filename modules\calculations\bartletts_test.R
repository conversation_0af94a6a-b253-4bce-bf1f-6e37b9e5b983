# <PERSON>'s Test calculation and output helpers

bartletts_test_uploadData_func <- function(btUserData) {
  ext <- tools::file_ext(btUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(btUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(btUserData$datapath),
         xlsx = readxl::read_xlsx(btUserData$datapath),
         txt = readr::read_tsv(btUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

bartletts_test_results_func <- function(data, format, multi_columns, response_var, factor_var) {
  tryCatch({
    if (format == "Multiple") {
      groups <- list()
      for (col in multi_columns) {
        groups[[col]] <- data[[col]][!is.na(data[[col]])]
      }
    } else {
      response <- data[[response_var]]
      factor <- data[[factor_var]]
      groups <- split(response, factor)
      groups <- lapply(groups, function(x) x[!is.na(x)])
    }
    
    if (length(groups) < 2) {
      stop("At least two groups are required for <PERSON>'s test")
    }
    
    groups <- groups[sapply(groups, length) >= 2]
    
    if (length(groups) < 2) {
      stop("At least two groups with at least 2 observations each are required")
    }
    
    test_result <- bartlett.test(groups)
    
    list(
      test = test_result,
      groups = groups,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Bartlett's test calculation:", e$message))
  })
}

bartletts_test_ht_html <- function(results, sigLvl) {
  test <- results$test
  p_value <- test$p.value
  
  conclusion <- if (p_value < sigLvl) {
    "Reject H0. The variances of the groups are not equal."
  } else {
    "Do not reject H0. The variances of the groups are equal."
  }
  
  withMathJax(tagList(
    h4("Bartlett's Test for Homogeneity of Variances"),
    p("$H_0$: The variances of the groups are equal."),
    p("$H_A$: The variances of at least two groups are not equal."),
    p(sprintf("Test Statistic (Bartlett's K-squared): %.4f", test$statistic)),
    p(sprintf("Degrees of Freedom: %d", test$parameter)),
    p(sprintf("P-value: %.4f", p_value)),
    p(strong("Conclusion: "), conclusion)
  ))
}

bartletts_test_summary_html <- function(results) {
  desc_stats <- do.call(rbind, lapply(names(results$groups), function(name) {
    data.frame(
      Group = name,
      N = length(results$groups[[name]]),
      Mean = mean(results$groups[[name]]),
      SD = sd(results$groups[[name]]),
      Variance = var(results$groups[[name]])
    )
  }))
  
  tagList(
    h4("Descriptive Statistics by Group"),
    renderTable(desc_stats, digits = 4)
  )
}

bartletts_test_plot <- function(results) {
  plot_data <- do.call(rbind, lapply(names(results$groups), function(name) {
    data.frame(
      Group = name,
      Values = results$groups[[name]]
    )
  }))
  
  ggplot(plot_data, aes(x = Group, y = Values, fill = Group)) +
    geom_boxplot(alpha = 0.7) +
    labs(title = "Boxplot of Response by Group",
         x = "Group",
         y = "Value") +
    theme_minimal() +
    theme(legend.position = "none")
}