# Ridge Regression UI
# Regularization

RidgeRegressionSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(
      inputId = ns("ridgeUserData"),
      label = "Upload Data File:",
      accept = c(".csv", ".txt", ".xlsx", ".xls"),
      buttonLabel = "Browse Files",
      placeholder = "No file selected"
    ),
    selectizeInput(
      inputId = ns("ridgeResponseVariable"),
      label = "Select Response Variable:",
      choices = NULL,
      options = list(placeholder = "Select response variable...")
    ),
    selectizeInput(
      inputId = ns("ridgePredictorVariables"),
      label = "Select Predictor Variables:",
      choices = NULL,
      multiple = TRUE,
      options = list(placeholder = "Select predictor variables...")
    ),
    numericInput(
      inputId = ns("ridgeLambda"),
      label = "Lambda (Regularization Parameter):",
      value = 1.0,
      min = 0.001,
      max = 100,
      step = 0.1
    ),
    numericInput(
      inputId = ns("ridgeConfLevel"),
      label = "Confidence Level:",
      value = 0.95,
      min = 0.5,
      max = 0.99,
      step = 0.01
    ),
    actionButton(
      inputId = ns("goRidge"),
      label = "Calculate Ridge Regression",
      class = "btn-primary",
      style = "width: 100%;"
    ),
    br(),
    br(),
    helpText(
      "Ridge regression adds L2 penalty to reduce overfitting.",
      "Lambda controls the strength of regularization.",
      "Higher lambda = more regularization = smaller coefficients.",
      "Useful for multicollinear predictors."
    )
  )
}

RidgeRegressionMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns("ridgeResults"))
  )
}

RidgeRegressionUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(RidgeRegressionSidebarUI(id)),
    mainPanel(RidgeRegressionMainUI(id))
  )
} 