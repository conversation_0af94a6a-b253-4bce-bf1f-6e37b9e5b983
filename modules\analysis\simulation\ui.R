SimulationUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        selectInput(ns("simType"), "Simulation Type", choices = c("Normal Distribution", "Binomial Distribution", "Custom")),
        numericInput(ns("simN"), "Sample Size", value = 100, min = 10, max = 10000),
        br(),
        actionButton(ns("goSim"), label = "Run Simulation", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("simError")),
        plotOutput(ns("simPlot")),
        tableOutput(ns("simResults"))
      )
    )
  )
} 