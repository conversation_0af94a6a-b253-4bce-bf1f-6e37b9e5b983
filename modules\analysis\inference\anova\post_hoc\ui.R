# Post-hoc Tests UI
PostHocTestSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("postHocUserData"), "Upload your Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
    selectizeInput(ns("postHocResponse"), "Response Variable", choices = NULL),
    selectizeInput(ns("postHocGroup"), "Group/Factor Column", choices = NULL),
    radioButtons(ns("postHocTestType"), "Test Type", choices = c("Tukey HSD", "Dunn's Test"), selected = "Tukey HSD", inline = TRUE),
    radioButtons(ns("postHocSigLvl"), "Significance Level (alpha)", choices = c("10%", "5%", "1%"), selected = "5%", inline = TRUE),
    br(),
    actionButton(ns("goPostHoc"), label = "Calculate", class = "act-btn")
  )
}

PostHocTestMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('postHocResults'))
  )
}

PostHocTestUI <- function(id) {
  ns <- NS(id)
  tagList(
    PostHocTestSidebarUI(id),
    PostHocTestMainUI(id)
  )
} 