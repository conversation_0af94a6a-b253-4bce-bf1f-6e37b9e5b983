# Non-parametric ANCOVA UI
# Rank-based analysis of covariance

NonparametricAncovaSidebarUI <- function(id) {
  ns <- NS(id)
  
  tagList(
    h4("Non-parametric ANCOVA"),
    p("Non-parametric ANCOVA uses rank transformations to analyze covariance without distributional assumptions."),
    
    # Data Input
    radioButtons(
      inputId = ns("npAncovaDataMethod"),
      label = "Data Input Method:",
      choices = c("Upload File" = "Upload File", "Manual Entry" = "Manual Entry"),
      selected = "Upload File"
    ),
    
    # File Upload
    conditionalPanel(
      condition = "input.npAncovaDataMethod == 'Upload File'",
      ns = ns,
      fileInput(
        inputId = ns("npAncovaUserData"),
        label = "Upload Data File:",
        accept = c(".csv", ".txt", ".xlsx", ".xls"),
        buttonLabel = "Browse Files",
        placeholder = "No file selected"
      ),
      selectizeInput(
        inputId = ns("npAncovaDependentVariable"),
        label = "Select Dependent Variable:",
        choices = NULL,
        options = list(placeholder = "Select dependent variable...")
      ),
      selectizeInput(
        inputId = ns("npAncovaGroupVariable"),
        label = "Select Group Variable:",
        choices = NULL,
        options = list(placeholder = "Select group variable...")
      ),
      selectizeInput(
        inputId = ns("npAncovaCovariate"),
        label = "Select Covariate:",
        choices = NULL,
        options = list(placeholder = "Select covariate...")
      )
    ),
    
    # Manual Entry
    conditionalPanel(
      condition = "input.npAncovaDataMethod == 'Manual Entry'",
      ns = ns,
      textAreaInput(
        inputId = ns("npAncovaManualData"),
        label = "Enter Data (comma-separated):",
        placeholder = "Group,Dependent,Covariate\nA,10,5\nA,12,6\nB,8,4\nB,11,7",
        rows = 8
      )
    ),
    
    # Analysis Options
    h5("Analysis Options"),
    
    checkboxInput(
      inputId = ns("npAncovaEffectSize"),
      label = "Calculate Effect Sizes",
      value = TRUE
    ),
    
    checkboxInput(
      inputId = ns("npAncovaPostHoc"),
      label = "Perform Post-hoc Tests",
      value = TRUE
    ),
    
    selectInput(
      inputId = ns("npAncovaPostHocMethod"),
      label = "Post-hoc Method:",
      choices = c("Wilcoxon Rank Sum" = "wilcoxon", "Kruskal-Wallis" = "kruskal"),
      selected = "wilcoxon"
    ),
    
    numericInput(
      inputId = ns("npAncovaConfLevel"),
      label = "Confidence Level:",
      value = 0.95,
      min = 0.5,
      max = 0.99,
      step = 0.01
    ),
    
    actionButton(
      inputId = ns("goNpAncova"),
      label = "Perform Non-parametric ANCOVA",
      class = "btn-primary",
      style = "width: 100%;"
    ),
    
    br(),
    br(),
    
    # Help text
    helpText(
      "Non-parametric ANCOVA uses rank transformations.",
      "No assumptions about normal distribution required.",
      "Robust to outliers and non-linear relationships.",
      "Effect sizes include rank correlation coefficients."
    )
  )
}

NonparametricAncovaMainUI <- function(id) {
  ns <- NS(id)
  
  tagList(
    uiOutput(ns("npAncovaResults"))
  )
}

NonparametricAncovaUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(
      NonparametricAncovaSidebarUI(id)
    ),
    mainPanel(
      NonparametricAncovaMainUI(id)
    )
  )
} 