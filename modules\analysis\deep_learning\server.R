DeepLearningServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    
    # Reactive values
    data <- reactiveVal(NULL)
    model <- reactiveVal(NULL)
    results <- reactiveVal(NULL)
    training_history <- reactiveVal(NULL)
    
    # Load required packages
    observe({
      req(input$data_file)
      # Note: In a real implementation, you would load keras, tensorflow, etc.
      # For now, we'll use placeholder functions
    })
    
    # Update variable choices when data is loaded
    observe({
      req(input$data_file)
      df <- read.csv(input$data_file$datapath, stringsAsFactors = FALSE)
      data(df)
      
      # Update target variable choices
      if (input$task_type != "autoencoder") {
        updateSelectInput(session, "target_var", choices = names(df))
        updateSelectInput(session, "feature_vars", choices = names(df))
      } else {
        updateSelectInput(session, "autoencoder_vars", choices = names(df))
      }
    })
    
    # Deep Learning Analysis
    observeEvent(input$run_analysis, {
      req(data(), input$task_type)
      
      withProgress(message = "Running Deep Learning Analysis...", {
        
        # Prepare data
        df <- data()
        
        if (input$task_type == "autoencoder") {
          # Autoencoder analysis
          result <- deep_learning_autoencoder_analysis(
            data = df,
            variables = input$autoencoder_vars,
            model_type = input$model_type,
            n_layers = input$n_layers,
            layer_sizes = input$layer_sizes,
            activation = input$activation,
            dropout_rate = input$dropout_rate,
            learning_rate = input$learning_rate,
            batch_size = input$batch_size,
            epochs = input$epochs,
            optimizer = input$optimizer,
            validation_split = input$validation_split
          )
        } else if (input$model_type == "resnet") {
          # Transfer learning analysis
          result <- deep_learning_transfer_analysis(
            data = df,
            target = input$target_var,
            features = input$feature_vars,
            pretrained_model = input$pretrained_model,
            freeze_base = input$freeze_base,
            fine_tune_layers = input$fine_tune_layers,
            learning_rate = input$learning_rate,
            batch_size = input$batch_size,
            epochs = input$epochs,
            validation_split = input$validation_split
          )
        } else {
          # Standard neural network analysis
          result <- deep_learning_analysis(
            data = df,
            target = input$target_var,
            features = input$feature_vars,
            task_type = input$task_type,
            model_type = input$model_type,
            n_layers = input$n_layers,
            layer_sizes = input$layer_sizes,
            activation = input$activation,
            dropout_rate = input$dropout_rate,
            learning_rate = input$learning_rate,
            batch_size = input$batch_size,
            epochs = input$epochs,
            optimizer = input$optimizer,
            validation_split = input$validation_split
          )
        }
        
        results(result)
        model(result$model)
        training_history(result$history)
        
        incProgress(1, detail = "Analysis complete!")
      })
    })
    
    # Training Progress Plot
    output$training_plot <- renderPlot({
      req(training_history())
      plot_training_progress(training_history())
    })
    
    # Performance Plot
    output$performance_plot <- renderPlot({
      req(results())
      plot_model_performance(results())
    })
    
    # Performance Table
    output$performance_table <- renderTable({
      req(results())
      results()$performance_metrics
    })
    
    # Feature Importance Plot
    output$feature_importance <- renderPlot({
      req(results(), input$task_type != "autoencoder")
      plot_feature_importance(results())
    })
    
    # Confusion Matrix
    output$confusion_matrix <- renderPlot({
      req(results(), input$task_type == "classification")
      plot_confusion_matrix(results())
    })
    
    # ROC Curve
    output$roc_curve <- renderPlot({
      req(results(), input$task_type == "classification")
      plot_roc_curve(results())
    })
    
    # Autoencoder Results
    output$reconstruction_plot <- renderPlot({
      req(results(), input$task_type == "autoencoder")
      plot_reconstruction_error(results())
    })
    
    output$latent_space <- renderPlot({
      req(results(), input$task_type == "autoencoder")
      plot_latent_space(results())
    })
    
    output$anomaly_detection <- renderPlot({
      req(results(), input$task_type == "autoencoder")
      plot_anomaly_detection(results())
    })
    
    # Transfer Learning Results
    output$layer_activations <- renderPlot({
      req(results(), input$model_type == "resnet")
      plot_layer_activations(results())
    })
    
    output$feature_maps <- renderPlot({
      req(results(), input$model_type == "resnet")
      plot_feature_maps(results())
    })
    
    output$transfer_performance <- renderTable({
      req(results(), input$model_type == "resnet")
      results()$transfer_metrics
    })
    
    # Training Log
    output$training_log <- renderPrint({
      req(training_history())
      cat("Training completed successfully!\n")
      cat("Final loss:", training_history()$final_loss, "\n")
      cat("Final accuracy:", training_history()$final_accuracy, "\n")
    })
    
    # Download handlers
    output$download_model <- downloadHandler(
      filename = function() {
        paste0("deep_learning_model_", Sys.Date(), ".rds")
      },
      content = function(file) {
        req(model())
        saveRDS(model(), file)
      }
    )
    
    output$download_results <- downloadHandler(
      filename = function() {
        paste0("deep_learning_results_", Sys.Date(), ".csv")
      },
      content = function(file) {
        req(results())
        write.csv(results()$predictions, file, row.names = FALSE)
      }
    )
  })
} 