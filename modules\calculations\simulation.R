# Simulation calculation and output helpers

# Data upload handling
simUploadData_func <- function(simUserData) {
  ext <- tools::file_ext(simUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(simUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(simUserData$datapath),
         xlsx = readxl::read_xlsx(simUserData$datapath),
         txt = readr::read_tsv(simUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

# Input validation
simValidationErrors_func <- function(sim_n, sim_type, sim_mean = NULL, sim_sd = NULL, sim_n_trials = NULL, sim_prob = NULL, sim_lambda = NULL, sim_rate = NULL, sim_min = NULL, sim_max = NULL) {
  errors <- c()
  
  if (is.null(sim_n) || sim_n < 10 || sim_n > 10000) {
    errors <- c(errors, "Sample size must be between 10 and 10,000.")
  }
  
  if (is.null(sim_type) || sim_type == "") {
    errors <- c(errors, "Please select a simulation type.")
  }
  
  # Distribution-specific validation
  if (sim_type == "Normal Distribution") {
    if (is.null(sim_mean) || is.na(sim_mean)) {
      errors <- c(errors, "Mean is required for normal distribution.")
    }
    if (is.null(sim_sd) || sim_sd <= 0) {
      errors <- c(errors, "Standard deviation must be positive.")
    }
  } else if (sim_type == "Binomial Distribution") {
    if (is.null(sim_n_trials) || sim_n_trials <= 0) {
      errors <- c(errors, "Number of trials must be positive.")
    }
    if (is.null(sim_prob) || sim_prob < 0 || sim_prob > 1) {
      errors <- c(errors, "Probability must be between 0 and 1.")
    }
  } else if (sim_type == "Poisson Distribution") {
    if (is.null(sim_lambda) || sim_lambda <= 0) {
      errors <- c(errors, "Lambda (mean) must be positive for Poisson distribution.")
    }
  } else if (sim_type == "Exponential Distribution") {
    if (is.null(sim_rate) || sim_rate <= 0) {
      errors <- c(errors, "Rate parameter must be positive for exponential distribution.")
    }
  } else if (sim_type == "Uniform Distribution") {
    if (is.null(sim_min) || is.null(sim_max)) {
      errors <- c(errors, "Both minimum and maximum values are required for uniform distribution.")
    }
    if (sim_min >= sim_max) {
      errors <- c(errors, "Maximum value must be greater than minimum value for uniform distribution.")
    }
  }
  
  errors
}

# Main simulation analysis
simResults_func <- function(sim_n, sim_type, sim_mean = NULL, sim_sd = NULL, sim_n_trials = NULL, sim_prob = NULL, sim_lambda = NULL, sim_rate = NULL, sim_min = NULL, sim_max = NULL, seed = NULL) {
  req(sim_n, sim_type)
  
  # Set seed if provided
  if (!is.null(seed)) {
    set.seed(seed)
  }
  
  # Perform simulation based on type
  if (sim_type == "Normal Distribution") {
    mean_val <- ifelse(is.null(sim_mean), 0, sim_mean)
    sd_val <- ifelse(is.null(sim_sd), 1, sim_sd)
    simulated_data <- rnorm(sim_n, mean = mean_val, sd = sd_val)
    
    # Calculate theoretical values
    theoretical_mean <- mean_val
    theoretical_sd <- sd_val
    theoretical_quantiles <- qnorm(c(0.025, 0.25, 0.5, 0.75, 0.975), mean = mean_val, sd = sd_val)
    theoretical_skewness <- 0
    theoretical_kurtosis <- 3
    
    # Calculate empirical statistics
    empirical_mean <- mean(simulated_data)
    empirical_sd <- sd(simulated_data)
    empirical_quantiles <- quantile(simulated_data, c(0.025, 0.25, 0.5, 0.75, 0.975))
    empirical_skewness <- moments::skewness(simulated_data)
    empirical_kurtosis <- moments::kurtosis(simulated_data)
    
    # Goodness-of-fit test
    shapiro_test <- shapiro.test(simulated_data)
    
    list(
      data = simulated_data,
      type = "Normal",
      parameters = list(mean = mean_val, sd = sd_val),
      theoretical_mean = theoretical_mean,
      theoretical_sd = theoretical_sd,
      theoretical_quantiles = theoretical_quantiles,
      theoretical_skewness = theoretical_skewness,
      theoretical_kurtosis = theoretical_kurtosis,
      empirical_mean = empirical_mean,
      empirical_sd = empirical_sd,
      empirical_quantiles = empirical_quantiles,
      empirical_skewness = empirical_skewness,
      empirical_kurtosis = empirical_kurtosis,
      goodness_of_fit = shapiro_test,
      n = sim_n
    )
  } else if (sim_type == "Binomial Distribution") {
    n_trials <- ifelse(is.null(sim_n_trials), 10, sim_n_trials)
    prob <- ifelse(is.null(sim_prob), 0.5, sim_prob)
    simulated_data <- rbinom(sim_n, size = n_trials, prob = prob)
    
    # Calculate theoretical values
    theoretical_mean <- n_trials * prob
    theoretical_sd <- sqrt(n_trials * prob * (1 - prob))
    theoretical_quantiles <- qbinom(c(0.025, 0.25, 0.5, 0.75, 0.975), size = n_trials, prob = prob)
    theoretical_skewness <- (1 - 2*prob) / sqrt(n_trials * prob * (1 - prob))
    theoretical_kurtosis <- 3 + (1 - 6*prob*(1-prob)) / (n_trials * prob * (1-prob))
    
    # Calculate empirical statistics
    empirical_mean <- mean(simulated_data)
    empirical_sd <- sd(simulated_data)
    empirical_quantiles <- quantile(simulated_data, c(0.025, 0.25, 0.5, 0.75, 0.975))
    empirical_skewness <- moments::skewness(simulated_data)
    empirical_kurtosis <- moments::kurtosis(simulated_data)
    
    # Chi-square goodness-of-fit test
    observed <- table(simulated_data)
    expected <- dbinom(as.numeric(names(observed)), size = n_trials, prob = prob) * sim_n
    chi_square_test <- chisq.test(observed, p = expected/sum(expected))
    
    list(
      data = simulated_data,
      type = "Binomial",
      parameters = list(n_trials = n_trials, prob = prob),
      theoretical_mean = theoretical_mean,
      theoretical_sd = theoretical_sd,
      theoretical_quantiles = theoretical_quantiles,
      theoretical_skewness = theoretical_skewness,
      theoretical_kurtosis = theoretical_kurtosis,
      empirical_mean = empirical_mean,
      empirical_sd = empirical_sd,
      empirical_quantiles = empirical_quantiles,
      empirical_skewness = empirical_skewness,
      empirical_kurtosis = empirical_kurtosis,
      goodness_of_fit = chi_square_test,
      n = sim_n
    )
  } else if (sim_type == "Poisson Distribution") {
    lambda <- ifelse(is.null(sim_lambda), 5, sim_lambda)
    simulated_data <- rpois(sim_n, lambda = lambda)
    
    # Calculate theoretical values
    theoretical_mean <- lambda
    theoretical_sd <- sqrt(lambda)
    theoretical_quantiles <- qpois(c(0.025, 0.25, 0.5, 0.75, 0.975), lambda = lambda)
    theoretical_skewness <- 1 / sqrt(lambda)
    theoretical_kurtosis <- 3 + 1 / lambda
    
    # Calculate empirical statistics
    empirical_mean <- mean(simulated_data)
    empirical_sd <- sd(simulated_data)
    empirical_quantiles <- quantile(simulated_data, c(0.025, 0.25, 0.5, 0.75, 0.975))
    empirical_skewness <- moments::skewness(simulated_data)
    empirical_kurtosis <- moments::kurtosis(simulated_data)
    
    # Chi-square goodness-of-fit test
    observed <- table(simulated_data)
    expected <- dpois(as.numeric(names(observed)), lambda = lambda) * sim_n
    chi_square_test <- chisq.test(observed, p = expected/sum(expected))
    
    list(
      data = simulated_data,
      type = "Poisson",
      parameters = list(lambda = lambda),
      theoretical_mean = theoretical_mean,
      theoretical_sd = theoretical_sd,
      theoretical_quantiles = theoretical_quantiles,
      theoretical_skewness = theoretical_skewness,
      theoretical_kurtosis = theoretical_kurtosis,
      empirical_mean = empirical_mean,
      empirical_sd = empirical_sd,
      empirical_quantiles = empirical_quantiles,
      empirical_skewness = empirical_skewness,
      empirical_kurtosis = empirical_kurtosis,
      goodness_of_fit = chi_square_test,
      n = sim_n
    )
  } else if (sim_type == "Exponential Distribution") {
    rate <- ifelse(is.null(sim_rate), 1, sim_rate)
    simulated_data <- rexp(sim_n, rate = rate)
    
    # Calculate theoretical values
    theoretical_mean <- 1/rate
    theoretical_sd <- 1/rate
    theoretical_quantiles <- qexp(c(0.025, 0.25, 0.5, 0.75, 0.975), rate = rate)
    theoretical_skewness <- 2
    theoretical_kurtosis <- 9
    
    # Calculate empirical statistics
    empirical_mean <- mean(simulated_data)
    empirical_sd <- sd(simulated_data)
    empirical_quantiles <- quantile(simulated_data, c(0.025, 0.25, 0.5, 0.75, 0.975))
    empirical_skewness <- moments::skewness(simulated_data)
    empirical_kurtosis <- moments::kurtosis(simulated_data)
    
    # Kolmogorov-Smirnov goodness-of-fit test
    ks_test <- ks.test(simulated_data, "pexp", rate = rate)
    
    list(
      data = simulated_data,
      type = "Exponential",
      parameters = list(rate = rate),
      theoretical_mean = theoretical_mean,
      theoretical_sd = theoretical_sd,
      theoretical_quantiles = theoretical_quantiles,
      theoretical_skewness = theoretical_skewness,
      theoretical_kurtosis = theoretical_kurtosis,
      empirical_mean = empirical_mean,
      empirical_sd = empirical_sd,
      empirical_quantiles = empirical_quantiles,
      empirical_skewness = empirical_skewness,
      empirical_kurtosis = empirical_kurtosis,
      goodness_of_fit = ks_test,
      n = sim_n
    )
  } else if (sim_type == "Uniform Distribution") {
    min_val <- ifelse(is.null(sim_min), 0, sim_min)
    max_val <- ifelse(is.null(sim_max), 1, sim_max)
    simulated_data <- runif(sim_n, min = min_val, max = max_val)
    
    # Calculate theoretical values
    theoretical_mean <- (min_val + max_val) / 2
    theoretical_sd <- sqrt((max_val - min_val)^2 / 12)
    theoretical_quantiles <- qunif(c(0.025, 0.25, 0.5, 0.75, 0.975), min = min_val, max = max_val)
    theoretical_skewness <- 0
    theoretical_kurtosis <- 9/5
    
    # Calculate empirical statistics
    empirical_mean <- mean(simulated_data)
    empirical_sd <- sd(simulated_data)
    empirical_quantiles <- quantile(simulated_data, c(0.025, 0.25, 0.5, 0.75, 0.975))
    empirical_skewness <- moments::skewness(simulated_data)
    empirical_kurtosis <- moments::kurtosis(simulated_data)
    
    # Kolmogorov-Smirnov goodness-of-fit test
    ks_test <- ks.test(simulated_data, "punif", min = min_val, max = max_val)
    
    list(
      data = simulated_data,
      type = "Uniform",
      parameters = list(min = min_val, max = max_val),
      theoretical_mean = theoretical_mean,
      theoretical_sd = theoretical_sd,
      theoretical_quantiles = theoretical_quantiles,
      theoretical_skewness = theoretical_skewness,
      theoretical_kurtosis = theoretical_kurtosis,
      empirical_mean = empirical_mean,
      empirical_sd = empirical_sd,
      empirical_quantiles = empirical_quantiles,
      empirical_skewness = empirical_skewness,
      empirical_kurtosis = empirical_kurtosis,
      goodness_of_fit = ks_test,
      n = sim_n
    )
  } else {
    # Custom simulation (uniform 0,1)
    simulated_data <- runif(sim_n, min = 0, max = 1)
    
    list(
      data = simulated_data,
      type = "Custom",
      parameters = list(min = 0, max = 1),
      theoretical_mean = 0.5,
      theoretical_sd = sqrt(1/12),
      theoretical_quantiles = qunif(c(0.025, 0.25, 0.5, 0.75, 0.975), min = 0, max = 1),
      theoretical_skewness = 0,
      theoretical_kurtosis = 9/5,
      empirical_mean = mean(simulated_data),
      empirical_sd = sd(simulated_data),
      empirical_quantiles = quantile(simulated_data, c(0.025, 0.25, 0.5, 0.75, 0.975)),
      empirical_skewness = moments::skewness(simulated_data),
      empirical_kurtosis = moments::kurtosis(simulated_data),
      goodness_of_fit = NULL,
      n = sim_n
    )
  }
}

# Mathematical documentation with hypothesis testing
simMathematicalDocumentation <- function(simResults_output) {
  renderUI({
    req(simResults_output())
    results <- simResults_output()
    
    withMathJax(
      tagList(
        h4("Mathematical Framework"),
        p(tags$b("Distribution Parameters:")),
        if (results$type == "Normal") {
          tagList(
            sprintf("\\( X \\sim N(\\mu = %.2f, \\sigma = %.2f) \\)", 
                    results$parameters$mean, results$parameters$sd),
            br(),
            sprintf("\\( f(x) = \\frac{1}{\\sigma\\sqrt{2\\pi}} e^{-\\frac{(x-\\mu)^2}{2\\sigma^2}} \\)"),
            br(), br()
          )
        } else if (results$type == "Binomial") {
          tagList(
            sprintf("\\( X \\sim Bin(n = %d, p = %.2f) \\)", 
                    results$parameters$n_trials, results$parameters$prob),
            br(),
            sprintf("\\( P(X = k) = \\binom{n}{k} p^k (1-p)^{n-k} \\)"),
            br(), br()
          )
        } else if (results$type == "Poisson") {
          tagList(
            sprintf("\\( X \\sim Pois(\\lambda = %.2f) \\)", results$parameters$lambda),
            br(),
            sprintf("\\( P(X = k) = \\frac{\\lambda^k e^{-\\lambda}}{k!} \\)"),
            br(), br()
          )
        } else if (results$type == "Exponential") {
          tagList(
            sprintf("\\( X \\sim Exp(\\lambda = %.2f) \\)", results$parameters$rate),
            br(),
            sprintf("\\( f(x) = \\lambda e^{-\\lambda x} \\)"),
            br(), br()
          )
        } else if (results$type == "Uniform") {
          tagList(
            sprintf("\\( X \\sim U(a = %.2f, b = %.2f) \\)", 
                    results$parameters$min, results$parameters$max),
            br(),
            sprintf("\\( f(x) = \\frac{1}{b-a} \\) for \\( a \\leq x \\leq b \\)"),
            br(), br()
          )
        },
        p(tags$b("Theoretical vs Empirical Comparison:")),
        sprintf("\\( H_0: \\) The simulated data follows the theoretical distribution"),
        br(),
        sprintf("\\( H_a: \\) The simulated data does not follow the theoretical distribution"),
        br(), br(),
        sprintf("\\( n = %d \\) observations", results$n),
        br(),
        sprintf("Theoretical mean: \\( \\mu = %.4f \\)", results$theoretical_mean),
        br(),
        sprintf("Empirical mean: \\( \\bar{x} = %.4f \\)", results$empirical_mean),
        br(),
        sprintf("Difference: \\( |\\bar{x} - \\mu| = %.4f \\)", 
                abs(results$empirical_mean - results$theoretical_mean)),
        br(),
        sprintf("Relative error: \\( \\frac{|\\bar{x} - \\mu|}{\\mu} = %.2f\\%% \\)", 
                abs(results$empirical_mean - results$theoretical_mean) / results$theoretical_mean * 100),
        br(), br()
      )
    )
  })
}

# Comprehensive summary output
simSummaryOutput <- function(simResults_output) {
  renderTable({
    req(simResults_output())
    results <- simResults_output()
    
    data.frame(
      Metric = c("Simulation Type", "Sample Size", "Theoretical Mean", "Empirical Mean", 
                 "Theoretical SD", "Empirical SD", "Mean Difference", "Relative Error (%)"),
      Value = c(
        results$type,
        results$n,
        round(results$theoretical_mean, 4),
        round(results$empirical_mean, 4),
        round(results$theoretical_sd, 4),
        round(results$empirical_sd, 4),
        round(results$empirical_mean - results$theoretical_mean, 4),
        round(abs(results$empirical_mean - results$theoretical_mean) / results$theoretical_mean * 100, 2)
      ),
      stringsAsFactors = FALSE
    )
  })
}

# Advanced statistical diagnostics
simAdvancedDiagnostics <- function(simResults_output) {
  renderTable({
    req(simResults_output())
    results <- simResults_output()
    
    data.frame(
      Metric = c("Theoretical Skewness", "Empirical Skewness", "Skewness Difference",
                 "Theoretical Kurtosis", "Empirical Kurtosis", "Kurtosis Difference",
                 "Goodness-of-Fit Test", "P-value"),
      Value = c(
        round(results$theoretical_skewness, 4),
        round(results$empirical_skewness, 4),
        round(abs(results$empirical_skewness - results$theoretical_skewness), 4),
        round(results$theoretical_kurtosis, 4),
        round(results$empirical_kurtosis, 4),
        round(abs(results$empirical_kurtosis - results$theoretical_kurtosis), 4),
        ifelse(!is.null(results$goodness_of_fit), 
               class(results$goodness_of_fit)[1], "N/A"),
        ifelse(!is.null(results$goodness_of_fit), 
               round(results$goodness_of_fit$p.value, 4), "N/A")
      ),
      stringsAsFactors = FALSE
    )
  })
}

# Quantile comparison
simQuantileComparison <- function(simResults_output) {
  renderTable({
    req(simResults_output())
    results <- simResults_output()
    
    data.frame(
      Quantile = c("2.5%", "25%", "50%", "75%", "97.5%"),
      Theoretical = round(results$theoretical_quantiles, 4),
      Empirical = round(results$empirical_quantiles, 4),
      Difference = round(results$empirical_quantiles - results$theoretical_quantiles, 4),
      Relative_Difference = round((results$empirical_quantiles - results$theoretical_quantiles) / results$theoretical_quantiles * 100, 2),
      stringsAsFactors = FALSE
    )
  })
}

# Enhanced plot with ggplot2
simEnhancedPlot <- function(simResults_output) {
  renderPlot({
    req(simResults_output())
    results <- simResults_output()
    
    # Create data frame for plotting
    plot_data <- data.frame(
      Value = results$data,
      Type = "Simulated"
    )
    
    # Create theoretical density curve
    if (results$type == "Normal") {
      x_range <- seq(min(results$data), max(results$data), length.out = 100)
      density_data <- data.frame(
        x = x_range,
        y = dnorm(x_range, mean = results$parameters$mean, sd = results$parameters$sd)
      )
    } else if (results$type == "Exponential") {
      x_range <- seq(0, max(results$data), length.out = 100)
      density_data <- data.frame(
        x = x_range,
        y = dexp(x_range, rate = results$parameters$rate)
      )
    } else if (results$type == "Uniform") {
      x_range <- seq(results$parameters$min, results$parameters$max, length.out = 100)
      density_data <- data.frame(
        x = x_range,
        y = dunif(x_range, min = results$parameters$min, max = results$parameters$max)
      )
    } else {
      density_data <- NULL
    }
    
    # Create comprehensive plot
    p1 <- ggplot2::ggplot(plot_data, ggplot2::aes(x = Value)) +
      ggplot2::geom_histogram(ggplot2::aes(y = ..density..), 
                              bins = 30, fill = "lightblue", alpha = 0.7) +
      ggplot2::geom_density(color = "red", linewidth = 1) +
      {if (!is.null(density_data)) 
        ggplot2::geom_line(data = density_data, ggplot2::aes(x = x, y = y), 
                           color = "darkgreen", linewidth = 1, linetype = "dashed")} +
      ggplot2::labs(title = paste("Distribution of", results$type, "Simulation"),
                    subtitle = sprintf("n = %d, Theoretical vs Empirical", results$n),
                    x = "Value", y = "Density") +
      ggplot2::theme_minimal() +
      ggplot2::theme(plot.title = ggplot2::element_text(size = 14, face = "bold"),
                     plot.subtitle = ggplot2::element_text(size = 12))
    
    # Q-Q plot
    if (results$type == "Normal") {
      qq_data <- data.frame(
        theoretical = qnorm(ppoints(length(results$data))),
        empirical = sort(results$data)
      )
    } else {
      qq_data <- data.frame(
        theoretical = results$theoretical_quantiles,
        empirical = results$empirical_quantiles
      )
    }
    
    p2 <- ggplot2::ggplot(qq_data, ggplot2::aes(x = theoretical, y = empirical)) +
      ggplot2::geom_point(color = "blue", alpha = 0.7) +
      ggplot2::geom_abline(slope = 1, intercept = 0, color = "red", linetype = "dashed") +
      ggplot2::labs(title = "Q-Q Plot: Theoretical vs Empirical",
                    x = "Theoretical Quantiles", y = "Empirical Quantiles") +
      ggplot2::theme_minimal()
    
    # Time series plot
    time_data <- data.frame(
      Time = 1:length(results$data),
      Value = results$data
    )
    
    p3 <- ggplot2::ggplot(time_data, ggplot2::aes(x = Time, y = Value)) +
      ggplot2::geom_line(color = "green", alpha = 0.7) +
      ggplot2::geom_hline(yintercept = results$theoretical_mean, color = "red", linetype = "dashed") +
      ggplot2::labs(title = "Time Series Plot",
                    x = "Observation", y = "Value") +
      ggplot2::theme_minimal()
    
    # Boxplot
    p4 <- ggplot2::ggplot(plot_data, ggplot2::aes(y = Value)) +
      ggplot2::geom_boxplot(fill = "orange", alpha = 0.7) +
      ggplot2::geom_hline(yintercept = results$theoretical_mean, color = "red", linetype = "dashed") +
      ggplot2::labs(title = "Boxplot with Theoretical Mean",
                    y = "Value") +
      ggplot2::theme_minimal()
    
    # Combine plots
    gridExtra::grid.arrange(p1, p2, p3, p4, ncol = 2)
  })
}

# Statistical conclusion
simStatisticalConclusion <- function(simResults_output) {
  renderUI({
    req(simResults_output())
    results <- simResults_output()
    
    # Calculate effect sizes
    mean_diff <- abs(results$empirical_mean - results$theoretical_mean)
    relative_error <- mean_diff / results$theoretical_mean * 100
    
    # Determine goodness of fit
    fit_quality <- ifelse(!is.null(results$goodness_of_fit), 
                         ifelse(results$goodness_of_fit$p.value > 0.05, "Good", "Poor"), 
                         "Unknown")
    
    tagList(
      h4("Statistical Conclusion"),
      p(tags$b("Distribution Fit Assessment:")),
      p(sprintf("The simulated data shows a %s fit to the theoretical %s distribution.", 
                tolower(fit_quality), results$type)),
      br(),
      p(tags$b("Parameter Estimation:")),
      p(sprintf("The empirical mean (%.4f) differs from the theoretical mean (%.4f) by %.4f (%.2f%% relative error).",
                results$empirical_mean, results$theoretical_mean, mean_diff, relative_error)),
      br(),
      p(tags$b("Sample Size Adequacy:")),
      p(sprintf("With n = %d observations, the simulation provides %s precision for parameter estimation.",
                results$n, ifelse(results$n >= 100, "excellent", ifelse(results$n >= 30, "good", "moderate")))),
      br(),
      if (!is.null(results$goodness_of_fit)) {
        tagList(
          p(tags$b("Goodness-of-Fit Test:")),
          p(sprintf("Test statistic: %.4f, p-value: %.4f", 
                    ifelse("statistic" %in% names(results$goodness_of_fit), 
                           results$goodness_of_fit$statistic, "N/A"),
                    results$goodness_of_fit$p.value)),
          p(ifelse(results$goodness_of_fit$p.value > 0.05,
                   "Conclusion: No significant evidence against the theoretical distribution (fail to reject H₀).",
                   "Conclusion: Significant evidence against the theoretical distribution (reject H₀)."))
        )
      }
    )
  })
}

# Data table with export capabilities
simDataTableOutput <- function(simResults_output) {
  renderUI({
    req(simResults_output())
    results <- simResults_output()
    
    # Create data frame for display
    sim_df <- data.frame(
      Observation = 1:length(results$data),
      Value = results$data
    )
    
    tagList(
      h4("Simulated Data"),
      div(
        DT::datatable(
          sim_df,
          rownames = FALSE,
          options = list(
            pageLength = 10,
            lengthMenu = list(c(10, 25, 50, 100, -1), c("10", "25", "50", "100", "all")),
            scrollX = TRUE,
            dom = 'Bfrtip',
            buttons = c('copy', 'csv', 'excel', 'pdf', 'print'),
            columnDefs = list(
              list(className = 'dt-center', targets = "_all"),
              list(width = '120px', targets = "_all")
            )
          ),
          extensions = 'Buttons',
          filter = 'top'
        ), 
        style = "width: 95%"
      ),
      br(),
      br()
    )
  }) 