# One Sample Inference Server
# Extracted and modularized from statInfr.R

source("modules/calculations/one_sample.R")

OneSampleInferenceServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    # Validators
    one_mean_iv <- InputValidator$new()
    one_mean_sd_known_iv <- InputValidator$new()
    one_mean_sd_unknown_iv <- InputValidator$new()
    one_mean_raw_iv <- InputValidator$new()
    one_mean_upload_iv <- InputValidator$new()
    one_mean_upload_var_iv <- InputValidator$new()
    one_mean_upload_sd_iv <- InputValidator$new()
    one_mean_ht_iv <- InputValidator$new()
    one_prop_iv <- InputValidator$new()
    one_sd_iv <- InputValidator$new()
    one_sd_ht_iv <- InputValidator$new()

    # Add rules (see statInfr.R for details)
    one_mean_iv$add_rule("sampleSize", sv_required())
    one_mean_iv$add_rule("sampleSize", sv_integer())
    one_mean_iv$add_rule("sampleSize", sv_gt(1))
    one_mean_iv$add_rule("sampleMean", sv_required())
    one_mean_raw_iv$add_rule("sample1", sv_required())
    one_mean_raw_iv$add_rule("sample1", sv_regex("^( )*(-)?([0-9]+(\\.[0-9]+)?)(,( )*(-)?[0-9]+(\\.[0-9]+)?)+([ \\r\\n])*$", "Data must be numeric values separated by a comma (ie: 2,3,4)"))
    one_mean_raw_iv$add_rule("sample1", ~ if (sd(createNumLst(input$sample1)) == 0) "No variance in sample data")
    one_mean_upload_iv$add_rule("oneMeanUserData", sv_required())
    one_mean_upload_iv$add_rule("oneMeanUserData", ~ if(!(tolower(tools::file_ext(input$oneMeanUserData$name)) %in% c("csv", "txt", "xls", "xlsx"))) "File format not accepted.")
    one_mean_upload_var_iv$add_rule("oneMeanVariable", sv_required())
    one_mean_sd_known_iv$add_rule("popuSD", sv_required())
    one_mean_sd_known_iv$add_rule("popuSD", sv_gt(0))
    one_mean_sd_unknown_iv$add_rule("sampSD", sv_required())
    one_mean_sd_unknown_iv$add_rule("sampSD", sv_gt(0))
    one_mean_ht_iv$add_rule("hypMean", sv_required())
    one_prop_iv$add_rule("numSuccesses", sv_required())
    one_prop_iv$add_rule("numSuccesses", sv_integer())
    one_prop_iv$add_rule("numSuccesses", sv_gte(0))
    one_prop_iv$add_rule("numTrials", sv_required())
    one_prop_iv$add_rule("numTrials", sv_integer())
    one_prop_iv$add_rule("numTrials", sv_gt(0))
    one_sd_iv$add_rule("SSDSampleSize", sv_required())
    one_sd_iv$add_rule("SSDSampleSize", sv_integer())
    one_sd_iv$add_rule("SSDSampleSize", sv_gt(1))
    one_sd_iv$add_rule("SSDStdDev", sv_required())
    one_sd_iv$add_rule("SSDStdDev", sv_gt(0))

    # Enable validators
    one_mean_iv$enable()
    one_mean_sd_known_iv$enable()
    one_mean_sd_unknown_iv$enable()
    one_mean_raw_iv$enable()
    one_mean_upload_iv$enable()
    one_mean_upload_var_iv$enable()
    one_mean_upload_sd_iv$enable()
    one_mean_ht_iv$enable()
    one_prop_iv$enable()
    one_sd_iv$enable()
    one_sd_ht_iv$enable()

    # File upload reactive
    oneMeanUploadData <- eventReactive(input$oneMeanUserData, {
      handle_file_upload(input$oneMeanUserData)
    })
    # Clear selectizeInputs and main panel, show preview on upload
    observeEvent(oneMeanUploadData(), {
      data <- oneMeanUploadData()
      # Clear selectizeInputs and main panel (if any selectizeInputs are used)
      output$oneSampleResults <- renderUI({ NULL })
      # If data is valid, show preview
      if (!is.null(data) && is.data.frame(data)) {
        output$oneSampleResults <- renderUI({
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('oneMeanPreviewTable'))
          )
        })
        output$oneMeanPreviewTable <- DT::renderDT({
          head(data, 20)
        })
      }
    })

    # --- Main Reactives for One-Sample Mean ---
    GetOneMeanCI <- reactive({
      if (is.null(input$sampleSize) || is.null(input$sampleMean)) return(NULL)
      if (input$sigmaKnown == "Known") {
        ZInterval(input$sampleSize, input$sampleMean, input$popuSD, 0.95)
      } else {
        TInterval(input$sampleSize, input$sampleMean, input$sampSD, 0.95)
      }
    })
    GetOneMeanHT <- reactive({
      if (is.null(input$sampleSize) || is.null(input$sampleMean) || is.null(input$hypMean)) return(NULL)
      if (input$sigmaKnown == "Known") {
        ZTest(input$sampleSize, input$sampleMean, input$popuSD, input$hypMean, "two.sided", 0.05)
      } else {
        TTest(input$sampleSize, input$sampleMean, input$sampSD, input$hypMean, "two.sided", 0.05)
      }
    })

    # Add a reactive to collect validation errors
    oneSampleValidationErrors <- reactive({
      errors <- c()
      # Example checks (expand as needed)
      if (is.null(input$sampleSize) || is.na(input$sampleSize) || input$sampleSize < 2) {
        errors <- c(errors, "Sample size (n) must be an integer greater than 1.")
      }
      if (is.null(input$sampleMean) || is.na(input$sampleMean)) {
        errors <- c(errors, "Sample mean is required.")
      }
      if (!is.null(input$sample1) && sd(createNumLst(input$sample1)) == 0) {
        errors <- c(errors, "No variance in sample data.")
      }
      # File upload checks
      if (!is.null(input$oneMeanUserData) && !(tolower(tools::file_ext(input$oneMeanUserData$name)) %in% c("csv", "txt", "xls", "xlsx"))) {
        errors <- c(errors, "File format not accepted.")
      }
      errors
    })

    # --- Outputs ---
    output$oneMeanCI <- renderUI({
      ci <- GetOneMeanCI()
      if (is.null(ci)) return(NULL)
      withMathJax(tagList(
        h4("Confidence Interval for Mean"),
        p("$\\bar{x} = $", ci[["Sample Mean"]]),
        p("$n = $", ci[["Sample Size"]]),
        p("$s = $", ci[["Sample SD"]]),
        p("$t^* = $", ci[["T Critical"]]),
        p("$SE = $", ci[["Std Error"]]),
        p("$ME = $", ci[["ME"]]),
        p("$CI = (", ci[["LCL"]], ", ", ci[["UCL"]], ")$")
      ))
    })
    output$oneMeanHT <- renderUI({
      ht <- GetOneMeanHT()
      if (is.null(ht)) return(NULL)
      withMathJax(tagList(
        h4("Hypothesis Test for Mean"),
        h5("Hypotheses"),
        p("$H_0: \\mu = \\mu_0$"),
        p("$H_A: \\mu \\neq \\mu_0$"),
        h5("Test Statistic"),
        p("$t = \\frac{\\bar{x} - \\mu_0}{s/\\sqrt{n}}$"),
        p("$t = $", ht[["Test Statistic"]]),
        h5("P-value Method"),
        p("$P = $", ht[["P-Value"]]),
        h5("Critical Value Method"),
        p("$t^* = $", ht[["T Critical"]]),
        h5("Conclusion"),
        if (ht[["P-Value"]] < 0.05) {
          p("Since $P < 0.05$, reject $H_0$.")
        } else {
          p("Since $P \\geq 0.05$, do not reject $H_0$.")
        }
      ))
    })
    # TODO: Add outputs for proportion and standard deviation

    # Show main results or error screen in main panel when Calculate is pressed
    observeEvent(input$goInference, {
      output$oneSampleResults <- renderUI({
        errors <- oneSampleValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in One Sample Inference", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("oneSampleTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("oneSampleAnalysis"),
                title = "Analysis",
                titlePanel("One Sample Inference Results"),
                br(),
                uiOutput(ns('oneMeanCI')),
                uiOutput(ns('oneMeanHT')),
                br(),
                h4("Effect Size"),
                uiOutput(ns('oneSampleEffectSize')),
                br(),
                h4("Assumptions Check"),
                uiOutput(ns('oneSampleAssumptions'))
              ),
              tabPanel(
                id = ns("oneSampleDataSummary"),
                title = "Data Summary",
                h4("Descriptive Statistics"),
                tableOutput(ns('oneSampleDescriptive')),
                br(),
                h4("Normality Test"),
                tableOutput(ns('oneSampleNormality'))
              ),
              tabPanel(
                id = ns("oneSampleUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('oneSampleDataTable'))
              )
            )
          )
        }
      })
    })
    
    # Additional outputs for the enhanced tabs
    output$oneSampleEffectSize <- renderUI({
      ci <- GetOneMeanCI()
      if (is.null(ci)) return(NULL)
      
      # Calculate Cohen's d effect size
      if (input$sigmaKnown == "Known") {
        cohens_d <- (input$sampleMean - input$hypMean) / input$popuSD
      } else {
        cohens_d <- (input$sampleMean - input$hypMean) / input$sampSD
      }
      
      tagList(
        p("Cohen's d effect size: ", round(cohens_d, 4)),
        p("Effect size interpretation:"),
        if (abs(cohens_d) < 0.2) {
          p("Small effect size")
        } else if (abs(cohens_d) < 0.5) {
          p("Medium effect size")
        } else {
          p("Large effect size")
        }
      )
    })
    
    output$oneSampleAssumptions <- renderUI({
      tagList(
        h5("Key Assumptions:"),
        p("1. Random sampling"),
        p("2. Normal distribution (approximately)"),
        p("3. Independent observations"),
        br(),
        p("Note: These assumptions should be checked with your data.")
      )
    })
    
    output$oneSampleDescriptive <- renderTable({
      ci <- GetOneMeanCI()
      if (is.null(ci)) return(NULL)
      
      desc <- data.frame(
        Statistic = c("Sample Size", "Mean", "Standard Deviation", "Standard Error", "95% CI Lower", "95% CI Upper"),
        Value = c(
          input$sampleSize,
          input$sampleMean,
          ifelse(input$sigmaKnown == "Known", input$popuSD, input$sampSD),
          ci[["Std Error"]],
          ci[["LCL"]],
          ci[["UCL"]]
        )
      )
      desc
    }, digits = 4)
    
    output$oneSampleNormality <- renderTable({
      # Placeholder for normality test results
      # In a real implementation, this would use the uploaded data
      data.frame(
        Test = c("Shapiro-Wilk"),
        Statistic = c("N/A"),
        P_value = c("N/A"),
        stringsAsFactors = FALSE
      )
    })
    
    output$oneSampleDataTable <- DT::renderDT({
      req(oneMeanUploadData())
      DT::datatable(oneMeanUploadData(),
        options = list(pageLength = 25,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(oneMeanUploadData())))))
    })
  })
} 