# Statistical Decision Trees calculation logic
#' @param data Data frame
#' @param target Name of target variable
#' @param predictors Vector of predictor variable names
#' @param tree_type 'classification' or 'regression'
#' @return List with model, summary, and plot function
decisionTreesResults_func <- function(data, target, predictors, tree_type = "classification", ...) {
  if (!requireNamespace("rpart", quietly = TRUE)) stop("Please install the 'rpart' package.")
  if (!requireNamespace("rpart.plot", quietly = TRUE)) stop("Please install the 'rpart.plot' package.")
  if (!(target %in% names(data))) stop("Target variable not found in data.")
  if (!all(predictors %in% names(data))) stop("Some predictors not found in data.")
  
  # Prepare formula
  formula <- as.formula(paste(target, "~", paste(predictors, collapse = "+")))
  method <- ifelse(tree_type == "regression", "anova", "class")
  
  # Fit tree
  fit <- rpart::rpart(formula, data = data, method = method)
  
  # Summary
  model_summary <- summary(fit)
  
  # Plot function
  plotfun <- function() {
    rpart.plot::rpart.plot(fit, main = paste("Decision Tree (", tree_type, ")"))
  }
  
  list(
    model = fit,
    summary = model_summary,
    plot = plotfun
  )
} 