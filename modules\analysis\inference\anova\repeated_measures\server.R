# Repeated Measures ANOVA Server
source("modules/calculations/repeated_measures_anova.R")
RepeatedMeasuresAnovaServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    rmAnovaUploadData <- eventReactive(input$rmAnovaUserData, {
      handle_file_upload(input$rmAnovaUserData)
    })
    observeEvent(rmAnovaUploadData(), {
      data <- rmAnovaUploadData()
      updateSelectizeInput(session, 'rmAnovaSubject', choices = names(data), server = TRUE)
      updateSelectizeInput(session, 'rmAnovaResponse', choices = names(data), server = TRUE)
      updateSelectizeInput(session, 'rmAnovaCondition', choices = names(data), server = TRUE)
      output$repeatedMeasuresAnovaResults <- renderUI({
        if (!is.null(data) && is.data.frame(data)) {
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('rmAnovaPreviewTable'))
          )
        } else NULL
      })
      output$rmAnovaPreviewTable <- DT::renderDT({
        head(data, 20)
      })
    })
    rmAnovaValidationErrors <- reactive({
      errors <- c()
      data <- rmAnovaUploadData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$rmAnovaSubject) || input$rmAnovaSubject == "") {
        errors <- c(errors, "Please select a subject ID column.")
      }
      if (is.null(input$rmAnovaResponse) || input$rmAnovaResponse == "") {
        errors <- c(errors, "Please select a response variable.")
      }
      if (is.null(input$rmAnovaCondition) || input$rmAnovaCondition == "") {
        errors <- c(errors, "Please select a condition/time column.")
      }
      errors
    })
    observeEvent(input$goRepeatedMeasuresAnova, {
      output$repeatedMeasuresAnovaResults <- renderUI({
        errors <- rmAnovaValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Repeated Measures ANOVA", errors = errors)
        } else {
          data <- rmAnovaUploadData()
          res <- calc_repeated_measures_anova(
            data,
            subject = input$rmAnovaSubject,
            response = input$rmAnovaResponse,
            condition = input$rmAnovaCondition
          )
          tagList(
            tabsetPanel(
              id = ns("rmAnovaTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("rmAnovaAnalysis"),
                title = "Analysis",
                titlePanel("Repeated Measures ANOVA Results"),
                br(),
                h4('ANOVA Table'),
                verbatimTextOutput(ns('rmAnovaTable')),
                br(),
                h4('Model Summary'),
                verbatimTextOutput(ns('rmAnovaModelSummary')),
                br(),
                h4("Effect Size"),
                uiOutput(ns('rmAnovaEffectSize')),
                br(),
                h4("Assumptions Check"),
                uiOutput(ns('rmAnovaAssumptions'))
              ),
              tabPanel(
                id = ns("rmAnovaDataSummary"),
                title = "Data Summary",
                h4("Descriptive Statistics by Condition"),
                tableOutput(ns('rmAnovaDescriptive')),
                br(),
                h4("Subject Summary"),
                tableOutput(ns('rmAnovaSubjectSummary'))
              ),
              tabPanel(
                id = ns("rmAnovaUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('rmAnovaDataTable'))
              )
            )
          )
        }
      })
    })
  })
} 