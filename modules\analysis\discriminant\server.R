DiscriminantServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    discData <- eventReactive(input$discUserData, {
      handle_file_upload(input$discUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(discData(), {
      data <- discData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'discPredictors', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'discGroup', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    discValidationErrors <- reactive({
      errors <- c()
      data <- discData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$discPredictors) || length(input$discPredictors) < 2) {
        errors <- c(errors, "Select at least two predictor variables.")
      }
      if (is.null(input$discGroup) || input$discGroup == "") {
        errors <- c(errors, "Select a grouping variable.")
      }
      if (is.null(input$discMethod) || input$discMethod == "") {
        errors <- c(errors, "Select a discriminant analysis method.")
      }
      
      # Check if selected variables are numeric where appropriate
      if (!is.null(input$discPredictors) && length(input$discPredictors) >= 2) {
        for (var in input$discPredictors) {
          if (!is.numeric(data[[var]])) {
            errors <- c(errors, sprintf("Predictor variable '%s' must be numeric.", var))
          }
        }
      }
      
      # Check if grouping variable has at least 2 groups
      if (!is.null(input$discGroup) && input$discGroup != "") {
        groups <- unique(data[[input$discGroup]])
        if (length(groups) < 2) {
          errors <- c(errors, "Grouping variable must have at least 2 distinct groups.")
        }
      }
      
      errors
    })
    
    # Discriminant analysis reactive
    discResult <- eventReactive(input$goDisc, {
      data <- discData()
      req(data, input$discPredictors, input$discGroup, input$discMethod)
      
      # Check if MASS package is available
      if (!requireNamespace("MASS", quietly = TRUE)) {
        stop("Package 'MASS' is required for discriminant analysis.")
      }
      
      # Remove rows with missing values
      complete_data <- data[complete.cases(data[, c(input$discPredictors, input$discGroup)]), ]
      
      if (nrow(complete_data) < 10) {
        stop("Insufficient complete cases for discriminant analysis.")
      }
      
      # Perform discriminant analysis
      discriminant_analysis(complete_data, input$discPredictors, input$discGroup, tolower(input$discMethod))
    })
    
    # Error handling
    output$discError <- renderUI({
      errors <- discValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          discResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "Discriminant Analysis Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$discModelSummary <- renderUI({
      req(discResult())
      res <- discResult()
      
      tagList(
        h4("Discriminant Analysis Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Metric = c("Method", "Number of Groups", "Number of Predictors", "Observations", "Prior Probabilities"),
            Value = c(
              toupper(res$method),
              res$n_groups,
              res$n_predictors,
              res$n_obs,
              paste(round(res$prior_probabilities, 3), collapse = ", ")
            )
          )
        }),
        h4("Discriminant Function Coefficients"),
        renderTable({
          res$coefficients
        }),
        h4("Group Means"),
        renderTable({
          res$group_means
        })
      )
    })
    
    output$discPlot <- renderPlot({
      req(discResult())
      res <- discResult()
      
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # Discriminant scores plot
      if (!is.null(res$discriminant_scores)) {
        plot(res$discriminant_scores, main = "Discriminant Scores",
             xlab = "LD1", ylab = "LD2", pch = 19, col = res$group_colors)
        legend("topright", legend = unique(res$groups), col = unique(res$group_colors), pch = 19)
      }
      
      # Confusion matrix heatmap
      if (!is.null(res$confusion_matrix)) {
        # Create heatmap of confusion matrix
        cm_matrix <- as.matrix(res$confusion_matrix)
        heatmap(cm_matrix, main = "Confusion Matrix", 
                Rowv = NA, Colv = NA, scale = "none", 
                col = colorRampPalette(c("white", "red"))(100))
      }
      
      # Classification accuracy by group
      if (!is.null(res$classification_accuracy)) {
        barplot(res$classification_accuracy$Accuracy, 
                names.arg = res$classification_accuracy$Group,
                main = "Classification Accuracy by Group", 
                ylab = "Accuracy", col = "steelblue")
        abline(h = 0.5, col = "red", lty = 2)
      }
      
      # Predictor importance
      if (!is.null(res$predictor_importance)) {
        barplot(res$predictor_importance$Importance, 
                names.arg = res$predictor_importance$Predictor,
                main = "Predictor Importance", 
                ylab = "Importance", col = "lightgreen", las = 2)
      }
      
      par(mfrow = c(1, 1))
    })
    
    output$discDiagnostics <- renderUI({
      req(discResult())
      res <- discResult()
      
      tagList(
        h4("Model Diagnostics"),
        renderTable({
          data.frame(
            Metric = c("Overall Accuracy", "Cross-validation Accuracy", "Wilks' Lambda", "P-value"),
            Value = c(
              round(res$overall_accuracy, 4),
              round(res$cv_accuracy, 4),
              round(res$wilks_lambda, 4),
              round(res$p_value, 4)
            )
          )
        }),
        h4("Confusion Matrix"),
        renderTable({
          res$confusion_matrix
        }),
        h4("Classification Results"),
        renderTable({
          res$classification_results
        })
      )
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$discDataSummary <- renderUI({
      req(discData(), input$discPredictors, input$discGroup)
      data <- discData()
      predictors <- input$discPredictors
      group_var <- input$discGroup
      
      tagList(
        h4("Dataset Summary"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "Predictors", "Grouping Variable", "Complete Cases"),
            Value = c(
              nrow(data),
              ncol(data),
              length(predictors),
              group_var,
              sum(complete.cases(data[, c(predictors, group_var)]))
            )
          )
        }),
        h4("Group Summary"),
        renderTable({
          group_counts <- table(data[[group_var]])
          data.frame(
            Group = names(group_counts),
            Count = as.numeric(group_counts),
            Proportion = round(as.numeric(prop.table(group_counts)), 4),
            stringsAsFactors = FALSE
          )
        })
      )
    })
    
    output$discAssumptions <- renderUI({
      req(discResult())
      res <- discResult()
      
      tagList(
        h4("Discriminant Analysis Assumptions"),
        renderTable({
          data.frame(
            Assumption = c("Multivariate Normality", "Homogeneity of Covariance", "Linear Relationships", "No Multicollinearity"),
            Status = c(
              ifelse(res$multivariate_normality_test > 0.05, "Pass", "Fail"),
              ifelse(res$homogeneity_test > 0.05, "Pass", "Fail"),
              "Pass",
              ifelse(res$vif_max < 10, "Pass", "Fail")
            ),
            Description = c(
              "Groups follow multivariate normal distribution",
              "Equal covariance matrices across groups",
              "Linear relationships between predictors",
              "No severe multicollinearity among predictors"
            )
          )
        }),
        h4("Model Performance Metrics"),
        renderTable({
          data.frame(
            Metric = c("Overall Accuracy", "Sensitivity", "Specificity", "Precision", "F1-Score"),
            Value = c(
              round(res$overall_accuracy, 4),
              round(res$sensitivity, 4),
              round(res$specificity, 4),
              round(res$precision, 4),
              round(res$f1_score, 4)
            ),
            Interpretation = c(
              "Proportion of correct classifications",
              "True positive rate",
              "True negative rate",
              "Positive predictive value",
              "Harmonic mean of precision and sensitivity"
            )
          )
        })
      )
    })
    
    output$discDiagnosticPlots <- renderPlot({
      req(discResult())
      res <- discResult()
      
      par(mfrow = c(2, 2))
      
      # Q-Q plot of discriminant scores
      if (!is.null(res$discriminant_scores)) {
        qqnorm(res$discriminant_scores, main = "Q-Q Plot of Discriminant Scores")
        qqline(res$discriminant_scores, col = "red")
      }
      
      # Boxplot of discriminant scores by group
      if (!is.null(res$discriminant_scores_by_group)) {
        boxplot(res$discriminant_scores_by_group, main = "Discriminant Scores by Group",
                xlab = "Group", ylab = "Discriminant Score")
      }
      
      # ROC curve
      if (!is.null(res$roc_curve)) {
        plot(res$roc_curve, main = "ROC Curve",
             xlab = "1 - Specificity", ylab = "Sensitivity")
        abline(a = 0, b = 1, lty = 2, col = "gray")
      }
      
      # Predictor distributions by group
      if (!is.null(res$predictor_distributions)) {
        # Plot first predictor distribution by group
        first_pred <- names(res$predictor_distributions)[1]
        if (!is.null(first_pred)) {
          hist(res$predictor_distributions[[first_pred]], 
               main = paste("Distribution of", first_pred, "by Group"),
               xlab = first_pred, freq = FALSE)
        }
      }
      
      par(mfrow = c(1, 1))
    })
    
    # Uploaded Data Tab Outputs
    output$discDataTable <- renderDT({
      req(discData())
      data <- discData()
      
      DT::datatable(
        data,
        options = list(
          pageLength = 10,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
        ),
        extensions = 'Buttons',
        filter = 'top',
        rownames = FALSE
      )
    })
    
    output$discDataInfo <- renderUI({
      req(discData())
      data <- discData()
      
      tagList(
        h4("Dataset Information"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "File Name"),
            Value = c(nrow(data), ncol(data), 
                     ifelse(!is.null(input$discUserData), input$discUserData$name, "Unknown"))
          )
        }),
        h4("Variable Information"),
        renderTable({
          data.frame(
            Variable = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Unique = sapply(data, function(x) length(unique(x)))
          )
        })
      )
    })
  })
} 