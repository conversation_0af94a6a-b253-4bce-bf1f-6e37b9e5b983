# UI Wrapper Function
advancedVisualizationUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(
      selectInput(ns("adv_viz_type"), "Choose Visualization:",
                 choices = c("3D Scatter Plot" = "3d_scatter",
                            "3D Surface Plot" = "3d_surface",
                            "Geographic Map" = "geo_map",
                            "Interactive Network" = "network",
                            "Heatmap Matrix" = "heatmap_matrix",
                            "Parallel Coordinates" = "parallel_coords",
                            "Radar/Spider Chart" = "radar",
                            "Treemap" = "treemap",
                            "Sankey Diagram" = "sankey",
                            "Custom Dashboard" = "dashboard")),
      conditionalPanel(
        condition = paste0("input['", ns("adv_viz_type"), "'] == '3d_scatter' || input['", ns("adv_viz_type"), "'] == '3d_surface'"),
        selectInput(ns("adv_viz_x"), "X Variable:", choices = NULL),
        selectInput(ns("adv_viz_y"), "Y Variable:", choices = NULL),
        selectInput(ns("adv_viz_z"), "Z Variable:", choices = NULL),
        selectInput(ns("adv_viz_color"), "Color Variable (Optional):", choices = c("None" = "none"), selected = "none"),
        selectInput(ns("adv_viz_size"), "Size Variable (Optional):", choices = c("None" = "none"), selected = "none"),
        selectInput(ns("adv_viz_group"), "Group Variable (Optional):", choices = c("None" = "none"), selected = "none")
      ),
      conditionalPanel(
        condition = paste0("input['", ns("adv_viz_type"), "'] == 'geo_map'"),
        selectInput(ns("adv_viz_lat"), "Latitude Variable:", choices = NULL),
        selectInput(ns("adv_viz_lon"), "Longitude Variable:", choices = NULL),
        selectInput(ns("adv_viz_map_color"), "Color Variable:", choices = NULL),
        selectInput(ns("adv_viz_map_size"), "Size Variable:", choices = NULL),
        selectInput(ns("adv_viz_map_type"), "Map Type:", 
                   choices = c("OpenStreetMap" = "osm", "Satellite" = "satellite", "Terrain" = "terrain"))
      ),
      conditionalPanel(
        condition = paste0("input['", ns("adv_viz_type"), "'] == 'network'"),
        selectInput(ns("adv_viz_from"), "From Node:", choices = NULL),
        selectInput(ns("adv_viz_to"), "To Node:", choices = NULL),
        selectInput(ns("adv_viz_edge_weight"), "Edge Weight (Optional):", choices = c("None" = "none"), selected = "none"),
        selectInput(ns("adv_viz_node_group"), "Node Group (Optional):", choices = c("None" = "none"), selected = "none"),
        numericInput(ns("adv_viz_network_layout"), "Layout Algorithm:", value = 1, min = 1, max = 5, step = 1)
      ),
      conditionalPanel(
        condition = paste0("input['", ns("adv_viz_type"), "'] == 'heatmap_matrix'"),
        selectInput(ns("adv_viz_matrix_vars"), "Variables for Matrix:", choices = NULL, multiple = TRUE),
        selectInput(ns("adv_viz_matrix_method"), "Correlation Method:", 
                   choices = c("Pearson" = "pearson", "Spearman" = "spearman", "Kendall" = "kendall"))
      ),
      conditionalPanel(
        condition = paste0("input['", ns("adv_viz_type"), "'] == 'parallel_coords'"),
        selectInput(ns("adv_viz_par_vars"), "Variables for Parallel Coordinates:", choices = NULL, multiple = TRUE),
        selectInput(ns("adv_viz_par_group"), "Group Variable (Optional):", choices = c("None" = "none"), selected = "none")
      ),
      conditionalPanel(
        condition = paste0("input['", ns("adv_viz_type"), "'] == 'radar'"),
        selectInput(ns("adv_viz_radar_vars"), "Variables for Radar Chart:", choices = NULL, multiple = TRUE),
        selectInput(ns("adv_viz_radar_group"), "Group Variable:", choices = NULL)
      ),
      conditionalPanel(
        condition = paste0("input['", ns("adv_viz_type"), "'] == 'treemap'"),
        selectInput(ns("adv_viz_tree_group"), "Group Variable:", choices = NULL),
        selectInput(ns("adv_viz_tree_size"), "Size Variable:", choices = NULL),
        selectInput(ns("adv_viz_tree_color"), "Color Variable (Optional):", choices = c("None" = "none"), selected = "none")
      ),
      conditionalPanel(
        condition = paste0("input['", ns("adv_viz_type"), "'] == 'sankey'"),
        selectInput(ns("adv_viz_sankey_from"), "From Variable:", choices = NULL),
        selectInput(ns("adv_viz_sankey_to"), "To Variable:", choices = NULL),
        selectInput(ns("adv_viz_sankey_value"), "Value Variable:", choices = NULL)
      ),
      textInput(ns("adv_viz_title"), "Title:", value = ""),
      textInput(ns("adv_viz_xlab"), "X Label:", value = ""),
      textInput(ns("adv_viz_ylab"), "Y Label:", value = ""),
      selectInput(ns("adv_viz_theme"), "Theme:", 
                 choices = c("Default" = "default", "Minimal" = "minimal", "Classic" = "classic", "Dark" = "dark")),
      selectInput(ns("adv_viz_palette"), "Color Palette:", 
                 choices = c("Default" = "default", "Viridis" = "viridis", "RColorBrewer" = "brewer", "Rainbow" = "rainbow")),
      numericInput(ns("adv_viz_alpha"), "Transparency:", value = 0.7, min = 0, max = 1, step = 0.1),
      numericInput(ns("adv_viz_width"), "Width (px):", value = 800, min = 200, max = 2000, step = 50),
      numericInput(ns("adv_viz_height"), "Height (px):", value = 600, min = 200, max = 2000, step = 50),
      checkboxInput(ns("adv_viz_interactive"), "Interactive Plot", value = TRUE),
      checkboxInput(ns("adv_viz_legend"), "Show Legend", value = TRUE),
      checkboxInput(ns("adv_viz_grid"), "Show Grid", value = TRUE),
      checkboxInput(ns("adv_viz_axes"), "Show Axes", value = TRUE),
      actionButton(ns("run_adv_viz"), "Generate Visualization", class = "btn-primary btn-lg"),
      actionButton(ns("reset_adv_viz"), "Reset", class = "btn-secondary"),
      downloadButton(ns("download_adv_viz"), "Download Plot")
    ),
    mainPanel(
      conditionalPanel(
        condition = paste0("input['", ns("adv_viz_interactive"), "'] == true"),
        # plotlyOutput(ns("adv_viz_plotly"), height = "600px")
        plotOutput(ns("adv_viz_plot"), height = "600px")
      ),
      conditionalPanel(
        condition = paste0("input['", ns("adv_viz_interactive"), "'] == false"),
        plotOutput(ns("adv_viz_plot"), height = "600px")
      ),
      verbatimTextOutput(ns("adv_viz_summary"))
    )
  )
} 