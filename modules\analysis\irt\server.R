IRTServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    irtData <- eventReactive(input$irtUserData, {
      handle_file_upload(input$irtUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(irtData(), {
      data <- irtData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'irtVars', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    irtValidationErrors <- reactive({
      errors <- c()
      data <- irtData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$irtVars) || length(input$irtVars) < 3) {
        errors <- c(errors, "Select at least three items for IRT analysis.")
      }
      if (nrow(data) < 50) {
        errors <- c(errors, "IRT analysis requires at least 50 respondents.")
      }
      
      # Check if selected variables are numeric
      if (!is.null(input$irtVars) && length(input$irtVars) >= 3) {
        for (var in input$irtVars) {
          if (!is.numeric(data[[var]])) {
            errors <- c(errors, sprintf("Item '%s' must be numeric.", var))
          }
        }
      }
      errors
    })
    
    # IRT analysis reactive
    irtResult <- eventReactive(input$goIRT, {
      data <- irtData()
      req(data, input$irtVars)
      
      # Check if mirt package is available
      if (!requireNamespace("mirt", quietly = TRUE)) {
        stop("Package 'mirt' is required for IRT analysis.")
      }
      
      # Remove rows with missing values
      complete_data <- data[complete.cases(data[, input$irtVars, drop = FALSE]), ]
      
      if (nrow(complete_data) < 50) {
        stop("Insufficient complete cases for IRT analysis.")
      }
      
      # Perform IRT analysis
      irt_model(complete_data, input$irtVars)
    })
    
    # Error handling
    output$irtError <- renderUI({
      errors <- irtValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          irtResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "IRT Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$irtModelSummary <- renderUI({
      req(irtResult())
      res <- irtResult()
      
      tagList(
        h4("IRT Model Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Metric = c("Model Type", "Number of Items", "Number of Respondents", "Log-Likelihood", "AIC", "BIC"),
            Value = c(
              "1-PL (Rasch) Model",
              res$n_items,
              res$n_respondents,
              round(res$log_likelihood, 3),
              round(res$aic, 3),
              round(res$bic, 3)
            )
          )
        }),
        h4("Item Parameters"),
        renderTable({
          res$item_parameters
        }),
        h4("Model Fit Statistics"),
        renderTable({
          res$model_fit
        })
      )
    })
    
    output$irtPlot <- renderPlot({
      req(irtResult())
      res <- irtResult()
      
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # Item characteristic curves
      if (requireNamespace("mirt", quietly = TRUE)) {
        mirt::plot(res$fit, type = "trace", main = "Item Characteristic Curves")
      }
      
      # Item information curves
      if (requireNamespace("mirt", quietly = TRUE)) {
        mirt::plot(res$fit, type = "infotrace", main = "Item Information Curves")
      }
      
      # Test information function
      if (requireNamespace("mirt", quietly = TRUE)) {
        mirt::plot(res$fit, type = "info", main = "Test Information Function")
      }
      
      # Item fit statistics
      if (!is.null(res$item_fit)) {
        plot(res$item_fit$S_X2, main = "Item Fit Statistics (S-X²)",
             xlab = "Item", ylab = "S-X² Statistic", pch = 19)
        abline(h = qchisq(0.95, df = 1), col = "red", lty = 2)
      }
      
      par(mfrow = c(1, 1))
    })
    
    output$irtDiagnostics <- renderUI({
      req(irtResult())
      res <- irtResult()
      
      tagList(
        h4("IRT Diagnostics"),
        renderTable({
          data.frame(
            Metric = c("M2 Statistic", "M2 DF", "M2 P-value", "RMSEA", "CFI", "TLI"),
            Value = c(
              round(res$m2_statistic, 3),
              res$m2_df,
              round(res$m2_p_value, 4),
              round(res$rmsea, 4),
              round(res$cfi, 4),
              round(res$tli, 4)
            )
          )
        }),
        h4("Item Fit Statistics"),
        renderTable({
          res$item_fit
        })
      )
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$irtDataSummary <- renderUI({
      req(irtData(), input$irtVars)
      data <- irtData()
      vars <- input$irtVars
      
      tagList(
        h4("Dataset Summary"),
        renderTable({
          data.frame(
            Metric = c("Number of Respondents", "Number of Items", "Selected Items", "Complete Cases", "Missing Cases"),
            Value = c(
              nrow(data),
              ncol(data),
              length(vars),
              sum(complete.cases(data[, vars, drop = FALSE])),
              sum(!complete.cases(data[, vars, drop = FALSE]))
            )
          )
        }),
        h4("Item Summary"),
        renderTable({
          data.frame(
            Item = vars,
            Mean = sapply(data[, vars, drop = FALSE], mean, na.rm = TRUE),
            SD = sapply(data[, vars, drop = FALSE], sd, na.rm = TRUE),
            Min = sapply(data[, vars, drop = FALSE], min, na.rm = TRUE),
            Max = sapply(data[, vars, drop = FALSE], max, na.rm = TRUE),
            Missing = sapply(data[, vars, drop = FALSE], function(x) sum(is.na(x))),
            stringsAsFactors = FALSE
          )
        })
      )
    })
    
    output$irtAssumptions <- renderUI({
      req(irtResult())
      res <- irtResult()
      
      tagList(
        h4("IRT Assumptions Check"),
        renderTable({
          data.frame(
            Assumption = c("Unidimensionality", "Local Independence", "Monotonicity", "Adequate Sample Size"),
            Status = c(
              ifelse(res$unidimensionality_test > 0.05, "Pass", "Fail"),
              ifelse(res$local_independence_test > 0.05, "Pass", "Fail"),
              "Pass",
              ifelse(res$n_respondents >= 50, "Pass", "Fail")
            ),
            Description = c(
              "Items measure single latent trait",
              "Items are independent given theta",
              "Item response functions are monotonic",
              "Sufficient respondents for stable estimates"
            )
          )
        }),
        h4("Model Selection Criteria"),
        renderTable({
          data.frame(
            Criterion = c("AIC", "BIC", "M2 Statistic", "RMSEA"),
            Value = c(
              round(res$aic, 3),
              round(res$bic, 3),
              round(res$m2_statistic, 3),
              round(res$rmsea, 4)
            ),
            Interpretation = c(
              "Lower is better",
              "Lower is better",
              "Non-significant is better",
              "Lower is better (<0.08)"
            )
          )
        })
      )
    })
    
    output$irtDiagnosticPlots <- renderPlot({
      req(irtResult())
      res <- irtResult()
      
      par(mfrow = c(2, 2))
      
      # Item difficulty distribution
      if (!is.null(res$item_difficulty)) {
        hist(res$item_difficulty, main = "Item Difficulty Distribution",
             xlab = "Difficulty", ylab = "Frequency", col = "lightblue")
      }
      
      # Item discrimination distribution
      if (!is.null(res$item_discrimination)) {
        hist(res$item_discrimination, main = "Item Discrimination Distribution",
             xlab = "Discrimination", ylab = "Frequency", col = "lightgreen")
      }
      
      # Person ability distribution
      if (!is.null(res$person_ability)) {
        hist(res$person_ability, main = "Person Ability Distribution",
             xlab = "Ability (Theta)", ylab = "Frequency", col = "lightcoral")
      }
      
      # Item fit residuals
      if (!is.null(res$item_fit_residuals)) {
        plot(res$item_fit_residuals, main = "Item Fit Residuals",
             xlab = "Item", ylab = "Residual", pch = 19)
        abline(h = 0, col = "red", lty = 2)
      }
      
      par(mfrow = c(1, 1))
    })
    
    # Uploaded Data Tab Outputs
    output$irtDataTable <- renderDT({
      req(irtData())
      data <- irtData()
      
      DT::datatable(
        data,
        options = list(
          pageLength = 10,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
        ),
        extensions = 'Buttons',
        filter = 'top',
        rownames = FALSE
      )
    })
    
    output$irtDataInfo <- renderUI({
      req(irtData())
      data <- irtData()
      
      tagList(
        h4("Dataset Information"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "File Name"),
            Value = c(nrow(data), ncol(data), 
                     ifelse(!is.null(input$irtUserData), input$irtUserData$name, "Unknown"))
          )
        }),
        h4("Variable Information"),
        renderTable({
          data.frame(
            Variable = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Unique = sapply(data, function(x) length(unique(x)))
          )
        })
      )
    })
  })
} 