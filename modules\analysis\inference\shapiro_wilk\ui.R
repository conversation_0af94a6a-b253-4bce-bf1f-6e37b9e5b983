shapiroWilkSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(
      inputId = ns("swUserData"),
      label   = strong("Upload your Data (.csv or .xls or .xlsx or .txt)"),
      accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
    ),
    div(
      id = ns("swUploadInputs"),
      selectizeInput(
        inputId = ns("swVariable"),
        label = strong("Select Variable"),
        choices = c(""),
        selected = NULL,
        options = list(placeholder = 'Select a column', onInitialize = I('function() { this.setValue(\"\"); }'))
      ),
      radioButtons(
        inputId = ns("swSigLvl"),
        label = strong("Significance Level (alpha)"),
        choices = c("10%", "5%", "1%"),
        selected = "5%",
        inline = TRUE
      )
    ),
    br(),
    actionButton(ns("goInference"), label = "Calculate", class = "act-btn")
  )
}

shapiroWilkMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('shapiroWilkResults'))
  )
}

shapiroWilkUI <- function(id) {
  ns <- NS(id)
  tagList(
    shapiroWilkSidebarUI(id),
    shapiroWilkMainUI(id)
  )
} 