# MANOVA calculation and output helpers

manova_uploadData_func <- function(manovaUserData) {
  ext <- tools::file_ext(manovaUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(manovaUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(manovaUserData$datapath),
         xlsx = readxl::read_xlsx(manovaUserData$datapath),
         txt = readr::read_tsv(manovaUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

manova_results_func <- function(data, response_vars, factor_var) {
  tryCatch({
    all_vars <- c(response_vars, factor_var)
    complete_data <- data[complete.cases(data[all_vars]), ]
    
    if (nrow(complete_data) < 10) {
      stop("At least 10 observations are required for MANOVA")
    }
    
    complete_data[[factor_var]] <- as.factor(complete_data[[factor_var]])
    
    formula_str <- paste("cbind(", paste(response_vars, collapse = ", "), ") ~", factor_var)
    model <- manova(as.formula(formula_str), data = complete_data)
    
    list(
      model = model,
      data = complete_data,
      response_vars = response_vars,
      factor_var = factor_var,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during MANOVA calculation:", e$message))
  })
}

manova_ht_html <- function(results, sigLvl) {
  model_summary <- summary(results$model, test = "Pillai")
  p_value <- model_summary$stats[1, "Pr(>F)"]
  
  conclusion <- if (p_value < sigLvl) "significant" else "not significant"
  
  withMathJax(tagList(
    h4("MANOVA Results (Pillai's Trace)"),
    p(sprintf("The effect of the factor (%s) on the set of response variables is %s.", 
              results$factor_var, conclusion))
  ))
}

manova_summary_html <- function(results) {
  univariate_summary <- summary.aov(results$model)
  
  tagList(
    h4("Multivariate Tests"),
    renderTable(summary(results$model)$stats, rownames = TRUE, digits = 4),
    h4("Univariate ANOVA Results"),
    renderPrint(univariate_summary)
  )
}

manova_plot <- function(results) {
  plot_data <- results$data %>%
    tidyr::pivot_longer(cols = all_of(results$response_vars), names_to = "Response", values_to = "Value")
  
  ggplot(plot_data, aes_string(x = results$factor_var, y = "Value", fill = results$factor_var)) +
    geom_boxplot(alpha = 0.7) +
    facet_wrap(~Response, scales = "free_y") +
    labs(title = "Boxplots of Response Variables by Group",
         x = results$factor_var,
         y = "Value") +
    theme_minimal() +
    theme(legend.position = "none")
}