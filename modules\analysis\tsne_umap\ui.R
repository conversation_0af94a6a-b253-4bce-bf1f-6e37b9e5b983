TSNEUMAPUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("embedUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("embedVars"), "Variables for Embedding", choices = NULL, multiple = TRUE),
        selectInput(ns("embedMethod"), "Method", choices = c("t-SNE", "UMAP")),
        numericInput(ns("embedDims"), "Output Dimensions", value = 2, min = 2, max = 3),
        br(),
        actionButton(ns("goEmbed"), label = "Run Embedding", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("embedError")),
        plotOutput(ns("embedPlot")),
        verbatimTextOutput(ns("embedDiag"))
      )
    )
  )
} 