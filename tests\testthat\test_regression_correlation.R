# Test Suite for Regression and Correlation Modules
# Tests: Linear Regression, Logistic Regression, Correlation, Robust Regression, etc.

library(testthat)

# Test Simple Linear Regression Module
test_that("Simple Linear Regression Module", {
  # Load test data
  data <- load_test_data("simple_linear_regression")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test simple linear regression
  if (exists("simple_linear_regression_analysis")) {
    result <- simple_linear_regression_analysis(data, names(data)[1], names(data)[2])
    expect_true(is.list(result))
    expect_true("coefficients" %in% names(result))
    expect_true("r_squared" %in% names(result))
    expect_true("f_statistic" %in% names(result))
    expect_true("p_value" %in% names(result))
  }
  
  cat("  ✓ Simple Linear Regression: Basic regression analysis tested\n")
})

# Test Multiple Linear Regression Module
test_that("Multiple Linear Regression Module", {
  # Load test data
  data <- load_test_data("multiple_linear_regression")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 3)
  
  # Test multiple linear regression
  if (exists("multiple_linear_regression_analysis")) {
    predictors <- names(data)[2:min(4, ncol(data))]
    result <- multiple_linear_regression_analysis(data, names(data)[1], predictors)
    expect_true(is.list(result))
    expect_true("coefficients" %in% names(result))
    expect_true("r_squared" %in% names(result))
    expect_true("adjusted_r_squared" %in% names(result))
    expect_true("f_statistic" %in% names(result))
    expect_true("p_value" %in% names(result))
  }
  
  cat("  ✓ Multiple Linear Regression: Multiple predictor regression tested\n")
})

# Test Logistic Regression Module
test_that("Logistic Regression Module", {
  # Load test data
  data <- load_test_data("logistic_regression")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test logistic regression
  if (exists("logistic_regression_analysis")) {
    result <- logistic_regression_analysis(data, names(data)[1], names(data)[2])
    expect_true(is.list(result))
    expect_true("coefficients" %in% names(result))
    expect_true("odds_ratios" %in% names(result))
    expect_true("aic" %in% names(result))
    expect_true("deviance" %in% names(result))
  }
  
  cat("  ✓ Logistic Regression: Binary outcome regression tested\n")
})

# Test Correlation Analysis Module
test_that("Correlation Analysis Module", {
  # Load test data
  data <- load_test_data("correlation")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test correlation analysis
  if (exists("correlation_analysis")) {
    result <- correlation_analysis(data, names(data)[1], names(data)[2])
    expect_true(is.list(result))
    expect_true("correlation_coefficient" %in% names(result))
    expect_true("p_value" %in% names(result))
    expect_true("confidence_interval" %in% names(result))
  }
  
  cat("  ✓ Correlation Analysis: Correlation coefficient tested\n")
})

# Test Robust Regression Module
test_that("Robust Regression Module", {
  # Load test data
  data <- load_test_data("robust_regression")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test robust regression
  if (exists("robust_regression_analysis")) {
    result <- robust_regression_analysis(data, names(data)[1], names(data)[2])
    expect_true(is.list(result))
    expect_true("coefficients" %in% names(result))
    expect_true("r_squared" %in% names(result))
    expect_true("residuals" %in% names(result))
  }
  
  cat("  ✓ Robust Regression: Outlier-resistant regression tested\n")
})

# Test Poisson Regression Module
test_that("Poisson Regression Module", {
  # Load test data
  data <- load_test_data("poisson_regression")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test Poisson regression
  if (exists("poisson_regression_analysis")) {
    result <- poisson_regression_analysis(data, names(data)[1], names(data)[2])
    expect_true(is.list(result))
    expect_true("coefficients" %in% names(result))
    expect_true("aic" %in% names(result))
    expect_true("deviance" %in% names(result))
  }
  
  cat("  ✓ Poisson Regression: Count data regression tested\n")
})

# Test Quasi Regression Module
test_that("Quasi Regression Module", {
  # Load test data
  data <- load_test_data("quasi_regression")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test quasi regression
  if (exists("quasi_regression_analysis")) {
    result <- quasi_regression_analysis(data, names(data)[1], names(data)[2], family = "quasibinomial")
    expect_true(is.list(result))
    expect_true("coefficients" %in% names(result))
    expect_true("dispersion" %in% names(result))
  }
  
  cat("  ✓ Quasi Regression: Overdispersed data regression tested\n")
})

# Test Zero-Inflated Models Module
test_that("Zero-Inflated Models Module", {
  # Load test data
  data <- load_test_data("zero_inflated")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test zero-inflated models
  if (exists("zero_inflated_analysis")) {
    result <- zero_inflated_analysis(data, names(data)[1], names(data)[2])
    expect_true(is.list(result))
    expect_true("coefficients" %in% names(result))
    expect_true("zero_inflation" %in% names(result))
  }
  
  cat("  ✓ Zero-Inflated Models: Excess zero count data tested\n")
})

# Test Bayesian Regression Module
test_that("Bayesian Regression Module", {
  # Load test data
  data <- load_test_data("bayesian_regression")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test Bayesian regression
  if (exists("bayesian_regression_analysis")) {
    result <- bayesian_regression_analysis(data, names(data)[1], names(data)[2])
    expect_true(is.list(result))
    expect_true("posterior_means" %in% names(result))
    expect_true("credible_intervals" %in% names(result))
  }
  
  cat("  ✓ Bayesian Regression: Bayesian inference tested\n")
})

# Test Survival Analysis Module
test_that("Survival Analysis Module", {
  # Load test data
  data <- load_test_data("survival_analysis")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test survival analysis
  if (exists("survival_analysis_analysis")) {
    result <- survival_analysis_analysis(data, names(data)[1], names(data)[2])
    expect_true(is.list(result))
    expect_true("survival_curve" %in% names(result))
    expect_true("median_survival" %in% names(result))
  }
  
  cat("  ✓ Survival Analysis: Time-to-event analysis tested\n")
})

# Test Cox Proportional Hazards Module
test_that("Cox Proportional Hazards Module", {
  # Load test data
  data <- load_test_data("coxph")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 3)
  
  # Test Cox PH model
  if (exists("coxph_analysis")) {
    result <- coxph_analysis(data, names(data)[1], names(data)[2], names(data)[3])
    expect_true(is.list(result))
    expect_true("hazard_ratios" %in% names(result))
    expect_true("p_values" %in% names(result))
  }
  
  cat("  ✓ Cox Proportional Hazards: Hazard ratio analysis tested\n")
})

# Test Nonlinear Regression Module
test_that("Nonlinear Regression Module", {
  # Load test data
  data <- load_test_data("nonlinear_regression")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test nonlinear regression
  if (exists("nonlinear_regression_analysis")) {
    result <- nonlinear_regression_analysis(data, names(data)[1], names(data)[2], formula = "y ~ a * exp(-b * x)")
    expect_true(is.list(result))
    expect_true("parameters" %in% names(result))
    expect_true("r_squared" %in% names(result))
  }
  
  cat("  ✓ Nonlinear Regression: Nonlinear model fitting tested\n")
})

# Test GAM Module
test_that("Generalized Additive Models Module", {
  # Load test data
  data <- load_test_data("gam")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test GAM
  if (exists("gam_analysis")) {
    result <- gam_analysis(data, names(data)[1], names(data)[2])
    expect_true(is.list(result))
    expect_true("smooth_terms" %in% names(result))
    expect_true("r_squared" %in% names(result))
  }
  
  cat("  ✓ GAM: Generalized additive models tested\n")
})

# Test MANOVA Module
test_that("MANOVA Module", {
  # Load test data
  data <- load_test_data("manova")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 3)
  
  # Test MANOVA
  if (exists("manova_analysis")) {
    response_vars <- names(data)[1:2]
    group_var <- names(data)[3]
    result <- manova_analysis(data, response_vars, group_var)
    expect_true(is.list(result))
    expect_true("pillai_trace" %in% names(result))
    expect_true("p_value" %in% names(result))
  }
  
  cat("  ✓ MANOVA: Multivariate analysis of variance tested\n")
})

# Test Mediation/Moderation Module
test_that("Mediation/Moderation Module", {
  # Load test data
  data <- load_test_data("mediation_moderation")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 3)
  
  # Test mediation analysis
  if (exists("mediation_moderation_analysis")) {
    result <- mediation_moderation_analysis(data, names(data)[1], names(data)[2], names(data)[3])
    expect_true(is.list(result))
    expect_true("direct_effect" %in% names(result))
    expect_true("indirect_effect" %in% names(result))
    expect_true("total_effect" %in% names(result))
  }
  
  cat("  ✓ Mediation/Moderation: Mediation analysis tested\n")
})

# Test Propensity Score Module
test_that("Propensity Score Module", {
  # Load test data
  data <- load_test_data("propensity_score")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 3)
  
  # Test propensity score analysis
  if (exists("propensity_score_analysis")) {
    result <- propensity_score_analysis(data, names(data)[1], names(data)[2], names(data)[3:min(5, ncol(data))])
    expect_true(is.list(result))
    expect_true("propensity_scores" %in% names(result))
    expect_true("balance_statistics" %in% names(result))
  }
  
  cat("  ✓ Propensity Score: Causal inference tested\n")
})

# Test Bayesian Hierarchical Module
test_that("Bayesian Hierarchical Module", {
  # Load test data
  data <- load_test_data("bayesian_hierarchical")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 3)
  
  # Test Bayesian hierarchical model
  if (exists("bayesian_hierarchical_analysis")) {
    result <- bayesian_hierarchical_analysis(data, names(data)[1], names(data)[2], names(data)[3])
    expect_true(is.list(result))
    expect_true("group_effects" %in% names(result))
    expect_true("overall_effect" %in% names(result))
  }
  
  cat("  ✓ Bayesian Hierarchical: Multilevel modeling tested\n")
})

cat("Regression and Correlation Modules: All tests completed\n") 