run_feature_selection <- function(data, response, predictors, method = "lasso") {
  tryCatch({
    x <- as.matrix(data[, predictors, drop = FALSE])
    y <- data[[response]]
    if (method == "lasso") {
      if (!requireNamespace("glmnet", quietly = TRUE)) stop("Package 'glmnet' required.")
      fit <- glmnet::cv.glmnet(x, y, alpha = 1)
      coefs <- coef(fit, s = "lambda.min")
      selected <- rownames(coefs)[as.numeric(coefs) != 0]
      plot_obj <- plot(fit)
      list(
        method = "lasso",
        selected = selected,
        coefficients = as.matrix(coefs),
        plot = plot_obj,
        error = NULL
      )
    } else if (method == "stepwise") {
      if (!requireNamespace("MASS", quietly = TRUE)) stop("Package 'MASS' required.")
      form <- as.formula(paste(response, "~", paste(predictors, collapse = "+")))
      fit <- MASS::stepAIC(lm(form, data = data), direction = "both", trace = FALSE)
      selected <- names(coef(fit))[-1]
      list(
        method = "stepwise",
        selected = selected,
        coefficients = coef(fit),
        plot = NULL,
        error = NULL
      )
    } else {
      stop("Unknown method.")
    }
  }, error = function(e) {
    list(selected = NULL, coefficients = NULL, plot = NULL, error = e$message)
  })
} 