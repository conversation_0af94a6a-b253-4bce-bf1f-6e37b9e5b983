jarqueBeraServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactives
    jbUploadData <- eventReactive(input$jbUserData, {
      handle_file_upload(input$jbUserData)
    })
    
    jbResults <- reactive({
      data <- jbUploadData()
      if (is.null(data) || is.null(input$jbVariable) || input$jbVariable == "") {
        return(NULL)
      }
      
      values <- data[[input$jbVariable]]
      values <- values[!is.na(values)]
      
      if (length(values) < 3) return(NULL)
      
      list(values = values, n = length(values))
    })
    
    # Validation errors
    jbValidationErrors <- reactive({
      errors <- c()
      data <- jbUploadData()
      
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$jbVariable) || input$jbVariable == "") {
        errors <- c(errors, "Please select a variable.")
        return(errors)
      }
      
      values <- data[[input$jbVariable]]
      if (length(na.omit(values)) < 3) {
        errors <- c(errors, "At least 3 non-missing values are required.")
      }
      
      errors
    })
    
    # Outputs
    output$jbHT <- renderUI({
      results <- jbResults()
      if (is.null(results)) return(NULL)
      jarqueBeraHT(jbResults, reactive({input$jbSigLvl}))
    })
    
    output$jarqueBeraPlot <- renderPlot({
      results <- jbResults()
      if (is.null(results)) return(NULL)
      jarqueBeraPlot(jbResults)
    })
    
    output$jbConclusionOutput <- renderUI({
      results <- jbResults()
      if (is.null(results)) return(NULL)
      jbConclusion(jbResults, reactive({input$jbSigLvl}))
    })
    
    output$renderJBData <- renderUI({
      req(jbUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("jbInitialUploadTable")), style = "width: 75%"),
        br(),
        br()
      )
    })
    
    output$jbInitialUploadTable <- DT::renderDT({
      req(jbUploadData())
      DT::datatable(jbUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(jbUploadData())))))
    })
    
    # Update selectizeInput choices after file upload
    observeEvent(jbUploadData(), {
      data <- jbUploadData()
      updateSelectizeInput(session, 'jbVariable', choices = character(0), selected = NULL, server = TRUE)
      output$jarqueBeraResults <- renderUI({ NULL })
      
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'jbVariable', choices = names(data), server = TRUE)
        output$jarqueBeraResults <- renderUI({
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('jbPreviewTable'))
          )
        })
        output$jbPreviewTable <- DT::renderDT({
          head(data, 20)
        })
      }
    })
    
    # Show main results or error screen when Calculate is pressed
    observeEvent(input$goInference, {
      output$jarqueBeraResults <- renderUI({
        errors <- jbValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Jarque-Bera Test", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("jbTabset"),
              selected = "Analysis",
              tabPanel(
                id    = ns("jb"),
                title = "Analysis",
                titlePanel("Jarque-Bera Test for Normality"),
                br(),
                uiOutput(ns('jbHT')),
                br(),
                plotOutput(ns('jarqueBeraPlot'), width = "50%", height = "400px"),
                br(),
                uiOutput(ns('jbConclusionOutput'))
              ),
              tabPanel(
                id    = ns("jbData"),
                title = "Uploaded Data",
                uiOutput(ns("renderJBData"))
              )
            )
          )
        }
      })
    })
  })
} 