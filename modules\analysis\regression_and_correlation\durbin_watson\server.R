durbinWatsonServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactives
    dwUploadData <- eventReactive(input$dwUserData, {
      handle_file_upload(input$dwUserData)
    })
    
    dwResults <- reactive({
      data <- dwUploadData()
      if (is.null(data) || is.null(input$dwResponse) || input$dwResponse == "" ||
          is.null(input$dwPredictors) || length(input$dwPredictors) == 0) {
        return(NULL)
      }
      
      # Check if all selected variables exist in the data
      all_vars <- c(input$dwResponse, input$dwPredictors)
      if (!all(all_vars %in% names(data))) {
        return(NULL)
      }
      
      # Remove rows with missing values
      complete_data <- data[complete.cases(data[all_vars]), ]
      
      if (nrow(complete_data) < 3) return(NULL)
      
      list(data = complete_data, response = input$dwResponse, 
           predictors = input$dwPredictors, n = nrow(complete_data))
    })
    
    # Validation errors
    dwValidationErrors <- reactive({
      errors <- c()
      data <- dwUploadData()
      
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$dwResponse) || input$dwResponse == "") {
        errors <- c(errors, "Please select a response variable.")
        return(errors)
      }
      
      if (is.null(input$dwPredictors) || length(input$dwPredictors) == 0) {
        errors <- c(errors, "Please select at least one predictor variable.")
        return(errors)
      }
      
      # Check if variables exist in data
      all_vars <- c(input$dwResponse, input$dwPredictors)
      missing_vars <- all_vars[!all_vars %in% names(data)]
      if (length(missing_vars) > 0) {
        errors <- c(errors, sprintf("Variables not found in data: %s", paste(missing_vars, collapse = ", ")))
      }
      
      # Check for sufficient observations
      if (!is.null(input$dwResponse) && !is.null(input$dwPredictors) && 
          length(input$dwPredictors) > 0) {
        complete_data <- data[complete.cases(data[all_vars]), ]
        if (nrow(complete_data) < 3) {
          errors <- c(errors, "At least 3 complete observations are required.")
        }
      }
      
      errors
    })
    
    # Outputs
    output$dwHT <- renderUI({
      results <- dwResults()
      if (is.null(results)) return(NULL)
      durbinWatsonHT(dwResults, reactive({input$dwSigLvl}))
    })
    
    output$durbinWatsonPlot <- renderPlot({
      results <- dwResults()
      if (is.null(results)) return(NULL)
      durbinWatsonPlot(dwResults)
    })
    
    output$dwConclusionOutput <- renderUI({
      results <- dwResults()
      if (is.null(results)) return(NULL)
      dwConclusion(dwResults, reactive({input$dwSigLvl}))
    })
    
    output$renderDWData <- renderUI({
      req(dwUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("dwInitialUploadTable")), style = "width: 75%"),
        br(),
        br()
      )
    })
    
    output$dwInitialUploadTable <- DT::renderDT({
      req(dwUploadData())
      DT::datatable(dwUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(dwUploadData())))))
    })
    
    # Update selectizeInput choices after file upload
    observeEvent(dwUploadData(), {
      data <- dwUploadData()
      updateSelectizeInput(session, 'dwResponse', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'dwPredictors', choices = character(0), selected = NULL, server = TRUE)
      output$durbinWatsonResults <- renderUI({ NULL })
      
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'dwResponse', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'dwPredictors', choices = names(data), server = TRUE)
        output$durbinWatsonResults <- renderUI({
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('dwPreviewTable'))
          )
        })
        output$dwPreviewTable <- DT::renderDT({
          head(data, 20)
        })
      }
    })
    
    # Show main results or error screen when Calculate is pressed
    observeEvent(input$goInference, {
      output$durbinWatsonResults <- renderUI({
        errors <- dwValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Durbin-Watson Test", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("dwTabset"),
              selected = "Analysis",
              tabPanel(
                id    = ns("dw"),
                title = "Analysis",
                titlePanel("Durbin-Watson Test for Autocorrelation"),
                br(),
                uiOutput(ns('dwHT')),
                br(),
                plotOutput(ns('durbinWatsonPlot'), width = "50%", height = "400px"),
                br(),
                uiOutput(ns('dwConclusionOutput'))
              ),
              tabPanel(
                id    = ns("dwData"),
                title = "Uploaded Data",
                uiOutput(ns("renderDWData"))
              )
            )
          )
        }
      })
    })
  })
} 