# Two-sample inference calculation and output helpers

two_sample_uploadData_func <- function(tsUserData) {
  ext <- tools::file_ext(tsUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(tsUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(tsUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(tsUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(tsUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

two_sample_results_func <- function(data, var1, var2, sigma1 = NULL, sigma2 = NULL, var_equal = FALSE, alternative = "two.sided", conf_level = 0.95) {
  tryCatch({
    x <- data[[var1]]
    y <- data[[var2]]
    
    x <- x[!is.na(x)]
    y <- y[!is.na(y)]
    
    if (length(x) < 2 || length(y) < 2) {
      stop("Each sample must have at least 2 observations.")
    }
    
    if (!is.null(sigma1) && !is.null(sigma2)) { # Z-test
      test_result <- BSDA::z.test(x, y, sigma.x = sigma1, sigma.y = sigma2, alternative = alternative, conf.level = conf_level)
    } else { # T-test
      test_result <- t.test(x, y, var.equal = var_equal, alternative = alternative, conf.level = conf_level)
    }
    
    list(
      test = test_result,
      data1 = x,
      data2 = y,
      var1 = var1,
      var2 = var2,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during two-sample test calculation:", e$message))
  })
}

two_sample_ht_html <- function(results, sigLvl) {
  test <- results$test
  p_value <- test$p.value
  
  conclusion <- if (p_value < sigLvl) "significant" else "not significant"
  
  withMathJax(tagList(
    h4("Two-Sample Test"),
    p(sprintf("The difference between the means of %s and %s is %s.", results$var1, results$var2, conclusion)),
    p(sprintf("Difference in means: %.4f", test$estimate[1] - test$estimate[2])),
    p(sprintf("Test Statistic: %.4f", test$statistic)),
    p(sprintf("P-value: %.4f", p_value))
  ))
}

two_sample_summary_html <- function(results) {
  test <- results$test
  
  desc_stats <- data.frame(
    Variable = c(results$var1, results$var2),
    N = c(length(results$data1), length(results$data2)),
    Mean = c(mean(results$data1), mean(results$data2)),
    SD = c(sd(results$data1), sd(results$data2))
  )
  
  tagList(
    h4("Test Details"),
    renderPrint(test),
    h4("Descriptive Statistics"),
    renderTable(desc_stats, digits = 4)
  )
}

two_sample_plot <- function(results) {
  plot_data <- data.frame(
    Value = c(results$data1, results$data2),
    Group = rep(c(results$var1, results$var2), c(length(results$data1), length(results$data2)))
  )
  
  ggplot(plot_data, aes(x = Group, y = Value, fill = Group)) +
    geom_boxplot(alpha = 0.7)) +
    labs(title = "Boxplot of Samples",
         x = "Group",
         y = "Value") +
    theme_minimal() +
    theme(legend.position = "none")
}