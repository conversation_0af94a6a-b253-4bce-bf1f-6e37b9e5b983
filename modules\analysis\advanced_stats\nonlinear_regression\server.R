NonlinearRegressionServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    nlrData <- eventReactive(input$nlrUserData, {
      handle_file_upload(input$nlrUserData)
    })
    
    observeEvent(nlrData(), {
      data <- nlrData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'nlrResponse', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'nlrPredictors', choices = names(data), server = TRUE)
      }
    })
    
    nlrValidationErrors <- reactive({
      errors <- c()
      data <- nlrData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$nlrResponse)) {
        errors <- c(errors, "Select a response variable.")
      }
      if (is.null(input$nlrPredictors) || length(input$nlrPredictors) < 1) {
        errors <- c(errors, "Select at least one predictor variable.")
      }
      if (!is.null(input$nlrResponse) && !is.numeric(data[[input$nlrResponse]])) {
        errors <- c(errors, "Response variable must be numeric.")
      }
      for (var in input$nlrPredictors) {
        if (!is.numeric(data[[var]])) {
          errors <- c(errors, sprintf("Predictor variable '%s' must be numeric.", var))
        }
      }
      errors
    })
    
    nlrResult <- eventReactive(input$goNLR, {
      data <- nlrData()
      req(data, input$nlrResponse, input$nlrPredictors)
      
      # Get parameters from UI
      formula_str <- ifelse(is.null(input$nlrFormula) || input$nlrFormula == "", NULL, input$nlrFormula)
      start_values <- ifelse(is.null(input$nlrStart) || input$nlrStart == "", NULL, input$nlrStart)
      
      nonlinear_regression_analysis(data, input$nlrResponse, input$nlrPredictors, 
                                  formula_str = formula_str, start_values = start_values)
    })
    
    observeEvent(input$goNLR, {
      output$nlrResultsUI <- renderUI({
        errors <- nlrValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Nonlinear Regression", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("nlrTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("nlrAnalysis"),
                title = "Analysis",
                titlePanel("Nonlinear Regression Results"),
                br(),
                h4("Model Formula"),
                textOutput(ns('nlrFormula')),
                h4("Coefficients"),
                tableOutput(ns('nlrCoefficients')),
                h4("Confidence Intervals"),
                tableOutput(ns('nlrConfidenceIntervals')),
                h4("Fit Statistics"),
                tableOutput(ns('nlrFitStats')),
                h4("Residual Statistics"),
                tableOutput(ns('nlrResidualStats')),
                h4("Convergence Information"),
                tableOutput(ns('nlrConvergence')),
                h4("Number of Parameters"),
                textOutput(ns('nlrParameters')),
                h4("Number of Observations"),
                textOutput(ns('nlrObservations')),
                h4("R-squared Interpretation"),
                textOutput(ns('nlrRSquared')),
                h4("Convergence Status"),
                textOutput(ns('nlrConvergenceStatus'))
              ),
              tabPanel(
                id = ns("nlrDiagnostics"),
                title = "Model Diagnostics",
                h4("Residuals vs Fitted Plot"),
                plotOutput(ns('nlrResidualPlot'), height = "300px"),
                h4("Q-Q Plot of Residuals"),
                plotOutput(ns('nlrQQPlot'), height = "300px"),
                h4("Observed vs Fitted Plot"),
                plotOutput(ns('nlrObservedFittedPlot'), height = "300px"),
                h4("Model Fit Plot (Single Predictor)"),
                plotOutput(ns('nlrModelPlot'), height = "300px")
              ),
              tabPanel(
                id = ns("nlrUploadedData"),
                title = "Uploaded Data",
                h4("Raw Uploaded Data"),
                DT::DTOutput(ns('nlrRawDataTable'))
              )
            )
          )
        }
      })
    })
    output$nlrRawDataTable <- DT::renderDT({
      req(nlrData())
      DT::datatable(nlrData(), options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), rownames = FALSE)
    })
    
    observeEvent(input$goNLR, {
      output$nlrError <- renderUI({
        tryCatch({ nlrResult(); NULL }, error = function(e) errorScreenUI(title = "Nonlinear Regression Error", errors = e$message))
      })
      
      # Model coefficients
      output$nlrCoefficients <- renderTable({
        res <- tryCatch(nlrResult(), error = function(e) NULL)
        if (is.null(res)) return(NULL)
        res$coefficients
      }, rownames = FALSE, digits = 4)
      
      # Confidence intervals
      output$nlrConfidenceIntervals <- renderTable({
        res <- tryCatch(nlrResult(), error = function(e) NULL)
        if (is.null(res)) return(NULL)
        res$confidence_intervals
      }, rownames = FALSE, digits = 4)
      
      # Model fit statistics
      output$nlrFitStats <- renderTable({
        res <- tryCatch(nlrResult(), error = function(e) NULL)
        if (is.null(res)) return(NULL)
        res$fit_statistics
      }, rownames = FALSE, digits = 4)
      
      # Residual statistics
      output$nlrResidualStats <- renderTable({
        res <- tryCatch(nlrResult(), error = function(e) NULL)
        if (is.null(res)) return(NULL)
        res$residual_statistics
      }, rownames = FALSE, digits = 4)
      
      # Convergence information
      output$nlrConvergence <- renderTable({
        res <- tryCatch(nlrResult(), error = function(e) NULL)
        if (is.null(res)) return(NULL)
        res$convergence
      }, rownames = FALSE, digits = 4)
      
      # Model formula
      output$nlrFormula <- renderText({
        res <- tryCatch(nlrResult(), error = function(e) NULL)
        if (is.null(res)) return(NULL)
        paste("Model formula:", res$formula)
      })
      
      # Number of parameters
      output$nlrParameters <- renderText({
        res <- tryCatch(nlrResult(), error = function(e) NULL)
        if (is.null(res)) return(NULL)
        paste("Number of parameters:", res$n_parameters)
      })
      
      # Number of observations
      output$nlrObservations <- renderText({
        res <- tryCatch(nlrResult(), error = function(e) NULL)
        if (is.null(res)) return(NULL)
        paste("Number of observations:", res$n_observations)
      })
      
      # R-squared interpretation
      output$nlrRSquared <- renderText({
        res <- tryCatch(nlrResult(), error = function(e) NULL)
        if (is.null(res)) return(NULL)
        
        r_squared <- res$r_squared
        if (!is.na(r_squared)) {
          if (r_squared < 0.3) fit_desc <- "poor"
          else if (r_squared < 0.5) fit_desc <- "fair"
          else if (r_squared < 0.7) fit_desc <- "moderate"
          else if (r_squared < 0.9) fit_desc <- "good"
          else fit_desc <- "excellent"
          paste("Model fit:", fit_desc, "(R² =", round(r_squared, 4), ")")
        } else {
          "Model fit information not available"
        }
      })
      
      # Convergence status
      output$nlrConvergenceStatus <- renderText({
        res <- tryCatch(nlrResult(), error = function(e) NULL)
        if (is.null(res)) return(NULL)
        
        converged <- res$convergence$Value[res$convergence$Metric == "Converged"]
        if (!is.na(converged) && converged) {
          "Model converged successfully"
        } else {
          "Model did not converge - results may be unreliable"
        }
      })
      
      # Residuals vs fitted plot
      output$nlrResidualPlot <- renderPlot({
        res <- tryCatch(nlrResult(), error = function(e) NULL)
        if (is.null(res)) return(NULL)
        
        pred_data <- res$predictions
        ggplot2::ggplot(pred_data, ggplot2::aes(x = fitted, y = residuals)) +
          ggplot2::geom_point(alpha = 0.6) +
          ggplot2::geom_hline(yintercept = 0, color = "red", linetype = "dashed") +
          ggplot2::geom_smooth(method = "loess", se = TRUE, color = "blue") +
          ggplot2::labs(title = "Residuals vs Fitted Values",
                       x = "Fitted Values", y = "Residuals") +
          ggplot2::theme_minimal()
      })
      
      # Q-Q plot of residuals
      output$nlrQQPlot <- renderPlot({
        res <- tryCatch(nlrResult(), error = function(e) NULL)
        if (is.null(res)) return(NULL)
        
        residuals <- res$predictions$residuals
        theoretical_quantiles <- qnorm(ppoints(length(residuals)))
        
        qq_data <- data.frame(
          Theoretical = theoretical_quantiles,
          Sample = sort(residuals)
        )
        
        ggplot2::ggplot(qq_data, ggplot2::aes(x = Theoretical, y = Sample)) +
          ggplot2::geom_point(alpha = 0.6) +
          ggplot2::geom_abline(intercept = 0, slope = 1, color = "red", linetype = "dashed") +
          ggplot2::labs(title = "Q-Q Plot of Residuals",
                       x = "Theoretical Quantiles", y = "Sample Quantiles") +
          ggplot2::theme_minimal()
      })
      
      # Observed vs fitted plot
      output$nlrObservedFittedPlot <- renderPlot({
        res <- tryCatch(nlrResult(), error = function(e) NULL)
        if (is.null(res)) return(NULL)
        
        pred_data <- res$predictions
        ggplot2::ggplot(pred_data, ggplot2::aes(x = response, y = fitted)) +
          ggplot2::geom_point(alpha = 0.6) +
          ggplot2::geom_abline(intercept = 0, slope = 1, color = "red", linetype = "dashed") +
          ggplot2::geom_smooth(method = "lm", se = TRUE, color = "blue") +
          ggplot2::labs(title = "Observed vs Fitted Values",
                       x = "Observed Values", y = "Fitted Values") +
          ggplot2::theme_minimal()
      })
      
      # Model fit plot (for single predictor)
      output$nlrModelPlot <- renderPlot({
        res <- tryCatch(nlrResult(), error = function(e) NULL)
        if (is.null(res)) return(NULL)
        
        pred_data <- res$predictions
        
        # Check if we have predictor data for plotting
        if (length(input$nlrPredictors) == 1) {
          predictor_name <- input$nlrPredictors[1]
          pred_data[[predictor_name]] <- nlrData()[[predictor_name]]
          
          ggplot2::ggplot(pred_data, ggplot2::aes_string(x = predictor_name, y = "response")) +
            ggplot2::geom_point(alpha = 0.6, color = "blue") +
            ggplot2::geom_line(ggplot2::aes_string(x = predictor_name, y = "fitted"), 
                             color = "red", size = 1) +
            ggplot2::labs(title = "Nonlinear Regression Fit",
                         x = predictor_name, y = "Response") +
            ggplot2::theme_minimal()
        } else {
          # Multiple predictors - show 3D plot or just fitted vs observed
          ggplot2::ggplot(pred_data, ggplot2::aes(x = response, y = fitted)) +
            ggplot2::geom_point(alpha = 0.6) +
            ggplot2::geom_abline(intercept = 0, slope = 1, color = "red", linetype = "dashed") +
            ggplot2::labs(title = "Model Fit - Multiple Predictors",
                         x = "Observed Values", y = "Fitted Values") +
            ggplot2::theme_minimal()
        }
      })
      
      # Parameter interpretation
      output$nlrParameterInterpretation <- renderText({
        res <- tryCatch(nlrResult(), error = function(e) NULL)
        if (is.null(res)) return(NULL)
        
        coef_table <- res$coefficients
        if (nrow(coef_table) > 0) {
          interpretations <- sapply(1:nrow(coef_table), function(i) {
            param <- coef_table$Parameter[i]
            estimate <- coef_table$Estimate[i]
            p_value <- coef_table$p_value[i]
            
            significance <- ifelse(p_value < 0.001, "highly significant",
                                 ifelse(p_value < 0.01, "very significant",
                                       ifelse(p_value < 0.05, "significant",
                                             ifelse(p_value < 0.1, "marginally significant", "not significant"))))
            
            paste(param, ":", round(estimate, 4), "(", significance, ", p =", round(p_value, 4), ")")
          })
          
          paste("Parameter interpretations:", paste(interpretations, collapse = "; "))
        } else {
          "Parameter interpretations not available"
        }
      })
    })
    
    # Model diagnostics summary
    output$nlrDiagnostics <- renderText({
      res <- nlrResult()
      if (is.null(res)) return(NULL)
      
      r_squared <- res$r_squared
      adj_r_squared <- res$adj_r_squared
      aic <- res$aic
      bic <- res$bic
      
      paste("Model Diagnostics: R² =", round(r_squared, 4), 
            ", Adjusted R² =", round(adj_r_squared, 4),
            ", AIC =", round(aic, 4), ", BIC =", round(bic, 4))
    })
  })
} 