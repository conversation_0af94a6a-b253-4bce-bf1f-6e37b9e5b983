# Reliability Analysis Server
# <PERSON>ronbach's alpha, split-half reliability, item analysis

source("modules/calculations/reliability_analysis.R")

ReliabilityAnalysisServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactive values
    reliability_data <- reactiveVal(NULL)
    reliability_results <- reactiveVal(NULL)
    
    # File upload reactive
    reliabilityUploadData <- eventReactive(input$reliabilityUserData, {
      handle_file_upload(input$reliabilityUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(reliabilityUploadData(), {
      data <- reliabilityUploadData()
      # Clear selectizeInputs and main panel
      updateSelectizeInput(session, 'reliabilityItems', choices = character(0), selected = NULL, server = TRUE)
      output$reliabilityResults <- renderUI({ NULL })
      
      # If data is valid, update choices
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'reliabilityItems', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    reliabilityValidationErrors <- reactive({
      errors <- c()
      
      if (input$reliabilityDataMethod == "Manual Entry") {
        # Manual entry validation
        if (is.null(input$reliabilityManualData) || input$reliabilityManualData == "") {
          errors <- c(errors, "Manual data entry is required.")
        } else {
          # Basic validation for manual entry
          tryCatch({
            data <- parse_reliability_data(input$reliabilityManualData)
            if (nrow(data) < 10) {
              errors <- c(errors, "At least 10 observations are required for reliability analysis.")
            }
            if (ncol(data) < 2) {
              errors <- c(errors, "At least 2 items are required for reliability analysis.")
            }
          }, error = function(e) {
            errors <- c(errors, "Invalid manual data format. Please check your input.")
          })
        }
      } else {
        # File upload validation
        data <- reliabilityUploadData()
        if (is.null(data) || !is.data.frame(data)) {
          errors <- c(errors, "No data uploaded or file could not be read.")
          return(errors)
        }
        if (is.null(input$reliabilityItems) || length(input$reliabilityItems) < 2) {
          errors <- c(errors, "Please select at least 2 items for reliability analysis.")
        } else {
          for (item in input$reliabilityItems) {
            item_data <- data[[item]]
            if (!is.numeric(item_data)) {
              errors <- c(errors, paste("Item", item, "must be numeric."))
            }
          }
          if (length(na.omit(data[input$reliabilityItems])) < 10) {
            errors <- c(errors, "At least 10 non-missing observations are required.")
          }
        }
      }
      
      errors
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goReliability, {
      output$reliabilityResults <- renderUI({
        errors <- reliabilityValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Reliability Analysis", errors = errors)
        } else {
          tryCatch({
            # Prepare data
            if (input$reliabilityDataMethod == "Manual Entry") {
              # Parse manual entry data
              data <- parse_reliability_data(input$reliabilityManualData)
            } else {
              # Use uploaded data
              req(reliabilityUploadData(), input$reliabilityItems)
              
              data <- reliabilityUploadData()
              items <- input$reliabilityItems
              
              # Select relevant columns
              data <- data[items]
            }
            
            # Remove rows with missing values
            complete_cases <- complete.cases(data)
            if (sum(complete_cases) < nrow(data)) {
              warning("Removing rows with missing values")
              data <- data[complete_cases, ]
            }
            
            # Check data structure
            if (ncol(data) < 2) {
              stop("At least 2 items are required for reliability analysis")
            }
            
            if (nrow(data) < 10) {
              stop("At least 10 observations are required")
            }
            
            # Perform reliability analysis
            results <- perform_reliability_analysis(data, 
                                                  cronbach_alpha = input$reliabilityCronbachAlpha,
                                                  split_half = input$reliabilitySplitHalf,
                                                  item_analysis = input$reliabilityItemAnalysis,
                                                  inter_item = input$reliabilityInterItem,
                                                  split_method = input$reliabilitySplitMethod,
                                                  conf_level = input$reliabilityConfLevel)
            
            # Store results
            reliability_results(results)
            
            # Display results
            tagList(
              h3("Reliability Analysis Results"),
              
              # Data summary
              h4("Data Summary"),
              renderTable({
                summary_table <- data.frame(
                  Statistic = c("Number of Items", "Number of Observations", "Missing Values"),
                  Value = c(results$n_items, results$n_observations, results$n_missing)
                )
                summary_table
              }, rownames = FALSE),
              
              br(),
              
              # Cronbach's Alpha
              if (!is.null(results$cronbach_alpha)) tagList(
                h4("Cronbach's Alpha"),
                renderTable({
                  alpha_table <- results$cronbach_alpha
                  alpha_table
                }, rownames = FALSE),
                br()
              ),
              
              # Split-half reliability
              if (!is.null(results$split_half)) tagList(
                h4("Split-Half Reliability"),
                renderDataTable({
                  split_table <- results$split_half
                  datatable(split_table, 
                           options = list(pageLength = 10, scrollX = TRUE),
                           rownames = FALSE) %>%
                    formatRound(columns = c("Correlation", "Spearman_Brown", "Guttman_Split_Half"), digits = 4)
                }),
                br()
              ),
              
              # Item analysis
              if (!is.null(results$item_analysis)) tagList(
                h4("Item Analysis"),
                renderDataTable({
                  item_table <- results$item_analysis
                  datatable(item_table, 
                           options = list(pageLength = 10, scrollX = TRUE),
                           rownames = FALSE) %>%
                    formatRound(columns = c("Mean", "SD", "Item_Total_Correlation", "Alpha_If_Deleted"), digits = 4)
                }),
                br()
              ),
              
              # Inter-item correlations
              if (!is.null(results$inter_item_correlations)) tagList(
                h4("Inter-Item Correlations"),
                renderDataTable({
                  cor_table <- results$inter_item_correlations
                  datatable(cor_table, 
                           options = list(pageLength = 10, scrollX = TRUE),
                           rownames = FALSE) %>%
                    formatRound(columns = c("Correlation", "p_value"), digits = 4)
                }),
                br()
              ),
              
              # Reliability visualization
              h4("Reliability Visualization"),
              fluidRow(
                column(6,
                  renderPlot({
                    if (!is.null(results$item_analysis)) {
                      ggplot(results$item_analysis, aes(x = reorder(Item, Item_Total_Correlation), y = Item_Total_Correlation)) +
                        geom_bar(stat = "identity", fill = "steelblue") +
                        coord_flip() +
                        labs(title = "Item-Total Correlations",
                             x = "Item", y = "Item-Total Correlation") +
                        theme_minimal()
                    }
                  })
                ),
                column(6,
                  renderPlot({
                    if (!is.null(results$cronbach_alpha)) {
                      # Create alpha plot
                      alpha_values <- seq(0.5, 1, by = 0.05)
                      alpha_interpretation <- cut(alpha_values, 
                                                breaks = c(0, 0.6, 0.7, 0.8, 0.9, 1),
                                                labels = c("Poor", "Questionable", "Acceptable", "Good", "Excellent"))
                      
                      alpha_df <- data.frame(Alpha = alpha_values, Interpretation = alpha_interpretation)
                      
                      ggplot(alpha_df, aes(x = Alpha, y = 1, fill = Interpretation)) +
                        geom_tile() +
                        geom_vline(xintercept = results$cronbach_alpha$Alpha, color = "red", size = 2) +
                        scale_fill_brewer(palette = "RdYlGn") +
                        labs(title = "Cronbach's Alpha Interpretation",
                             x = "Alpha Value", y = "") +
                        theme_minimal() +
                        theme(axis.text.y = element_blank(), axis.ticks.y = element_blank())
                    }
                  })
                )
              ),
              
              br(),
              
              # Scale statistics
              h4("Scale Statistics"),
              renderTable({
                scale_table <- results$scale_stats
                scale_table
              }, rownames = FALSE),
              
              br(),
              
              # Educational content
              h4("Interpretation"),
              p(strong("Cronbach's Alpha:"), "Measures internal consistency reliability. Values above 0.7 are generally acceptable."),
              p(strong("Split-Half Reliability:"), "Measures reliability by splitting items into two halves and correlating scores."),
              p(strong("Item Analysis:"), "Examines how each item contributes to the overall scale reliability."),
              p(strong("Inter-Item Correlations:"), "Shows relationships between individual items.")
            )
          }, error = function(e) {
            errorScreenUI(title = "Error in Reliability Analysis", errors = e$message)
          })
        }
      })
    })
    
    # Helper function to parse numeric input
    parse_reliability_data <- function(input_text) {
      if (is.null(input_text) || input_text == "") {
        stop("Please enter data")
      }
      
      # Parse the input text into a data frame
      lines <- strsplit(input_text, "\n")[[1]]
      lines <- lines[lines != ""]
      
      if (length(lines) < 2) {
        stop("At least 2 lines required (header + data)")
      }
      
      # Parse header
      header <- strsplit(lines[1], ",")[[1]]
      header <- trimws(header)
      
      # Parse data
      data_lines <- lines[-1]
      data_matrix <- matrix(NA, nrow = length(data_lines), ncol = length(header))
      
      for (i in 1:length(data_lines)) {
        values <- strsplit(data_lines[i], ",")[[1]]
        values <- trimws(values)
        values <- as.numeric(values)
        
        if (length(values) != length(header)) {
          stop(paste("Line", i + 1, "has incorrect number of values"))
        }
        
        data_matrix[i, ] <- values
      }
      
      data.frame(data_matrix, stringsAsFactors = FALSE)
    }
  })
} 