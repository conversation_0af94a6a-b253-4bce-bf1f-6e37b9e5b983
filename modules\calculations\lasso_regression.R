# Lasso Regression calculation and output helpers

lasso_regression_uploadData_func <- function(lrUserData) {
  ext <- tools::file_ext(lrUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(lrUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(lrUserData$datapath),
         xlsx = readxl::read_xlsx(lrUserData$datapath),
         txt = readr::read_tsv(lrUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

lasso_regression_results_func <- function(data, response_var, predictor_vars, use_cv = TRUE, cv_folds = 10) {
  tryCatch({
    if (!requireNamespace("glmnet", quietly = TRUE)) {
      stop("Package 'glmnet' needed for lasso regression.")
    }
    
    all_vars <- c(response_var, predictor_vars)
    model_data <- data[complete.cases(data[all_vars]), ]
    
    if (nrow(model_data) < 10) {
      stop("At least 10 observations are required for lasso regression")
    }
    
    X <- as.matrix(model_data[predictor_vars])
    y <- model_data[[response_var]]
    
    if (use_cv) {
      cv_fit <- glmnet::cv.glmnet(X, y, alpha = 1, nfolds = cv_folds)
      best_lambda <- cv_fit$lambda.min
      model <- glmnet::glmnet(X, y, alpha = 1, lambda = best_lambda)
    } else {
      model <- glmnet::glmnet(X, y, alpha = 1)
      cv_fit <- NULL
    }
    
    list(
      model = model,
      cv_fit = cv_fit,
      data = model_data,
      response_var = response_var,
      predictor_vars = predictor_vars,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Lasso regression calculation:", e$message))
  })
}

lasso_regression_ht_html <- function(results) {
  # No formal hypothesis test for lasso, so this will display model info
  tagList(
    h4("Lasso Regression Model"),
    p(paste("Number of Predictors:", length(results$predictor_vars)))
  )
}

lasso_regression_summary_html <- function(results) {
  coefs <- coef(results$model)
  
  coef_df <- data.frame(
    Variable = rownames(coefs),
    Coefficient = as.numeric(coefs)
  )
  
  tagList(
    h4("Coefficients"),
    renderTable(coef_df, digits = 4),
    if (!is.null(results$cv_fit)) {
      tagList(
        h4("Cross-Validation Results"),
        p(paste("Optimal Lambda:", results$cv_fit$lambda.min))
      )
    }
  )
}

lasso_regression_plot <- function(results) {
  if (!is.null(results$cv_fit)) {
    plot(results$cv_fit)
  } else {
    plot(results$model, xvar = "lambda", label = TRUE)
  }
}