# Data Quality Assessment Server

dataQualityServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns

    # Upload and parse data
    dqUploadData <- reactive({
      req(input$dqUserData)
      ext <- tools::file_ext(input$dqUserData$name)
      if (ext %in% c("csv", "txt")) {
        read.csv(input$dqUserData$datapath)
      } else if (ext %in% c("xls", "xlsx")) {
        if (!requireNamespace("readxl", quietly = TRUE)) stop("Install 'readxl' for Excel support.")
        readxl::read_excel(input$dqUserData$datapath)
      } else {
        stop("Unsupported file type.")
      }
    })

    # Run Data Quality Assessment with error handling
    dqResults <- eventReactive(input$goDQ, {
      df <- dqUploadData()
      tryCatch({
        dataQualityResults_func(df)
      }, error = function(e) {
        structure(list(error = TRUE, message = e$message), class = "dq_error")
      })
    })

    # Outputs
    output$dqHT <- renderUI({
      results <- dqResults()
      if (inherits(results, "dq_error")) {
        div(style = "color: red;", paste("Error:", results$message))
      } else if (is.null(results)) {
        NULL
      } else {
        tagList(
          h4("Missing Value Summary"),
          uiOutput(ns("missingTable")),
          h4("Duplicate Rows"),
          verbatimTextOutput(ns("duplicateCount")),
          uiOutput(ns("duplicateRows")),
          h4("Outlier Summary (Numeric Columns)"),
          uiOutput(ns("outlierTable")),
          h4("Data Type Consistency"),
          uiOutput(ns("typeTable")),
          h4("Summary Statistics"),
          verbatimTextOutput(ns("summaryStats"))
        )
      }
    })
    output$missingTable <- renderUI({
      results <- dqResults()
      if (inherits(results, "dq_error") || is.null(results)) return(NULL)
      DT::DTOutput(ns("missingTableInner"))
    })
    output$missingTableInner <- DT::renderDT({
      results <- dqResults()
      if (inherits(results, "dq_error") || is.null(results)) return(NULL)
      DT::datatable(results$missing_table)
    })
    output$duplicateCount <- renderPrint({
      results <- dqResults()
      if (inherits(results, "dq_error") || is.null(results)) return(NULL)
      paste("Number of duplicate rows:", results$duplicate_count)
    })
    output$duplicateRows <- renderUI({
      results <- dqResults()
      if (inherits(results, "dq_error") || is.null(results)) return(NULL)
      if (nrow(results$duplicate_rows) == 0) return(NULL)
      DT::DTOutput(ns("duplicateRowsInner"))
    })
    output$duplicateRowsInner <- DT::renderDT({
      results <- dqResults()
      if (inherits(results, "dq_error") || is.null(results)) return(NULL)
      if (nrow(results$duplicate_rows) == 0) return(NULL)
      DT::datatable(results$duplicate_rows)
    })
    output$outlierTable <- renderUI({
      results <- dqResults()
      if (inherits(results, "dq_error") || is.null(results) || is.null(results$outlier_table)) return(NULL)
      DT::DTOutput(ns("outlierTableInner"))
    })
    output$outlierTableInner <- DT::renderDT({
      results <- dqResults()
      if (inherits(results, "dq_error") || is.null(results) || is.null(results$outlier_table)) return(NULL)
      DT::datatable(results$outlier_table)
    })
    output$typeTable <- renderUI({
      results <- dqResults()
      if (inherits(results, "dq_error") || is.null(results)) return(NULL)
      DT::DTOutput(ns("typeTableInner"))
    })
    output$typeTableInner <- DT::renderDT({
      results <- dqResults()
      if (inherits(results, "dq_error") || is.null(results)) return(NULL)
      DT::datatable(results$type_table)
    })
    output$summaryStats <- renderPrint({
      results <- dqResults()
      if (inherits(results, "dq_error") || is.null(results)) return(NULL)
      print(results$summary_stats)
    })
    output$dqPlot <- renderPlot({
      results <- dqResults()
      if (inherits(results, "dq_error") || is.null(results)) return(NULL)
      results$plot_missingness()
    })
    output$dqConclusionOutput <- renderUI({
      results <- dqResults()
      if (inherits(results, "dq_error") || is.null(results)) return(NULL)
      tags$p("Data quality assessment complete. See tables and plots above.")
    })
    output$renderDQData <- renderUI({
      req(dqUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(uiOutput(ns("dqInitialUploadTable")), style = "width: 75%"),
        br(),
        br()
      )
    })
    output$dqInitialUploadTable <- renderUI({
      req(dqUploadData())
      DT::DTOutput(ns("dqInitialUploadTableInner"))
    })
    output$dqInitialUploadTableInner <- DT::renderDT({
      req(dqUploadData())
      DT::datatable(dqUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(dqUploadData())))))
    })
  })
} 