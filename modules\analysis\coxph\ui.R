CoxPHUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("coxUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("coxTime"), "Time Variable", choices = NULL),
        selectizeInput(ns("coxEvent"), "Event Variable", choices = NULL),
        selectizeInput(ns("coxCovariates"), "Covariates", choices = NULL, multiple = TRUE),
        br(),
        actionButton(ns("goCox"), label = "Fit Cox Model", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("coxError")),
        verbatimTextOutput(ns("coxSummary")),
        plotOutput(ns("coxSurvPlot")),
        plotOutput(ns("coxDiagPlot"))
      )
    )
  )
} 