# Placeholder for Competing Risks calculation helpers if needed in future 

# Competing Risks calculation helpers

competing_risks_analysis <- function(time, status, group = NULL) {
  if (!requireNamespace("cmprsk", quietly = TRUE)) {
    warning("Package 'cmprsk' not available. Competing risks analysis cannot be performed.")
    return(list(
      error = "Package 'cmprsk' required for competing risks analysis",
      available = FALSE,
      message = "Please install cmprsk package from Bioconductor or GitHub to enable competing risks analysis"
    ))
  }
  
  fit <- cmprsk::cuminc(time, status, group = group)
  plot_fun <- function() { plot(fit, main = "Cumulative Incidence Functions") }
  list(
    fit = fit,
    summary = summary(fit),
    plot = plot_fun,
    available = TRUE
  )
} 