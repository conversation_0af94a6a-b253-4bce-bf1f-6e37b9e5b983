# Log-linear Models Server
# Multi-way contingency tables

source("modules/calculations/log_linear_models.R")

LogLinearModelsServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactive values
    log_linear_data <- reactiveVal(NULL)
    log_linear_results <- reactiveVal(NULL)
    
    # File upload reactive
    logLinearUploadData <- eventReactive(input$logLinearUserData, {
      handle_file_upload(input$logLinearUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(logLinearUploadData(), {
      data <- logLinearUploadData()
      # Clear selectizeInputs and main panel
      updateSelectizeInput(session, 'logLinearVariables', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'logLinearCountVariable', choices = character(0), selected = NULL, server = TRUE)
      output$logLinearResults <- renderUI({ NULL })
      
      # If data is valid, update choices
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'logLinearVariables', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'logLinearCountVariable', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    logLinearValidationErrors <- reactive({
      errors <- c()
      
      if (input$logLinearDataMethod == "Manual Entry") {
        # Manual entry validation
        if (is.null(input$logLinearManualData) || input$logLinearManualData == "") {
          errors <- c(errors, "Manual data entry is required.")
        } else {
          # Basic validation for manual entry
          tryCatch({
            data <- parse_log_linear_data(input$logLinearManualData)
            if (nrow(data) < 4) {
              errors <- c(errors, "At least 4 observations are required for log-linear analysis.")
            }
            if (ncol(data) < 3) {
              errors <- c(errors, "At least 2 categorical variables and 1 count variable are required.")
            }
          }, error = function(e) {
            errors <- c(errors, "Invalid manual data format. Please check your input.")
          })
        }
      } else {
        # File upload validation
        data <- logLinearUploadData()
        if (is.null(data) || !is.data.frame(data)) {
          errors <- c(errors, "No data uploaded or file could not be read.")
          return(errors)
        }
        if (is.null(input$logLinearVariables) || length(input$logLinearVariables) < 2) {
          errors <- c(errors, "Please select at least 2 categorical variables.")
        } else {
          for (var in input$logLinearVariables) {
            var_data <- data[[var]]
            if (is.numeric(var_data) && length(unique(var_data)) > 20) {
              errors <- c(errors, paste("Variable", var, "appears to be continuous. Log-linear models require categorical variables."))
            }
          }
        }
        if (nrow(data) < 4) {
          errors <- c(errors, "At least 4 observations are required.")
        }
      }
      
      errors
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goLogLinear, {
      output$logLinearResults <- renderUI({
        errors <- logLinearValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Log-linear Analysis", errors = errors)
        } else {
          tryCatch({
            # Prepare data
            if (input$logLinearDataMethod == "Manual Entry") {
              # Parse manual entry data
              data <- parse_log_linear_data(input$logLinearManualData)
            } else {
              # Use uploaded data
              req(logLinearUploadData(), input$logLinearVariables)
              
              data <- logLinearUploadData()
              variables <- input$logLinearVariables
              count_var <- input$logLinearCountVariable
              
              # Select relevant columns
              if (is.null(count_var) || count_var == "") {
                # Create frequency table from categorical variables
                data <- data[variables]
                # Count frequencies
                freq_table <- table(data)
                data <- as.data.frame(freq_table)
                names(data) <- c(variables, "Count")
              } else {
                data <- data[c(variables, count_var)]
                names(data)[length(names(data))] <- "Count"
              }
            }
            
            # Remove rows with missing values
            complete_cases <- complete.cases(data)
            if (sum(complete_cases) < nrow(data)) {
              warning("Removing rows with missing values")
              data <- data[complete_cases, ]
            }
            
            # Check data structure
            if (ncol(data) < 3) {
              stop("At least 2 categorical variables and 1 count variable are required")
            }
            
            if (nrow(data) < 4) {
              stop("At least 4 observations are required")
            }
            
            # Perform log-linear analysis
            results <- perform_log_linear_analysis(data, 
                                                 hierarchical = input$logLinearHierarchical,
                                                 model_selection = input$logLinearModelSelection,
                                                 selection_criterion = input$logLinearSelectionCriterion,
                                                 residuals = input$logLinearResiduals,
                                                 conf_level = input$logLinearConfLevel)
            
            # Store results
            log_linear_results(results)
            
            # Display results
            tagList(
              h3("Log-linear Models Results"),
              
              # Data summary
              h4("Data Summary"),
              renderTable({
                summary_table <- data.frame(
                  Statistic = c("Number of Variables", "Number of Observations", "Number of Cells", "Missing Values"),
                  Value = c(results$n_variables, results$n_observations, results$n_cells, results$n_missing)
                )
                summary_table
              }, rownames = FALSE),
              
              br(),
              
              # Contingency table
              h4("Contingency Table"),
              renderTable({
                as.data.frame(results$contingency_table)
              }, rownames = TRUE),
              
              br(),
              
              # Model selection results
              if (!is.null(results$model_selection)) tagList(
                h4("Model Selection Results"),
                renderDataTable({
                  selection_table <- results$model_selection
                  datatable(selection_table, 
                           options = list(pageLength = 10, scrollX = TRUE),
                           rownames = FALSE) %>%
                    formatRound(columns = c("AIC", "BIC", "Deviance", "p_value"), digits = 4)
                }),
                br()
              ),
              
              # Best model fit statistics
              h4("Best Model Fit Statistics"),
              renderTable({
                fit_table <- results$fit_statistics
                fit_table
              }, rownames = FALSE),
              
              br(),
              
              # Parameter estimates
              h4("Parameter Estimates"),
              renderDataTable({
                param_table <- results$parameter_estimates
                datatable(param_table, 
                         options = list(pageLength = 10, scrollX = TRUE),
                         rownames = FALSE) %>%
                  formatRound(columns = c("Estimate", "Std_Error", "z_value", "p_value", "CI_Lower", "CI_Upper"), digits = 4)
              }),
              
              br(),
              
              # Residuals
              if (!is.null(results$residuals)) tagList(
                h4("Residuals Analysis"),
                renderDataTable({
                  residuals_table <- results$residuals
                  datatable(residuals_table, 
                           options = list(pageLength = 10, scrollX = TRUE),
                           rownames = FALSE) %>%
                    formatRound(columns = c("Observed", "Fitted", "Raw_Residual", "Pearson_Residual", 
                                          "Deviance_Residual", "Standardized_Pearson"), digits = 4)
                }),
                br()
              ),
              
              # Visualization
              h4("Model Diagnostics"),
              fluidRow(
                column(6,
                  renderPlot({
                    if (!is.null(results$residuals)) {
                      ggplot(results$residuals, aes(x = Fitted, y = Pearson_Residual)) +
                        geom_point(alpha = 0.7) +
                        geom_hline(yintercept = 0, color = "red", linetype = "dashed") +
                        geom_hline(yintercept = c(-2, 2), color = "orange", linetype = "dotted") +
                        labs(title = "Pearson Residuals vs Fitted Values",
                             x = "Fitted Values", y = "Pearson Residuals") +
                        theme_minimal()
                    }
                  })
                ),
                column(6,
                  renderPlot({
                    if (!is.null(results$residuals)) {
                      ggplot(results$residuals, aes(x = Observed, y = Fitted)) +
                        geom_point(alpha = 0.7) +
                        geom_abline(intercept = 0, slope = 1, color = "red", linetype = "dashed") +
                        labs(title = "Fitted vs Observed Values",
                             x = "Observed Values", y = "Fitted Values") +
                        theme_minimal()
                    }
                  })
                )
              ),
              
              br(),
              
              # Educational content
              h4("Interpretation"),
              p(strong("Log-linear Models:"), "Analyze relationships between categorical variables in multi-way contingency tables."),
              p(strong("Model Selection:"), "Compare different models using AIC, BIC, or likelihood ratio tests."),
              p(strong("Parameter Estimates:"), "Show the strength and direction of associations between variables."),
              p(strong("Residuals:"), "Help identify cells that are poorly fitted by the model.")
            )
          }, error = function(e) {
            errorScreenUI(title = "Error in Log-linear Analysis", errors = e$message)
          })
        }
      })
    })
    
    # Helper function to parse numeric input
    parse_log_linear_data <- function(input_text) {
      if (is.null(input_text) || input_text == "") {
        stop("Please enter data")
      }
      
      # Parse the input text into a data frame
      lines <- strsplit(input_text, "\n")[[1]]
      lines <- lines[lines != ""]
      
      if (length(lines) < 2) {
        stop("At least 2 lines required (header + data)")
      }
      
      # Parse header
      header <- strsplit(lines[1], ",")[[1]]
      header <- trimws(header)
      
      # Parse data
      data_lines <- lines[-1]
      data_matrix <- matrix(NA, nrow = length(data_lines), ncol = length(header))
      
      for (i in 1:length(data_lines)) {
        values <- strsplit(data_lines[i], ",")[[1]]
        values <- trimws(values)
        
        # Convert to numeric for count variable, keep as character for categorical
        if (length(values) != length(header)) {
          stop(paste("Line", i + 1, "has incorrect number of values"))
        }
        
        # Try to convert to numeric, keep as character if it fails
        numeric_values <- suppressWarnings(as.numeric(values))
        data_matrix[i, ] <- ifelse(is.na(numeric_values), values, numeric_values)
      }
      
      data.frame(data_matrix, stringsAsFactors = FALSE)
    }
  })
} 