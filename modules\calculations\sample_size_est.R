# Sample Size Estimation calculation and output helpers

sample_size_est_uploadData_func <- function(sseUserData) {
  # No data upload needed for sample size estimation
  return(NULL)
}

sample_size_est_results_func <- function(test_type, effect_size, alpha = 0.05, power = 0.8, n_groups = 2, alternative = "two.sided", r2 = NULL, p1 = NULL, p2 = NULL) {
  tryCatch({
    if (!requireNamespace("pwr", quietly = TRUE)) {
      stop("Package 'pwr' is required for Sample Size Estimation.")
    }
    pwr_result <- switch(test_type,
      "t-test" = pwr::pwr.t.test(d = effect_size, sig.level = alpha, power = power, type = "two.sample", alternative = alternative),
      "paired t-test" = pwr::pwr.t.test(d = effect_size, sig.level = alpha, power = power, type = "paired", alternative = alternative),
      "anova" = pwr::pwr.anova.test(k = n_groups, f = effect_size, sig.level = alpha, power = power),
      "correlation" = pwr::pwr.r.test(r = effect_size, sig.level = alpha, power = power, alternative = alternative),
      "chi-square" = pwr::pwr.chisq.test(w = effect_size, sig.level = alpha, power = power, df = (n_groups - 1)),
      "regression" = if (!is.null(r2)) pwr::pwr.f2.test(u = n_groups - 1, f2 = r2 / (1 - r2), sig.level = alpha, power = power) else stop("r2 required for regression."),
      "proportion" = if (!is.null(p1) && !is.null(p2)) pwr::pwr.2p.test(h = abs(pwr::ES.h(p1, p2)), sig.level = alpha, power = power, alternative = alternative) else stop("p1 and p2 required for proportions."),
      stop("Invalid test type specified.")
    )
    list(
      pwr_result = pwr_result,
      test_type = test_type,
      effect_size = effect_size,
      alpha = alpha,
      power = power,
      n_groups = n_groups,
      alternative = alternative,
      r2 = r2,
      p1 = p1,
      p2 = p2,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Sample Size Estimation:", e$message))
  })
}

sample_size_est_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  tagList(
    h4("Sample Size Estimation Results"),
    p(paste("Test type:", results$test_type)),
    p(paste("Effect size:", results$effect_size)),
    p(paste("Alpha:", results$alpha)),
    p(paste("Power:", results$power)),
    if (!is.null(results$n_groups)) p(paste("Number of groups:", results$n_groups)),
    if (!is.null(results$r2)) p(paste("R-squared:", results$r2)),
    if (!is.null(results$p1) && !is.null(results$p2)) p(paste("Proportions:", results$p1, "vs", results$p2)),
    p(paste("Required sample size (n):", ceiling(results$pwr_result$n)))
  )
}

sample_size_est_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  tagList(
    h4("Power Analysis Result Object"),
    renderPrint(results$pwr_result)
  )
}

sample_size_est_plot <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  # Power curve plot for t-test, paired t-test, or ANOVA
  if (results$test_type %in% c("t-test", "paired t-test", "anova")) {
    n_seq <- seq(5, 200, by = 1)
    power_seq <- sapply(n_seq, function(n) {
      if (results$test_type == "anova") {
        pwr::pwr.anova.test(k = results$n_groups, f = results$effect_size, sig.level = results$alpha, n = n)$power
      } else {
        pwr::pwr.t.test(d = results$effect_size, sig.level = results$alpha, n = n, type = ifelse(results$test_type == "paired t-test", "paired", "two.sample"), alternative = results$alternative)$power
      }
    })
    plot(n_seq, power_seq, type = "l", col = "blue", lwd = 2, xlab = "Sample Size (n)", ylab = "Power", main = "Power Curve")
    abline(h = results$power, col = "red", lty = 2)
    abline(v = ceiling(results$pwr_result$n), col = "green", lty = 2)
    legend("bottomright", legend = c("Power Curve", "Target Power", "Required n"), col = c("blue", "red", "green"), lty = c(1,2,2))
  } else {
    plot.new()
    title("No power curve available for this test type.")
  }
}
