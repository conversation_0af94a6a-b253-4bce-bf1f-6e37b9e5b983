ROCAnalysisServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Source the calculations file
    source("modules/calculations/roc_analysis.R")
    
    # File upload reactive
    rocData <- eventReactive(input$rocUserData, {
      handle_roc_data_upload(input$rocUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(rocData(), {
      data <- rocData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'rocResponse', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'rocPredictor', choices = names(data), server = TRUE)
      }
    })
    
    # ROC analysis reactive
    rocAnalysis <- reactive({
      req(rocData(), input$rocResponse, input$rocPredictor)
      perform_roc_analysis(rocData(), input$rocResponse, input$rocPredictor)
    })
    
    # Validation errors reactive
    rocValidationErrors <- reactive({
      validate_roc_inputs(rocData(), input$rocResponse, input$rocPredictor)
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goROC, {
      output$rocResults <- renderUI({
        errors <- rocValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in ROC Analysis", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("rocTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("rocAnalysis"),
                title = "Analysis",
                titlePanel("ROC Analysis Results"),
                br(),
                h4("Area Under the Curve (AUC)"),
                tableOutput(ns('rocAUC')),
                br(),
                h4("Optimal Threshold"),
                tableOutput(ns('rocOptimalThreshold')),
                br(),
                h4("Performance at Optimal Threshold"),
                tableOutput(ns('rocPerformance')),
                br(),
                h4("Threshold Analysis"),
                tableOutput(ns('rocThresholds')),
                br(),
                h4("ROC Curve"),
                plotOutput(ns('rocCurve'), height = "400px")
              ),
              tabPanel(
                id = ns("rocDiagnostics"),
                title = "Data Summary/Diagnostics",
                h4("Data Characteristics"),
                tableOutput(ns('rocDataSummary')),
                br(),
                h4("Class Distribution"),
                tableOutput(ns('rocClassDistribution')),
                br(),
                h4("Predictor Summary"),
                tableOutput(ns('rocPredictorSummary')),
                br(),
                h4("Assumptions Check"),
                uiOutput(ns('rocAssumptions')),
                br(),
                h4("Distribution Plots"),
                plotOutput(ns('rocDistributionPlot'), height = "400px")
              ),
              tabPanel(
                id = ns("rocUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('rocDataTable'))
              )
            )
          )
        }
      })
    })
    
    # AUC results
    output$rocAUC <- renderTable({
      req(rocAnalysis())
      render_roc_auc_table(rocAnalysis())
    }, digits = 4)
    
    # Optimal threshold
    output$rocOptimalThreshold <- renderTable({
      req(rocAnalysis())
      render_roc_optimal_threshold_table(rocAnalysis())
    }, digits = 4)
    
    # Performance at optimal threshold
    output$rocPerformance <- renderTable({
      req(rocAnalysis())
      render_roc_performance_table(rocAnalysis())
    }, digits = 4)
    
    # Threshold analysis
    output$rocThresholds <- renderTable({
      req(rocAnalysis())
      render_roc_thresholds_table(rocAnalysis())
    }, digits = 3)
    
    # ROC curve
    output$rocCurve <- renderPlot({
      req(rocAnalysis())
      render_roc_curve_plot(rocAnalysis())
    })
    
    # Data characteristics
    output$rocDataSummary <- renderTable({
      req(rocAnalysis())
      render_roc_data_summary_table(rocAnalysis(), rocData())
    }, digits = 4)
    
    # Class distribution
    output$rocClassDistribution <- renderTable({
      req(rocAnalysis())
      render_roc_class_distribution_table(rocAnalysis())
    }, digits = 4)
    
    # Predictor summary
    output$rocPredictorSummary <- renderTable({
      req(rocAnalysis())
      render_roc_predictor_summary_table(rocAnalysis())
    }, digits = 4)
    
    # Assumptions check
    output$rocAssumptions <- renderUI({
      req(rocAnalysis())
      render_roc_assumptions_check(rocAnalysis())
    })
    
    # Distribution plot
    output$rocDistributionPlot <- renderPlot({
      req(rocAnalysis())
      render_roc_distribution_plot(rocAnalysis())
    })
    
    # Data table
    output$rocDataTable <- DT::renderDT({
      req(rocData())
      render_roc_data_table(rocData())
    })
  })
} 