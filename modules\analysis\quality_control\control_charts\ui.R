# Quality Control Charts UI
# Process monitoring and control

QualityControlChartsUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(
      width = 3,
      radioButtons(
        inputId = ns("qcDataMethod"),
        label = "Data Input Method:",
        choices = c("Upload File" = "Upload File", "Manual Entry" = "Manual Entry"),
        selected = "Upload File"
      ),
      conditionalPanel(
        condition = "input.qcDataMethod == 'Upload File'",
        ns = ns,
        fileInput(
          inputId = ns("qcUserData"),
          label = "Upload Data File:",
          accept = c(".csv", ".txt", ".xlsx", ".xls"),
          buttonLabel = "Browse Files",
          placeholder = "No file selected"
        ),
        selectizeInput(
          inputId = ns("qcVariable"),
          label = "Select Variable:",
          choices = NULL,
          options = list(placeholder = "Select variable...")
        ),
        selectizeInput(
          inputId = ns("qcGroupVariable"),
          label = "Select Group Variable (optional):",
          choices = NULL,
          options = list(placeholder = "Select group variable...")
        )
      ),
      conditionalPanel(
        condition = "input.qcDataMethod == 'Manual Entry'",
        ns = ns,
        textAreaInput(
          inputId = ns("qcManualData"),
          label = "Enter Data (comma-separated):",
          placeholder = "Group,Value\n1,10.2\n1,10.5\n1,10.1\n2,10.8\n2,10.3\n2,10.6",
          rows = 10
        )
      ),
      h5("Control Chart Type"),
      selectInput(
        inputId = ns("qcChartType"),
        label = "Select Chart Type:",
        choices = c("X-bar and R Chart" = "xbar_r", 
                   "X-bar and S Chart" = "xbar_s",
                   "Individual Values Chart" = "individual",
                   "Moving Range Chart" = "moving_range",
                   "P Chart" = "p_chart",
                   "NP Chart" = "np_chart",
                   "C Chart" = "c_chart",
                   "U Chart" = "u_chart"),
        selected = "xbar_r"
      ),
      h5("Control Limits"),
      numericInput(
        inputId = ns("qcSigmaLevel"),
        label = "Sigma Level:",
        value = 3,
        min = 1,
        max = 6,
        step = 0.5
      ),
      checkboxInput(
        inputId = ns("qcUseHistoricalLimits"),
        label = "Use Historical Data for Limits",
        value = FALSE
      ),
      conditionalPanel(
        condition = "input.qcUseHistoricalLimits",
        ns = ns,
        numericInput(
          inputId = ns("qcHistoricalPeriods"),
          label = "Historical Periods:",
          value = 20,
          min = 5,
          max = 100,
          step = 1
        )
      ),
      h5("Additional Options"),
      checkboxInput(
        inputId = ns("qcShowTrends"),
        label = "Show Trend Lines",
        value = TRUE
      ),
      checkboxInput(
        inputId = ns("qcShowViolations"),
        label = "Highlight Violations",
        value = TRUE
      ),
      checkboxInput(
        inputId = ns("qcShowStatistics"),
        label = "Show Process Statistics",
        value = TRUE
      ),
      actionButton(
        inputId = ns("goQC"),
        label = "Generate Control Charts",
        class = "btn-primary",
        style = "width: 100%;"
      ),
      br(),
      br(),
      helpText(
        "X-bar charts monitor process means.",
        "R and S charts monitor process variability.",
        "Individual charts for single measurements.",
        "Attribute charts for count data."
      )
    ),
    mainPanel(
      textOutput(ns("qcError")),
      plotOutput(ns("qcPlot")),
      tableOutput(ns("qcTable")),
      verbatimTextOutput(ns("qcSummary"))
    )
  )
} 