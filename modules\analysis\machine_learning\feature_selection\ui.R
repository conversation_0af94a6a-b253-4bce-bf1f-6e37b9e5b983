FeatureSelectionUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("fsUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("fsResponse"), "Response Variable", choices = NULL),
        selectizeInput(ns("fsPredictors"), "Predictor Variables", choices = NULL, multiple = TRUE),
        selectInput(ns("fsMethod"), "Method", choices = c("Stepwise", "LASSO", "Ridge", "Elastic Net")),
        br(),
        actionButton(ns("goFS"), label = "Run Feature Selection", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("fsError")),
        tableOutput(ns("fsSummary")),
        plotOutput(ns("fsPlot"))
      )
    )
  )
} 