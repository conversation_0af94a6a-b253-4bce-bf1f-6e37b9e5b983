# Chi-Square calculation and output helpers

chi_square_uploadData_func <- function(chiSquareUserData) {
  ext <- tools::file_ext(chiSquareUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(chiSquareUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(chiSquareUserData$datapath),
         xlsx = readxl::read_xlsx(chiSquareUserData$datapath),
         txt = readr::read_tsv(chiSquareUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

chi_square_results_func <- function(matrix_data, yates_correction) {
  tryCatch({
    # --- Core Tests ---
    chi_test <- chisq.test(x = matrix_data, correct = yates_correction)
    fisher_test <- fisher.test(matrix_data)
    
    # --- Supplementary Calculations ---
    # Calculation Matrix
    observed <- as.vector(chi_test$observed)
    expected <- as.vector(chi_test$expected)
    residuals <- as.vector(chi_test$residuals)
    o_minus_e <- observed - expected
    o_minus_e_sq <- o_minus_e^2
    o_minus_e_sq_by_e <- o_minus_e_sq / expected
    
    calc_matrix <- data.frame(
      O = observed,
      E = expected,
      "O - E" = o_minus_e,
      "(O - E)^2" = o_minus_e_sq,
      "(O - E)^2 / E" = o_minus_e_sq_by_e,
      "Std. Residuals" = residuals,
      check.names = FALSE
    )
    
    # Effect Size (Cramer's V)
    n <- sum(matrix_data)
    df <- min(nrow(matrix_data) - 1, ncol(matrix_data) - 1)
    cramers_v <- sqrt(chi_test$statistic / (n * df))
    
    # Assumption Check
    min_expected <- min(expected)
    
    # --- Return Results ---
    list(
      chi_test = chi_test,
      fisher_test = fisher_test,
      calc_matrix = calc_matrix,
      cramers_v = cramers_v,
      min_expected = min_expected,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Chi-Square calculation:", e$message))
  })
}

chi_square_ht_html <- function(results, sigLvl) {
  chi_test <- results$chi_test
  
  # Chi-Square Conclusion
  chi_conclusion <- if (chi_test$p.value < sigLvl) {
    "Reject H0. The row and column variables are associated."
  } else {
    "Do not reject H0. The row and column variables are independent."
  }
  
  # Fisher's Conclusion
  fisher_test <- results$fisher_test
  fisher_conclusion <- if (fisher_test$p.value < sigLvl) {
    "Reject H0. The row and column variables are associated."
  } else {
    "Do not reject H0. The row and column variables are independent."
  }
  
  withMathJax(tagList(
    h4("Chi-Square Test for Independence"),
    p(sprintf("$H_0$: The row and column variables are independent.")),
    p(sprintf("$H_A$: The row and column variables are associated.")),
    p(sprintf("Test Statistic: $\\chi^2 = %.4f$", chi_test$statistic)),
    p(sprintf("P-value: %.4f", chi_test$p.value)),
    p(strong("Conclusion: "), chi_conclusion),
    hr(),
    h4("Fisher's Exact Test"),
    p(sprintf("P-value: %.4f", fisher_test$p.value)),
    p(strong("Conclusion: "), fisher_conclusion)
  ))
}

chi_square_summary_html <- function(results, matrix_data) {
  # Effect Size Interpretation
  cramers_v <- results$cramers_v
  effect_interp <- if (cramers_v < 0.1) "Negligible" else
                   if (cramers_v < 0.3) "Small" else
                   if (cramers_v < 0.5) "Medium" else "Large"
                   
  # Assumption Check
  min_expected <- results$min_expected
  assumption_status <- if (min_expected < 1) {
    "Violated - At least one expected cell count is less than 1. Fisher's test is recommended."
  } else if (min_expected < 5) {
    "Caution - At least one expected cell count is less than 5. Fisher's test may be more appropriate."
  } else {
    "Met - All expected cell counts are 5 or greater."
  }
  
  # Observed Table with Totals
  obs_totaled <- cbind(matrix_data, Total = rowSums(matrix_data))
  obs_totaled <- rbind(obs_totaled, Total = colSums(obs_totaled))
  
  # Expected Table with Totals
  exp_matrix <- round(matrix(results$chi_test$expected, nrow = nrow(matrix_data)), 4)
  dimnames(exp_matrix) <- dimnames(matrix_data)
  exp_totaled <- cbind(exp_matrix, Total = rowSums(exp_matrix))
  exp_totaled <- rbind(exp_totaled, Total = colSums(exp_totaled))
  
  tagList(
    h4("Effect Size"),
    p(strong("Cramer's V: "), round(cramers_v, 4)),
    p(strong("Interpretation: "), effect_interp),
    hr(),
    h4("Assumptions Check"),
    p(strong("Minimum Expected Frequency: "), round(min_expected, 4)),
    p(assumption_status),
    hr(),
    h4("Observed Frequencies"),
    renderTable(obs_totaled, rownames = TRUE),
    hr(),
    h4("Expected Frequencies"),
    renderTable(exp_totaled, rownames = TRUE),
    hr(),
    h4("Chi-Square Calculation Details"),
    renderTable(round(results$calc_matrix, 4))
  )
}

chi_square_plot <- function(results) {
  obs <- as.data.frame(as.table(results$chi_test$observed))
  exp <- as.data.frame(as.table(results$chi_test$expected))
  
  plot_data <- merge(obs, exp, by = names(obs)[-ncol(obs)])
  colnames(plot_data) <- c("Var1", "Var2", "Observed", "Expected")
  
  ggplot(plot_data, aes(x = Var1, y = Observed, fill = Var2)) +
    geom_bar(stat = "identity", position = "dodge") +
    geom_point(aes(y = Expected), position = position_dodge(width = 0.9), shape = 4, size = 3) +
    labs(title = "Observed vs. Expected Frequencies",
         x = "",
         y = "Frequency") +
    theme_minimal()
}