# Two Sample Inference Server
# Extracted and modularized from statInfr.R

source("modules/calculations/two_sample.R")

TwoSampleInferenceServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    # File upload reactive
    twoSampUploadData <- eventReactive(input$twoSampUserData, {
      handle_file_upload(input$twoSampUserData)
    })
    # Clear selectizeInputs and main panel, show preview on upload
    observeEvent(twoSampUploadData(), {
      data <- twoSampUploadData()
      output$twoSampleResults <- renderUI({ NULL })
      if (!is.null(data) && is.data.frame(data)) {
        output$twoSampleResults <- renderUI({
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('twoSampPreviewTable'))
          )
        })
        output$twoSampPreviewTable <- DT::renderDT({
          head(data, 20)
        })
      }
    })
    # --- Main Reactives for Two-Sample Independent Means ---
    GetTwoSampCI <- reactive({
      if (is.null(input$sampleMean1) || is.null(input$sampleMean2)) return(NULL)
      if (input$bothsigmaKnown == "bothKnown") {
        # Z interval for difference of means (not shown in original, but would use ZInterval for difference)
        # Placeholder: use TwoSampZTest for now
        TwoSampZTest(input$sampleMean1, input$popuSD1, input$sampleSize1, input$sampleMean2, input$popuSD2, input$sampleSize2, "two.sided", 0.05)
      } else {
        var_equal <- input$bothsigmaEqual == "TRUE"
        TwoSampTTest(input$sampleMean1, input$sampSD1, input$sampleSize1, input$sampleMean2, input$sampSD2, input$sampleSize2, var_equal, "two.sided", 0.05)
      }
    })
    # Add a reactive to collect validation errors
    twoSampleValidationErrors <- reactive({
      errors <- c()
      # Example checks (expand as needed)
      if (is.null(input$sampleMean1) || is.na(input$sampleMean1)) {
        errors <- c(errors, "Sample Mean 1 is required.")
      }
      if (is.null(input$sampleMean2) || is.na(input$sampleMean2)) {
        errors <- c(errors, "Sample Mean 2 is required.")
      }
      if (!is.null(input$twoSampUserData) && !(tolower(tools::file_ext(input$twoSampUserData$name)) %in% c("csv", "txt", "xls", "xlsx"))) {
        errors <- c(errors, "File format not accepted.")
      }
      errors
    })
    # --- Outputs ---
    output$twoSampCI <- renderUI({
      ci <- GetTwoSampCI()
      if (is.null(ci)) return(NULL)
      withMathJax(tagList(
        h4("Confidence Interval / Test for Difference of Means"),
        h5("Hypotheses"),
        p("$H_0: \\mu_1 = \\mu_2$"),
        p("$H_A: \\mu_1 \\neq \\mu_2$"),
        h5("Test Statistic"),
        p("$t = \\frac{(\\bar{x}_1 - \\bar{x}_2)}{SE}$"),
        p("$t = $", ci[["Test Statistic"]]),
        h5("P-value Method"),
        p("$P = $", ci[["P-Value"]]),
        h5("Critical Value Method"),
        p("$t^* = $", ci[["T Critical"]]),
        h5("Conclusion"),
        if (ci[["P-Value"]] < 0.05) {
          p("Since $P < 0.05$, reject $H_0$.")
        } else {
          p("Since $P \\geq 0.05$, do not reject $H_0$.")
        }
      ))
    })
    # Show main results or error screen in main panel when Calculate is pressed
    observeEvent(input$goInference, {
      output$twoSampleResults <- renderUI({
        errors <- twoSampleValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Two Sample Inference", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("twoSampleTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("twoSampleAnalysis"),
                title = "Analysis",
                titlePanel("Two Sample Inference Analysis"),
                br(),
                uiOutput(ns('twoSampCI')),
                br(),
                h4("Effect Size"),
                uiOutput(ns('twoSampleEffectSize')),
                br(),
                h4("Assumptions Check"),
                uiOutput(ns('twoSampleAssumptions'))
              ),
              tabPanel(
                id = ns("twoSampleDataSummary"),
                title = "Data Summary",
                h4("Sample Statistics"),
                tableOutput(ns('twoSampleStats')),
                br(),
                h4("Descriptive Statistics"),
                tableOutput(ns('twoSampleDescriptive')),
                br(),
                h4("Normality Tests"),
                tableOutput(ns('twoSampleNormality'))
              ),
              tabPanel(
                id = ns("twoSampleUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('twoSampleDataTable'))
              )
            )
          )
        }
      })
    })
    
    # Additional outputs for the enhanced tabs
    output$twoSampleEffectSize <- renderUI({
      ci <- GetTwoSampCI()
      if (is.null(ci)) return(NULL)
      
      # Calculate Cohen's d effect size
      pooled_sd <- sqrt(((input$sampleSize1 - 1) * input$sampSD1^2 + (input$sampleSize2 - 1) * input$sampSD2^2) / (input$sampleSize1 + input$sampleSize2 - 2))
      cohens_d <- (input$sampleMean1 - input$sampleMean2) / pooled_sd
      
      tagList(
        p("Cohen's d effect size: ", round(cohens_d, 4)),
        p("Effect size interpretation:"),
        if (abs(cohens_d) < 0.2) {
          p("Small effect size")
        } else if (abs(cohens_d) < 0.5) {
          p("Medium effect size")
        } else {
          p("Large effect size")
        }
      )
    })
    
    output$twoSampleAssumptions <- renderUI({
      tagList(
        h5("Key Assumptions:"),
        p("1. Independent samples"),
        p("2. Normal distribution (approximately)"),
        p("3. Equal variances (if using pooled t-test)"),
        p("4. Random sampling"),
        br(),
        p("Note: These assumptions should be checked with your data.")
      )
    })
    
    output$twoSampleStats <- renderTable({
      ci <- GetTwoSampCI()
      if (is.null(ci)) return(NULL)
      
      stats <- data.frame(
        Sample = c("Sample 1", "Sample 2"),
        Mean = c(input$sampleMean1, input$sampleMean2),
        SD = c(input$sampSD1, input$sampSD2),
        N = c(input$sampleSize1, input$sampleSize2)
      )
      stats
    }, digits = 4)
    
    output$twoSampleDescriptive <- renderTable({
      ci <- GetTwoSampCI()
      if (is.null(ci)) return(NULL)
      
      # Calculate additional statistics
      se1 <- input$sampSD1 / sqrt(input$sampleSize1)
      se2 <- input$sampSD2 / sqrt(input$sampleSize2)
      
      desc <- data.frame(
        Statistic = c("Mean Difference", "Standard Error", "95% CI Lower", "95% CI Upper", "t-statistic", "p-value"),
        Value = c(
          input$sampleMean1 - input$sampleMean2,
          ci[["Standard Error"]],
          ci[["Confidence Interval"]][1],
          ci[["Confidence Interval"]][2],
          ci[["Test Statistic"]],
          ci[["P-Value"]]
        )
      )
      desc
    }, digits = 4)
    
    output$twoSampleNormality <- renderTable({
      # Placeholder for normality test results
      # In a real implementation, this would use the uploaded data
      data.frame(
        Test = c("Shapiro-Wilk (Sample 1)", "Shapiro-Wilk (Sample 2)"),
        Statistic = c("N/A", "N/A"),
        P_value = c("N/A", "N/A"),
        stringsAsFactors = FALSE
      )
    })
    
    output$twoSampleDataTable <- DT::renderDT({
      req(twoSampUploadData())
      DT::datatable(twoSampUploadData(),
        options = list(pageLength = 25,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(twoSampUploadData())))))
    })
    
    # TODO: Add outputs and logic for dependent means, proportions, and variances
  })
} 