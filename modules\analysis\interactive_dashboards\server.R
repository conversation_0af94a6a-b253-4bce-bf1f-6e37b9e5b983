InteractiveDashboardsServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Source the calculations file
    source("modules/calculations/interactive_dashboards.R")
    
    # File upload reactive
    dashData <- eventReactive(input$dashUserData, {
      handle_dashboard_data_upload(input$dashUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(dashData(), {
      data <- dashData()
      if (!is.null(data) && is.data.frame(data)) {
        update_dashboard_type_choices(session, data)
      }
    })
    
    # Validation errors reactive
    dashValidationErrors <- reactive({
      validate_dashboard_inputs(dashData(), input$dashType)
    })
    
    # Dashboard analysis reactive
    dashResult <- eventReactive(input$goDash, {
      perform_dashboard_analysis(dashData(), input$dashType)
    })
    
    # Error handling
    output$dashError <- renderUI({
      errors <- dashValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          dashResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "Dashboard Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$dashModelSummary <- renderUI({
      req(dashResult())
      render_dashboard_summary_ui(dashResult())
    })
    
    # Summary statistics table
    output$dashSummaryTable <- renderTable({
      req(dashResult())
      render_dashboard_summary_table(dashResult())
    })
    
    # Correlation matrix
    output$dashCorrelationMatrix <- renderTable({
      req(dashResult())
      render_dashboard_correlation_matrix(dashResult())
    })
    
    # Frequency tables
    output$dashFrequencyTables <- renderUI({
      req(dashResult())
      render_dashboard_frequency_tables(dashResult())
    })
    
    # Group summaries
    output$dashGroupSummaries <- renderUI({
      req(dashResult())
      render_dashboard_group_summaries(dashResult())
    })
    
    # Data preview
    output$dashDataPreview <- renderTable({
      req(dashResult())
      render_dashboard_data_preview(dashResult())
    })
    
    # Plots
    output$dashPlots <- renderPlot({
      req(dashResult())
      render_dashboard_plots(dashResult())
    })
    
    # Data table
    output$dashDataTable <- DT::renderDT({
      req(dashData())
      render_dashboard_data_table(dashData())
    })
  })
} 