discriminantAnalysisServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactives
    daUploadData <- eventReactive(input$daUserData, {
      handle_file_upload(input$daUserData)
    })
    
    daResults <- reactive({
      data <- daUploadData()
      if (is.null(data) || is.null(input$daGroup) || is.null(input$daPredictors) || 
          length(input$daPredictors) == 0) {
        return(NULL)
      }
      daResults_func(
        TRUE,
        data,
        input$daGroup,
        input$daPredictors,
        input$daMethod
      )
    })
    
    # Validation errors
    daValidationErrors <- reactive({
      errors <- c()
      data <- daUploadData()
      
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$daGroup) || input$daGroup == "") {
        errors <- c(errors, "Please select a group variable.")
      }
      
      if (is.null(input$daPredictors) || length(input$daPredictors) == 0) {
        errors <- c(errors, "Please select at least one predictor variable.")
      }
      
      if (!is.null(input$daGroup) && !is.null(input$daPredictors) && 
          input$daGroup %in% input$daPredictors) {
        errors <- c(errors, "Group variable cannot be included in predictors.")
      }
      
      if (!is.null(input$daGroup) && input$daGroup %in% names(data)) {
        if (length(unique(data[[input$daGroup]])) < 2) {
          errors <- c(errors, "Group variable must have at least two unique values.")
        }
      }
      
      if (!is.null(input$daPredictors) && length(input$daPredictors) > 0) {
        for (var in input$daPredictors) {
          if (!var %in% names(data)) {
            errors <- c(errors, sprintf("Predictor variable '%s' not found in the data.", var))
          } else {
            if (length(unique(data[[var]])) < 2) {
              errors <- c(errors, sprintf("Predictor variable '%s' must have at least two unique values.", var))
            }
          }
        }
      }
      
      errors
    })
    
    # Outputs
    output$daResults <- renderUI({
      results <- daResults()
      if (is.null(results)) return(NULL)
      discriminantAnalysisResults(daResults, reactive({input$daSigLvl}))
    })
    
    output$discriminantAnalysisPlot <- renderPlot({
      results <- daResults()
      if (is.null(results)) return(NULL)
      discriminantAnalysisPlot(daResults, reactive({input$daSigLvl}))
    })
    
    output$daClassification <- renderUI({
      results <- daResults()
      if (is.null(results)) return(NULL)
      discriminantAnalysisClassification(daResults, reactive({input$daSigLvl}))
    })
    
    output$daCoefficientsOutput <- renderUI({
      results <- daResults()
      if (is.null(results)) return(NULL)
      discriminantAnalysisCoefficients(daResults, reactive({input$daSigLvl}))
    })
    
    output$daCoefficientsTable <- DT::renderDT({
      results <- daResults()
      if (is.null(results) || is.null(results$coefficients)) return(NULL)
      
      coef_df <- results$coefficients
      DT::datatable(coef_df,
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(coef_df)))))
    })
    
    output$daPredictionsOutput <- renderUI({
      results <- daResults()
      if (is.null(results)) return(NULL)
      discriminantAnalysisPredictions(daResults, reactive({input$daSigLvl}))
    })
    
    output$daPredictionsTable <- DT::renderDT({
      results <- daResults()
      if (is.null(results) || is.null(results$predictions)) return(NULL)
      
      pred_df <- results$predictions
      DT::datatable(pred_df,
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(pred_df)))))
    })
    
    output$renderDAData <- renderUI({
      req(daUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("daInitialUploadTable")), style = "width: 75%"),
        br(),
        br()
      )
    })
    
    output$daInitialUploadTable <- DT::renderDT({
      req(daUploadData())
      DT::datatable(daUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(daUploadData())))))
    })
    
    # Update selectizeInput choices after file upload
    observeEvent(daUploadData(), {
      data <- daUploadData()
      
      # Clear selectizeInputs and main panel
      updateSelectizeInput(session, 'daGroup', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'daPredictors', choices = character(0), selected = NULL, server = TRUE)
      output$discriminantAnalysisResults <- renderUI({ NULL })
      
      # If data is valid, update choices and show preview
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'daGroup', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'daPredictors', choices = names(data), server = TRUE)
        
        output$discriminantAnalysisResults <- renderUI({
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('daPreviewTable'))
          )
        })
        
        output$daPreviewTable <- DT::renderDT({
          head(data, 20)
        })
      }
    })
    
    # Show main results or error screen when Calculate is pressed
    observeEvent(input$goDiscriminantAnalysis, {
      output$discriminantAnalysisResults <- renderUI({
        errors <- daValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Discriminant Analysis", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("daTabset"),
              selected = "Analysis",
              tabPanel(
                id    = ns("da"),
                title = "Analysis",
                titlePanel("Discriminant Analysis Results"),
                br(),
                uiOutput(ns('daResults')),
                br(),
                plotOutput(ns('discriminantAnalysisPlot'), width = "100%", height = "500px"),
                br(),
                uiOutput(ns('daClassification'))
              ),
              tabPanel(
                id    = ns("daCoef"),
                title = "Discriminant Functions",
                DTOutput(ns("daCoefficientsTable")),
                uiOutput(ns("daCoefficientsOutput"))
              ),
              tabPanel(
                id    = ns("daPred"),
                title = "Predictions",
                DTOutput(ns("daPredictionsTable")),
                uiOutput(ns("daPredictionsOutput"))
              ),
              tabPanel(
                id    = ns("daData"),
                title = "Uploaded Data",
                uiOutput(ns("renderDAData"))
              )
            )
          )
        }
      })
    })
  })
} 