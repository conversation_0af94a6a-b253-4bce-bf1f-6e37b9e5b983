MultipleImputationUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("miceUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("miceVars"), "Variables to Impute", choices = NULL, multiple = TRUE),
        numericInput(ns("miceM"), "Number of Imputations", value = 5, min = 1, max = 20),
        br(),
        actionButton(ns("goMICE"), label = "Run Imputation", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("miceError")),
        tableOutput(ns("miceResults")),
        plotOutput(ns("micePlot"))
      )
    )
  )
} 