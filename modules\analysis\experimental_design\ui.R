experimentalDesignSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    div(
      id = ns("edInputs"),
      selectizeInput(
        inputId = ns("edTool"),
        label = strong("Experimental Design Tool"),
        choices = c("Sample Size Calculator", "Power Analysis", "Design Efficiency", "Randomization", "Blocking Design"),
        selected = "Sample Size Calculator",
        options = list(placeholder = 'Select tool')
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Sample Size Calculator'", ns("edTool")),
        selectizeInput(
          inputId = ns("edTestType"),
          label = strong("Test Type"),
          choices = c("One-Sample t-test", "Two-Sample t-test", "Paired t-test", "One-Way ANOVA", "Two-Way ANOVA", "Chi-Square Test", "Correlation"),
          selected = "Two-Sample t-test",
          options = list(placeholder = 'Select test type')
        ),
        numericInput(
          inputId = ns("edEffectSize"),
          label = strong("Effect Size (<PERSON>'s d)"),
          value = 0.5,
          min = 0.1,
          max = 2.0,
          step = 0.1
        ),
        numericInput(
          inputId = ns("edPower"),
          label = strong("Power (1 - β)"),
          value = 0.8,
          min = 0.5,
          max = 0.99,
          step = 0.01
        ),
        numericInput(
          inputId = ns("edAlpha"),
          label = strong("Significance Level (α)"),
          value = 0.05,
          min = 0.01,
          max = 0.1,
          step = 0.01
        )
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Power Analysis'", ns("edTool")),
        numericInput(
          inputId = ns("edSampleSize"),
          label = strong("Sample Size per Group"),
          value = 30,
          min = 5,
          max = 1000,
          step = 5
        ),
        numericInput(
          inputId = ns("edEffectSizePower"),
          label = strong("Effect Size"),
          value = 0.5,
          min = 0.1,
          max = 2.0,
          step = 0.1
        ),
        numericInput(
          inputId = ns("edAlphaPower"),
          label = strong("Significance Level"),
          value = 0.05,
          min = 0.01,
          max = 0.1,
          step = 0.01
        )
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Design Efficiency'", ns("edTool")),
        numericInput(
          inputId = ns("edFactors"),
          label = strong("Number of Factors"),
          value = 3,
          min = 2,
          max = 10,
          step = 1
        ),
        numericInput(
          inputId = ns("edLevels"),
          label = strong("Levels per Factor"),
          value = 2,
          min = 2,
          max = 5,
          step = 1
        ),
        numericInput(
          inputId = ns("edRuns"),
          label = strong("Number of Runs"),
          value = 8,
          min = 4,
          max = 100,
          step = 1
        )
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Randomization'", ns("edTool")),
        numericInput(
          inputId = ns("edTotalSubjects"),
          label = strong("Total Number of Subjects"),
          value = 60,
          min = 10,
          max = 1000,
          step = 5
        ),
        numericInput(
          inputId = ns("edGroups"),
          label = strong("Number of Groups"),
          value = 3,
          min = 2,
          max = 10,
          step = 1
        ),
        checkboxInput(
          inputId = ns("edStratified"),
          label = strong("Stratified Randomization"),
          value = FALSE
        )
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Blocking Design'", ns("edTool")),
        numericInput(
          inputId = ns("edBlocks"),
          label = strong("Number of Blocks"),
          value = 4,
          min = 2,
          max = 20,
          step = 1
        ),
        numericInput(
          inputId = ns("edTreatments"),
          label = strong("Number of Treatments"),
          value = 3,
          min = 2,
          max = 10,
          step = 1
        ),
        selectizeInput(
          inputId = ns("edBlockType"),
          label = strong("Blocking Type"),
          choices = c("Complete Block", "Incomplete Block", "Latin Square"),
          selected = "Complete Block",
          options = list(placeholder = 'Select blocking type')
        )
      )
    ),
    br(),
    actionButton(ns("goExperimentalDesign"), label = "Calculate", class = "act-btn")
  )
}

experimentalDesignMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('experimentalDesignResults'))
  )
}

experimentalDesignUI <- function(id) {
  ns <- NS(id)
  tagList(
    experimentalDesignSidebarUI(id),
    experimentalDesignMainUI(id)
  )
} 