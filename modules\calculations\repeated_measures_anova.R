# Repeated Measures ANOVA calculation and output helpers

repeated_measures_anova_uploadData_func <- function(rmaUserData, subject_col, dv_col, within_col) {
  tryCatch(
    {
      if (is.null(rmaUserData) || is.null(subject_col) || is.null(dv_col) || is.null(within_col)) {
        return(NULL)
      }
      
      # Read data based on file type
      if (grepl("\\.csv$", rmaUserData$name, ignore.case = TRUE)) {
        df <- read.csv(rmaUserData$datapath, stringsAsFactors = TRUE)
      } else if (grepl("\\.tsv$", rmaUserData$name, ignore.case = TRUE)) {
        df <- read.tsv(rmaUserData$datapath, stringsAsFactors = TRUE)
      } else if (grepl("\\.xlsx$", rmaUserData$name, ignore.case = TRUE)) {
        df <- readxl::read_excel(rmaUserData$datapath)
      } else {
        stop("Unsupported file type. Please upload a CSV, TSV, or XLSX file.")
      }
      
      # Check for required columns
      required_cols <- c(subject_col, dv_col, within_col)
      if (!all(required_cols %in% names(df))) {
        missing_cols <- required_cols[!required_cols %in% names(df)]
        stop(paste("The following columns were not found in the uploaded file:", paste(missing_cols, collapse = ", ")))
      }
      
      # Ensure columns have correct types
      df[[subject_col]] <- as.factor(df[[subject_col]])
      df[[within_col]] <- as.factor(df[[within_col]])
      df[[dv_col]] <- as.numeric(df[[dv_col]])
      
      return(df)
    },
    error = function(e) {
      return(list(error = paste("Error reading data:", e$message)))
    }
  )
}

repeated_measures_anova_results_func <- function(data, subject_col, dv_col, within_col) {
  tryCatch({
    if (is.null(data) || nrow(data) == 0) {
      stop("No data provided for analysis.")
    }
    
    if (!requireNamespace("afex", quietly = TRUE)) {
      stop("Package 'afex' is required for Repeated Measures ANOVA.")
    }
    
    # Perform the repeated measures ANOVA
    model <- afex::aov_ez(
      id = subject_col,
      dv = dv_col,
      data = data,
      within = within_col
    )
    
    list(
      model = model,
      summary = summary(model),
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Repeated Measures ANOVA:", e$message))
  })
}

repeated_measures_anova_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  tagList(
    h4("Repeated Measures ANOVA Results"),
    p("See summary table for full results.")
  )
}

repeated_measures_anova_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  renderPrint(results$summary)
}

repeated_measures_anova_plot <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  
  if (!requireNamespace("ggplot2", quietly = TRUE)) {
    stop("Package 'ggplot2' is required for plotting.")
  }
  
  # Plot using afex's built-in plotting
  afex::afex_plot(results$model, x = deparse(results$model$within), trace = NULL, error = "within") +
    labs(title = "Repeated Measures ANOVA Plot", y = deparse(results$model$dv)) +
    theme_minimal()
}