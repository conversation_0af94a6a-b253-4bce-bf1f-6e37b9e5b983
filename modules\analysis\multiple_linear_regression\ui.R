# Multiple Linear Regression UI
MultipleLinearRegressionSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("mlrUserData"), "Upload your Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
    selectizeInput(ns("mlrResponse"), "Response Variable (y)", choices = NULL),
    selectizeInput(ns("mlrExplanatory"), "Explanatory Variables (x1, x2, ..., xn)", choices = NULL, multiple = TRUE),
    radioButtons(ns("mlrSigLvl"), "Significance Level (alpha)", choices = c("10%", "5%", "1%"), selected = "5%", inline = TRUE),
    br(),
    actionButton(ns("goMLR"), label = "Calculate", class = "act-btn")
  )
}

MultipleLinearRegressionMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('mlrResults'))
  )
}

multipleLinearRegressionUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(MultipleLinearRegressionSidebarUI(id)),
    mainPanel(MultipleLinearRegressionMainUI(id))
  )
} 