# Bootstrap calculation and output helpers

bootstrap_uploadData_func <- function(bsUserData) {
  ext <- tools::file_ext(bsUserData$name)
  ext <- tolower(ext)
  
  if (ext == "csv") {
    readr::read_csv(bsUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(bsUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(bsUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(bsUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

# Bootstrap analysis function that matches the server.R expectations
bootstrap_analysis <- function(data, variables, n_samples) {
  tryCatch({
    if (!requireNamespace("boot", quietly = TRUE)) {
      stop("Package 'boot' is required for bootstrap analysis.")
    }
    
    # Calculate mean for selected variables
    selected_data <- data[, variables, drop = FALSE]
    
    # Define statistic function for bootstrap
    stat_func <- function(data, indices) {
      d <- data[indices, , drop = FALSE]
      return(mean(as.matrix(d), na.rm = TRUE))
    }
    
    # Perform bootstrap
    boot_results <- boot::boot(data = selected_data, statistic = stat_func, R = n_samples)
    
    # Calculate confidence intervals
    boot_ci <- boot::boot.ci(boot_results, conf = 0.95, type = "perc")
    
    # Calculate statistics
    original_mean <- mean(as.matrix(selected_data), na.rm = TRUE)
    bootstrap_mean <- mean(boot_results$t)
    bias <- bootstrap_mean - original_mean
    standard_error <- sd(boot_results$t)
    
    # Calculate quantiles
    bootstrap_samples <- boot_results$t[, 1]
    
    list(
      original_mean = original_mean,
      bootstrap_mean = bootstrap_mean,
      bias = bias,
      standard_error = standard_error,
      bootstrap_samples = bootstrap_samples,
      confidence_intervals = data.frame(
        Method = "Percentile",
        Lower = boot_ci$percent[4],
        Upper = boot_ci$percent[5]
      ),
      min = min(bootstrap_samples),
      q1 = quantile(bootstrap_samples, 0.25),
      median = median(bootstrap_samples),
      q3 = quantile(bootstrap_samples, 0.75),
      max = max(bootstrap_samples),
      skewness = ifelse(requireNamespace("moments", quietly = TRUE), 
                       moments::skewness(bootstrap_samples), NA),
      kurtosis = ifelse(requireNamespace("moments", quietly = TRUE), 
                       moments::kurtosis(bootstrap_samples), NA),
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Bootstrap analysis:", e$message))
  })
}

bootstrap_results_func <- function(data, variables, n_bootstrap = 1000) {
  bootstrap_analysis(data, variables, n_bootstrap)
}

bootstrap_ht_html <- function(results) {
  # No formal hypothesis test for bootstrap, summary is more informative
  tagList(
    h4("Bootstrap Analysis"),
    p("See summary table for bootstrap estimates and confidence intervals.")
  )
}

bootstrap_summary_html <- function(results) {
  tagList(
    h4("Bootstrap Results"),
    p(paste("Original Mean:", round(results$original_mean, 4))),
    p(paste("Bootstrap Mean:", round(results$bootstrap_mean, 4))),
    p(paste("Bias:", round(results$bias, 4))),
    p(paste("Standard Error:", round(results$standard_error, 4))),
    h4("Confidence Interval"),
    tableOutput(results$confidence_intervals)
  )
}

bootstrap_plot <- function(results) {
  if (is.null(results$bootstrap_samples)) return(NULL)
  
  # Create bootstrap distribution plot
  hist(results$bootstrap_samples, 
       main = "Bootstrap Distribution", 
       xlab = "Bootstrap Statistic", 
       ylab = "Frequency",
       col = "lightblue",
       border = "black")
  abline(v = results$original_mean, col = "red", lwd = 2, lty = 2)
  legend("topright", legend = "Original Mean", col = "red", lwd = 2, lty = 2)
}
