# Placeholder for Species Distribution Models UI
speciesDistributionSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("sdmUserData"), "Upload your Data (.csv or .xls or .xlsx or .txt)", accept = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")),
    uiOutput(ns("sdmColSelectors")),
    actionButton(ns("goSDM"), label = "Calculate", class = "act-btn")
  )
}

speciesDistributionMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('sdmHT')),
    plotOutput(ns('sdmPlot'), width = "50%", height = "400px"),
    uiOutput(ns('sdmConclusionOutput'))
  )
}

speciesDistributionUI <- function(id) {
  ns <- NS(id)
  tagList(
    speciesDistributionSidebarUI(id),
    speciesDistributionMainUI(id),
    tabsetPanel(
      id = ns("sdmTabset"),
      selected = "Analysis",
      tabPanel(
        id    = ns("sdmAnalysis"),
        title = "Species Distribution Models Analysis",
        titlePanel("Species Distribution Models Analysis"),
        br(),
        uiOutput(ns('sdmHT')),
        br(),
        plotOutput(ns('sdmPlot'), width = "50%", height = "400px"),
        br(),
        uiOutput(ns('sdmConclusionOutput'))
      ),
      tabPanel(
        id    = ns("sdmData"),
        title = "Uploaded Data",
        uiOutput(ns("renderSDMData"))
      )
    )
  )
} 