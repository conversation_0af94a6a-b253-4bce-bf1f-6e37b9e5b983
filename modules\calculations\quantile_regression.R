# Quantile Regression calculation and output helpers

quantile_regression_uploadData_func <- function(qrUserData, response_var, predictor_vars) {
  tryCatch(
    {
      if (is.null(qrUserData) || is.null(response_var) || is.null(predictor_vars)) {
        return(NULL)
      }
      
      # Read data based on file type
      if (grepl("\\.csv$", qrUserData$name, ignore.case = TRUE)) {
        df <- read.csv(qrUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.tsv$", qrUserData$name, ignore.case = TRUE)) {
        df <- read.tsv(qrUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.xlsx$", qrUserData$name, ignore.case = TRUE)) {
        df <- readxl::read_excel(qrUserData$datapath)
      } else {
        stop("Unsupported file type. Please upload a CSV, TSV, or XLSX file.")
      }
      
      # Check for required columns
      required_cols <- c(response_var, predictor_vars)
      if (!all(required_cols %in% names(df))) {
        missing_cols <- required_cols[!required_cols %in% names(df)]
        stop(paste("The following columns were not found in the uploaded file:", paste(missing_cols, collapse = ", ")))
      }
      
      return(df)
    },
    error = function(e) {
      return(list(error = paste("Error reading data:", e$message)))
    }
  )
}

quantile_regression_results_func <- function(data, response_var, predictor_vars, quantile = 0.5) {
  tryCatch({
    if (is.null(data) || nrow(data) == 0) {
      stop("No data provided for analysis.")
    }
    
    if (!requireNamespace("quantreg", quietly = TRUE)) {
      stop("Package 'quantreg' needed for quantile regression.")
    }
    
    formula_str <- paste0("`", response_var, "` ~ ", paste0("`", predictor_vars, "`", collapse = " + "))
    model <- quantreg::rq(as.formula(formula_str), data = data, tau = quantile)
    
    list(
      model = model,
      summary = summary(model, se = "boot"),
      data = data,
      response_var = response_var,
      predictor_vars = predictor_vars,
      quantile = quantile,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Quantile Regression calculation:", e$message))
  })
}

quantile_regression_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  tagList(
    h4("Quantile Regression Model"),
    p("See summary table for model coefficients and diagnostics.")
  )
}

quantile_regression_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  renderPrint(results$summary)
}

quantile_regression_plot <- function(results) {
  if (!is.null(results$error) || length(results$predictor_vars) != 1) {
    return(NULL)
  }
  
  if (!requireNamespace("ggplot2", quietly = TRUE)) {
    stop("Package 'ggplot2' is required for plotting.")
  }
  
  ggplot(results$data, aes_string(x = results$predictor_vars[1], y = results$response_var)) +
    geom_point(alpha = 0.5) +
    geom_quantile(quantiles = results$quantile, color = "blue") +
    labs(title = paste("Quantile Regression (Quantile =", results$quantile, ")"),
         y = results$response_var) +
    theme_minimal()
}