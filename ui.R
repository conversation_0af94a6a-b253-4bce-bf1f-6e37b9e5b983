HEAD <- tags$head(tags$link(rel = "stylesheet",
                            type="text/css",
                            href="cougarstats-styles.css"),
                  tags$link(rel = "icon",
                            type="image/x-icon",
                            href="favicon.ico"))

BODY <-
  div(
    navbarPage(
      id = "mainBanner",
      title = div(class = "navbarLogo",
                  img(src ="CougarStatsLogo.png", height = 100),
                  span(" CougarStats ", class = "pageTitle")),
      tabPanel("Methods",
               navbarPage(title = NULL,
                          tabPanel("Basic Statistics",
                                   tabsetPanel(
                                     tabPanel("Descriptive Statistics", descStatsUI(id = "ds")),
                                     tabPanel("Probability Distributions", probDistUI(id = "pd")),
                                     tabPanel("Sample Size Estimation", sampSizeEstUI(id = "sse")),
                                     tabPanel("Effect Size Calculators", EffectSizeUI(id = "es")),
                                     tabPanel("Power Analysis", PowerAnalysisUI(id = "power"))
                                   )
                          ),
                          tabPanel("Statistical Inference", statisticalInferenceUI(id = "si")),
                          tabPanel("Regression & Modeling",
                                   tabsetPanel(
                                     tabPanel("Linear Regression",
                                       tabsetPanel(
                                         tabPanel("Simple Linear Regression", simpleLinearRegressionUI(id = "slr")),
                                         tabPanel("Multiple Linear Regression", multipleLinearRegressionUI(id = "mlr")),
                                         tabPanel("Polynomial Regression", PolynomialRegressionUI(id = "poly")),
                                         tabPanel("Stepwise Regression", StepwiseRegressionUI(id = "stepwise")),
                                         tabPanel("Robust Regression", RobustRegressionUI(id = "robust"))
                                       )
                                     ),
                                     tabPanel("Regularized Regression",
                                       tabsetPanel(
                                         tabPanel("Ridge Regression", RidgeRegressionUI(id = "ridge")),
                                         tabPanel("Lasso Regression", LassoRegressionUI(id = "lasso")),
                                         tabPanel("Elastic Net Regression", ElasticNetRegressionUI(id = "elastic_net")),
                                         tabPanel("Quantile Regression", quantileRegressionUI(id = "quantile_regression"))
                                       )
                                     ),
                                     tabPanel("Generalized Linear Models",
                                       tabsetPanel(
                                         tabPanel("Logistic Regression", logisticRegressionUI(id = "logr")),
                                         tabPanel("Poisson Regression", PoissonRegressionUI(id = "pois")),
                                         tabPanel("Quasi-Regression", QuasiRegressionUI(id = "quasi")),
                                         tabPanel("Zero-Inflated Models", ZeroInflatedUI(id = "zero"))
                                       )
                                     ),
                                     tabPanel("Advanced Regression",
                                       tabsetPanel(
                                         tabPanel("Propensity Score Analysis", PropensityScoreUI(id = "ps")),
                                         tabPanel("Bayesian Networks", bayesianNetworksUI(id = "bayesian_networks")),
                                         tabPanel("Causal Inference", causalInferenceUI(id = "causal_inference")),
                                         tabPanel("GEE (Generalized Estimating Equations)", geeUI(id = "gee")),
                                         tabPanel("GLMM (Generalized Linear Mixed Models)", glmmUI(id = "glmm")),
                                         tabPanel("Generalized Additive Models (GAM)", GAMUI(id = "gam")),
                                         tabPanel("Nonlinear Regression", NonlinearRegressionUI(id = "nlr")),
                                         tabPanel("Bayesian Regression", BayesianRegressionUI(id = "bayes_reg")),
                                         tabPanel("Mediation & Moderation", MediationModerationUI(id = "mm"))
                                       )
                                     ),
                                     tabPanel("Correlation Analysis",
                                       tabsetPanel(
                                         tabPanel("Correlation Analysis", CorrelationUI(id = "corr")),
                                         tabPanel("Partial Correlations", PartialCorrelationsUI(id = "partial_corr"))
                                       )
                                     )
                                   )
                          ),
                          tabPanel("Multivariate & Advanced",
                                   tabsetPanel(
                                     tabPanel("Multivariate Analysis",
                                       tabsetPanel(
                                         tabPanel("MANOVA", MANOVAUI(id = "manova")),
                                         tabPanel("Discriminant Analysis", DiscriminantUI(id = "disc")),
                                         tabPanel("Factor Analysis", factorAnalysisUI(id = "factor_analysis")),
                                         tabPanel("Discriminant Analysis (New)", discriminantAnalysisUI(id = "discriminant_analysis")),
                                         tabPanel("Correspondence Analysis", correspondenceAnalysisUI(id = "correspondence_analysis")),
                                         tabPanel("Canonical Correlation (CCA)", CCAUI(id = "cca")),
                                         tabPanel("Principal Component Analysis (PCA)", PCAUI(id = "pca")),
                                         tabPanel("Multidimensional Scaling (MDS)", MDSUI(id = "mds"))
                                       )
                                     ),
                                     tabPanel("Mixed & Multilevel Models",
                                       tabsetPanel(
                                         tabPanel("Mixed-Effects Models", MixedEffectsUI(id = "me")),
                                         tabPanel("Mixed ANOVA", MixedAnovaUI(id = "mixed_anova")),
                                         tabPanel("Multilevel Modeling", multilevelModelingUI(id = "mlm")),
                                         tabPanel("Bayesian Hierarchical Models", BayesianHierarchicalUI(id = "bh"))
                                       )
                                     ),
                                     tabPanel("Latent Variable Models",
                                       tabsetPanel(
                                         tabPanel("Structural Equation Modeling (SEM)", SEMUI(id = "sem")),
                                         tabPanel("Latent Class Analysis", LatentClassUI(id = "lc")),
                                         tabPanel("Item Response Theory (IRT)", IRTUI(id = "irt")),
                                         tabPanel("Log-linear Models", LogLinearModelsUI(id = "log_linear"))
                                       )
                                     )
                                   )
                          ),
                          tabPanel("Machine Learning & AI",
                                   tabsetPanel(
                                     tabPanel("Supervised Learning",
                                       tabsetPanel(
                                         tabPanel("Supervised Learning", SupervisedMLUI(id = "sml")),
                                         tabPanel("Model Comparison", ModelComparisonUI(id = "mcomp")),
                                         tabPanel("Feature Selection", FeatureSelectionUI(id = "fsel")),
                                         tabPanel("Decision Trees", decisionTreesUI(id = "decision_trees"))
                                       )
                                     ),
                                     tabPanel("Unsupervised Learning",
                                       tabsetPanel(
                                         tabPanel("Cluster Analysis", ClusterAnalysisUI(id = "clust")),
                                         tabPanel("Unsupervised Learning", UnsupervisedMLUI(id = "uml")),
                                         tabPanel("TSNE/UMAP", TSNEUMAPUI(id = "tsne"))
                                       )
                                     ),
                                     tabPanel("Ensemble Methods", ensembleMethodsUI(id = "ensemble")),
                                     tabPanel("Deep Learning & AI",
                                       tabsetPanel(
                                         tabPanel("Deep Learning", DeepLearningUI(id = "dl")),
                                         tabPanel("Natural Language Processing", NaturalLanguageProcessingUI(id = "nlp"))
                                       )
                                     )
                                   )
                          ),
                          tabPanel("Specialized Analysis",
                                   tabsetPanel(
                                     tabPanel("Survival Analysis",
                                       tabsetPanel(
                                         tabPanel("Basic Survival", SurvivalAnalysisUI(id = "surv")),
                                         tabPanel("Advanced Survival", AdvancedSurvivalUI(id = "asurv")),
                                         tabPanel("Cox Proportional Hazards", CoxPHUI(id = "cox")),
                                         tabPanel("Competing Risks", CompetingRisksUI(id = "comp")),
                                         tabPanel("Stratified Kaplan-Meier", StratifiedKMUI(id = "skm"))
                                       )
                                     ),
                                     tabPanel("Time Series & Forecasting",
                                       tabsetPanel(
                                         tabPanel("Time Series Analysis", TimeSeriesUI(id = "ts")),
                                         tabPanel("STL Decomposition", STLDecompositionUI(id = "stl")),
                                         tabPanel("State Space Models", StateSpaceUI(id = "ss")),
                                         tabPanel("Change Point Detection", ChangePointUI(id = "cp")),
                                         tabPanel("Spectral Analysis", SpectralAnalysisUI(id = "spec")),
                                         tabPanel("Ljung-Box Test", ljungBoxUI(id = "ljung_box")),
                                         tabPanel("Augmented Dickey-Fuller Test", augmentedDickeyFullerUI(id = "augmented_dickey_fuller"))
                                       )
                                     ),
                                     tabPanel("Longitudinal Analysis", longitudinalAnalysisUI(id = "long")),
                                     tabPanel("Spatial & Network",
                                       tabsetPanel(
                                         tabPanel("Spatial Analysis", SpatialAnalysisUI(id = "spatial")),
                                         tabPanel("Network Analysis", NetworkAnalysisUI(id = "net")),
                                         tabPanel("Social Network Analysis", socialNetworksUI(id = "social_networks"))
                                       )
                                     ),
                                     tabPanel("Quality Control",
                                       tabsetPanel(
                                         tabPanel("Control Charts", QualityControlChartsUI(id = "qc_charts"))
                                       )
                                     ),
                                     tabPanel("Experimental Design",
                                       tabsetPanel(
                                         tabPanel("Experimental Design Tools", experimentalDesignUI(id = "experimental_design"))
                                       )
                                     ),
                                     tabPanel("Real-time Analytics", RealTimeAnalyticsUI(id = "rta"))
                                   )
                          ),
                          tabPanel("Data & Visualization",
                                   tabsetPanel(
                                     tabPanel("Data Management",
                                       tabsetPanel(
                                         tabPanel("Data Summarization", DataSummarizationUI(id = "dsum")),
                                         tabPanel("Missingness Visualization", MissingnessVizUI(id = "miss")),
                                         tabPanel("Outlier Detection", OutlierDetectionUI(id = "outlier")),
                                         tabPanel("Variable Transformation", VariableTransformationUI(id = "var_trans")),
                                         tabPanel("Multiple Imputation", MultipleImputationUI(id = "mi")),
                                         tabPanel("Data Quality Assessment", dataQualityUI(id = "data_quality"))
                                       )
                                     ),
                                     tabPanel("Visualization",
                                       tabsetPanel(
                                         tabPanel("Advanced Visualization", advancedVisualizationUI(id = "adv_viz")),
                                         tabPanel("Custom Plot Builder", CustomPlotUI(id = "plot")),
                                         tabPanel("Correlation Heatmap", CorrHeatmapUI(id = "heat")),
                                         tabPanel("Pairwise Plot Matrix", PairwisePlotMatrixUI(id = "pair"))
                                       )
                                     ),
                                     tabPanel("Survey & Psychometrics", 
                                       tabsetPanel(
                                         tabPanel("Survey & Psychometrics", SurveyPsychometricsUI(id = "survey")),
                                         tabPanel("Reliability Analysis", ReliabilityAnalysisUI(id = "reliability"))
                                       )
                                     ),
                                     tabPanel("Interactive Dashboards", InteractiveDashboardsUI(id = "dash"))
                                   )
                          )
               )
      ),
      tabPanel("Authors", authorsUI())
    )
  )

ui <- fluidPage(id = "mainContainer",
                theme = bs_theme(version = 4, primary = "#18536F"),
                use_darkmode(),
                tags$div(style = paste(
                           "position: fixed",
                           "top: 20px",
                           "right: 30px",
                           "z-index: 99",
                           "font-size: 18px",
                           "border: none",
                           "outline: none",
                           "color: white",
                           "cursor: pointer",
                           "padding: 15px",
                           "border-radius: 4px",
                           sep = "; "
                         ),
                         HTML(r"[<button id=\"togglemode\" type=\"button\" class=\"btn btn-warning\">Toggle Dark Mode</button>]")),
                tags$div(style = paste("position: absolute",
                                       "top: 20px",
                                       "right: 200px",
                                       "z-index: 99",
                                       "font-size: 18px",
                                       "border: none",
                                       "outline: none",
                                       "color: white",
                                       "cursor: pointer",
                                       "padding: 15px",
                                       "border-radius: 4px",
                                       sep = "; "),
                         tags$a(href = "https://apps.apple.com/us/app/cougarstats/id6476070179",
                                target = "_blank",
                                tags$img(src = "AppStoreLogo.svg", title = "App Store Link", width = "150px"))),
                HEAD,
                BODY)
