SEMUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("semUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        textAreaInput(ns("semModel"), "Model Specification", value = "", rows = 5),
        br(),
        actionButton(ns("goSEM"), label = "Run SEM", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("semError")),
        tableOutput(ns("semResults")),
        plotOutput(ns("semPlot"))
      )
    )
  )
} 