MetaAnalysisUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("metaUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectInput(ns("metaEffectType"), "Effect Size Type", choices = c("Cohen's d", "Odds Ratio", "Correlation")),
        br(),
        actionButton(ns("goMeta"), label = "Run Meta-Analysis", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("metaError")),
        plotOutput(ns("metaForestPlot")),
        plotOutput(ns("metaFunnelPlot")),
        tableOutput(ns("metaSummary"))
      )
    )
  )
} 