# Placeholder for GLMM UI
glmmSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("glmmUserData"), "Upload your Data (.csv or .xls or .xlsx or .txt)", accept = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")),
    uiOutput(ns("glmmColSelectors")),
    actionButton(ns("goGLMM"), label = "Calculate", class = "act-btn")
  )
}

glmmMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('glmmHT')),
    plotOutput(ns('glmmPlot'), width = "50%", height = "400px"),
    uiOutput(ns('glmmConclusionOutput'))
  )
}

glmmUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(glmmSidebarUI(id)),
    mainPanel(glmmMainUI(id))
  )
} 