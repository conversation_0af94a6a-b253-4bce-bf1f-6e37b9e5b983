# Friedman Test Server
source("modules/calculations/friedman.R")
FriedmanTestServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    friedmanUploadData <- eventReactive(input$friedmanUserData, {
      handle_file_upload(input$friedmanUserData)
    })
    observeEvent(friedmanUploadData(), {
      data <- friedmanUploadData()
      updateSelectizeInput(session, 'friedmanSubject', choices = names(data), server = TRUE)
      updateSelectizeInput(session, 'friedmanResponse', choices = names(data), server = TRUE)
      updateSelectizeInput(session, 'friedmanCondition', choices = names(data), server = TRUE)
      output$friedmanResults <- renderUI({
        if (!is.null(data) && is.data.frame(data)) {
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('friedmanPreviewTable'))
          )
        } else NULL
      })
      output$friedmanPreviewTable <- DT::renderDT({
        head(data, 20)
      })
    })
    friedmanValidationErrors <- reactive({
      errors <- c()
      data <- friedmanUploadData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$friedmanSubject) || input$friedmanSubject == "") {
        errors <- c(errors, "Please select a subject ID column.")
      }
      if (is.null(input$friedmanResponse) || input$friedmanResponse == "") {
        errors <- c(errors, "Please select a response variable.")
      }
      if (is.null(input$friedmanCondition) || input$friedmanCondition == "") {
        errors <- c(errors, "Please select a condition/time column.")
      }
      errors
    })
    observeEvent(input$goFriedman, {
      output$friedmanResults <- renderUI({
        errors <- friedmanValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Friedman Test", errors = errors)
        } else {
          data <- friedmanUploadData()
          res <- calc_friedman_test(
            data,
            response = input$friedmanResponse,
            condition = input$friedmanCondition,
            subject = input$friedmanSubject
          )
          tagList(
            tabsetPanel(
              id = ns("friedmanTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("friedmanAnalysis"),
                title = "Analysis",
                titlePanel("Friedman Test Results"),
                br(),
                withMathJax(tagList(
                  h4('Hypotheses'),
                  p('$H_0$: The distributions of the groups are identical'),
                  p('$H_A$: At least one group differs in distribution'),
                  h4('Test Statistic'),
                  p('$Q = $', signif(res$statistic, 4)),
                  h4('P-value Method'),
                  p('$P = $', signif(res$p.value, 4)),
                  h4('Conclusion'),
                  if (res$p.value < 0.05) {
                    p('Since $P < 0.05$, reject $H_0$.')
                  } else {
                    p('Since $P \\geq 0.05$, do not reject $H_0$.')
                  }
                )),
                br(),
                h4("Effect Size"),
                uiOutput(ns('friedmanEffectSize')),
                br(),
                h4("Assumptions Check"),
                uiOutput(ns('friedmanAssumptions'))
              ),
              tabPanel(
                id = ns("friedmanDataSummary"),
                title = "Data Summary",
                h4("Descriptive Statistics by Condition"),
                tableOutput(ns('friedmanDescriptive')),
                br(),
                h4("Rank Summary"),
                tableOutput(ns('friedmanRankSummary'))
              ),
              tabPanel(
                id = ns("friedmanUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('friedmanDataTable'))
              )
            )
          )
        }
      })
    })
  })
} 