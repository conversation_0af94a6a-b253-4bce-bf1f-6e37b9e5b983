PowerAnalysisUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        shinyjs::useShinyjs(),
        div(
          id = ns("inputPanel"),
          radioButtons(
            inputId      = ns("powerDataInput"),
            label        = strong("Data"),
            choiceValues = list("Enter Parameters", "Upload Data"),
            choiceNames  = list("Enter Parameters", "Upload Data"),
            selected     = "Enter Parameters",
            inline       = TRUE
          ),
          conditionalPanel(
            condition = sprintf("input['%s'] == 'Upload Data'", ns("powerDataInput")),
            fileInput(
              inputId = ns("powerUserData"),
              label   = strong("Upload your Data (.csv or .xls or .xlsx or .txt)"),
              accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
            ),
            selectizeInput(
              inputId = ns("powerGroup1"),
              label   = strong("Select Group 1 Variable"),
              choices = NULL,
              multiple = FALSE
            ),
            selectizeInput(
              inputId = ns("powerGroup2"),
              label   = strong("Select Group 2 Variable"),
              choices = NULL,
              multiple = FALSE
            )
          ),
          selectInput(ns("powerTestType"), "Test Type", choices = c("t-test", "ANOVA", "Regression", "Proportion")),
          uiOutput(ns("powerParams")),
          br(),
          actionButton(ns("goPower"), label = "Calculate Power", class = "act-btn")
        )
      ),
      mainPanel(
        uiOutput(ns("powerError")),
        uiOutput(ns("powerResults"))
      )
    )
  )
} 