UnsupervisedMLServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    umlData <- eventReactive(input$umlUserData, {
      handle_file_upload(input$umlUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(umlData(), {
      data <- umlData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, "umlVars", choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    umlValidationErrors <- reactive({
      errors <- c()
      data <- umlData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$umlVars) || length(input$umlVars) < 2) {
        errors <- c(errors, "Please select at least 2 variables for clustering.")
      }
      
      if (!is.null(input$umlVars) && length(input$umlVars) >= 2) {
        # Check if all variables are numeric
        for (var in input$umlVars) {
          if (!is.numeric(data[[var]])) {
            errors <- c(errors, sprintf("Variable '%s' must be numeric.", var))
          }
        }
        
        # Check for sufficient observations
        n_obs <- nrow(data)
        n_vars <- length(input$umlVars)
        
        if (n_obs < 10) {
          errors <- c(errors, "At least 10 observations are required for clustering.")
        }
        
        if (n_obs <= n_vars) {
          errors <- c(errors, sprintf("Number of observations (%d) must be greater than number of variables (%d).", n_obs, n_vars))
        }
      }
      
      # Check number of clusters
      if (!is.null(input$umlClusters) && (input$umlClusters < 2 || input$umlClusters > 20)) {
        errors <- c(errors, "Number of clusters must be between 2 and 20.")
      }
      
      errors
    })
    
    # Unsupervised ML analysis reactive
    umlResult <- eventReactive(input$goUML, {
      data <- umlData()
      req(data, input$umlVars, input$umlMethod, input$umlClusters)
      
      # Remove rows with missing values
      complete_data <- data[complete.cases(data[, input$umlVars, drop = FALSE]), ]
      
      if (nrow(complete_data) < 10) {
        stop("Insufficient complete cases for clustering analysis.")
      }
      
      # Prepare data for clustering
      x <- complete_data[, input$umlVars, drop = FALSE]
      
      # Perform clustering based on method
      tryCatch({
        method <- tolower(input$umlMethod)
        n_clusters <- input$umlClusters
        
        if (method == "k-means") {
          # K-means clustering
          set.seed(123)  # For reproducibility
          km_result <- kmeans(x, centers = n_clusters, nstart = 25)
          
          # Calculate cluster statistics
          cluster_stats <- data.frame(
            Cluster = 1:n_clusters,
            Size = km_result$size,
            Within_SS = km_result$withinss,
            stringsAsFactors = FALSE
          )
          
          # Calculate silhouette score if cluster package is available
          silhouette_score <- NULL
          if (requireNamespace("cluster", quietly = TRUE)) {
            sil <- cluster::silhouette(km_result$cluster, dist(x))
            silhouette_score <- mean(sil[, 3])
          }
          
          list(
            method = "K-means",
            clusters = km_result$cluster,
            centers = km_result$centers,
            total_ss = km_result$totss,
            within_ss = km_result$tot.withinss,
            between_ss = km_result$betweenss,
            cluster_stats = cluster_stats,
            silhouette_score = silhouette_score,
            n_clusters = n_clusters,
            n_observations = nrow(complete_data),
            n_variables = length(input$umlVars),
            variables = input$umlVars,
            data = complete_data
          )
          
        } else if (method == "hierarchical") {
          # Hierarchical clustering
          dist_matrix <- dist(x)
          hc_result <- hclust(dist_matrix, method = "ward.D2")
          
          # Cut tree to get clusters
          clusters <- cutree(hc_result, k = n_clusters)
          
          # Calculate cluster centers
          centers <- aggregate(x, by = list(clusters), FUN = mean)[, -1]
          
          # Calculate cluster statistics
          cluster_stats <- data.frame(
            Cluster = 1:n_clusters,
            Size = table(clusters),
            stringsAsFactors = FALSE
          )
          
          # Calculate silhouette score
          silhouette_score <- NULL
          if (requireNamespace("cluster", quietly = TRUE)) {
            sil <- cluster::silhouette(clusters, dist_matrix)
            silhouette_score <- mean(sil[, 3])
          }
          
          list(
            method = "Hierarchical",
            clusters = clusters,
            centers = centers,
            dendrogram = hc_result,
            cluster_stats = cluster_stats,
            silhouette_score = silhouette_score,
            n_clusters = n_clusters,
            n_observations = nrow(complete_data),
            n_variables = length(input$umlVars),
            variables = input$umlVars,
            data = complete_data
          )
          
        } else if (method == "gmm") {
          # Gaussian Mixture Model
          if (!requireNamespace("mclust", quietly = TRUE)) {
            stop("Package 'mclust' is required for Gaussian Mixture Models.")
          }
          
          gmm_result <- mclust::Mclust(x, G = n_clusters)
          
          # Calculate cluster statistics
          cluster_stats <- data.frame(
            Cluster = 1:n_clusters,
            Size = table(gmm_result$classification),
            stringsAsFactors = FALSE
          )
          
          list(
            method = "Gaussian Mixture Model",
            clusters = gmm_result$classification,
            centers = gmm_result$parameters$mean,
            model = gmm_result,
            cluster_stats = cluster_stats,
            n_clusters = n_clusters,
            n_observations = nrow(complete_data),
            n_variables = length(input$umlVars),
            variables = input$umlVars,
            data = complete_data
          )
          
        } else {
          stop("Unsupported clustering method.")
        }
        
      }, error = function(e) {
        stop(e$message)
      })
    })
    
    # Error handling
    output$umlError <- renderUI({
      errors <- umlValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          umlResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "Unsupervised ML Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$umlModelSummary <- renderUI({
      req(umlResult())
      res <- umlResult()
      
      tagList(
        h4("Unsupervised Machine Learning Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Metric = c("Clustering Method", "Number of Clusters", "Number of Variables", "Number of Observations"),
            Value = c(
              res$method,
              res$n_clusters,
              res$n_variables,
              res$n_observations
            )
          )
        }),
        h4("Cluster Statistics"),
        renderTable({
          res$cluster_stats
        }),
        if (!is.null(res$silhouette_score)) {
          tagList(
            h4("Model Quality"),
            renderTable({
              data.frame(
                Metric = c("Silhouette Score"),
                Value = c(round(res$silhouette_score, 4))
              )
            })
          )
        }
      )
    })
    
    output$umlPlot <- renderPlot({
      req(umlResult())
      res <- umlResult()
      
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # Main clustering plot (first two variables)
      if (res$n_variables >= 2) {
        plot(res$data[[res$variables[1]]], res$data[[res$variables[2]]], 
             col = res$clusters, pch = 19, 
             main = paste(res$method, "Clustering"),
             xlab = res$variables[1], ylab = res$variables[2])
        
        # Add cluster centers if available
        if (!is.null(res$centers)) {
          if (is.matrix(res$centers)) {
            points(res$centers[, 1], res$centers[, 2], 
                   col = 1:res$n_clusters, pch = 4, cex = 2, lwd = 3)
          } else {
            points(res$centers[1], res$centers[2], 
                   col = 1:res$n_clusters, pch = 4, cex = 2, lwd = 3)
          }
        }
      }
      
      # Cluster size distribution
      barplot(res$cluster_stats$Size, main = "Cluster Sizes",
              xlab = "Cluster", ylab = "Number of Observations",
              col = rainbow(res$n_clusters))
      
      # Dendrogram for hierarchical clustering
      if (res$method == "Hierarchical" && !is.null(res$dendrogram)) {
        plot(res$dendrogram, main = "Hierarchical Clustering Dendrogram",
             xlab = "Observations", ylab = "Distance")
        abline(h = mean(res$dendrogram$height), col = "red", lty = 2)
      } else {
        # Alternative: cluster quality plot
        if (!is.null(res$silhouette_score)) {
          plot(1, res$silhouette_score, main = "Silhouette Score",
               xlab = "", ylab = "Score", pch = 19, cex = 2, col = "blue",
               ylim = c(-1, 1))
          abline(h = 0, col = "red", lty = 2)
        } else {
          # Variable importance or distribution
          if (res$n_variables >= 2) {
            boxplot(res$data[[res$variables[1]]] ~ res$clusters, 
                    main = paste("Distribution by Cluster -", res$variables[1]),
                    xlab = "Cluster", ylab = res$variables[1])
          }
        }
      }
      
      # Variable distributions by cluster
      if (res$n_variables >= 1) {
        boxplot(res$data[[res$variables[1]]] ~ res$clusters, 
                main = paste("Distribution by Cluster -", res$variables[1]),
                xlab = "Cluster", ylab = res$variables[1])
      }
      
      par(mfrow = c(1, 1))
    })
    
    output$umlDiagnostics <- renderUI({
      req(umlResult())
      res <- umlResult()
      
      tagList(
        h4("Clustering Diagnostics"),
        renderTable({
          data.frame(
            Metric = c("Clustering Method", "Number of Clusters", "Total Observations", "Average Cluster Size"),
            Value = c(
              res$method,
              res$n_clusters,
              res$n_observations,
              round(mean(res$cluster_stats$Size), 2)
            )
          )
        }),
        if (!is.null(res$silhouette_score)) {
          tagList(
            h4("Quality Metrics"),
            renderTable({
              data.frame(
                Metric = c("Silhouette Score", "Interpretation"),
                Value = c(
                  round(res$silhouette_score, 4),
                  ifelse(res$silhouette_score > 0.7, "Strong structure",
                         ifelse(res$silhouette_score > 0.5, "Reasonable structure",
                                ifelse(res$silhouette_score > 0.25, "Weak structure", "No substantial structure")))
                )
              )
            })
          )
        }
      )
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$umlDataSummary <- renderUI({
      req(umlData(), input$umlVars)
      data <- umlData()
      vars <- input$umlVars
      
      tagList(
        h4("Dataset Summary"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "Variables for Clustering", "Complete Cases"),
            Value = c(
              nrow(data),
              ncol(data),
              length(vars),
              sum(complete.cases(data[, vars, drop = FALSE]))
            )
          )
        }),
        h4("Variable Summary"),
        renderTable({
          if (!is.null(vars) && length(vars) > 0) {
            summary_stats <- data.frame(
              Variable = vars,
              Mean = sapply(vars, function(v) round(mean(data[[v]], na.rm = TRUE), 4)),
              SD = sapply(vars, function(v) round(sd(data[[v]], na.rm = TRUE), 4)),
              Min = sapply(vars, function(v) round(min(data[[v]], na.rm = TRUE), 4)),
              Max = sapply(vars, function(v) round(max(data[[v]], na.rm = TRUE), 4)),
              Missing = sapply(vars, function(v) sum(is.na(data[[v]]))),
              stringsAsFactors = FALSE
            )
            summary_stats
          } else {
            data.frame(Metric = "N/A", Value = "N/A")
          }
        })
      )
    })
    
    output$umlAssumptions <- renderUI({
      req(umlResult())
      res <- umlResult()
      
      tagList(
        h4("Clustering Assumptions Check"),
        renderTable({
          data.frame(
            Assumption = c("Numeric Variables", "Adequate Sample Size", "Variable Scaling", "Cluster Number"),
            Status = c(
              "Pass",
              ifelse(res$n_observations >= 30, "Pass", "Fail"),
              "Pass",
              ifelse(res$n_clusters >= 2 && res$n_clusters <= res$n_observations/10, "Pass", "Fail")
            ),
            Description = c(
              "All variables are numeric for clustering",
              "Sufficient observations for reliable clustering",
              "Variables are appropriately scaled",
              "Number of clusters is reasonable for data size"
            )
          )
        }),
        h4("Clustering Method Guidelines"),
        renderTable({
          data.frame(
            Method = c("K-means", "Hierarchical", "GMM"),
            Use_When = c(
              "Spherical clusters, known number of clusters",
              "Unknown number of clusters, hierarchical structure",
              "Elliptical clusters, probabilistic assignment"
            ),
            Pros = c(
              "Fast, simple, works well with spherical clusters",
              "Visual dendrogram, no assumptions about cluster shape",
              "Probabilistic, handles different cluster shapes"
            )
          )
        })
      )
    })
    
    output$umlDiagnosticPlots <- renderPlot({
      req(umlResult())
      res <- umlResult()
      
      par(mfrow = c(2, 2))
      
      # Elbow plot for k-means
      if (res$method == "K-means") {
        # Calculate within-cluster sum of squares for different k
        wss <- numeric(10)
        for (i in 1:10) {
          km <- kmeans(res$data[, res$variables, drop = FALSE], centers = i, nstart = 25)
          wss[i] <- km$tot.withinss
        }
        plot(1:10, wss, type = "b", main = "Elbow Plot",
             xlab = "Number of Clusters", ylab = "Within-Cluster Sum of Squares")
      } else {
        # Alternative: cluster quality plot
        if (!is.null(res$silhouette_score)) {
          plot(1, res$silhouette_score, main = "Silhouette Score",
               xlab = "", ylab = "Score", pch = 19, cex = 2, col = "blue",
               ylim = c(-1, 1))
          abline(h = 0, col = "red", lty = 2)
        }
      }
      
      # Cluster size distribution
      barplot(res$cluster_stats$Size, main = "Cluster Sizes",
              xlab = "Cluster", ylab = "Number of Observations",
              col = rainbow(res$n_clusters))
      
      # Variable distributions by cluster
      if (res$n_variables >= 1) {
        boxplot(res$data[[res$variables[1]]] ~ res$clusters, 
                main = paste("Distribution by Cluster -", res$variables[1]),
                xlab = "Cluster", ylab = res$variables[1])
      }
      
      # Correlation heatmap of variables
      if (res$n_variables >= 2) {
        cor_matrix <- cor(res$data[, res$variables, drop = FALSE])
        heatmap(cor_matrix, main = "Variable Correlation Matrix",
                Rowv = NA, Colv = NA, scale = "none")
      }
      
      par(mfrow = c(1, 1))
    })
    
    # Uploaded Data Tab Outputs
    output$umlDataTable <- renderDT({
      req(umlData())
      data <- umlData()
      
      DT::datatable(
        data,
        options = list(
          pageLength = 10,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
        ),
        extensions = 'Buttons',
        filter = 'top',
        rownames = FALSE
      )
    })
    
    output$umlDataInfo <- renderUI({
      req(umlData())
      data <- umlData()
      
      tagList(
        h4("Dataset Information"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "File Name"),
            Value = c(nrow(data), ncol(data), 
                     ifelse(!is.null(input$umlUserData), input$umlUserData$name, "Unknown"))
          )
        }),
        h4("Variable Information"),
        renderTable({
          data.frame(
            Variable = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Unique = sapply(data, function(x) length(unique(x)))
          )
        })
      )
    })
    
    # Legacy outputs for backward compatibility
    output$umlSummary <- renderTable({
      req(umlResult())
      res <- umlResult()
      
      data.frame(
        Metric = c("Clustering Method", "Number of Clusters", "Number of Observations", "Silhouette Score"),
        Value = c(
          res$method,
          res$n_clusters,
          res$n_observations,
          ifelse(!is.null(res$silhouette_score), round(res$silhouette_score, 4), "N/A")
        )
      )
    })
  })
} 