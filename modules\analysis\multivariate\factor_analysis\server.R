factorAnalysisServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactives
    faUploadData <- eventReactive(input$faUserData, {
      handle_file_upload(input$faUserData)
    })
    
    faResults <- reactive({
      data <- faUploadData()
      if (is.null(data) || is.null(input$faVariables) || length(input$faVariables) < 2) {
        return(NULL)
      }
      faResults_func(
        TRUE,
        data,
        input$faVariables,
        input$faNFactors,
        input$faRotation,
        input$faMethod
      )
    })
    
    # Validation errors
    faValidationErrors <- reactive({
      errors <- c()
      data <- faUploadData()
      
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$faVariables) || length(input$faVariables) < 2) {
        errors <- c(errors, "Please select at least two variables for factor analysis.")
      }
      
      if (length(input$faVariables) < input$faNFactors) {
        errors <- c(errors, "Number of factors cannot exceed the number of variables.")
      }
      
      if (!is.null(input$faVariables) && length(input$faVariables) > 0) {
        for (var in input$faVariables) {
          if (!var %in% names(data)) {
            errors <- c(errors, sprintf("Variable '%s' not found in the data.", var))
          } else {
            if (length(unique(data[[var]])) < 2) {
              errors <- c(errors, sprintf("Variable '%s' must have at least two unique values.", var))
            }
          }
        }
      }
      
      errors
    })
    
    # Outputs
    output$faResults <- renderUI({
      results <- faResults()
      if (is.null(results)) return(NULL)
      factorAnalysisResults(faResults)
    })
    
    output$factorAnalysisPlot <- renderPlot({
      results <- faResults()
      if (is.null(results)) return(NULL)
      factorAnalysisPlot(faResults)
    })
    
    output$faInterpretation <- renderUI({
      results <- faResults()
      if (is.null(results)) return(NULL)
      factorAnalysisInterpretation(faResults)
    })
    
    output$faLoadingsOutput <- renderUI({
      results <- faResults()
      if (is.null(results)) return(NULL)
      factorAnalysisLoadings(faResults)
    })
    
    output$faLoadingsTable <- DT::renderDT({
      results <- faResults()
      if (is.null(results) || is.null(results$loadings)) return(NULL)
      
      loadings_df <- results$loadings
      DT::datatable(loadings_df,
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(loadings_df)))))
    })
    
    output$faScoresOutput <- renderUI({
      results <- faResults()
      if (is.null(results)) return(NULL)
      factorAnalysisScores(faResults)
    })
    
    output$faScoresTable <- DT::renderDT({
      results <- faResults()
      if (is.null(results) || is.null(results$scores)) return(NULL)
      
      scores_df <- results$scores
      DT::datatable(scores_df,
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(scores_df)))))
    })
    
    output$renderFAData <- renderUI({
      req(faUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("faInitialUploadTable")), style = "width: 75%"),
        br(),
        br()
      )
    })
    
    output$faInitialUploadTable <- DT::renderDT({
      req(faUploadData())
      DT::datatable(faUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(faUploadData())))))
    })
    
    # Update selectizeInput choices after file upload
    observeEvent(faUploadData(), {
      data <- faUploadData()
      
      # Clear selectizeInputs and main panel
      updateSelectizeInput(session, 'faVariables', choices = character(0), selected = NULL, server = TRUE)
      output$factorAnalysisResults <- renderUI({ NULL })
      
      # If data is valid, update choices and show preview
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'faVariables', choices = names(data), server = TRUE)
        
        output$factorAnalysisResults <- renderUI({
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('faPreviewTable'))
          )
        })
        
        output$faPreviewTable <- DT::renderDT({
          head(data, 20)
        })
      }
    })
    
    # Show main results or error screen when Calculate is pressed
    observeEvent(input$goFactorAnalysis, {
      output$factorAnalysisResults <- renderUI({
        errors <- faValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Factor Analysis", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("faTabset"),
              selected = "Analysis",
              tabPanel(
                id    = ns("fa"),
                title = "Analysis",
                titlePanel("Factor Analysis Results"),
                br(),
                uiOutput(ns('faResults')),
                br(),
                plotOutput(ns('factorAnalysisPlot'), width = "100%", height = "500px"),
                br(),
                uiOutput(ns('faInterpretation'))
              ),
              tabPanel(
                id    = ns("faLoadings"),
                title = "Factor Loadings",
                DTOutput(ns("faLoadingsTable")),
                uiOutput(ns("faLoadingsOutput"))
              ),
              tabPanel(
                id    = ns("faScores"),
                title = "Factor Scores",
                DTOutput(ns("faScoresTable")),
                uiOutput(ns("faScoresOutput"))
              ),
              tabPanel(
                id    = ns("faData"),
                title = "Uploaded Data",
                uiOutput(ns("renderFAData"))
              )
            )
          )
        }
      })
    })
  })
} 