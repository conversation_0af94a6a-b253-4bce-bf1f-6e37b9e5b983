# Post-hoc Tests calculation and output helpers

post_hoc_uploadData_func <- function(phUserData) {
  ext <- tools::file_ext(phUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(phUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(phUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(phUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(phUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

post_hoc_results_func <- function(data, response, group, test_type = "Tukey HSD") {
  tryCatch({
    
    result <- calc_post_hoc(data, response, group, test_type)
    
    list(
      results = result,
      data = data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Post-hoc test calculation:", e$message))
  })
}

post_hoc_ht_html <- function(results) {
  tagList(
    h4("Post-hoc Test Results"),
    p("The table below shows the pairwise comparisons between groups.")
  )
}

post_hoc_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  out <- list(renderPrint(results$results))
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

post_hoc_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  plot(results$results, main = 'Post-hoc Comparisons')
}

# --- Helper function from original file ---

calc_post_hoc <- function(data, response, group, test_type = "Tukey HSD") {
  if (test_type == "Tukey HSD") {
    model <- aov(as.formula(paste(response, "~", group)), data = data)
    result <- TukeyHSD(model)
  } else if (test_type == "Dunn's Test") {
    if (!requireNamespace("FSA", quietly = TRUE)) stop("Package 'FSA' needed for Dunn's test.")
    result <- FSA::dunnTest(as.formula(paste(response, "~", group)), data = data)
  } else {
    stop("Unknown post-hoc test type.")
  }
  result
}