PCAServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    pcaData <- eventReactive(input$pcaUserData, {
      handle_file_upload(input$pcaUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(pcaData(), {
      data <- pcaData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'pcaVars', choices = names(data), server = TRUE)
      }
    })
    
    # PCA analysis reactive
    pcaAnalysis <- reactive({
      req(pcaData(), input$pcaVars)
      data <- pcaData()
      
      # Remove rows with missing values
      complete_data <- data[complete.cases(data[, input$pcaVars, drop = FALSE]), ]
      
      if (nrow(complete_data) < 2) return(NULL)
      
      # Get parameters from UI
      scale_data <- ifelse(is.null(input$pcaScale), TRUE, input$pcaScale)
      center_data <- ifelse(is.null(input$pcaCenter), TRUE, input$pcaCenter)
      
      # Perform PCA analysis
      tryCatch({
        pca_analysis(complete_data, input$pcaVars, scale. = scale_data, center = center_data)
      }, error = function(e) {
        NULL
      })
    })
    
    # Validation errors reactive
    pcaValidationErrors <- reactive({
      errors <- c()
      data <- pcaData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$pcaVars) || length(input$pcaVars) < 2) {
        errors <- c(errors, "Select at least two variables for PCA.")
      }
      
      if (!is.null(input$pcaVars) && length(input$pcaVars) >= 2) {
        for (var in input$pcaVars) {
          if (!is.numeric(data[[var]])) {
            errors <- c(errors, sprintf("Variable '%s' must be numeric.", var))
          }
        }
        
        # Check for sufficient observations
        complete_cases <- complete.cases(data[, input$pcaVars, drop = FALSE])
        n_complete <- sum(complete_cases)
        if (n_complete < 2) {
          errors <- c(errors, "At least 2 complete observations are required for PCA.")
        }
        
        # Check for sufficient variables relative to observations
        if (n_complete <= length(input$pcaVars)) {
          errors <- c(errors, "Number of observations must be greater than number of variables.")
        }
      }
      
      errors
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goPCA, {
      output$pcaResults <- renderUI({
        errors <- pcaValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in PCA", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("pcaTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("pcaAnalysis"),
                title = "Analysis",
                titlePanel("Principal Component Analysis Results"),
                br(),
                h4("Component Summary"),
                tableOutput(ns('pcaComponentSummary')),
                br(),
                h4("Variable Loadings"),
                tableOutput(ns('pcaLoadings')),
                br(),
                h4("Component Scores (First 10 observations)"),
                tableOutput(ns('pcaScores')),
                br(),
                h4("Model Fit Statistics"),
                tableOutput(ns('pcaFitStats')),
                br(),
                h4("Component Selection"),
                uiOutput(ns('pcaComponentSelection'))
              ),
              tabPanel(
                id = ns("pcaDiagnostics"),
                title = "Data Summary/Diagnostics",
                h4("Data Characteristics"),
                tableOutput(ns('pcaDataSummary')),
                br(),
                h4("Correlation Matrix"),
                tableOutput(ns('pcaCorrelationMatrix')),
                br(),
                h4("Assumptions Check"),
                uiOutput(ns('pcaAssumptions')),
                br(),
                h4("Scree Plot"),
                plotOutput(ns('pcaScreePlot'), height = "400px"),
                br(),
                h4("Cumulative Variance Plot"),
                plotOutput(ns('pcaCumulativePlot'), height = "400px"),
                br(),
                h4("Biplot"),
                plotOutput(ns('pcaBiplot'), height = "500px")
              ),
              tabPanel(
                id = ns("pcaUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('pcaDataTable'))
              )
            )
          )
        }
      })
    })
    
    # Component summary
    output$pcaComponentSummary <- renderTable({
      req(pcaAnalysis())
      analysis <- pcaAnalysis()
      if (!is.null(analysis)) {
        analysis$summary
      }
    }, digits = 4)
    
    # Variable loadings
    output$pcaLoadings <- renderTable({
      req(pcaAnalysis())
      analysis <- pcaAnalysis()
      if (!is.null(analysis)) {
        loadings_df <- as.data.frame(analysis$loadings)
        loadings_df$Variable <- rownames(loadings_df)
        loadings_df <- loadings_df[, c("Variable", names(loadings_df)[-ncol(loadings_df)])]
        loadings_df
      }
    }, digits = 4)
    
    # Component scores
    output$pcaScores <- renderTable({
      req(pcaAnalysis())
      analysis <- pcaAnalysis()
      if (!is.null(analysis)) {
        scores_df <- as.data.frame(analysis$scores)
        scores_df$Observation <- 1:nrow(scores_df)
        scores_df <- scores_df[, c("Observation", names(scores_df)[-ncol(scores_df)])]
        head(scores_df, 10)
      }
    }, digits = 4)
    
    # Model fit statistics
    output$pcaFitStats <- renderTable({
      req(pcaAnalysis())
      analysis <- pcaAnalysis()
      if (!is.null(analysis)) {
        # Calculate additional fit statistics
        total_variance <- sum(analysis$explained_variance)
        explained_variance <- analysis$explained_variance
        proportion_variance <- analysis$proportion_variance
        
        result <- data.frame(
          Statistic = c("Total Variance", "Number of Components", "Kaiser Criterion Components", "80% Variance Components"),
          Value = c(
            round(total_variance, 4),
            analysis$n_components,
            analysis$kaiser_components,
            sum(cumsum(proportion_variance) <= 0.8) + 1
          ),
          stringsAsFactors = FALSE
        )
        result
      }
    }, digits = 4)
    
    # Component selection guidance
    output$pcaComponentSelection <- renderUI({
      req(pcaAnalysis())
      analysis <- pcaAnalysis()
      
      if (!is.null(analysis)) {
        kaiser_components <- analysis$kaiser_components
        var_80_components <- sum(cumsum(analysis$proportion_variance) <= 0.8) + 1
        
        tagList(
          h5("Component Selection Guidelines:"),
          p(strong("Kaiser Criterion (eigenvalue > 1): "), kaiser_components, " components"),
          p(strong("80% Variance Rule: "), var_80_components, " components"),
          br(),
          p("Note: Consider both criteria and interpretability when selecting the number of components.")
        )
      }
    })
    
    # Data characteristics
    output$pcaDataSummary <- renderTable({
      req(pcaAnalysis())
      analysis <- pcaAnalysis()
      if (!is.null(analysis)) {
        data <- pcaData()
        vars <- input$pcaVars
        
        summary_stats <- data.frame(
          Variable = vars,
          N = sapply(vars, function(v) sum(!is.na(data[[v]]))),
          Mean = sapply(vars, function(v) round(mean(data[[v]], na.rm = TRUE), 4)),
          SD = sapply(vars, function(v) round(sd(data[[v]], na.rm = TRUE), 4)),
          Min = sapply(vars, function(v) round(min(data[[v]], na.rm = TRUE), 4)),
          Max = sapply(vars, function(v) round(max(data[[v]], na.rm = TRUE), 4)),
          stringsAsFactors = FALSE
        )
        summary_stats
      }
    }, digits = 4)
    
    # Correlation matrix
    output$pcaCorrelationMatrix <- renderTable({
      req(pcaAnalysis())
      analysis <- pcaAnalysis()
      if (!is.null(analysis)) {
        data <- pcaData()
        vars <- input$pcaVars
        
        cor_matrix <- cor(data[, vars], use = "complete.obs")
        cor_df <- as.data.frame(cor_matrix)
        cor_df$Variable <- rownames(cor_df)
        cor_df <- cor_df[, c("Variable", names(cor_df)[-ncol(cor_df)])]
        cor_df
      }
    }, digits = 4)
    
    # Assumptions check
    output$pcaAssumptions <- renderUI({
      req(pcaAnalysis())
      analysis <- pcaAnalysis()
      
      if (!is.null(analysis)) {
        data <- pcaData()
        vars <- input$pcaVars
        
        # Check assumptions
        n_obs <- nrow(data[complete.cases(data[, vars, drop = FALSE]), ])
        n_vars <- length(vars)
        
        # KMO test
        kmo_text <- if (!is.na(analysis$kmo)) {
          kmo_interpretation <- if (analysis$kmo >= 0.9) {
            "(Excellent)"
          } else if (analysis$kmo >= 0.8) {
            "(Good)"
          } else if (analysis$kmo >= 0.7) {
            "(Fair)"
          } else {
            "(Poor)"
          }
          paste("KMO =", round(analysis$kmo, 4), kmo_interpretation)
        } else {
          "KMO test not available"
        }
        
        # Bartlett's test
        bartlett_text <- if (!is.na(analysis$bartlett$chi_square)) {
          bartlett_interpretation <- if (analysis$bartlett$p_value < 0.05) {
            "(Significant)"
          } else {
            "(Not significant)"
          }
          paste("Bartlett's χ² =", round(analysis$bartlett$chi_square, 4), 
                ", p =", round(analysis$bartlett$p_value, 4), bartlett_interpretation)
        } else {
          "Bartlett's test not available"
        }
        
        tagList(
          h5("PCA Assumptions:"),
          p("1. Variables are continuous"),
          p("2. Linear relationships between variables"),
          p("3. Adequate sample size"),
          p("4. No multicollinearity"),
          p("5. Variables are correlated"),
          br(),
          p(strong("Sample size: "), n_obs, "observations,", n_vars, "variables"),
          p(strong("Ratio: "), round(n_obs/n_vars, 2), "observations per variable"),
          p(strong("Kaiser-Meyer-Olkin: "), kmo_text),
          p(strong("Bartlett's Test: "), bartlett_text),
          br(),
          p("Note: KMO ≥ 0.8 and significant Bartlett's test suggest PCA is appropriate.")
        )
      }
    })
    
    # Scree plot
    output$pcaScreePlot <- renderPlot({
      req(pcaAnalysis())
      analysis <- pcaAnalysis()
      
      if (!is.null(analysis)) {
        scree_data <- analysis$scree_data
        ggplot2::ggplot(scree_data, ggplot2::aes(x = Component, y = Eigenvalue)) +
          ggplot2::geom_line() +
          ggplot2::geom_point() +
          ggplot2::geom_hline(yintercept = 1, linetype = "dashed", color = "red") +
          ggplot2::labs(title = "Scree Plot", x = "Component", y = "Eigenvalue") +
          ggplot2::theme_minimal()
      }
    })
    
    # Cumulative variance plot
    output$pcaCumulativePlot <- renderPlot({
      req(pcaAnalysis())
      analysis <- pcaAnalysis()
      
      if (!is.null(analysis)) {
        cumulative_data <- data.frame(
          Component = 1:length(analysis$cumulative_proportion),
          Cumulative_Variance = analysis$cumulative_proportion * 100
        )
        
        ggplot2::ggplot(cumulative_data, ggplot2::aes(x = Component, y = Cumulative_Variance)) +
          ggplot2::geom_line() +
          ggplot2::geom_point() +
          ggplot2::geom_hline(yintercept = 80, linetype = "dashed", color = "red") +
          ggplot2::labs(title = "Cumulative Variance Explained", 
                       x = "Component", y = "Cumulative Variance (%)") +
          ggplot2::theme_minimal()
      }
    })
    
    # Biplot
    output$pcaBiplot <- renderPlot({
      req(pcaAnalysis())
      analysis <- pcaAnalysis()
      
      if (!is.null(analysis)) {
        # Create biplot using ggplot2
        scores <- as.data.frame(analysis$scores[, 1:2])
        loadings <- as.data.frame(analysis$loadings[, 1:2])
        
        # Scale loadings for visibility
        loadings_scaled <- loadings * max(abs(scores)) * 0.8
        
        ggplot2::ggplot() +
          ggplot2::geom_point(data = scores, ggplot2::aes(x = PC1, y = PC2), alpha = 0.6) +
          ggplot2::geom_segment(data = loadings_scaled, 
                               ggplot2::aes(x = 0, y = 0, xend = PC1, yend = PC2),
                               arrow = ggplot2::arrow(length = ggplot2::unit(0.2, "cm")),
                               color = "red", size = 1) +
          ggplot2::geom_text(data = loadings_scaled, 
                            ggplot2::aes(x = PC1, y = PC2, label = rownames(loadings)),
                            color = "red", fontface = "bold", size = 4) +
          ggplot2::labs(title = "PCA Biplot", x = "PC1", y = "PC2") +
          ggplot2::theme_minimal() +
          ggplot2::coord_fixed()
      }
    })
    
    # Data table
    output$pcaDataTable <- DT::renderDT({
      req(pcaData())
      DT::datatable(pcaData(),
        options = list(pageLength = 25,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(pcaData())))))
    })
  })
} 