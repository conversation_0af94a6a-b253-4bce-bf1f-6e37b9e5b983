VariableTransformationUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("vtUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("vtVars"), "Variables to Transform", choices = NULL, multiple = TRUE),
        selectInput(ns("vtMethod"), "Transformation", choices = c("Log", "Square Root", "Box-Cox", "Z-score", "Min-Max Scaling")),
        br(),
        actionButton(ns("goVT"), label = "Transform", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("vtError")),
        tableOutput(ns("vtResults")),
        plotOutput(ns("vtPlot"))
      )
    )
  )
} 