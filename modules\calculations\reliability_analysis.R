# Reliability Analysis calculation and output helpers

reliability_analysis_uploadData_func <- function(raUserData, item_cols) {
  tryCatch(
    {
      if (is.null(raUserData) || is.null(item_cols)) {
        return(NULL)
      }
      
      # Read data based on file type
      if (grepl("\\.csv$", raUserData$name, ignore.case = TRUE)) {
        df <- read.csv(raUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.tsv$", raUserData$name, ignore.case = TRUE)) {
        df <- read.tsv(raUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.xlsx$", raUserData$name, ignore.case = TRUE)) {
        df <- readxl::read_excel(raUserData$datapath)
      } else {
        stop("Unsupported file type. Please upload a CSV, TSV, or XLSX file.")
      }
      
      # Check for required columns
      if (!all(item_cols %in% names(df))) {
        missing_cols <- item_cols[!item_cols %in% names(df)]
        stop(paste("The following columns were not found in the uploaded file:", paste(missing_cols, collapse = ", ")))
      }
      
      return(df[, item_cols])
    },
    error = function(e) {
      return(list(error = paste("Error reading data:", e$message)))
    }
  )
}

reliability_analysis_results_func <- function(data) {
  tryCatch({
    if (is.null(data) || nrow(data) == 0) {
      stop("No data provided for analysis.")
    }
    
    if (!requireNamespace("psych", quietly = TRUE)) {
      stop("Package 'psych' is required for Reliability Analysis.")
    }
    
    # Perform reliability analysis
    alpha_results <- psych::alpha(data)
    
    list(
      alpha_results = alpha_results,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Reliability Analysis:", e$message))
  })
}

reliability_analysis_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  tagList(
    h4("Reliability Analysis (Cronbach's Alpha)"),
    p(paste("Raw Alpha:", round(results$alpha_results$total$raw_alpha, 3)))
  )
}

reliability_analysis_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  renderPrint(results$alpha_results)
}

reliability_analysis_plot <- function(results) {
  # The psych package does not have a default plot for alpha objects.
  # A plot of item-total correlations could be created manually if desired.
  return(NULL)
}
