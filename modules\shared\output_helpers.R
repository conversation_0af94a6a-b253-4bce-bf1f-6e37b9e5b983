# Shared output formatting helpers

# (Copy relevant functions from R/printOneMean.R and R/printANOVA.R)

# Example:
printHTConclusion <- function(region, reject, suffEvidence, altHyp, altHypValue) {
  conclusion <- tagList(
    withMathJax(),
    p(tags$b("Conclusion:")),
    sprintf("At \\( \\alpha = %s \\), since the test statistic falls in the %s region we %s \\(
               H_{0}\\) and conclude that there %s enough statistical evidence to support that \\(%s %s\\).",
            SigLvl(),
            region,
            reject,
            suffEvidence,
            altHyp,
            altHypValue),
    br(),
    br()
  )
  return(conclusion)
}

# ... (repeat for other helpers) 