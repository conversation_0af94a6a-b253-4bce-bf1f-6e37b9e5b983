# One-sample inference calculation and output helpers

one_sample_uploadData_func <- function(osUserData) {
  ext <- tools::file_ext(osUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(osUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(osUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(osUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(osUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

one_sample_results_func <- function(data, var, mu = 0, sigma = NULL, alternative = "two.sided", conf_level = 0.95) {
  tryCatch({
    x <- data[[var]]
    x <- x[!is.na(x)]
    
    if (length(x) < 2) {
      stop("At least 2 observations are required.")
    }
    
    if (!is.null(sigma)) { # Z-test
      test_result <- BSDA::z.test(x, mu = mu, sigma.x = sigma, alternative = alternative, conf.level = conf_level)
    } else { # T-test
      test_result <- t.test(x, mu = mu, alternative = alternative, conf.level = conf_level)
    }
    
    list(
      test = test_result,
      data = x,
      var = var,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during one-sample test calculation:", e$message))
  })
}

one_sample_ht_html <- function(results, sigLvl) {
  test <- results$test
  p_value <- test$p.value
  
  conclusion <- if (p_value < sigLvl) "significant" else "not significant"
  
  withMathJax(tagList(
    h4("One-Sample Test"),
    p(sprintf("The difference between the sample mean and the hypothesized mean is %s.", conclusion)),
    p(sprintf("Sample Mean: %.4f", test$estimate)),
    p(sprintf("Test Statistic: %.4f", test$statistic)),
    p(sprintf("P-value: %.4f", p_value))
  ))
}

one_sample_summary_html <- function(results) {
  test <- results$test
  
  desc_stats <- data.frame(
    Variable = results$var,
    N = length(results$data),
    Mean = mean(results$data),
    SD = sd(results$data)
  )
  
  tagList(
    h4("Test Details"),
    renderPrint(test),
    h4("Descriptive Statistics"),
    renderTable(desc_stats, digits = 4)
  )
}

one_sample_plot <- function(results) {
  plot_data <- data.frame(
    Value = results$data
  )
  
  ggplot(plot_data, aes(x = Value)) +
    geom_histogram(bins = 20, fill = "steelblue", alpha = 0.7) +
    geom_vline(xintercept = results$test$null.value, color = "red", linetype = "dashed") +
    labs(title = "Distribution of Sample Data",
         x = "Value",
         y = "Frequency") +
    theme_minimal()
}