# Multiple Linear Regression calculation and output helpers

multiple_linear_regression_uploadData_func <- function(mlrUserData) {
  ext <- tools::file_ext(mlrUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(mlrUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(mlrUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(mlrUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(mlrUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

multiple_linear_regression_results_func <- function(data, response, explanatory) {
  tryCatch({
    model <- calc_mlr_model(data, response, explanatory)
    
    list(
      model = model,
      data = data,
      response = response,
      explanatory = explanatory,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Multiple Linear Regression calculation:", e$message))
  })
}

multiple_linear_regression_ht_html <- function(results) {
  model_summary <- summary(results$model)
  f_stat <- model_summary$fstatistic
  p_value <- pf(f_stat[1], f_stat[2], f_stat[3], lower.tail = FALSE)
  
  tagList(
    h4("Overall Model F-test"),
    p(sprintf("F-statistic: %.4f on %d and %d DF, p-value: %.4f", f_stat[1], f_stat[2], f_stat[3], p_value))
  )
}

multiple_linear_regression_summary_html <- function(results) {
  tagList(
    h4("Coefficients"),
    renderTable(broom::tidy(results$model)),
    h4("Model Fit"),
    renderTable(data.frame(
      R_squared = summary(results$model)$r.squared,
      Adj_R_squared = summary(results$model)$adj.r.squared,
      AIC = AIC(results$model),
      BIC = BIC(results$model)
    ))
  )
}

multiple_linear_regression_plot <- function(results) {
  if (!requireNamespace("ggResidpanel", quietly = TRUE)) {
    return(p("Package 'ggResidpanel' required for plotting."))
  }
  ggResidpanel::resid_panel(results$model)
}

# --- Helper functions from original file ---

calc_mlr_model <- function(data, response, explanatory) {
  lm(reformulate(as.character(explanatory), response), data = data)
}