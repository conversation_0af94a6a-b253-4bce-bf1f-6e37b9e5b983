# Quality Control Charts Server
# Process monitoring and control

source("modules/calculations/quality_control_charts.R")

QualityControlChartsServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactive values
    qc_data <- reactiveVal(NULL)
    qc_results <- reactiveVal(NULL)
    
    # File upload reactive
    qcUploadData <- eventReactive(input$qcUserData, {
      handle_file_upload(input$qcUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(qcUploadData(), {
      data <- qcUploadData()
      # Clear selectizeInputs and main panel
      updateSelectizeInput(session, 'qcVariable', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'qcGroupVariable', choices = character(0), selected = NULL, server = TRUE)
      output$qcResults <- renderUI({ NULL })
      
      # If data is valid, update choices
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'qcVariable', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'qcGroupVariable', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    qcValidationErrors <- reactive({
      errors <- c()
      
      if (input$qcDataMethod == "Manual Entry") {
        # Manual entry validation
        if (is.null(input$qcManualData) || input$qcManualData == "") {
          errors <- c(errors, "Manual data entry is required.")
        } else {
          # Basic validation for manual entry
          tryCatch({
            data <- parse_qc_data(input$qcManualData)
            if (nrow(data) < 5) {
              errors <- c(errors, "At least 5 observations are required for quality control analysis.")
            }
          }, error = function(e) {
            errors <- c(errors, "Invalid manual data format. Please check your input.")
          })
        }
      } else {
        # File upload validation
        data <- qcUploadData()
        if (is.null(data) || !is.data.frame(data)) {
          errors <- c(errors, "No data uploaded or file could not be read.")
          return(errors)
        }
        if (is.null(input$qcVariable) || input$qcVariable == "") {
          errors <- c(errors, "Please select a variable for quality control analysis.")
        } else {
          var_data <- data[[input$qcVariable]]
          if (!is.numeric(var_data)) {
            errors <- c(errors, "Selected variable must be numeric.")
          }
          if (length(na.omit(var_data)) < 5) {
            errors <- c(errors, "At least 5 non-missing observations are required.")
          }
        }
      }
      
      errors
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goQC, {
      output$qcResults <- renderUI({
        errors <- qcValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Quality Control Analysis", errors = errors)
        } else {
          tryCatch({
            # Prepare data
            if (input$qcDataMethod == "Manual Entry") {
              # Parse manual entry data
              data <- parse_qc_data(input$qcManualData)
            } else {
              # Use uploaded data
              req(qcUploadData(), input$qcVariable)
              
              data <- qcUploadData()
              variable <- input$qcVariable
              group_var <- input$qcGroupVariable
              
              # Select relevant columns
              if (is.null(group_var) || group_var == "") {
                # Individual values chart
                data <- data.frame(Value = data[[variable]], Group = 1:nrow(data))
              } else {
                # Grouped data
                data <- data.frame(Value = data[[variable]], Group = data[[group_var]])
              }
            }
            
            # Remove rows with missing values
            complete_cases <- complete.cases(data)
            if (sum(complete_cases) < nrow(data)) {
              warning("Removing rows with missing values")
              data <- data[complete_cases, ]
            }
            
            # Check data structure
            if (nrow(data) < 5) {
              stop("At least 5 observations are required")
            }
            
            # Perform quality control analysis
            results <- perform_quality_control_analysis(data, 
                                                      chart_type = input$qcChartType,
                                                      sigma_level = input$qcSigmaLevel,
                                                      use_historical_limits = input$qcUseHistoricalLimits,
                                                      historical_periods = input$qcHistoricalPeriods,
                                                      show_trends = input$qcShowTrends,
                                                      show_violations = input$qcShowViolations)
            
            # Store results
            qc_results(results)
            
            # Display results
            tagList(
              h3("Quality Control Charts Results"),
              
              # Data summary
              h4("Data Summary"),
              renderTable({
                summary_table <- data.frame(
                  Statistic = c("Number of Observations", "Number of Groups", "Chart Type"),
                  Value = c(results$n_observations, results$n_groups, results$chart_type)
                )
                summary_table
              }, rownames = FALSE),
              
              br(),
              
              # Control chart
              h4("Control Chart"),
              renderPlot({
                if (results$chart_type == "xbar_r") {
                  # X-bar chart
                  ggplot(results$chart_data, aes(x = Group, y = Mean)) +
                    geom_line() +
                    geom_point() +
                    geom_hline(yintercept = results$xbar_center, color = "blue", linetype = "solid") +
                    geom_hline(yintercept = results$xbar_ucl, color = "red", linetype = "dashed") +
                    geom_hline(yintercept = results$xbar_lcl, color = "red", linetype = "dashed") +
                    labs(title = "X-bar Control Chart",
                         x = "Group", y = "Mean") +
                    theme_minimal()
                } else if (results$chart_type == "individual") {
                  # Individual values chart
                  ggplot(results$chart_data, aes(x = 1:nrow(results$chart_data), y = Value)) +
                    geom_line() +
                    geom_point() +
                    geom_hline(yintercept = results$individual_center, color = "blue", linetype = "solid") +
                    geom_hline(yintercept = results$individual_ucl, color = "red", linetype = "dashed") +
                    geom_hline(yintercept = results$individual_lcl, color = "red", linetype = "dashed") +
                    labs(title = "Individual Values Control Chart",
                         x = "Observation", y = "Value") +
                    theme_minimal()
                }
              }),
              
              br(),
              
              # Range Chart
              if (results$chart_type == "xbar_r") tagList(
                h4("Range Chart"),
                renderPlot({
                  ggplot(results$range_data, aes(x = Subgroup, y = Range)) +
                    geom_line() +
                    geom_point() +
                    geom_hline(yintercept = results$range_limits$ucl, color = "red", linetype = "dashed") +
                    geom_hline(yintercept = results$range_limits$lcl, color = "red", linetype = "dashed") +
                    geom_hline(yintercept = results$range_limits$center, color = "blue", linetype = "solid") +
                    labs(title = "Range Control Chart",
                         x = "Subgroup", y = "Range") +
                    theme_minimal()
                }),
                br()
              ),
              
              # Process capability
              if (!is.null(results$process_capability)) tagList(
                h4("Process Capability"),
                renderTable({
                  capability_table <- results$process_capability
                  capability_table
                }, rownames = FALSE),
                br()
              ),
              
              # Violations
              if (!is.null(results$violations) && length(results$violations) > 0) tagList(
                h4("Control Chart Violations"),
                renderDataTable({
                  # Combine all violations
                  all_violations <- do.call(rbind, results$violations)
                  if (!is.null(all_violations) && nrow(all_violations) > 0) {
                    datatable(all_violations, 
                             options = list(pageLength = 10, scrollX = TRUE),
                             rownames = FALSE)
                  } else {
                    data.frame(Message = "No violations detected")
                  }
                }),
                br()
              ),
              
              # Control limits summary
              h4("Control Limits Summary"),
              renderTable({
                if (results$chart_type == "xbar_r") {
                  limits_table <- data.frame(
                    Chart = c("X-bar Chart", "X-bar Chart", "X-bar Chart", "R Chart", "R Chart", "R Chart"),
                    Limit = c("Center Line", "UCL", "LCL", "Center Line", "UCL", "LCL"),
                    Value = c(results$xbar_center, results$xbar_ucl, results$xbar_lcl,
                             results$r_center, results$r_ucl, results$r_lcl),
                    stringsAsFactors = FALSE
                  )
                } else if (results$chart_type == "individual") {
                  limits_table <- data.frame(
                    Chart = c("Individual Chart", "Individual Chart", "Individual Chart"),
                    Limit = c("Center Line", "UCL", "LCL"),
                    Value = c(results$individual_center, results$individual_ucl, results$individual_lcl),
                    stringsAsFactors = FALSE
                  )
                }
                limits_table
              }, rownames = FALSE),
              
              br(),
              
              # Educational content
              h4("Interpretation"),
              p(strong("Control Charts:"), "Monitor process stability and detect special causes of variation."),
              p(strong("Control Limits:"), "UCL and LCL represent the expected range of variation under normal conditions."),
              p(strong("Violations:"), "Points outside control limits or unusual patterns indicate special causes."),
              p(strong("Process Capability:"), "Measures how well the process meets specifications.")
            )
          }, error = function(e) {
            errorScreenUI(title = "Error in Quality Control Analysis", errors = e$message)
          })
        }
      })
    })
    
    # Helper function to parse numeric input
    parse_qc_data <- function(input_text) {
      if (is.null(input_text) || input_text == "") {
        stop("Please enter data")
      }
      
      # Parse the input text into a data frame
      lines <- strsplit(input_text, "\n")[[1]]
      lines <- lines[lines != ""]
      
      if (length(lines) < 2) {
        stop("At least 2 lines required (header + data)")
      }
      
      # Parse header
      header <- strsplit(lines[1], ",")[[1]]
      header <- trimws(header)
      
      # Parse data
      data_lines <- lines[-1]
      data_matrix <- matrix(NA, nrow = length(data_lines), ncol = length(header))
      
      for (i in 1:length(data_lines)) {
        values <- strsplit(data_lines[i], ",")[[1]]
        values <- trimws(values)
        values <- as.numeric(values)
        
        if (length(values) != length(header)) {
          stop(paste("Line", i + 1, "has incorrect number of values"))
        }
        
        data_matrix[i, ] <- values
      }
      
      data.frame(data_matrix, stringsAsFactors = FALSE)
    }
  })
} 