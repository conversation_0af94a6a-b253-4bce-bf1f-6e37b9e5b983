library(testthat)
library(here)
source(here::here("modules/shared/utils.R"))
source(here::here("modules/shared/parse_helpers.R"))
source(here::here("modules/shared/validation_helpers.R"))
source(here::here("modules/shared/file_upload.R"))
source(here::here("modules/shared/desc_stats_helpers.R"))
source(here::here("modules/shared/plot_helpers.R"))
source(here::here("modules/shared/plot_options_menu.R"))
source(here::here("modules/shared/output_helpers.R"))

test_that("createNumLst parses numbers correctly", {
  expect_equal(createNumLst("1,2,3"), c(1,2,3))
})

test_that("parse_numeric parses space/comma separated numbers", {
  expect_equal(parse_numeric("1 2,3"), c(1,2,3))
})

test_that("validate_numeric_input works", {
  expect_true(validate_numeric_input(c(1,2,3), min=1, max=3))
  expect_false(validate_numeric_input(c(0,2,3), min=1, max=3))
})

test_that("handle_file_upload returns NULL for NULL input", {
  expect_null(handle_file_upload(NULL))
})

test_that("DecimalCount returns 0 for integer", {
  expect_equal(DecimalCount(5), 0)
})

test_that("generic_plot_theme returns a ggplot2 theme", {
  expect_true(inherits(generic_plot_theme(), "theme"))
})

test_that("plotOptionsMenuUI returns a tagList", {
  expect_true(is.list(plotOptionsMenuUI("test")))
})

test_that("printHTConclusion returns a tagList", {
  expect_true(is.list(printHTConclusion("rejection", "reject", "is", "x > 0", "")))
}) 