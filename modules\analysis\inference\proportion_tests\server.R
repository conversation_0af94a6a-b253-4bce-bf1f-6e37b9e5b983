# Proportion Tests Server
source("modules/calculations/proportion_tests.R")
ProportionTestServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    propTestUploadData <- eventReactive(input$propTestUserData, {
      handle_file_upload(input$propTestUserData)
    })
    observeEvent(propTestUploadData(), {
      data <- propTestUploadData()
      updateSelectizeInput(session, 'propTestGroup', choices = names(data), server = TRUE)
      updateSelectizeInput(session, 'propTestResponse', choices = names(data), server = TRUE)
      output$propTestResults <- renderUI({
        if (!is.null(data) && is.data.frame(data)) {
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('propTestPreviewTable'))
          )
        } else NULL
      })
      output$propTestPreviewTable <- DT::renderDT({
        head(data, 20)
      })
    })
    propTestValidationErrors <- reactive({
      errors <- c()
      data <- propTestUploadData()
      if (input$propTestType == "One-sample Proportion" || input$propTestType == "Binomial Test") {
        if (is.na(input$propTestSuccesses) || is.na(input$propTestTrials) || input$propTestTrials < 1) {
          errors <- c(errors, "Please enter valid numbers for successes and trials.")
        } else if (input$propTestSuccesses > input$propTestTrials) {
          errors <- c(errors, "Number of successes cannot exceed number of trials.")
        }
      } else if (input$propTestType == "Two-sample Proportion" || input$propTestType == "Fisher's Exact Test") {
        if (is.null(data) || !is.data.frame(data)) {
          errors <- c(errors, "No data uploaded or file could not be read.")
          return(errors)
        }
        if (is.null(input$propTestGroup) || input$propTestGroup == "") {
          errors <- c(errors, "Please select a group column.")
        }
        if (is.null(input$propTestResponse) || input$propTestResponse == "") {
          errors <- c(errors, "Please select a response column.")
        }
      }
      errors
    })
    observeEvent(input$goPropTest, {
      output$propTestResults <- renderUI({
        errors <- propTestValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Proportion Test", errors = errors)
        } else {
          res <- NULL
          if (input$propTestType == "One-sample Proportion") {
            res <- calc_proportion_test(input$propTestSuccesses, input$propTestTrials, type = "one_sample")
          } else if (input$propTestType == "Binomial Test") {
            res <- calc_proportion_test(input$propTestSuccesses, input$propTestTrials, type = "binomial")
          } else if (input$propTestType == "Two-sample Proportion") {
            res <- calc_proportion_test(input$propTestSuccesses, input$propTestTrials, type = "two_sample")
          } else if (input$propTestType == "Fisher's Exact Test") {
            res <- calc_proportion_test(input$propTestSuccesses, input$propTestTrials, type = "fisher")
          }
          
          tagList(
            tabsetPanel(
              id = ns("propTestTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("propTestAnalysis"),
                title = "Analysis",
                titlePanel("Proportion Test Results"),
                br(),
                withMathJax(tagList(
                  h4('Hypotheses'),
                  if (input$propTestType == "One-sample Proportion" || input$propTestType == "Binomial Test") {
                    tagList(
                      p('$H_0$: $p = p_0$'),
                      p('$H_A$: $p \\neq p_0$')
                    )
                  } else {
                    tagList(
                      p('$H_0$: $p_1 = p_2$'),
                      p('$H_A$: $p_1 \\neq p_2$')
                    )
                  },
                  h4('Test Statistic'),
                  if (!is.null(res$statistic)) {
                    p('$Z = $', signif(res$statistic, 4))
                  },
                  h4('P-value Method'),
                  p('$P = $', signif(res$p.value, 4)),
                  h4('Confidence Interval'),
                  if (!is.null(res$conf.int)) {
                    p('$CI = (', signif(res$conf.int[1], 4), ', ', signif(res$conf.int[2], 4), ')$')
                  },
                  h4('Conclusion'),
                  if (res$p.value < 0.05) {
                    p('Since $P < 0.05$, reject $H_0$.')
                  } else {
                    p('Since $P \\geq 0.05$, do not reject $H_0$.')
                  }
                )),
                br(),
                h4("Effect Size"),
                uiOutput(ns('propTestEffectSize')),
                br(),
                h4("Assumptions Check"),
                uiOutput(ns('propTestAssumptions'))
              ),
              tabPanel(
                id = ns("propTestDataSummary"),
                title = "Data Summary",
                h4("Proportion Summary"),
                tableOutput(ns('propTestSummary')),
                br(),
                h4("Contingency Table"),
                tableOutput(ns('propTestContingency'))
              ),
              tabPanel(
                id = ns("propTestUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('propTestDataTable'))
              )
            )
          )
        }
      })
    })
    
    # Additional outputs for the enhanced tabs
    output$propTestEffectSize <- renderUI({
      tagList(
        p("Effect size interpretation:"),
        p("- 0.0 to 0.1: Negligible"),
        p("- 0.1 to 0.3: Small"),
        p("- 0.3 to 0.5: Medium"),
        p("- 0.5 to 1.0: Large")
      )
    })
    
    output$propTestAssumptions <- renderUI({
      tagList(
        h5("Key Assumptions:"),
        p("1. Independent observations"),
        p("2. Binomial distribution (for one-sample)"),
        p("3. Large sample size (for normal approximation)"),
        p("4. Random sampling"),
        br(),
        p("Note: For small samples, exact tests are preferred.")
      )
    })
    
    output$propTestSummary <- renderTable({
      # Placeholder for proportion summary
      data.frame(
        Statistic = c("Successes", "Trials", "Proportion", "Standard Error"),
        Value = c("N/A", "N/A", "N/A", "N/A"),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    output$propTestContingency <- renderTable({
      # Placeholder for contingency table
      data.frame(
        Group = c("Group 1", "Group 2"),
        Successes = c("N/A", "N/A"),
        Failures = c("N/A", "N/A"),
        Total = c("N/A", "N/A"),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    output$propTestDataTable <- DT::renderDT({
      req(propTestUploadData())
      DT::datatable(propTestUploadData(),
        options = list(pageLength = 25,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(propTestUploadData())))))
    })
  })
} 