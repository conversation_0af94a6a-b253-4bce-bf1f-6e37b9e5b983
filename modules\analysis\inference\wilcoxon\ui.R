wilcoxonSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(
      inputId = ns("wilUserData"),
      label   = strong("Upload your Data (.csv, .xls, .xlsx, .txt)"),
      accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
    ),
    selectizeInput(
      inputId = ns("wilX"),
      label   = strong("First Variable (paired)"),
      choices = c(""),
      selected = NULL,
      options = list(placeholder = 'Select first variable')
    ),
    selectizeInput(
      inputId = ns("wilY"),
      label   = strong("Second Variable (paired)"),
      choices = c(""),
      selected = NULL,
      options = list(placeholder = 'Select second variable')
    ),
    radioButtons(
      inputId = ns("wilAlternative"),
      label   = strong("Alternative Hypothesis"),
      choices = c("two.sided", "less", "greater"),
      selected = "two.sided",
      inline = TRUE
    ),
    sliderInput(
      inputId = ns("wilConfLevel"),
      label   = strong("Confidence Level"),
      min = 0.80, max = 0.99, value = 0.95, step = 0.01
    ),
    br(),
    actionButton(ns("goInference"), label = "Calculate", class = "act-btn")
  )
}

wilcoxonMainUI <- function(id) {
  ns <- NS(id)
  uiOutput(ns('wilcoxonResults'))
}