SpectralAnalysisUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("specUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("specSeries"), "Series Variable", choices = NULL),
        br(),
        actionButton(ns("goSpec"), label = "Run Spectral Analysis", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("specError")),
        plotOutput(ns("specPlot")),
        tableOutput(ns("specSummary"))
      )
    )
  )
} 