# Cluster Analysis calculation and output helpers

cluster_analysis_uploadData_func <- function(caUserData) {
  ext <- tools::file_ext(caUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(caUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(caUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(caUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(caUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

cluster_analysis_results_func <- function(data, vars, method = "kmeans", k = 3) {
  tryCatch({
    x <- data[, vars, drop = FALSE]
    x_scaled <- scale(na.omit(x))
    
    if (method == "kmeans") {
      fit <- kmeans(x_scaled, centers = k, nstart = 25)
    } else if (method == "hclust") {
      d <- dist(x_scaled)
      fit <- hclust(d, method = "ward.D2")
    } else {
      stop("Unknown clustering method.")
    }
    
    list(
      fit = fit,
      data = x_scaled,
      method = method,
      k = k,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Cluster Analysis:", e$message))
  })
}

cluster_analysis_ht_html <- function(results) {
  # No formal hypothesis test for clustering, summary is more informative
  tagList(
    h4("Cluster Analysis"),
    p("See summary table for cluster details.")
  )
}

cluster_analysis_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  if (results$method == "kmeans") {
    summary_table <- data.frame(
      Cluster = 1:results$k,
      Size = results$fit$size,
      Within_SS = round(results$fit$withinss, 4)
    )
    out <- list(
      h4("K-Means Clustering Summary"),
      renderTable(summary_table)
    )
  } else {
    out <- list(
      h4("Hierarchical Clustering Summary"),
      p(paste("Number of clusters:", results$k))
    )
  }
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

cluster_analysis_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  if (results$method == "kmeans") {
    plot(results$data, col = results$fit$cluster, main = 'K-Means Clusters')
  } else if (results$method == "hclust") {
    plot(results$fit, main = 'Hierarchical Clustering Dendrogram')
  }
}