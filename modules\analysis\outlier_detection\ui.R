OutlierDetectionUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("outUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("outVars"), "Variables", choices = NULL, multiple = TRUE),
        br(),
        actionButton(ns("goOut"), label = "Detect Outliers", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("outError")),
        tableOutput(ns("outResults")),
        plotOutput(ns("outPlot"))
      )
    )
  )
} 