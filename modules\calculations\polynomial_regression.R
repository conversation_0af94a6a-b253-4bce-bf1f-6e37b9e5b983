# Polynomial Regression calculation and output helpers

polynomial_regression_uploadData_func <- function(prUserData) {
  ext <- tools::file_ext(prUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(prUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(prUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(prUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(prUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

polynomial_regression_results_func <- function(data, response_var, predictor_var, degree = 2) {
  tryCatch({
    all_vars <- c(response_var, predictor_var)
    model_data <- data[complete.cases(data[all_vars]), ]
    
    formula_str <- paste0("`", response_var, "` ~ poly(`", predictor_var, "`, ", degree, ", raw = TRUE)")
    model <- lm(as.formula(formula_str), data = model_data)
    
    list(
      model = model,
      data = model_data,
      response_var = response_var,
      predictor_var = predictor_var,
      degree = degree,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Polynomial Regression calculation:", e$message))
  })
}

polynomial_regression_ht_html <- function(results) {
  # No single hypothesis test, summary is more informative
  tagList(
    h4("Polynomial Regression Model"),
    p("See summary table for model coefficients and diagnostics.")
  )
}

polynomial_regression_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  model_summary <- summary(results$model)
  out <- list(
    h4("Model Summary"),
    renderPrint(model_summary)
  )
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

polynomial_regression_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  # Fitted curve
  x <- results$data[[results$predictor_var]]
  y <- results$data[[results$response_var]]
  plot(x, y, main = 'Polynomial Regression Fit', xlab = results$predictor_var, ylab = results$response_var)
  lines(sort(x), fitted(results$model)[order(x)], col = 'red', lwd = 2)
  # Residuals
  resids <- residuals(results$model)
  plot(resids, type = 'h', main = 'Polynomial Regression Residuals', ylab = 'Residuals')
}