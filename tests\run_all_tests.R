#!/usr/bin/env Rscript
# Comprehensive Test Runner for CougarStats
# Runs all test suites and provides detailed reporting

library(testthat)
library(dplyr)
library(ggplot2)
library(DescTools)  # For Mode function
library(survival)   # For survival analysis functions
library(e1071)      # For skewness and kurtosis functions

# Set up test environment
cat("Starting Comprehensive CougarStats Module Testing\n")
cat("================================================\n\n")

# Source shared modules first
cat("Loading shared modules...\n")
shared_files <- list.files("modules/shared", pattern = "\\.R$", full.names = TRUE)
for (file in shared_files) {
  tryCatch({
    source(file)
    cat(sprintf("  Loaded: %s\n", basename(file)))
  }, error = function(e) {
    cat(sprintf("  Failed to load: %s - %s\n", basename(file), e$message))
  })
}

# Source all calculation modules
cat("Loading calculation modules...\n")
calculation_files <- list.files("modules/calculations", pattern = "\\.R$", full.names = TRUE, recursive = TRUE)
for (file in calculation_files) {
  tryCatch({
    source(file)
    cat(sprintf("  Loaded: %s\n", basename(file)))
  }, error = function(e) {
    cat(sprintf("  Failed to load: %s - %s\n", basename(file), e$message))
  })
}

# Initialize test results storage
test_results <- list()
test_summary <- list(
  total_modules = 0,
  passed = 0,
  failed = 0,
  errors = 0,
  start_time = Sys.time()
)

# Helper function to load test data
load_test_data <- function(module_name) {
  sample_data_dir <- "sample_data"
  
  # Test data mapping
  test_data_mapping <- list(
    # Basic Statistics
    "desc_stats" = "desc_stats.csv",
    "prob_dist" = "desc_stats.csv",
    "sample_size_est" = "desc_stats.csv",
    "data_summarization" = "data_summarization.csv",
    "missingness_viz" = "missingness.csv",
    "outlier_detection" = "desc_stats.csv",
    "variable_transformation" = "desc_stats.csv",
    "pairwise_plot_matrix" = "desc_stats.csv",
    "simulation" = "simulation.csv",
    
    # Inference Tests
    "one_sample" = "ttest_independent.csv",
    "two_sample" = "ttest_independent.csv",
    "paired_t_test" = "ttest_paired.csv",
    "anova" = "anova_oneway.csv",
    "two_way_anova" = "anova_oneway.csv",
    "repeated_measures_anova" = "anova_oneway.csv",
    "friedman" = "friedman.csv",
    "levene" = "anova_oneway.csv",
    "post_hoc" = "anova_oneway.csv",
    "kruskal_wallis" = "kruskal_wallis.csv",
    "chi_square" = "chi_square.csv",
    "mcnemar" = "chi_square.csv",
    "cochrans_q" = "chi_square.csv",
    "proportion_tests" = "proportion_test.csv",
    "mann_whitney" = "ttest_independent.csv",
    "wilcoxon" = "wilcoxon.csv",
    "bayesian" = "ttest_independent.csv",
    "bayesian_model_comparison" = "regression_multiple.csv",
    "custom_test" = "custom_test.csv",
    "power_analysis" = "desc_stats.csv",
    
    # Regression & Correlation
    "simple_linear_regression" = "regression_simple.csv",
    "multiple_linear_regression" = "regression_multiple.csv",
    "logistic_regression" = "logistic_regression.csv",
    "correlation" = "correlation.csv",
    "robust_regression" = "regression_multiple.csv",
    "poisson_regression" = "poisson_regression.csv",
    "quasi_regression" = "poisson_regression.csv",
    "zero_inflated" = "zero_inflated.csv",
    "bayesian_regression" = "regression_multiple.csv",
    "survival_analysis" = "survival.csv",
    "nonlinear_regression" = "nonlinear_regression.csv",
    "gam" = "gam.csv",
    "manova" = "manova.csv",
    "mediation_moderation" = "mediation_moderation.csv",
    "propensity_score" = "propensity_score.csv",
    "bayesian_hierarchical" = "bayesian_hierarchical.csv",
    
    # Advanced Analysis
    "pca" = "pca.csv",
    "cluster_analysis" = "cluster_analysis.csv",
    "roc_analysis" = "roc_analysis.csv",
    "meta_analysis" = "meta_analysis.csv",
    "mixed_effects" = "mixed_effects.csv",
    "survey_psychometrics" = "desc_stats.csv",
    "time_series" = "time_series.csv",
    "stl_decomposition" = "time_series.csv",
    "state_space" = "state_space.csv",
    "change_point" = "time_series.csv",
    "spectral_analysis" = "time_series.csv",
    "network_analysis" = "network_analysis.csv",
    "sem" = "sem.csv",
    "latent_class" = "latent_class.csv",
    "irt" = "irt.csv",
    "multiple_imputation" = "multiple_imputation.csv",
    "tsne_umap" = "tsne_umap.csv",
    "discriminant" = "discriminant.csv",
    "cca" = "cca.csv",
    "mds" = "mds.csv",
    "competing_risks" = "survival.csv",
    "correlation_heatmap" = "correlation.csv",
    "coxph" = "coxph.csv",
    "stratified_km" = "stratified_km.csv",
    "advanced_survival" = "advanced_survival.csv",
    
    # Machine Learning
    "supervised_ml" = "supervised_ml.csv",
    "unsupervised_ml" = "unsupervised_ml.csv",
    "model_comparison" = "model_comparison.csv",
    "feature_selection" = "feature_selection.csv",
    
    # Other modules
    "bootstrap" = "bootstrapping.csv",
    "permutation_tests" = "permutation_tests.csv"
  )
  
  data_file <- test_data_mapping[[module_name]]
  if (is.null(data_file)) {
    # Default to desc_stats.csv if no specific mapping
    data_file <- "desc_stats.csv"
  }
  
  file_path <- file.path(sample_data_dir, data_file)
  if (file.exists(file_path)) {
    read.csv(file_path, stringsAsFactors = FALSE)
  } else {
    # Create minimal test data if file doesn't exist
    data.frame(
      x = rnorm(20),
      y = rnorm(20),
      group = rep(c("A", "B"), each = 10),
      stringsAsFactors = FALSE
    )
  }
}

# Function to run tests for a module
run_module_tests <- function(module_name, test_function) {
  cat(sprintf("Testing module: %s\n", module_name))
  test_summary$total_modules <<- test_summary$total_modules + 1
  
  tryCatch({
    result <- test_function()
    if (result$success) {
      test_summary$passed <<- test_summary$passed + 1
      cat(sprintf("  ✓ %s: PASSED\n", module_name))
    } else {
      test_summary$failed <<- test_summary$failed + 1
      cat(sprintf("  ✗ %s: FAILED - %s\n", module_name, result$error))
    }
    test_results[[module_name]] <<- result
  }, error = function(e) {
    test_summary$errors <<- test_summary$errors + 1
    cat(sprintf("  ✗ %s: ERROR - %s\n", module_name, e$message))
    test_results[[module_name]] <<- list(success = FALSE, error = e$message)
  })
}

# Function to test calculation functions
test_calculation_function <- function(function_name, test_data, ...) {
  if (exists(function_name)) {
    tryCatch({
      func <- get(function_name)
      result <- func(test_data, ...)
      return(list(success = TRUE, result = result))
    }, error = function(e) {
      return(list(success = FALSE, error = e$message))
    })
  } else {
    return(list(success = FALSE, error = "Function not found"))
  }
}

# Create wrapper functions for testing
create_test_wrappers <- function() {
  # Descriptive Statistics wrapper
  desc_stats_analysis <<- function(data, variable) {
    if (is.character(variable)) {
      var_data <- data[[variable]]
    } else {
      var_data <- variable
    }
    var_data <- as.numeric(var_data)
    var_data <- var_data[!is.na(var_data)]
    
    if (length(var_data) == 0) {
      stop("No valid numeric data")
    }
    
    # Use the createDSColumn function from the shared module
    result <- createDSColumn(var_data)
    return(result)
  }
  
  # Simple Linear Regression wrapper
  simple_linear_regression_analysis <<- function(data, x_var, y_var) {
    if (is.character(x_var)) {
      x <- data[[x_var]]
    } else {
      x <- x_var
    }
    if (is.character(y_var)) {
      y <- data[[y_var]]
    } else {
      y <- y_var
    }
    
    x <- as.numeric(x)
    y <- as.numeric(y)
    
    # Remove NA values
    complete_cases <- complete.cases(x, y)
    x <- x[complete_cases]
    y <- y[complete_cases]
    
    if (length(x) < 2) {
      stop("Insufficient data for regression")
    }
    
    # Use the calc_slr_model function
    model <- calc_slr_model(x, y)
    summary_result <- calc_slr_summary(model)
    
    return(list(
      model = model,
      summary = summary_result,
      coefficients = coef(model),
      r_squared = summary_result$r.squared
    ))
  }
  
  # PCA wrapper
  pca_analysis <<- function(data, variables = NULL) {
    if (is.null(variables)) {
      # Use all numeric columns
      numeric_cols <- sapply(data, is.numeric)
      if (sum(numeric_cols) < 2) {
        stop("Need at least 2 numeric variables for PCA")
      }
      data_numeric <- data[, numeric_cols, drop = FALSE]
    } else {
      data_numeric <- data[, variables, drop = FALSE]
    }
    
    # Remove rows with missing values
    data_numeric <- data_numeric[complete.cases(data_numeric), , drop = FALSE]
    
    if (nrow(data_numeric) < 2) {
      stop("Insufficient data for PCA")
    }
    
    # Perform PCA
    pca_result <- prcomp(data_numeric, scale. = TRUE)
    
    return(list(
      pca = pca_result,
      summary = summary(pca_result),
      loadings = pca_result$rotation,
      scores = pca_result$x
    ))
  }
  
  # Advanced Survival wrapper
  advanced_survival_analysis <<- function(data, time_var, event_var, group_var = NULL) {
    if (is.character(time_var)) {
      time <- data[[time_var]]
    } else {
      time <- time_var
    }
    if (is.character(event_var)) {
      event <- data[[event_var]]
    } else {
      event <- event_var
    }
    
    time <- as.numeric(time)
    event <- as.numeric(event)
    
    # Remove NA values
    complete_cases <- complete.cases(time, event)
    time <- time[complete_cases]
    event <- event[complete_cases]
    
    if (length(time) < 2) {
      stop("Insufficient data for survival analysis")
    }
    
    # Create survival object
    surv_obj <- Surv(time, event)
    
    # Fit Kaplan-Meier
    km_fit <- survfit(surv_obj ~ 1)
    
    result <- list(
      surv_obj = surv_obj,
      km_fit = km_fit,
      summary = summary(km_fit)
    )
    
    # Add group comparison if group variable provided
    if (!is.null(group_var)) {
      if (is.character(group_var)) {
        group <- data[[group_var]]
      } else {
        group <- group_var
      }
      group <- group[complete_cases]
      
      if (length(unique(group)) > 1) {
        km_group_fit <- survfit(surv_obj ~ group)
        logrank_test <- survdiff(surv_obj ~ group)
        result$km_group_fit <- km_group_fit
        result$logrank_test <- logrank_test
      }
    }
    
    return(result)
  }
}

# Create the test wrapper functions
create_test_wrappers()

cat("\n")

# Test Basic Statistics Modules
cat("Testing Basic Statistics Modules...\n")
cat("-----------------------------------\n")

# Test Descriptive Statistics
data <- load_test_data("desc_stats")
result <- test_calculation_function("desc_stats_analysis", data, names(data)[1])
run_module_tests("Descriptive Statistics", function() result)

# Test Simple Linear Regression
slr_data <- load_test_data("simple_linear_regression")
if (ncol(slr_data) >= 2) {
  result <- test_calculation_function("simple_linear_regression_analysis", slr_data, names(slr_data)[1], names(slr_data)[2])
  run_module_tests("Simple Linear Regression", function() result)
} else {
  run_module_tests("Simple Linear Regression", function() list(success = FALSE, error = "Insufficient columns in test data"))
}

# Test PCA
pca_data <- load_test_data("pca")
result <- test_calculation_function("pca_analysis", pca_data)
run_module_tests("PCA", function() result)

# Test Advanced Survival
surv_data <- load_test_data("advanced_survival")
if (ncol(surv_data) >= 2) {
  result <- test_calculation_function("advanced_survival_analysis", surv_data, names(surv_data)[1], names(surv_data)[2])
  run_module_tests("Advanced Survival", function() result)
} else {
  run_module_tests("Advanced Survival", function() list(success = FALSE, error = "Insufficient columns in test data"))
}

cat("\n")

# Calculate and display summary
test_summary$end_time <- Sys.time()
test_summary$duration <- as.numeric(difftime(test_summary$end_time, test_summary$start_time, units = "mins"))
test_summary$success_rate <- round((test_summary$passed / test_summary$total_modules) * 100, 1)

cat("================================================\n")
cat("TEST SUMMARY\n")
cat("================================================\n")
cat(sprintf("Total Modules Tested: %d\n", test_summary$total_modules))
cat(sprintf("Passed: %d\n", test_summary$passed))
cat(sprintf("Failed: %d\n", test_summary$failed))
cat(sprintf("Errors: %d\n", test_summary$errors))
cat(sprintf("Success Rate: %.1f%%\n", test_summary$success_rate))
cat(sprintf("Total Duration: %.2f minutes\n", test_summary$duration))
cat("\n")

# Save results
saveRDS(test_results, "tests/test_results.rds")
saveRDS(test_summary, "tests/test_summary.rds")

cat("Detailed results saved to tests/test_results.rds\n")
cat("Test summary saved to tests/test_summary.rds\n")
cat("\n")

# Display detailed results
cat("================================================\n")
cat("DETAILED RESULTS\n")
cat("================================================\n\n")

if (test_summary$failed > 0 || test_summary$errors > 0) {
  cat("FAILED TESTS:\n")
  for (module_name in names(test_results)) {
    if (!test_results[[module_name]]$success) {
      cat(sprintf("  %s: %s\n", module_name, test_results[[module_name]]$error))
    }
  }
  cat("\n")
}

if (test_summary$passed > 0) {
  cat("PASSED TESTS:\n")
  for (module_name in names(test_results)) {
    if (test_results[[module_name]]$success) {
      cat(sprintf("  %s\n", module_name))
    }
  }
  cat("\n")
}

if (test_summary$failed > 0 || test_summary$errors > 0) {
  cat("Some tests failed. Check the detailed results above.\n")
} else {
  cat("All tests passed successfully!\n")
} 