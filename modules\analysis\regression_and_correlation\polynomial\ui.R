# Polynomial Regression UI
# Non-linear relationships

PolynomialRegressionSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    radioButtons(
      inputId = ns("polyDataMethod"),
      label = "Data Input Method:",
      choices = c("Manual Entry" = "Manual Entry", "Upload File" = "Upload File"),
      selected = "Manual Entry"
    ),
    conditionalPanel(
      condition = "input.polyDataMethod == 'Manual Entry'",
      ns = ns,
      textAreaInput(
        inputId = ns("polyData"),
        label = "Enter Response Values (comma, space, or newline separated):",
        placeholder = "1, 2, 3, 4, 5, 6",
        rows = 4
      ),
      textAreaInput(
        inputId = ns("polyPredictorData"),
        label = "Enter Predictor Values (comma, space, or newline separated):",
        placeholder = "1, 2, 3, 4, 5, 6",
        rows = 4
      )
    ),
    conditionalPanel(
      condition = "input.polyDataMethod == 'Upload File'",
      ns = ns,
      fileInput(
        inputId = ns("polyUserData"),
        label = "Upload Data File:",
        accept = c(".csv", ".txt", ".xlsx", ".xls"),
        buttonLabel = "Browse Files",
        placeholder = "No file selected"
      ),
      selectizeInput(
        inputId = ns("polyResponseVariable"),
        label = "Select Response Variable:",
        choices = NULL,
        options = list(placeholder = "Select response variable...")
      ),
      selectizeInput(
        inputId = ns("polyPredictorVariable"),
        label = "Select Predictor Variable:",
        choices = NULL,
        options = list(placeholder = "Select predictor variable...")
      )
    ),
    numericInput(
      inputId = ns("polyDegree"),
      label = "Polynomial Degree:",
      value = 2,
      min = 1,
      max = 5,
      step = 1
    ),
    numericInput(
      inputId = ns("polyConfLevel"),
      label = "Confidence Level:",
      value = 0.95,
      min = 0.5,
      max = 0.99,
      step = 0.01
    ),
    actionButton(
      inputId = ns("goPoly"),
      label = "Calculate Polynomial Regression",
      class = "btn-primary",
      style = "width: 100%;"
    ),
    br(),
    br(),
    helpText(
      "Polynomial regression fits non-linear relationships.",
      "Degree 1 = linear, Degree 2 = quadratic, etc.",
      "Higher degrees can capture more complex patterns.",
      "Be careful of overfitting with high degrees."
    )
  )
}

PolynomialRegressionMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns("polyResults"))
  )
}

PolynomialRegressionUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(PolynomialRegressionSidebarUI(id)),
    mainPanel(PolynomialRegressionMainUI(id))
  )
} 