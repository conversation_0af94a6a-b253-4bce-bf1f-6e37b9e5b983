DataSummarizationServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    # Placeholder for data upload and validation
    dsData <- eventReactive(input$dsUserData, {
      handle_file_upload(input$dsUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(dsData(), {
      data <- dsData()
      if (!is.null(data) && is.data.frame(data)) {
        # Add any variable selection logic here if needed
      }
    })
    
    dsResult <- eventReactive(input$goDS, {
      data <- dsData()
      req(data)
      automated_data_summarization(data)
    })
    
    observeEvent(input$goDS, {
      output$dsError <- renderUI({
        tryCatch({ 
          res <- dsResult()
          if (is.null(res)) {
            errorScreenUI(title = "Data Summarization Error", errors = "No results available")
          } else {
            NULL
          }
        }, error = function(e) {
          errorScreenUI(title = "Data Summarization Error", errors = e$message)
        })
      })
      
      output$dsResults <- renderUI({
        res <- tryCatch(dsResult(), error = function(e) NULL)
        if (is.null(res)) return(NULL)
        
        tagList(
          tabsetPanel(
            id = ns("dsTabset"),
            selected = "Analysis",
            tabPanel(
              id = ns("dsAnalysis"),
              title = "Analysis",
              titlePanel("Data Summarization Analysis"),
              br(),
              h4("Summary Statistics"),
              tableOutput(ns('dsSummary')),
              br(),
              h4("Data Overview Plot"),
              plotOutput(ns('dsPlot'), height = "600px"),
              br(),
              h4("Data Quality Report"),
              uiOutput(ns('dsQualityReport'))
            ),
            tabPanel(
              id = ns("dsDataSummary"),
              title = "Data Summary",
              h4("Variable Information"),
              tableOutput(ns('dsVariableInfo')),
              br(),
              h4("Missing Data Summary"),
              tableOutput(ns('dsMissingData')),
              br(),
              h4("Data Types"),
              tableOutput(ns('dsDataTypes'))
            ),
            tabPanel(
              id = ns("dsUploadedData"),
              title = "Uploaded Data",
              h4("Raw Data"),
              DT::DTOutput(ns('dsDataTable'))
            )
          )
        )
      })
    })
    
    # Additional outputs for the enhanced tabs
    output$dsSummary <- renderTable({
      res <- tryCatch(dsResult(), error = function(e) NULL)
      if (is.null(res) || is.null(res$summary)) {
        data.frame(Message = 'No results to display yet. Please check your input or try again.')
      } else {
        res$summary
      }
    })
    
    output$dsPlot <- renderPlot({
      res <- tryCatch(dsResult(), error = function(e) NULL)
      if (is.null(res) || is.null(res$skim)) {
        plot.new()
        title('No plot to display.')
      } else {
        plot(res$skim)
      }
    })
    
    output$dsQualityReport <- renderUI({
      data <- dsData()
      if (is.null(data)) return(NULL)
      
      tagList(
        h5("Data Quality Assessment:"),
        p("Total observations: ", nrow(data)),
        p("Total variables: ", ncol(data)),
        p("Missing values: ", sum(is.na(data))),
        p("Complete cases: ", sum(complete.cases(data))),
        p("Memory usage: ", format(object.size(data), units = "MB"))
      )
    })
    
    output$dsVariableInfo <- renderTable({
      data <- dsData()
      if (is.null(data)) return(NULL)
      
      var_info <- data.frame(
        Variable = names(data),
        Type = sapply(data, class),
        N = sapply(data, function(x) sum(!is.na(x))),
        Missing = sapply(data, function(x) sum(is.na(x))),
        stringsAsFactors = FALSE
      )
      var_info
    }, digits = 0)
    
    output$dsMissingData <- renderTable({
      data <- dsData()
      if (is.null(data)) return(NULL)
      
      missing_summary <- data.frame(
        Variable = names(data),
        Missing_Count = sapply(data, function(x) sum(is.na(x))),
        Missing_Percent = round(sapply(data, function(x) sum(is.na(x))/length(x)*100, 2)),
        stringsAsFactors = FALSE
      )
      missing_summary[missing_summary$Missing_Count > 0, ]
    }, digits = 2)
    
    output$dsDataTypes <- renderTable({
      data <- dsData()
      if (is.null(data)) return(NULL)
      
      type_counts <- table(sapply(data, class))
      data.frame(
        Data_Type = names(type_counts),
        Count = as.numeric(type_counts),
        stringsAsFactors = FALSE
      )
    }, digits = 0)
    
    output$dsDataTable <- DT::renderDT({
      req(dsData())
      DT::datatable(dsData(),
        options = list(pageLength = 25,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(dsData())))))
    })
  })
} 