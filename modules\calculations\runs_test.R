# Runs Test calculation and output helpers

runs_test_uploadData_func <- function(rtUserData, var) {
  tryCatch(
    {
      if (is.null(rtUserData) || is.null(var)) {
        return(NULL)
      }
      
      # Read data based on file type
      if (grepl("\\.csv$", rtUserData$name, ignore.case = TRUE)) {
        df <- read.csv(rtUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.tsv$", rtUserData$name, ignore.case = TRUE)) {
        df <- read.tsv(rtUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.xlsx$", rtUserData$name, ignore.case = TRUE)) {
        df <- readxl::read_excel(rtUserData$datapath)
      } else {
        stop("Unsupported file type. Please upload a CSV, TSV, or XLSX file.")
      }
      
      if (!var %in% names(df)) {
        stop(paste("Variable '", var, "' not found in the uploaded file.", sep = ""))
      }
      
      # Return the selected variable as a numeric vector
      return(as.numeric(df[[var]]))
    },
    error = function(e) {
      # Return a list with an error message
      return(list(error = paste("Error reading data:", e$message)))
    }
  )
}

runs_test_results_func <- function(data, method = "median", custom_value = NULL) {
  tryCatch({
    if (is.null(data) || length(data) < 3) {
      stop("At least 3 observations are required.")
    }
    
    if (!requireNamespace("randtests", quietly = TRUE)) {
      stop("Package 'randtests' needed for runs test.")
    }
    
    if (method == "custom") {
      test_result <- randtests::runs.test(data, threshold = custom_value)
    } else {
      test_result <- randtests::runs.test(data)
    }
    
    list(
      test = test_result,
      data = data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Runs test calculation:", e$message))
  })
}

runs_test_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  
  p_value <- results$test$p.value
  conclusion <- if (p_value < 0.05) "not random" else "random"
  
  tagList(
    h4("Wald-Wolfowitz Runs Test"),
    p(sprintf("The sequence of data is likely %s (p = %.3f).", conclusion, p_value))
  )
}

runs_test_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  renderPrint(results$test)
}

runs_test_plot <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  
  if (!requireNamespace("ggplot2", quietly = TRUE)) {
    stop("Package 'ggplot2' is required for plotting.")
  }
  
  plot_data <- data.frame(
    Index = 1:length(results$data),
    Value = results$data
  )
  
  ggplot(plot_data, aes(x = Index, y = Value)) +
    geom_line(alpha = 0.7) +
    geom_point() +
    geom_hline(yintercept = median(results$data), color = "red", linetype = "dashed") +
    labs(title = "Data Sequence with Median",
         x = "Index",
         y = "Value") +
    theme_minimal()
}