# cmprsk Package Installation Guide

## Issue
The `cmprsk` package is not available on CRAN and needs to be installed from alternative sources. This package is required for competing risks survival analysis in CougarStats.

## Error Message
```
Error in library(cmprsk) : there is no package called 'cmprsk'
```

## Solutions

### Option 1: Automatic Installation (Recommended)
Run the provided installation script:
```r
source("install_cmprsk.R")
```

This script will attempt to install cmprsk from multiple sources automatically.

### Option 2: Install from Bioconductor
```r
# Install BiocManager if not already installed
install.packages("BiocManager")

# Install cmprsk from Bioconductor
BiocManager::install("cmprsk")
```

### Option 3: Install from GitHub
```r
# Install remotes if not already installed
install.packages("remotes")

# Install cmprsk from GitHub
remotes::install_github("cran/cmprsk")
```

### Option 4: Manual Download and Installation
1. Visit: https://cran.r-project.org/src/contrib/Archive/cmprsk/
2. Download the latest version (e.g., `cmprsk_2.2-11.tar.gz`)
3. Install using:
```r
install.packages("path/to/cmprsk_2.2-11.tar.gz", repos = NULL, type = "source")
```

## Verification
After installation, verify that cmprsk is working:
```r
library(cmprsk)
# Test with sample data
time <- c(1, 2, 3, 4, 5)
status <- c(1, 1, 0, 1, 0)
fit <- cmprsk::cuminc(time, status)
```

## What if Installation Fails?

If you cannot install cmprsk, **CougarStats will still work** with the following limitations:

### Available Features:
- ✅ Kaplan-Meier survival analysis
- ✅ Cox proportional hazards regression
- ✅ Parametric survival models (Weibull, exponential, etc.)
- ✅ Frailty models
- ✅ All other statistical analyses

### Unavailable Features:
- ❌ Competing risks analysis
- ❌ Cumulative incidence functions
- ❌ Gray's test for competing risks

## Troubleshooting

### Common Issues:

1. **Permission Errors**: Run R as administrator or use a user library
2. **Network Issues**: Check your internet connection
3. **R Version**: Ensure you're using a recent version of R
4. **System Dependencies**: On Linux, you may need additional system libraries

### Getting Help:
- Check the console for detailed error messages
- Try installing from a different source
- Consider updating R to the latest version
- Contact your system administrator if you don't have installation permissions

## Alternative Analysis Methods

If competing risks analysis is not available, consider these alternatives:

1. **Kaplan-Meier Analysis**: For overall survival analysis
2. **Cox Regression**: For analyzing the effect of covariates on survival
3. **Parametric Models**: For modeling survival distributions
4. **Stratified Analysis**: For analyzing different groups separately

## Conclusion

The cmprsk package is only required for competing risks analysis. All other survival analysis methods in CougarStats will work without it. If you specifically need competing risks analysis, follow the installation instructions above. Otherwise, you can use the other available survival analysis methods. 