kruskalWallisServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    # Reactives
    kwUploadData <- eventReactive(input$kwUserData, {
      handle_file_upload(input$kwUserData)
    })
    kwStackedIsValid <- eventReactive({input$kwResponse; input$kwFactors}, {
      kwStackedIsValid_func(input$kwResponse, input$kwFactors)
    })
    kwResults <- reactive({
      data <- kwUploadData()
      # Only run if the selected columns exist in the data
      valid_multi <- is.null(input$kwMultiColumns) || all(input$kwMultiColumns %in% names(data))
      valid_factors <- is.null(input$kwFactors) || input$kwFactors %in% names(data)
      valid_response <- is.null(input$kwResponse) || input$kwResponse %in% names(data)
      if (is.null(data) || (!valid_multi && !valid_factors && !valid_response)) {
        return(NULL)
      }
      kwResults_func(
        TRUE, # Replace with appropriate validation if needed
        input$kwFormat,
        input$kwMultiColumns,
        data,
        input$kwFactors,
        input$kwResponse
      )
    })
    # Add a reactive to collect validation errors
    kwValidationErrors <- reactive({
      errors <- c()
      data <- kwUploadData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (input$kwFormat == "Multiple") {
        if (is.null(input$kwMultiColumns) || length(input$kwMultiColumns) < 2) {
          errors <- c(errors, "Please select at least two columns for multi-column analysis.")
        } else {
          for (col in input$kwMultiColumns) {
            if (length(unique(data[[col]])) < 2) {
              errors <- c(errors, sprintf("Column '%s' must have at least two unique values.", col))
            }
            if (all(data[[col]] == data[[col]][1])) {
              errors <- c(errors, sprintf("All values in column '%s' are the same. There must be variance.", col))
            }
          }
        }
      } else if (input$kwFormat == "Stacked") {
        if (is.null(input$kwResponse) || input$kwResponse == "") {
          errors <- c(errors, "Please select a response variable.")
        }
        if (is.null(input$kwFactors) || input$kwFactors == "") {
          errors <- c(errors, "Please select a factor variable.")
        }
        if (!is.null(input$kwResponse) && !is.null(input$kwFactors) && input$kwResponse == input$kwFactors) {
          errors <- c(errors, "Response and factor variables must be different.")
        }
        if (!is.null(input$kwResponse) && !is.null(input$kwFactors) &&
            length(unique(data[[input$kwResponse]])) < 2) {
          errors <- c(errors, sprintf("Response variable '%s' must have at least two unique values.", input$kwResponse))
        }
        if (!is.null(input$kwFactors) && length(unique(data[[input$kwFactors]])) < 2) {
          errors <- c(errors, sprintf("Factor variable '%s' must have at least two unique values.", input$kwFactors))
        }
      }
      errors
    })
    # Outputs
    output$kwHT <- renderUI({
      results <- kwResults()
      if (is.null(results)) return(NULL)
      kruskalWallisHT(kwResults, reactive({input$kwSigLvl}))
    })
    output$kruskalWallisPlot <- renderPlot({
      results <- kwResults()
      if (is.null(results)) return(NULL)
      kruskalWallisPlot(kwResults, reactive({input$kwSigLvl}))
    })
    output$kwConclusionOutput <- renderUI({
      results <- kwResults()
      if (is.null(results)) return(NULL)
      kwConclusion(kwResults, reactive({input$kwSigLvl}))
    })
    output$renderKWRM <- renderUI({
      results <- kwResults()
      if (is.null(results) || is.null(results$data)) return(NULL)
      kwRankedTableOutput(results$data)
    })
    output$renderrankedmean <- DT::renderDT({
      results <- kwResults()
      if (is.null(results) || is.null(results$data)) return(NULL)
      df <- results$data
      if (is.null(df)) return(NULL)
      df %>%
        dplyr::select(Group = ind, Value = values, Rank = Rank) %>%
        dplyr::arrange(Group, Rank)
    })
    output$renderKWData <- renderUI({
      req(kwUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("kwInitialUploadTable")), style = "width: 75%"),
        br(),
        br()
      )
    })
    output$kwInitialUploadTable <- DT::renderDT({
      req(kwUploadData())
      DT::datatable(kwUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(kwUploadData())))))
    })
    # Update selectizeInput choices for response and factor after file upload
    observeEvent(kwUploadData(), {
      data <- kwUploadData()
      # Clear selectizeInputs and main panel
      updateSelectizeInput(session, 'kwResponse', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'kwFactors', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'kwMultiColumns', choices = character(0), selected = NULL, server = TRUE)
      output$kruskalWallisResults <- renderUI({ NULL })
      # If data is valid, update choices and show preview
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'kwResponse', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'kwFactors', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'kwMultiColumns', choices = names(data), server = TRUE)
        output$kruskalWallisResults <- renderUI({
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('kwPreviewTable'))
          )
        })
        output$kwPreviewTable <- DT::renderDT({
          head(data, 20)
        })
      }
    })
    # Show main results or error screen in main panel when Calculate is pressed
    observeEvent(input$goInference, {
      output$kruskalWallisResults <- renderUI({
        errors <- kwValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Kruskal-Wallis Test", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("kwTabset"),
              selected = "Analysis",
              tabPanel(
                id    = ns("kw"),
                title = "Analysis",
                titlePanel("Hypothesis Test"),
                br(),
                uiOutput(ns('kwHT')),
                br(),
                plotOutput(ns('kruskalWallisPlot'), width = "50%", height = "400px"),
                br(),
                uiOutput(ns('kwConclusionOutput'))
              ),
              tabPanel(
                id    = ns("kwRM"),
                title = "Data table with Ranks",
                DTOutput(ns("renderrankedmean")),
                uiOutput(ns("renderKWRM"))
              ),
              tabPanel(
                id    = ns("kwData"),
                title = "Uploaded Data",
                uiOutput(ns("renderKWData"))
              )
            )
          )
        }
      })
    })
  })
} 