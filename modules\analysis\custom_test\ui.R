# Custom Test UI
CustomTestSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("customUserData"), "Upload your Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
    textInput(ns("customFormula"), "Model Formula (e.g., y ~ x1 + x2)", value = ""),
    selectizeInput(ns("customResponse"), "Response Variable", choices = NULL),
    selectizeInput(ns("customPredictors"), "Predictor Variables", choices = NULL, multiple = TRUE),
    selectInput(ns("customTestType"), "Test Type", choices = c("t-test", "ANOVA", "Linear Regression", "Logistic Regression", "Other")),
    br(),
    actionButton(ns("goCustomTest"), label = "Calculate", class = "act-btn")
  )
}

CustomTestMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('customTestResults'))
  )
}

CustomTestUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(
      CustomTestSidebarUI(id)
    ),
    mainPanel(
      CustomTestMainUI(id)
    )
  )
} 