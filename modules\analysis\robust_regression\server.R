RobustRegressionServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    # Placeholder for data upload and validation
    robustData <- eventReactive(input$robustUserData, {
      handle_file_upload(input$robustUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(robustData(), {
      data <- robustData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'robustResponse', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'robustPredictors', choices = names(data), server = TRUE)
      }
    })
    
    robustResult <- eventReactive(input$goRobust, {
      data <- robustData()
      req(data, input$robustResponse, input$robustPredictors)
      x <- as.matrix(data[, input$robustPredictors, drop = FALSE])
      y <- data[[input$robustResponse]]
      method <- switch(input$robustMethod,
                       "Huber M-estimator" = "huber",
                       "MM-estimator" = "mm",
                       "Least Trimmed Squares" = "lts")
      robust_regression(x, y, method)
    })
    
    observeEvent(input$goRobust, {
      output$robustError <- renderUI({
        tryCatch({ 
          res <- robustResult()
          if (is.null(res)) {
            errorScreenUI(title = "Robust Regression Error", errors = "No results available")
          } else {
            NULL
          }
        }, error = function(e) {
          errorScreenUI(title = "Robust Regression Error", errors = e$message)
        })
      })
      
      output$robustResults <- renderUI({
        res <- robustResult()
        if (is.null(res)) return(NULL)
        
        tagList(
          tabsetPanel(
            id = ns("robustTabset"),
            selected = "Analysis",
            tabPanel(
              id = ns("robustAnalysis"),
              title = "Analysis",
              titlePanel("Robust Regression Results"),
              br(),
              h4("Model Summary"),
              tableOutput(ns('robustModelSummary')),
              br(),
              h4("Coefficients"),
              tableOutput(ns('robustCoefficients')),
              br(),
              h4("Model Fit"),
              uiOutput(ns('robustModelFit')),
              br(),
              h4("Interpretation"),
              uiOutput(ns('robustInterpretation'))
            ),
            tabPanel(
              id = ns("robustDiagnostics"),
              title = "Diagnostics",
              h4("Residuals vs Fitted"),
              plotOutput(ns('robustResidualPlot')),
              br(),
              h4("Q-Q Plot"),
              plotOutput(ns('robustQQPlot')),
              br(),
              h4("Diagnostic Statistics"),
              tableOutput(ns('robustDiagnosticStats'))
            ),
            tabPanel(
              id = ns("robustUploadedData"),
              title = "Uploaded Data",
              h4("Raw Data"),
              uiOutput(ns('robustDataTable'))
            )
          )
        )
      })
    })
    
    # Additional outputs for the enhanced tabs
    output$robustModelSummary <- renderTable({
      res <- robustResult()
      if (is.null(res)) return(NULL)
      
      data.frame(
        Statistic = c("Method", "Observations", "Predictors", "R-squared"),
        Value = c(input$robustMethod, 
                 ifelse(!is.null(res$n), res$n, "N/A"),
                 ifelse(!is.null(res$p), res$p, "N/A"),
                 ifelse(!is.null(res$r.squared), round(res$r.squared, 4), "N/A")),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    output$robustCoefficients <- renderTable({
      res <- robustResult()
      if (is.null(res)) return(NULL)
      as.data.frame(res$coefficients)
    }, rownames = TRUE, digits = 4)
    
    output$robustModelFit <- renderUI({
      res <- robustResult()
      if (is.null(res)) return(NULL)
      
      tagList(
        p("Model fit statistics will be displayed here."),
        p("R-squared: ", ifelse(!is.null(res$r.squared), round(res$r.squared, 4), "N/A")),
        p("Adjusted R-squared: ", ifelse(!is.null(res$adj.r.squared), round(res$adj.r.squared, 4), "N/A"))
      )
    })
    
    output$robustInterpretation <- renderUI({
      tagList(
        h5("Robust Regression Interpretation:"),
        p("Robust regression methods are less sensitive to outliers than ordinary least squares."),
        p("The coefficients represent the estimated change in the response variable for a one-unit change in the predictor."),
        p("This method is particularly useful when your data contains influential observations or outliers.")
      )
    })
    
    output$robustResidualPlot <- renderPlot({
      res <- robustResult()
      if (is.null(res)) return(NULL)
      
      if (!is.null(res$summary$residuals) && !is.null(res$summary$fitted.values)) {
        plot(res$summary$fitted.values, res$summary$residuals, 
             xlab = "Fitted Values", ylab = "Residuals", 
             main = "Residuals vs Fitted Values")
        abline(h = 0, col = "red", lty = 2)
        grid()
      }
    })
    
    output$robustQQPlot <- renderPlot({
      res <- robustResult()
      if (is.null(res)) return(NULL)
      
      if (!is.null(res$summary$residuals)) {
        qqnorm(res$summary$residuals, main = "Normal Q-Q Plot of Residuals")
        qqline(res$summary$residuals, col = "red")
        grid()
      }
    })
    
    output$robustDiagnosticStats <- renderTable({
      res <- robustResult()
      if (is.null(res)) return(NULL)
      
      # Placeholder for diagnostic statistics
      data.frame(
        Statistic = c("Mean Residual", "Residual SD", "Max Residual", "Min Residual"),
        Value = c("N/A", "N/A", "N/A", "N/A"),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    output$robustDataTable <- renderUI({
      req(robustData())
      DT::DTOutput(ns("robustDataTableInner"))
    })
    
    output$robustDataTableInner <- DT::renderDT({
      req(robustData())
      DT::datatable(robustData(),
        options = list(pageLength = 25,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(robustData())))))
    })
  })
} 