# Placeholder for Portfolio Optimization UI
portfolioOptimizationSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("poUserData"), "Upload your Data (.csv or .xls or .xlsx or .txt)", accept = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")),
    uiOutput(ns("poColSelectors")),
    actionButton(ns("goPO"), label = "Calculate", class = "act-btn")
  )
}

portfolioOptimizationMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('poHT')),
    plotOutput(ns('poPlot'), width = "50%", height = "400px"),
    uiOutput(ns('poConclusionOutput'))
  )
}

portfolioOptimizationUI <- function(id) {
  ns <- NS(id)
  tagList(
    portfolioOptimizationSidebarUI(id),
    portfolioOptimizationMainUI(id),
    tabsetPanel(
      id = ns("poTabset"),
      selected = "Analysis",
      tabPanel(
        id    = ns("poAnalysis"),
        title = "Portfolio Optimization Analysis",
        titlePanel("Portfolio Optimization Analysis"),
        br(),
        uiOutput(ns('poHT')),
        br(),
        plotOutput(ns('poPlot'), width = "50%", height = "400px"),
        br(),
        uiOutput(ns('poConclusionOutput'))
      ),
      tabPanel(
        id    = ns("poData"),
        title = "Uploaded Data",
        uiOutput(ns("renderPOData"))
      )
    )
  )
} 