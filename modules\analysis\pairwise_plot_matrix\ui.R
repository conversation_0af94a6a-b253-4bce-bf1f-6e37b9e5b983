PairwisePlotMatrixUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("ppmUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("ppmVars"), "Variables", choices = NULL, multiple = TRUE),
        br(),
        actionButton(ns("goPPM"), label = "Generate Plot Matrix", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("ppmError")),
        plotOutput(ns("ppmPlot"))
      )
    )
  )
} 