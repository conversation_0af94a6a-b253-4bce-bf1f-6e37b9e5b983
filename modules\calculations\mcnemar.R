# McNemar's Test calculation and output helpers

mcnemar_uploadData_func <- function(mcnemarUserData) {
  ext <- tools::file_ext(mcnemarUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(mcnemarUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(mcnemarUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(mcnemarUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(mcnemarUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

mcnemar_results_func <- function(data, var1, var2) {
  tryCatch({
    x <- data[[var1]]
    y <- data[[var2]]
    
    tbl <- table(x, y)
    
    if (nrow(tbl) != 2 || ncol(tbl) != 2) {
      stop("McN<PERSON><PERSON>'s test requires a 2x2 contingency table.")
    }
    
    test_result <- mcnemar.test(tbl)
    
    list(
      test = test_result,
      table = tbl,
      var1 = var1,
      var2 = var2,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during McNemar's test calculation:", e$message))
  })
}

mcnemar_ht_html <- function(results, sigLvl) {
  test <- results$test
  p_value <- test$p.value
  
  conclusion <- if (p_value < sigLvl) "a significant change in proportions" else "no significant change in proportions"
  
  withMathJax(tagList(
    h4("McNemar's Test"),
    p(sprintf("There is %s.", conclusion)),
    p(sprintf("Test Statistic (chi-squared): %.4f", test$statistic)),
    p(sprintf("P-value: %.4f", p_value))
  ))
}

mcnemar_summary_html <- function(results) {
  tagList(
    h4("Contingency Table"),
    renderTable(results$table, rownames = TRUE)
  )
}

mcnemar_plot <- function(results) {
  plot_data <- as.data.frame(results$table)
  
  ggplot(plot_data, aes(x = x, y = y, fill = Freq)) +
    geom_tile() +
    geom_text(aes(label = Freq), color = "white", size = 6) +
    scale_fill_gradient(low = "lightblue", high = "steelblue") +
    labs(title = "Contingency Table Heatmap",
         x = results$var1,
         y = results$var2) +
    theme_minimal()
}