# Two-Way ANOVA calculation and output helpers

two_way_anova_uploadData_func <- function(twaUserData) {
  ext <- tools::file_ext(twaUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(twaUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(twaUserData$datapath),
         xlsx = readxl::read_xlsx(twaUserData$datapath),
         txt = readr::read_tsv(twaUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

two_way_anova_results_func <- function(data, response_var, factor_var1, factor_var2, interaction = TRUE) {
  tryCatch({
    all_vars <- c(response_var, factor_var1, factor_var2)
    complete_data <- data[complete.cases(data[all_vars]), ]
    
    if (nrow(complete_data) < 10) {
      stop("At least 10 observations are required for Two-Way ANOVA")
    }
    
    complete_data[[factor_var1]] <- as.factor(complete_data[[factor_var1]])
    complete_data[[factor_var2]] <- as.factor(complete_data[[factor_var2]])
    
    if (interaction) {
      formula_str <- as.formula(paste0("`", response_var, "` ~ `", factor_var1, "` * `", factor_var2, "`"))
    } else {
      formula_str <- as.formula(paste0("`", response_var, "` ~ `", factor_var1, "` + `", factor_var2, "`"))
    }
    
    model <- aov(formula_str, data = complete_data)
    
    list(
      model = model,
      data = complete_data,
      response_var = response_var,
      factor_var1 = factor_var1,
      factor_var2 = factor_var2,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Two-Way ANOVA calculation:", e$message))
  })
}

two_way_anova_ht_html <- function(results) {
  # No single hypothesis test, summary table is more informative
  tagList(
    h4("Two-Way ANOVA Model"),
    p("See summary table for main effects and interactions.")
  )
}

two_way_anova_summary_html <- function(results) {
  anova_table <- anova(results$model)
  
  tagList(
    h4("ANOVA Table"),
    renderPrint(anova_table)
  )
}

two_way_anova_plot <- function(results) {
  if (requireNamespace("emmeans", quietly = TRUE)) {
    emmeans::emmip(results$model, as.formula(paste("~", results$factor_var1, "|", results$factor_var2)))
  }
}