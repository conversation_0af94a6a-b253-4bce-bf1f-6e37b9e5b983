sampSizeEstUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        shinyjs::useShinyjs(),
        div(
          id = ns("inputPanel"),
          selectInput(
            ns("sampleSizeType"),
            "Sample Size Estimation Type",
            choices = c("One Mean", "Two Means", "One Proportion", "Two Proportions"),
            selected = "One Mean"
          ),
          uiOutput(ns("ssInputs")),
          br(),
          actionButton(
            inputId = ns("goSampleSize"),
            label   = "Calculate",
            class   = "act-btn"
          )
        )
      ),
      mainPanel(
        uiOutput(ns("ssOutputs"))
      )
    )
  )
} 