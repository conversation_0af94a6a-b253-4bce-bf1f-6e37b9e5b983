source("modules/calculations/wilcoxon.R")

wilcoxonServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # --- Reactives ---
    wilUploadData <- eventReactive(input$wilUserData, {
      handle_file_upload(input$wilUserData)
    })
    
    wilValidationErrors <- reactive({
      errors <- c()
      data <- wilUploadData()
      xvar <- input$wilX
      yvar <- input$wilY
      
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(xvar) || !(xvar %in% names(data))) {
        errors <- c(errors, "Please select a valid X variable.")
      } else if (!is.numeric(data[[xvar]])) {
        errors <- c(errors, sprintf("Column '%s' must be numeric.", xvar))
      }
      if (is.null(yvar) || !(yvar %in% names(data))) {
        errors <- c(errors, "Please select a valid Y variable.")
      } else if (!is.numeric(data[[yvar]])) {
        errors <- c(errors, sprintf("Column '%s' must be numeric.", yvar))
      }
      if (!is.null(xvar) && !is.null(yvar) && (xvar %in% names(data)) && (yvar %in% names(data)) && length(data[[xvar]]) != length(data[[yvar]])) {
        errors <- c(errors, "X and Y must have the same number of observations.")
      }
      errors
    })
    
    wilResults <- eventReactive(input$goInference, {
      # Ensure validation passes before calculating
      if (length(wilValidationErrors()) > 0) {
        return(NULL)
      }
      wilcoxon_results_func(
        data = wilUploadData(),
        x_var = input$wilX,
        y_var = input$wilY,
        conf_level = input$wilConfLevel,
        alternative = input$wilAlternative
      )
    })
    
    # --- Observers ---
    observeEvent(wilUploadData(), {
      data <- wilUploadData()
      updateSelectizeInput(session, 'wilX', choices = names(data), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'wilY', choices = names(data), selected = NULL, server = TRUE)
      
      # Show data preview
      output$wilcoxonResults <- renderUI({
        if (!is.null(data) && is.data.frame(data)) {
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('wilPreviewTable'))
          )
        }
      })
      output$wilPreviewTable <- DT::renderDT({
        head(data, 20)
      })
    })
    
    observeEvent(input$goInference, {
      output$wilcoxonResults <- renderUI({
        errors <- wilValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Wilcoxon Signed-Rank Test", errors = errors)
        } else {
          results <- wilResults()
          req(results)
          
          tagList(
            tabsetPanel(
              id = ns("wilTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("wilAnalysis"),
                title = "Analysis",
                titlePanel("Wilcoxon Signed-Rank Test Results"),
                br(),
                wilcoxon_ht_html(results, input$wilConfLevel)
              ),
              tabPanel(
                id = ns("wilDataSummary"),
                title = "Data Summary",
                wilcoxon_summary_tables_html(results)
              ),
              tabPanel(
                id = ns("wilUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('wilDataTable'))
              )
            )
          )
        }
      })
      
      output$wilDataTable <- DT::renderDT({
        req(wilUploadData())
        DT::datatable(wilUploadData(),
          options = list(pageLength = 25,
                         lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                         columnDefs = list(list(className = 'dt-center', targets = 0:ncol(wilUploadData())))))
      })
    })
  })
}