# Natural Language Processing calculation and output helpers

natural_language_processing_uploadData_func <- function(nlpUserData) {
  ext <- tools::file_ext(nlpUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(nlpUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(nlpUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(nlpUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(nlpUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

natural_language_processing_results_func <- function(data, text_column, analysis_type = "sentiment", ...) {
  tryCatch({
    
    results <- perform_nlp_analysis(data, text_column, analysis_type, ...)
    
    list(
      results = results,
      data = data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during NLP analysis:", e$message))
  })
}

natural_language_processing_ht_html <- function(results) {
  tagList(
    h4(paste("NLP Analysis:", results$results$method))
  )
}

natural_language_processing_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  res <- results$results
  out <- list()
  if (res$analysis_type == "sentiment") {
    out <- c(out, renderTable(as.data.frame(res$summary$overall_sentiment)))
  } else if (res$analysis_type == "topic_modeling") {
    out <- c(out, renderTable(res$summary$topic_terms))
  } else if (res$analysis_type == "keyword_extraction") {
    out <- c(out, renderTable(res$results))
  } else {
    out <- c(out, p("Summary not available for this analysis type."))
  }
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

natural_language_processing_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  res <- results$results
  if (res$analysis_type == "sentiment" && !is.null(res$summary$sentiment_distribution)) {
    barplot(as.numeric(res$summary$sentiment_distribution), names.arg = names(res$summary$sentiment_distribution), main = 'Sentiment Distribution', col = c('green', 'red', 'gray'))
  } else if (res$analysis_type == "topic_modeling" && !is.null(res$summary$topic_terms)) {
    barplot(res$summary$topic_terms$Frequency, names.arg = res$summary$topic_terms$Term, las = 2, main = 'Top Topic Terms')
  } else if (res$analysis_type == "keyword_extraction" && !is.null(res$results)) {
    barplot(res$results$Frequency, names.arg = res$results$Keyword, las = 2, main = 'Top Keywords')
  } else {
    plot.new(); title("No plot available for this analysis type.")
  }
}

# --- Helper functions from original file ---

perform_nlp_analysis <- function(data, text_column, analysis_type = "sentiment", 
                                id_column = NULL, group_column = NULL, 
                                language = "english", custom_stopwords = NULL) {
  
  required_packages <- c("tm", "quanteda", "wordcloud", "topicmodels")
  sapply(required_packages, require, character.only = TRUE)
  
  texts <- data[[text_column]]
  
  results <- switch(analysis_type,
    "sentiment" = perform_sentiment_analysis(texts, data, id_column, group_column, language),
    "topic_modeling" = perform_topic_modeling(texts, data, id_column, group_column, language, custom_stopwords),
    "keyword_extraction" = perform_keyword_extraction(texts, data, id_column, group_column, language),
    stop("Unknown analysis type: ", analysis_type)
  )
  
  results$analysis_type <- analysis_type
  return(results)
}

perform_sentiment_analysis <- function(texts, data, id_column, group_column, language) {
  
  if (!requireNamespace("syuzhet", quietly = TRUE)) {
    stop("Package 'syuzhet' is required for sentiment analysis.")
  }
  
  sentiment_scores <- syuzhet::get_sentiment(texts, method="syuzhet")
  
  sentiment_results <- data.frame(
    text = texts,
    sentiment_score = sentiment_scores,
    sentiment_label = ifelse(sentiment_scores > 0, "Positive", ifelse(sentiment_scores < 0, "Negative", "Neutral")),
    stringsAsFactors = FALSE
  )
  
  sentiment_summary <- list(
    overall_sentiment = table(sentiment_results$sentiment_label)
  )
  
  return(list(
    method = "Syuzhet Sentiment Analysis",
    results = sentiment_results,
    summary = sentiment_summary
  ))
}

perform_topic_modeling <- function(texts, data, id_column, group_column, language, custom_stopwords, n_topics = 5) {
  
  corpus <- tm::Corpus(tm::VectorSource(texts))
  corpus <- tm::tm_map(corpus, tm::content_transformer(tolower))
  corpus <- tm::tm_map(corpus, tm::removeNumbers)
  corpus <- tm::tm_map(corpus, tm::removePunctuation)
  corpus <- tm::tm_map(corpus, tm::removeWords, stopwords(language))
  if (!is.null(custom_stopwords)) {
    corpus <- tm::tm_map(corpus, tm::removeWords, custom_stopwords)
  }
  corpus <- tm::tm_map(corpus, tm::stripWhitespace)
  
  dtm <- tm::DocumentTermMatrix(corpus)
  dtm <- dtm[rowSums(as.matrix(dtm)) > 0, ]
  
  lda_model <- topicmodels::LDA(dtm, k = n_topics, control = list(seed = 123))
  
  topic_terms <- as.data.frame(topicmodels::terms(lda_model, 10))
  
  return(list(
    method = "LDA Topic Modeling",
    summary = list(topic_terms = topic_terms)
  ))
}

perform_keyword_extraction <- function(texts, data, id_column, group_column, language) {
  
  corpus <- tm::Corpus(tm::VectorSource(texts))
  corpus <- tm::tm_map(corpus, tm::content_transformer(tolower))
  corpus <- tm::tm_map(corpus, tm::removeNumbers)
  corpus <- tm::tm_map(corpus, tm::removePunctuation)
  corpus <- tm::tm_map(corpus, tm::removeWords, stopwords(language))
  corpus <- tm::tm_map(corpus, tm::stripWhitespace)
  
  dtm <- tm::DocumentTermMatrix(corpus)
  
  term_freq <- colSums(as.matrix(dtm))
  term_freq <- sort(term_freq, decreasing = TRUE)
  
  keyword_results <- data.frame(
    keyword = names(term_freq),
    frequency = as.numeric(term_freq),
    stringsAsFactors = FALSE
  )
  
  return(list(
    method = "Term Frequency Keyword Extraction",
    results = head(keyword_results, 20)
  ))
}