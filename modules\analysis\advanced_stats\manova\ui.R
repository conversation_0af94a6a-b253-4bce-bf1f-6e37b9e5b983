MANOVAUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("manovaUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("manovaResponses"), "Response Variables (select multiple)", choices = NULL, multiple = TRUE),
        selectizeInput(ns("manovaGroup"), "Group Variable", choices = NULL),
        br(),
        actionButton(ns("goMANOVA"), label = "Run MANOVA", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("manovaError")),
        tableOutput(ns("manovaSummary")),
        plotOutput(ns("manovaPlot"))
      )
    )
  )
} 