# Model Comparison calculation and output helpers

model_comparison_uploadData_func <- function(mcUserData) {
  ext <- tools::file_ext(mcUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(mcUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(mcUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(mcUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(mcUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

model_comparison_results_func <- function(data, response, predictors, models = c("lm", "rf"), cv_folds = 5) {
  tryCatch({
    if (!requireNamespace("caret", quietly = TRUE)) stop("Package 'caret' required.")
    
    results <- list()
    x <- as.matrix(data[, predictors, drop = FALSE])
    y <- data[[response]]
    ctrl <- caret::trainControl(method = "cv", number = cv_folds)
    
    for (m in models) {
      fit <- caret::train(x, y, method = m, trControl = ctrl)
      
      # Extract metrics, handling cases where AIC/BIC are not available
      model_metrics <- c(
        RMSE = fit$results$RMSE[1], 
        Rsquared = fit$results$Rsquared[1]
      )
      
      if("finalModel" %in% names(fit) && "lm" %in% class(fit$finalModel)) {
          model_metrics <- c(model_metrics, AIC = AIC(fit$finalModel), BIC = BIC(fit$finalModel))
      } else {
          model_metrics <- c(model_metrics, AIC = NA, BIC = NA)
      }
      
      results[[m]] <- model_metrics
    }
    
    summary_df <- do.call(rbind, results)
    best <- rownames(summary_df)[which.min(summary_df[, "RMSE"])]
    
    list(
      summary = summary_df,
      best_model = best,
      error = NULL
    )
  }, error = function(e) {
    list(summary = NULL, best_model = NULL, error = e$message)
  })
}

model_comparison_ht_html <- function(results) {
  tagList(
    h4("Model Comparison"),
    p(paste("The best model based on RMSE is:", results$best_model))
  )
}

model_comparison_summary_html <- function(results) {
  tagList(
    h4("Model Performance Metrics"),
    renderTable(results$summary, rownames = TRUE)
  )
}

model_comparison_plot <- function(results) {
  # No standard plot for this, summary table is the main output
  p("No plot available for model comparison. Please refer to the summary table.")
}