# Survival Analysis Server
SurvivalAnalysisServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive with error handling
    survUploadData <- eventReactive(input$survUserData, {
      tryCatch({
        data <- handle_file_upload(input$survUserData)
        
        # Validate uploaded data
        errors <- validate_data(data, min_rows = 2)
        if (length(errors) > 0) {
          log_error(paste("Data validation failed:", paste(errors, collapse = "; ")), "survival_upload")
          return(NULL)
        }
        
        log_info("Data uploaded successfully", "survival_upload")
        return(data)
        
      }, error = function(e) {
        log_error(e$message, "survival_upload")
        return(NULL)
      })
    })
    
    # Update variable choices when data is uploaded
    observeEvent(survUploadData(), {
      data <- survUploadData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'survTime', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'survEvent', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'survGroup', choices = c("None", names(data)), server = TRUE)
        updateSelectizeInput(session, 'survCovariates', choices = names(data), server = TRUE, multiple = TRUE)
        updateSelectizeInput(session, 'survStratify', choices = names(data), server = TRUE, multiple = TRUE)
      }
    })
    
    # Enhanced survival analysis using new calculations with error handling
    survAnalysis <- reactive({
      req(survUploadData(), input$survTime, input$survEvent)
      data <- survUploadData()
      
      if (is.null(data)) return(NULL)
      
      # Validate required variables
      if (!input$survTime %in% names(data) || !input$survEvent %in% names(data)) {
        log_error("Required variables not found in data", "survival_analysis")
        return(NULL)
      }
      
      # Remove rows with missing values
      complete_data <- data[complete.cases(data[, c(input$survTime, input$survEvent)]), ]
      
      if (nrow(complete_data) < 2) {
        log_error("Insufficient complete cases for analysis", "survival_analysis")
        return(NULL)
      }
      
      # Get analysis parameters
      method <- input$survMethod
      group_var <- if (!is.null(input$survGroup) && input$survGroup != "" && input$survGroup != "None") input$survGroup else NULL
      covariates <- if (!is.null(input$survCovariates) && length(input$survCovariates) > 0) input$survCovariates else NULL
      stratify_vars <- if (!is.null(input$survStratify) && length(input$survStratify) > 0) input$survStratify else NULL
      
      # Monitor performance and handle errors
      monitor_performance({
        tryCatch({
          # Use the new survival analysis functions
          if (method == "Kaplan-Meier") {
            result <- kaplan_meier_analysis(
              data = complete_data,
              time = input$survTime,
              event = input$survEvent,
              group = group_var,
              conf_level = input$survConfLevel
            )
          } else if (method == "Cox Regression") {
            # Validate covariates for Cox regression
            if (is.null(covariates) || length(covariates) == 0) {
              stop("Covariates are required for Cox regression")
            }
            
            # Check if covariates exist in data
            missing_covariates <- covariates[!covariates %in% names(complete_data)]
            if (length(missing_covariates) > 0) {
              stop(paste("Covariates not found in data:", paste(missing_covariates, collapse = ", ")))
            }
            
            result <- cox_regression_analysis(
              data = complete_data,
              time = input$survTime,
              event = input$survEvent,
              covariates = covariates,
              group = group_var,
              conf_level = input$survConfLevel
            )
          } else if (method == "Parametric Models") {
            # Validate distribution parameter
            valid_distributions <- c("weibull", "exponential", "lognormal", "loglogistic", "gompertz")
            if (!input$survDistribution %in% valid_distributions) {
              stop(paste("Invalid distribution. Must be one of:", paste(valid_distributions, collapse = ", ")))
            }
            
            result <- parametric_survival_analysis(
              data = complete_data,
              time = input$survTime,
              event = input$survEvent,
              covariates = covariates,
              group = group_var,
              distribution = input$survDistribution,
              conf_level = input$survConfLevel
            )
          } else if (method == "Competing Risks") {
            # Check if cmprsk package is available
            if (!requireNamespace("cmprsk", quietly = TRUE)) {
              stop("Competing risks analysis requires the 'cmprsk' package. Please install it using the provided installation script or run: source('install_cmprsk.R')")
            }
            
            result <- competing_risks_analysis(
              data = complete_data,
              time = input$survTime,
              event = input$survEvent,
              group = group_var,
              conf_level = input$survConfLevel
            )
            
            # Check if competing risks analysis was successful
            if (!is.null(result$available) && !result$available) {
              stop(result$message)
            }
          } else if (method == "Frailty Models") {
            # Validate frailty distribution
            valid_frailty_dists <- c("gamma", "lognormal")
            if (!input$survFrailtyDist %in% valid_frailty_dists) {
              stop(paste("Invalid frailty distribution. Must be one of:", paste(valid_frailty_dists, collapse = ", ")))
            }
            
            result <- frailty_model_analysis(
              data = complete_data,
              time = input$survTime,
              event = input$survEvent,
              covariates = covariates,
              group = group_var,
              frailty_dist = input$survFrailtyDist,
              conf_level = input$survConfLevel
            )
          } else {
            # Fallback to original method
            result <- perform_basic_survival_analysis(
              data = complete_data,
              time = input$survTime,
              event = input$survEvent,
              group = group_var
            )
          }
          
          # Validate result
          if (is.null(result)) {
            stop("Analysis returned null result")
          }
          
          # Add original data and parameters to result
          result$data <- complete_data
          result$time_var <- input$survTime
          result$event_var <- input$survEvent
          result$group_var <- group_var
          result$method <- method
          
          return(result)
          
        }, error = function(e) {
          log_error(paste("Survival analysis failed:", e$message), "survival_analysis")
          
          # Fallback to original method if new functions fail
          tryCatch({
            perform_basic_survival_analysis(
              data = complete_data,
              time = input$survTime,
              event = input$survEvent,
              group = group_var
            )
          }, error = function(e2) {
            log_error(paste("Fallback analysis also failed:", e2$message), "survival_analysis")
            stop("Analysis failed. Please check your data and parameters.")
          })
        })
        
      }, paste("Survival", method))
    })
    
    # Enhanced validation errors reactive
    survValidationErrors <- reactive({
      tryCatch({
        errors <- c()
        
        data <- survUploadData()
        if (is.null(data) || !is.data.frame(data)) {
          errors <- c(errors, "No data uploaded or file could not be read.")
          return(errors)
        }
        
        if (is.null(input$survTime) || input$survTime == "") {
          errors <- c(errors, "Please select a time variable.")
        }
        
        if (is.null(input$survEvent) || input$survEvent == "") {
          errors <- c(errors, "Please select an event variable.")
        }
        
        if (!is.null(input$survTime) && !is.null(input$survEvent) && 
            input$survTime != "" && input$survEvent != "") {
          
          # Check if time and event variables are different
          if (input$survTime == input$survEvent) {
            errors <- c(errors, "Time and event variables must be different.")
          }
          
          # Check if time variable is numeric and positive
          if (input$survTime %in% names(data)) {
            time_var <- data[[input$survTime]]
            if (!is.numeric(time_var)) {
              errors <- c(errors, "Time variable must be numeric.")
            } else {
              if (any(time_var <= 0, na.rm = TRUE)) {
                errors <- c(errors, "Time values must be positive.")
              }
              
              # Check for sufficient variance
              if (sd(time_var, na.rm = TRUE) == 0) {
                errors <- c(errors, "Time variable must have variance (not all the same).")
              }
            }
          }
          
          # Check if event variable is binary
          if (input$survEvent %in% names(data)) {
            event_var <- data[[input$survEvent]]
            unique_vals <- unique(event_var)
            if (length(unique_vals) != 2) {
              errors <- c(errors, "Event variable must be binary (exactly 2 unique values).")
            }
          }
          
          # Check for sufficient observations
          n_obs <- nrow(data)
          if (n_obs < 2) {
            errors <- c(errors, "At least 2 observations are required.")
          }
          
          # Check if group variable is selected and valid
          if (!is.null(input$survGroup) && input$survGroup != "" && input$survGroup != "None") {
            if (input$survGroup %in% names(data)) {
              group_var <- data[[input$survGroup]]
              if (length(unique(group_var)) < 2) {
                errors <- c(errors, "Group variable must have at least 2 unique values.")
              }
              
              # Check for sufficient observations per group
              group_counts <- table(group_var)
              if (any(group_counts < 2)) {
                errors <- c(errors, "Each group must have at least 2 observations.")
              }
            } else {
              errors <- c(errors, "Selected group variable not found in data.")
            }
          }
          
          # Check covariates for Cox regression
          if (input$survMethod == "Cox Regression") {
            if (is.null(input$survCovariates) || length(input$survCovariates) == 0) {
              errors <- c(errors, "Please select at least one covariate for Cox regression.")
            } else {
              # Check if covariates exist in data
              missing_covariates <- input$survCovariates[!input$survCovariates %in% names(data)]
              if (length(missing_covariates) > 0) {
                errors <- c(errors, paste("Covariates not found in data:", paste(missing_covariates, collapse = ", ")))
              }
            }
          }
          
          # Check stratification variables
          if (!is.null(input$survStratify) && length(input$survStratify) > 0) {
            missing_strata <- input$survStratify[!input$survStratify %in% names(data)]
            if (length(missing_strata) > 0) {
              errors <- c(errors, paste("Stratification variables not found in data:", paste(missing_strata, collapse = ", ")))
            }
          }
        }
        
        # Log validation errors if any
        if (length(errors) > 0) {
          log_warning(paste("Validation errors:", paste(errors, collapse = "; ")), "survival_validation")
        }
        
        errors
        
      }, error = function(e) {
        log_error(paste("Validation error check failed:", e$message), "survival_validation")
        c("An error occurred during validation. Please try again.")
      })
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goSurv, {
      output$survResults <- renderUI({
        errors <- survValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Survival Analysis", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("survTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("survAnalysis"),
                title = "Analysis",
                titlePanel("Survival Analysis Results"),
                br(),
                h4("Method Summary"),
                textOutput(ns('survMethodSummary')),
                br(),
                h4("Survival Summary"),
                verbatimTextOutput(ns('survSummary')),
                br(),
                h4("Survival Table"),
                tableOutput(ns('survTable')),
                br(),
                h4("Model Diagnostics"),
                tableOutput(ns('survModelDiagnostics')),
                br(),
                h4("Effect Size and Interpretation"),
                uiOutput(ns('survEffectSize'))
              ),
              tabPanel(
                id = ns("survDiagnostics"),
                title = "Data Summary/Diagnostics",
                h4("Data Summary"),
                tableOutput(ns('survDataSummary')),
                br(),
                h4("Event Summary"),
                tableOutput(ns('survEventSummary')),
                br(),
                h4("Assumptions Check"),
                uiOutput(ns('survAssumptions')),
                br(),
                h4("Survival Curves"),
                plotOutput(ns('survPlot'), height = "400px"),
                br(),
                h4("Diagnostic Plots"),
                plotOutput(ns('survDiagnosticPlots'), height = "600px")
              ),
              tabPanel(
                id = ns("survAdvanced"),
                title = "Advanced Analysis",
                h4("Model Comparison"),
                tableOutput(ns('survModelComparison')),
                br(),
                h4("Predicted Survival"),
                tableOutput(ns('survPredicted')),
                br(),
                h4("Hazard Analysis"),
                plotOutput(ns('survHazardPlot'), height = "400px")
              ),
              tabPanel(
                id = ns("survUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                uiOutput(ns('survDataTable'))
              )
            )
          )
        }
      })
    })
    
    # Method summary
    output$survMethodSummary <- renderText({
      req(survAnalysis())
      analysis <- survAnalysis()
      if (!is.null(analysis)) {
        paste("Analysis Method:", analysis$method,
              "\nTime Variable:", analysis$time_var,
              "\nEvent Variable:", analysis$event_var,
              ifelse(!is.null(analysis$group_var), paste("\nGroup Variable:", analysis$group_var), ""))
      }
    })
    
    # Survival summary
    output$survSummary <- renderPrint({
      req(survAnalysis())
      analysis <- survAnalysis()
      if (!is.null(analysis)) {
        if ("summary" %in% names(analysis)) {
          print(analysis$summary)
        } else if ("fit" %in% names(analysis)) {
          summary(analysis$fit)
        } else {
          cat("Summary not available for this analysis method.")
        }
      }
    })
    
    # Survival table
    output$survTable <- renderTable({
      req(survAnalysis())
      analysis <- survAnalysis()
      if (!is.null(analysis)) {
        if ("survival_table" %in% names(analysis)) {
          analysis$survival_table
        } else if ("fit" %in% names(analysis)) {
          summary_table <- summary(analysis$fit)
          
          # Create table with survival times and probabilities
          surv_table <- data.frame(
            Time = summary_table$time,
            n.risk = summary_table$n.risk,
            n.event = summary_table$n.event,
            n.censor = summary_table$n.censor,
            Survival = round(summary_table$surv, 4),
            Std.Error = round(summary_table$std.err, 4),
            Lower.CI = round(summary_table$lower, 4),
            Upper.CI = round(summary_table$upper, 4),
            stringsAsFactors = FALSE
          )
          
          # Add group information if available
          if (!is.null(analysis$group_var)) {
            surv_table$Group <- summary_table$strata
          }
          
          surv_table
        } else {
          data.frame(Message = "Survival table not available for this method")
        }
      }
    }, digits = 4)
    
    # Model diagnostics
    output$survModelDiagnostics <- renderTable({
      req(survAnalysis())
      analysis <- survAnalysis()
      if (!is.null(analysis)) {
        if ("diagnostics" %in% names(analysis)) {
          analysis$diagnostics
        } else {
          data.frame(
            Diagnostic = "Basic Analysis",
            Value = "Standard survival analysis performed",
            stringsAsFactors = FALSE
          )
        }
      }
    }, rownames = FALSE, digits = 4)
    
    # Effect size and interpretation
    output$survEffectSize <- renderUI({
      req(survAnalysis())
      analysis <- survAnalysis()
      
      if (!is.null(analysis)) {
        if ("effect_size" %in% names(analysis)) {
          effect <- analysis$effect_size
          
          tagList(
            h5("Effect Size Analysis:"),
            p(strong("Hazard Ratio: "), round(effect$hazard_ratio, 4)),
            p(strong("95% CI: "), paste0("[", round(effect$ci_lower, 4), ", ", round(effect$ci_upper, 4), "]")),
            p(strong("P-value: "), round(effect$p_value, 4)),
            p(strong("Interpretation: "), effect$interpretation),
            br(),
            p(strong("Effect Size Guidelines:")),
            p("- HR < 0.5: Large protective effect"),
            p("- HR 0.5-0.8: Moderate protective effect"),
            p("- HR 0.8-1.2: No meaningful effect"),
            p("- HR 1.2-2.0: Moderate harmful effect"),
            p("- HR > 2.0: Large harmful effect")
          )
        } else {
          tagList(
            h5("Effect Size Analysis:"),
            p("Effect size analysis not available for this method.")
          )
        }
      }
    })
    
    # Diagnostic plots
    output$survDiagnosticPlots <- renderPlot({
      req(survAnalysis())
      analysis <- survAnalysis()
      
      if (!is.null(analysis)) {
        if ("diagnostic_plots" %in% names(analysis)) {
          # Use diagnostic plots from analysis
          plots <- analysis$diagnostic_plots
          if (length(plots) == 1) {
            print(plots[[1]])
          } else if (length(plots) > 1) {
            # Arrange multiple plots
            n_plots <- length(plots)
            n_cols <- min(2, n_plots)
            n_rows <- ceiling(n_plots / n_cols)
            do.call(grid.arrange, c(plots, ncol = n_cols))
          }
        } else {
          # Create basic diagnostic plots
          par(mfrow = c(2, 2))
          
          # 1. Log-log plot for proportional hazards
          if ("fit" %in% names(analysis)) {
            plot(analysis$fit, fun = "cloglog", main = "Log-log Plot")
          }
          
          # 2. Residual plot
          if ("residuals" %in% names(analysis)) {
            plot(analysis$residuals, main = "Residual Plot")
          }
          
          # 3. Schoenfeld residuals
          if ("schoenfeld" %in% names(analysis)) {
            plot(analysis$schoenfeld, main = "Schoenfeld Residuals")
          }
          
          # 4. Deviance residuals
          if ("deviance" %in% names(analysis)) {
            plot(analysis$deviance, main = "Deviance Residuals")
          }
        }
      }
    })
    
    # Model comparison
    output$survModelComparison <- renderTable({
      req(survAnalysis())
      analysis <- survAnalysis()
      if (!is.null(analysis)) {
        if ("model_comparison" %in% names(analysis)) {
          analysis$model_comparison
        } else {
          data.frame(
            Model = analysis$method,
            AIC = "Not available",
            BIC = "Not available",
            Log_Likelihood = "Not available",
            stringsAsFactors = FALSE
          )
        }
      }
    }, rownames = FALSE, digits = 4)
    
    # Predicted survival
    output$survPredicted <- renderTable({
      req(survAnalysis())
      analysis <- survAnalysis()
      if (!is.null(analysis)) {
        if ("predicted_survival" %in% names(analysis)) {
          analysis$predicted_survival
        } else {
          data.frame(
            Time = "Not available",
            Predicted_Survival = "Not available",
            Lower_CI = "Not available",
            Upper_CI = "Not available",
            stringsAsFactors = FALSE
          )
        }
      }
    }, rownames = FALSE, digits = 4)
    
    # Hazard plot
    output$survHazardPlot <- renderPlot({
      req(survAnalysis())
      analysis <- survAnalysis()
      
      if (!is.null(analysis)) {
        if ("hazard_plot" %in% names(analysis)) {
          print(analysis$hazard_plot)
        } else {
          # Create basic hazard plot
          plot.new()
          text(0.5, 0.5, "Hazard plot not available for this method")
        }
      }
    })
    
    # Data summary
    output$survDataSummary <- renderTable({
      req(survAnalysis())
      analysis <- survAnalysis()
      if (!is.null(analysis)) {
        data <- analysis$data
        
        summary_stats <- data.frame(
          Variable = c("Time", "Event"),
          N = c(sum(!is.na(data[[analysis$time_var]])), sum(!is.na(data[[analysis$event_var]]))),
          Mean = c(round(mean(data[[analysis$time_var]], na.rm = TRUE), 4), NA),
          SD = c(round(sd(data[[analysis$time_var]], na.rm = TRUE), 4), NA),
          Min = c(round(min(data[[analysis$time_var]], na.rm = TRUE), 4), NA),
          Max = c(round(max(data[[analysis$time_var]], na.rm = TRUE), 4), NA),
          stringsAsFactors = FALSE
        )
        
        # Add group summary if available
        if (!is.null(analysis$group_var)) {
          group_summary <- table(data[[analysis$group_var]])
          for (i in 1:length(group_summary)) {
            summary_stats <- rbind(summary_stats, 
                                 data.frame(
                                   Variable = paste("Group", names(group_summary)[i]),
                                   N = group_summary[i],
                                   Mean = NA, SD = NA, Min = NA, Max = NA,
                                   stringsAsFactors = FALSE
                                 ))
          }
        }
        
        summary_stats
      }
    }, digits = 4)
    
    # Event summary
    output$survEventSummary <- renderTable({
      req(survAnalysis())
      analysis <- survAnalysis()
      if (!is.null(analysis)) {
        data <- analysis$data
        
        # Overall event summary
        event_table <- table(data[[analysis$event_var]])
        total_obs <- nrow(data)
        
        result <- data.frame(
          Event_Status = names(event_table),
          Count = as.numeric(event_table),
          Percentage = round(as.numeric(event_table) / total_obs * 100, 2),
          stringsAsFactors = FALSE
        )
        
        # Add group-specific event summary if available
        if (!is.null(analysis$group_var)) {
          group_event_table <- table(data[[analysis$group_var]], data[[analysis$event_var]])
          for (i in 1:nrow(group_event_table)) {
            group_name <- rownames(group_event_table)[i]
            for (j in 1:ncol(group_event_table)) {
              event_status <- colnames(group_event_table)[j]
              count <- group_event_table[i, j]
              group_total <- sum(group_event_table[i, ])
              percentage <- round(count / group_total * 100, 2)
              
              result <- rbind(result, 
                             data.frame(
                               Event_Status = paste(group_name, "-", event_status),
                               Count = count,
                               Percentage = percentage,
                               stringsAsFactors = FALSE
                             ))
            }
          }
        }
        
        result
      }
    }, digits = 4)
    
    # Assumptions check
    output$survAssumptions <- renderUI({
      req(survAnalysis())
      analysis <- survAnalysis()
      
      if (!is.null(analysis)) {
        data <- analysis$data
        
        # Check assumptions
        time_var <- data[[analysis$time_var]]
        event_var <- data[[analysis$event_var]]
        
        # Check for censoring
        censoring_rate <- round((1 - sum(event_var) / length(event_var)) * 100, 2)
        
        # Check for tied observations
        tied_obs <- sum(duplicated(time_var))
        
        tagList(
          h5("Key Assumptions:"),
          p("1. Independent observations"),
          p("2. Non-informative censoring"),
          p("3. Random sampling"),
          p("4. Proportional hazards (for log-rank test)"),
          br(),
          p(strong("Censoring rate: "), censoring_rate, "%"),
          p(strong("Tied observations: "), tied_obs),
          p(strong("Total observations: "), nrow(data)),
          br(),
          p("Note: High censoring rates (>80%) may affect reliability of results.")
        )
      }
    })
    
    # Survival plot
    output$survPlot <- renderPlot({
      req(survAnalysis())
      analysis <- survAnalysis()
      
      if (!is.null(analysis)) {
        # Create survival plot
        plot(analysis$fit, 
             main = "Survival Curves",
             xlab = "Time",
             ylab = "Survival Probability",
             col = c("blue", "red", "green", "purple", "orange")[1:length(unique(analysis$fit$strata))],
             lwd = 2)
        
        # Add legend if multiple groups
        if (!is.null(analysis$group_var)) {
          legend("topright", 
                 legend = names(analysis$fit$strata),
                 col = c("blue", "red", "green", "purple", "orange")[1:length(unique(analysis$fit$strata))],
                 lwd = 2,
                 title = analysis$group_var)
        }
        
        # Add grid
        grid()
      }
    })
    
    # Data table
    output$survDataTable <- renderUI({
      req(survUploadData())
      DT::DTOutput(ns("survDataTableInner"))
    })
    
    output$survDataTableInner <- DT::renderDT({
      req(survUploadData())
      DT::datatable(survUploadData(),
        options = list(pageLength = 25,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(survUploadData())))))
    })
  })
} 