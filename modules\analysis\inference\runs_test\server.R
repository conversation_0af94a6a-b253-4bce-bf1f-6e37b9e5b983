runsTestServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactives
    rtUploadData <- eventReactive(input$rtUserData, {
      handle_file_upload(input$rtUserData)
    })
    
    rtResults <- reactive({
      data <- rtUploadData()
      if (is.null(data) || is.null(input$rtVariable) || input$rtVariable == "") {
        return(NULL)
      }
      
      values <- data[[input$rtVariable]]
      values <- values[!is.na(values)]
      
      if (length(values) < 3) return(NULL)
      
      list(values = values, n = length(values), method = input$rtMethod, 
           custom_value = input$rtCustomValue)
    })
    
    # Validation errors
    rtValidationErrors <- reactive({
      errors <- c()
      data <- rtUploadData()
      
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$rtVariable) || input$rtVariable == "") {
        errors <- c(errors, "Please select a variable.")
        return(errors)
      }
      
      values <- data[[input$rtVariable]]
      if (length(na.omit(values)) < 3) {
        errors <- c(errors, "At least 3 non-missing values are required.")
      }
      
      errors
    })
    
    # Outputs
    output$rtHT <- renderUI({
      results <- rtResults()
      if (is.null(results)) return(NULL)
      runsTestHT(rtResults, reactive({input$rtSigLvl}))
    })
    
    output$runsTestPlot <- renderPlot({
      results <- rtResults()
      if (is.null(results)) return(NULL)
      runsTestPlot(rtResults)
    })
    
    output$rtConclusionOutput <- renderUI({
      results <- rtResults()
      if (is.null(results)) return(NULL)
      rtConclusion(rtResults, reactive({input$rtSigLvl}))
    })
    
    output$renderRTData <- renderUI({
      req(rtUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("rtInitialUploadTable")), style = "width: 75%"),
        br(),
        br()
      )
    })
    
    output$rtInitialUploadTable <- DT::renderDT({
      req(rtUploadData())
      DT::datatable(rtUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(rtUploadData())))))
    })
    
    # Update selectizeInput choices after file upload
    observeEvent(rtUploadData(), {
      data <- rtUploadData()
      updateSelectizeInput(session, 'rtVariable', choices = character(0), selected = NULL, server = TRUE)
      output$runsTestResults <- renderUI({ NULL })
      
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'rtVariable', choices = names(data), server = TRUE)
        output$runsTestResults <- renderUI({
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('rtPreviewTable'))
          )
        })
        output$rtPreviewTable <- DT::renderDT({
          head(data, 20)
        })
      }
    })
    
    # Show main results or error screen when Calculate is pressed
    observeEvent(input$goInference, {
      output$runsTestResults <- renderUI({
        errors <- rtValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Runs Test", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("rtTabset"),
              selected = "Analysis",
              tabPanel(
                id    = ns("rt"),
                title = "Analysis",
                titlePanel("Wald-Wolfowitz Runs Test"),
                br(),
                uiOutput(ns('rtHT')),
                br(),
                plotOutput(ns('runsTestPlot'), width = "50%", height = "400px"),
                br(),
                uiOutput(ns('rtConclusionOutput'))
              ),
              tabPanel(
                id    = ns("rtData"),
                title = "Uploaded Data",
                uiOutput(ns("renderRTData"))
              )
            )
          )
        }
      })
    })
  })
} 