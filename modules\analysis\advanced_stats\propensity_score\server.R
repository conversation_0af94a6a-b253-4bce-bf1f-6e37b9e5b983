PropensityScoreServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    psData <- eventReactive(input$psUserData, {
      handle_file_upload(input$psUserData)
    })
    
    observeEvent(psData(), {
      data <- psData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'psTreatment', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'psCovariates', choices = names(data), server = TRUE)
      }
    })
    
    psValidationErrors <- reactive({
      errors <- c()
      data <- psData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$psTreatment)) {
        errors <- c(errors, "Select a treatment variable.")
      }
      if (is.null(input$psCovariates) || length(input$psCovariates) < 1) {
        errors <- c(errors, "Select at least one covariate variable.")
      }
      if (!is.null(input$psTreatment)) {
        treatment_values <- unique(data[[input$psTreatment]])
        if (length(treatment_values) != 2) {
          errors <- c(errors, "Treatment variable must have exactly two distinct values.")
        }
      }
      errors
    })
    
    psResult <- eventReactive(input$goPS, {
      data <- psData()
      req(data, input$psTreatment, input$psCovariates)
      
      # Get parameters from UI
      method <- ifelse(is.null(input$psMethod), "matching", input$psMethod)
      
      propensity_score_analysis(data, input$psTreatment, input$psCovariates, method = method)
    })
    
    observeEvent(input$goPS, {
      output$psResultsUI <- renderUI({
        errors <- psValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Propensity Score Analysis", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("psTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("psAnalysis"),
                title = "Analysis",
                titlePanel("Propensity Score Analysis Results"),
                br(),
                h4("Model Coefficients"),
                tableOutput(ns('psCoefficients')),
                h4("Fit Statistics"),
                tableOutput(ns('psFitStats')),
                h4("Method Used"),
                textOutput(ns('psMethod')),
                h4("Number of Observations"),
                textOutput(ns('psObservations')),
                h4("Treatment Groups"),
                textOutput(ns('psTreatmentGroups')),
                h4("Balance Table"),
                tableOutput(ns('psBalanceTable')),
                h4("Overall Balance"),
                tableOutput(ns('psOverallBalance')),
                h4("Matched Pairs (if matching)"),
                textOutput(ns('psMatchedPairs')),
                h4("Sample Sizes (if matching)"),
                textOutput(ns('psSampleSizes')),
                h4("Balance Improvement"),
                textOutput(ns('psBalanceImprovement'))
              ),
              tabPanel(
                id = ns("psDiagnostics"),
                title = "Diagnostics",
                h4("Propensity Score Distribution Plot"),
                plotOutput(ns('psDistributionPlot'), height = "300px"),
                h4("Balance Plot (Before/After)"),
                plotOutput(ns('psBalancePlot'), height = "300px")
              ),
              tabPanel(
                id = ns("psUploadedData"),
                title = "Uploaded Data",
                h4("Raw Uploaded Data"),
                DT::DTOutput(ns('psRawDataTable'))
              )
            )
          )
        }
      })
    })
    output$psRawDataTable <- DT::renderDT({
      req(psData())
      DT::datatable(psData(), options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), rownames = FALSE)
    })
    
    # Propensity score model coefficients
    output$psCoefficients <- renderTable({
      res <- psResult()
      if (is.null(res)) return(NULL)
      res$coefficients
    }, rownames = FALSE, digits = 4)
    
    # Model fit statistics
    output$psFitStats <- renderTable({
      res <- psResult()
      if (is.null(res)) return(NULL)
      res$fit_statistics
    }, rownames = FALSE, digits = 4)
    
    # Method used
    output$psMethod <- renderText({
      res <- psResult()
      if (is.null(res)) return(NULL)
      paste("Method used:", res$method)
    })
    
    # Number of observations
    output$psObservations <- renderText({
      res <- psResult()
      if (is.null(res)) return(NULL)
      paste("Number of observations:", res$n_observations)
    })
    
    # Number of treated and control
    output$psTreatmentGroups <- renderText({
      res <- psResult()
      if (is.null(res)) return(NULL)
      paste("Treated:", res$n_treated, ", Control:", res$n_control)
    })
    
    # Balance table (for both matching and weighting)
    output$psBalanceTable <- renderTable({
      res <- psResult()
      if (is.null(res)) return(NULL)
      res$balance_table
    }, rownames = FALSE, digits = 4)
    
    # Overall balance statistics (for both matching and weighting)
    output$psOverallBalance <- renderTable({
      res <- psResult()
      if (is.null(res)) return(NULL)
      res$overall_balance
    }, rownames = FALSE, digits = 4)
    
    # Number of matched pairs (matching only)
    output$psMatchedPairs <- renderText({
      res <- psResult()
      if (is.null(res) || res$method != "matching") return(NULL)
      paste("Number of matched pairs:", res$n_matched / 2)
    })
    
    # Original vs matched sample sizes (matching only)
    output$psSampleSizes <- renderText({
      res <- psResult()
      if (is.null(res) || res$method != "matching") return(NULL)
      paste("Original sample:", res$n_original, ", Matched sample:", res$n_matched)
    })
    
    # Balance improvement summary
    output$psBalanceImprovement <- renderText({
      res <- psResult()
      if (is.null(res)) return(NULL)
      
      if (res$method == "matching") {
        mean_diff <- mean(abs(res$balance_table$Std_Mean_Diff))
        if (mean_diff < 0.1) balance_desc <- "excellent"
        else if (mean_diff < 0.2) balance_desc <- "good"
        else if (mean_diff < 0.3) balance_desc <- "fair"
        else balance_desc <- "poor"
        paste("Balance quality:", balance_desc, "(mean absolute standardized difference =", round(mean_diff, 4), ")")
      } else if (res$method == "weighting") {
        mean_diff_unweighted <- mean(abs(res$balance_table$Std_Mean_Diff_Unweighted))
        mean_diff_weighted <- mean(abs(res$balance_table$Std_Mean_Diff_Weighted))
        improvement <- mean_diff_unweighted - mean_diff_weighted
        paste("Balance improvement:", round(improvement, 4), 
              "(unweighted:", round(mean_diff_unweighted, 4), 
              "→ weighted:", round(mean_diff_weighted, 4), ")")
      } else {
        "Balance improvement not available"
      }
    })
    
    # Propensity score distribution plot
    output$psDistributionPlot <- renderPlot({
      res <- psResult()
      if (is.null(res)) return(NULL)
      
      # Get original data
      data <- psData()
      treatment_var <- input$psTreatment
      
      # Create treatment indicator
      if (!is.numeric(data[[treatment_var]])) {
        data$treatment_indicator <- as.numeric(as.factor(data[[treatment_var]])) - 1
      } else {
        data$treatment_indicator <- data[[treatment_var]]
      }
      
      # Add propensity scores
      data$propensity_score <- res$propensity_scores
      
      ggplot2::ggplot(data, ggplot2::aes(x = propensity_score, fill = factor(treatment_indicator))) +
        ggplot2::geom_density(alpha = 0.7) +
        ggplot2::labs(title = "Propensity Score Distribution",
                     x = "Propensity Score", y = "Density", fill = "Treatment") +
        ggplot2::scale_fill_discrete(labels = c("Control", "Treated")) +
        ggplot2::theme_minimal()
    })
    
    # Balance plot (before/after)
    output$psBalancePlot <- renderPlot({
      res <- psResult()
      if (is.null(res)) return(NULL)
      
      balance_data <- res$balance_table
      
      if (res$method == "matching") {
        # For matching, show standardized mean differences
        plot_data <- data.frame(
          Variable = balance_data$Variable,
          Std_Mean_Diff = abs(balance_data$Std_Mean_Diff)
        )
        
        ggplot2::ggplot(plot_data, ggplot2::aes(x = Variable, y = Std_Mean_Diff)) +
          ggplot2::geom_bar(stat = "identity", fill = "steelblue", alpha = 0.7) +
          ggplot2::geom_hline(yintercept = 0.1, color = "red", linetype = "dashed") +
          ggplot2::labs(title = "Balance After Matching - Absolute Standardized Mean Differences",
                       x = "Covariates", y = "|Standardized Mean Difference|") +
          ggplot2::theme_minimal() +
          ggplot2::theme(axis.text.x = ggplot2::element_text(angle = 45, hjust = 1))
        
      } else if (res$method == "weighting") {
        # For weighting, show before/after comparison
        plot_data <- data.frame(
          Variable = balance_data$Variable,
          Before = abs(balance_data$Std_Mean_Diff_Unweighted),
          After = abs(balance_data$Std_Mean_Diff_Weighted)
        )
        
        plot_data_long <- tidyr::gather(plot_data, key = "Time", value = "Std_Mean_Diff", -Variable)
        
        ggplot2::ggplot(plot_data_long, ggplot2::aes(x = Variable, y = Std_Mean_Diff, fill = Time)) +
          ggplot2::geom_bar(stat = "identity", position = "dodge", alpha = 0.7) +
          ggplot2::geom_hline(yintercept = 0.1, color = "red", linetype = "dashed") +
          ggplot2::labs(title = "Balance Before and After Weighting",
                       x = "Covariates", y = "|Standardized Mean Difference|", fill = "Time") +
          ggplot2::scale_fill_discrete(labels = c("Before", "After")) +
          ggplot2::theme_minimal() +
          ggplot2::theme(axis.text.x = ggplot2::element_text(angle = 45, hjust = 1))
      }
    })
    
    # Model diagnostics
    output$psModelDiagnostics <- renderText({
      res <- psResult()
      if (is.null(res)) return(NULL)
      
      aic <- res$fit_statistics$Value[res$fit_statistics$Statistic == "AIC"]
      bic <- res$fit_statistics$Value[res$fit_statistics$Statistic == "BIC"]
      pseudo_r2 <- res$fit_statistics$Value[res$fit_statistics$Statistic == "Pseudo R-squared"]
      
      paste("Propensity Score Model: AIC =", round(aic, 4), 
            ", BIC =", round(bic, 4), 
            ", Pseudo R² =", round(pseudo_r2, 4))
    })
    
    # Assumption checks
    output$psAssumptions <- renderText({
      res <- psResult()
      if (is.null(res)) return(NULL)
      
      # Check for common support
      ps_scores <- res$propensity_scores
      treated_scores <- ps_scores[data$treatment_indicator == 1]
      control_scores <- ps_scores[data$treatment_indicator == 0]
      
      treated_range <- range(treated_scores)
      control_range <- range(control_scores)
      
      overlap_min <- max(treated_range[1], control_range[1])
      overlap_max <- min(treated_range[2], control_range[2])
      
      if (overlap_min < overlap_max) {
        paste("Common support assumption: Satisfied (overlap range:", 
              round(overlap_min, 4), "to", round(overlap_max, 4), ")")
      } else {
        "Common support assumption: Violated - no overlap in propensity scores"
      }
    })
    
    # Summary statistics
    output$psSummary <- renderText({
      res <- psResult()
      if (is.null(res)) return(NULL)
      
      if (res$method == "matching") {
        paste("Matching Summary: Successfully matched", res$n_matched, "observations from", 
              res$n_original, "total observations")
      } else if (res$method == "weighting") {
        paste("Weighting Summary: Applied inverse probability weights to", res$n_observations, "observations")
      } else {
        "Summary not available"
      }
    })
  })
} 