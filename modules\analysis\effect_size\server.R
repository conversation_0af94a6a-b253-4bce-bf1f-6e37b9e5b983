EffectSizeServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactive value to store results
    esResults <- reactiveVal(NULL)
    
    # Get data based on user input
    esData <- reactive({
      if (input$esDataInput == 'Enter Raw Data') {
        # Parse raw data input
        group1 <- as.numeric(unlist(strsplit(input$esGroup1Data, ",|\\s+")))
        group2 <- as.numeric(unlist(strsplit(input$esGroup2Data, ",|\\s+")))
        
        # Remove NAs and return
        list(
          group1 = group1[!is.na(group1)],
          group2 = group2[!is.na(group2)]
        )
      } else {
        # Handle file upload
        if (is.null(input$esUserData)) {
          return(NULL)
        }
        
        # Use the calculation file's upload function
        upload_result <- effect_size_uploadData_func(
          uploaded_file = input$esUserData,
          group1_var = input$esGroup1,
          group2_var = input$esGroup2
        )
        
        if (!is.null(upload_result$error)) {
          showNotification(upload_result$error, type = "error")
          return(NULL)
        }
        
        return(upload_result)
      }
    })
    
    # Update variable choices when data is uploaded
    observeEvent(esData(), {
      data <- esData()
      if (!is.null(data$data) && is.data.frame(data$data)) {
        updateSelectizeInput(session, 'esGroup1', 
                           choices = names(data$data), 
                           selected = input$esGroup1)
        updateSelectizeInput(session, 'esGroup2', 
                           choices = names(data$data),
                           selected = input$esGroup2)
      }
    })
    
    # Render UI for effect size-specific parameters
    output$esParams <- renderUI({
      switch(input$esTestType,
        "t-test" = tagList(
          p("Calculating effect sizes for independent samples t-test.")
        ),
        "ANOVA" = tagList(
          p("Calculating effect sizes for ANOVA.")
        ),
        "Proportion" = tagList(
          p("Calculating effect sizes for proportions.")
        ),
        "Correlation" = tagList(
          p("Calculating correlation effect sizes.")
        ),
        NULL
      )
    })
    # Calculate effect sizes when Go button is clicked
    observeEvent(input$goES, {
      # Get the data
      data <- esData()
      
      # Check if we have valid data
      if (is.null(data) || 
          (input$esDataInput == 'Upload Data' && 
           (is.null(input$esGroup1) || is.null(input$esGroup2)))) {
        showNotification("Please provide valid data and select variables.", 
                        type = "error")
        return()
      }
      
      # Get the groups
      if (input$esDataInput == 'Enter Raw Data') {
        group1 <- data$group1
        group2 <- data$group2
      } else {
        group1 <- data$group1
        group2 <- data$group2
      }
      
      # Check group sizes
      if (length(group1) < 2 || length(group2) < 2) {
        showNotification("Each group must have at least 2 observations.", 
                        type = "error")
        return()
      }
      
      # Calculate effect sizes using the calculation file functions
      withProgress(message = 'Calculating effect sizes...', value = 0, {
        # Basic effect sizes
        results <- effect_size_results_func(
          group1 = group1,
          group2 = group2,
          type = c("cohens_d", "hedges_g", "glass_delta")
        )
        
        # Check for errors
        if (!is.null(results$error)) {
          showNotification(results$error, type = "error")
          return()
        }
        
        # Add additional effect sizes based on test type
        if (input$esTestType == "ANOVA") {
          eta_result <- calculate_eta_squared(
            x = c(group1, group2),
            group = rep(c("Group1", "Group2"), c(length(group1), length(group2)))
          )
          if (!is.null(eta_result$error)) {
            showNotification(eta_result$error, type = "warning")
          } else {
            results <- c(results, eta_result)
          }
        } else if (input$esTestType == "Proportion") {
          # For proportion, we'll calculate odds ratio if data is binary
          if (length(unique(c(group1, group2))) == 2) {
            or_result <- calculate_odds_ratio(
              x = rep(c(0, 1), c(length(group1), length(group2))),
              y = c(ifelse(group1 > median(c(group1, group2)), 1, 0),
                   ifelse(group2 > median(c(group1, group2)), 1, 0))
            )
            if (!is.null(or_result$error)) {
              showNotification(or_result$error, type = "warning")
            } else {
              results <- c(results, or_result)
            }
          }
        } else if (input$esTestType == "Correlation" && length(group1) == length(group2)) {
          # For correlation, we'll use Pearson's r
          results$pearson_r <- cor(group1, group2)
          
          # Add Fisher's z CI if possible
          if (length(group1) > 3) {
            r <- results$pearson_r
            z <- 0.5 * log((1 + r) / (1 - r))
            se_z <- 1 / sqrt(length(group1) - 3)
            ci_z <- z + c(-1, 1) * qnorm(0.975) * se_z
            results$pearson_r_ci <- (exp(2 * ci_z) - 1) / (exp(2 * ci_z) + 1)
          }
        }
        
        # Store the results
        esResults(results)
      })
    })
    # Render the results UI
    output$esResults <- renderUI({
      results <- esResults()
      if (is.null(results)) {
        return(tags$div(class = "alert alert-info", "No results to display. Please run the analysis first."))
      }
      
      # Use the calculation file's HTML function
      tagList(
        effect_size_ht_html(results),
        hr(),
        h4("Summary Statistics"),
        htmlOutput(ns("esSummary")),
        hr(),
        h4("Effect Sizes"),
        htmlOutput(ns("esEffectSizes")),
        hr(),
        h4("Visualization"),
        plotOutput(ns("esPlot"), height = "400px")
      )
    })
    
    # Render summary statistics
    output$esSummary <- renderText({
      results <- esResults()
      if (is.null(results) || !is.null(results$error)) {
        return("")
      }
      
      # Use the calculation file's summary function
      effect_size_summary_html(results)
    })
    
    # Render effect sizes
    output$esEffectSizes <- renderText({
      results <- esResults()
      if (is.null(results) || !is.null(results$error)) {
        return("")
      }
      
      # Create a data frame of effect sizes
      effect_sizes <- data.frame(
        `Effect Size` = character(),
        Value = character(),
        stringsAsFactors = FALSE
      )
      
      # Add each effect size if it exists
      if (!is.null(results$cohens_d)) {
        effect_sizes <- rbind(effect_sizes, data.frame(
          `Effect Size` = "Cohen's d (95% CI)",
          Value = sprintf("%.3f [%.3f, %.3f]", 
                         results$cohens_d, 
                         results$cohens_d_ci[1], 
                         results$cohens_d_ci[2]),
          stringsAsFactors = FALSE
        ))
      }
      
      if (!is.null(results$hedges_g)) {
        effect_sizes <- rbind(effect_sizes, data.frame(
          `Effect Size` = "Hedges' g (95% CI)",
          Value = sprintf("%.3f [%.3f, %.3f]", 
                         results$hedges_g, 
                         results$hedges_g_ci[1], 
                         results$hedges_g_ci[2]),
          stringsAsFactors = FALSE
        ))
      }
      
      if (!is.null(results$glass_delta)) {
        effect_sizes <- rbind(effect_sizes, data.frame(
          `Effect Size` = "Glass's Δ",
          Value = sprintf("%.3f", results$glass_delta),
          stringsAsFactors = FALSE
        ))
      }
      
      if (!is.null(results$eta_squared)) {
        effect_sizes <- rbind(effect_sizes, data.frame(
          `Effect Size` = "Eta squared (η²)",
          Value = sprintf("%.3f", results$eta_squared),
          stringsAsFactors = FALSE
        ))
      }
      
      if (!is.null(results$partial_eta_squared)) {
        effect_sizes <- rbind(effect_sizes, data.frame(
          `Effect Size` = "Partial Eta squared (η²p)",
          Value = sprintf("%.3f", results$partial_eta_squared),
          stringsAsFactors = FALSE
        ))
      }
      
      if (!is.null(results$odds_ratio)) {
        effect_sizes <- rbind(effect_sizes, data.frame(
          `Effect Size` = "Odds Ratio (95% CI)",
          Value = if (!is.null(results$odds_ratio_ci)) {
            sprintf("%.3f [%.3f, %.3f]", 
                   results$odds_ratio, 
                   results$odds_ratio_ci[1], 
                   results$odds_ratio_ci[2])
          } else {
            sprintf("%.3f", results$odds_ratio)
          },
          stringsAsFactors = FALSE
        ))
      }
      
      if (!is.null(results$pearson_r)) {
        effect_sizes <- rbind(effect_sizes, data.frame(
          `Effect Size` = "Pearson's r (95% CI)",
          Value = if (!is.null(results$pearson_r_ci)) {
            sprintf("%.3f [%.3f, %.3f]", 
                   results$pearson_r, 
                   results$pearson_r_ci[1], 
                   results$pearson_r_ci[2])
          } else {
            sprintf("%.3f", results$pearson_r)
          },
          stringsAsFactors = FALSE
        ))
      }
      
      # Convert to HTML table
      knitr::kable(effect_sizes, format = "html", row.names = FALSE) %>%
        kableExtra::kable_styling(bootstrap_options = c("striped", "hover"))
    })
    
    # Render plot
    output$esPlot <- renderPlot({
      results <- esResults()
      if (is.null(results) || !is.null(results$error)) {
        return(NULL)
      }
      
      # Use the calculation file's plot function
      effect_size_plot(results)
    })
    
    # Download handler for results
    output$downloadES <- downloadHandler(
      filename = function() {
        paste0("effect_size_results_", format(Sys.time(), "%Y%m%d_%H%M%S"), 
               ifelse(input$esFormat == "csv", ".csv", ".xlsx"))
      },
      content = function(file) {
        results <- esResults()
        if (is.null(results)) {
          return(NULL)
        }
        
        # Get the data
        data <- esData()
        
        # Prepare data for export
        export_data <- data.frame(
          Group = c(rep("Group 1", length(data$group1)), 
                   rep("Group 2", length(data$group2))),
          Value = c(data$group1, data$group2)
        )
        
        # Prepare effect sizes
        effect_sizes <- data.frame(
          Effect_Size = c(
            "Cohen's d", "Cohen's d Lower CI", "Cohen's d Upper CI",
            "Hedges' g", "Hedges' g Lower CI", "Hedges' g Upper CI",
            "Glass's Delta", "Eta squared", "Partial Eta squared", 
            "Odds Ratio", "Odds Ratio Lower CI", "Odds Ratio Upper CI",
            "Pearson's r", "Pearson's r Lower CI", "Pearson's r Upper CI"
          ),
          Value = c(
            ifelse(!is.null(results$cohens_d), results$cohens_d, NA),
            ifelse(!is.null(results$cohens_d_ci[1]), results$cohens_d_ci[1], NA),
            ifelse(!is.null(results$cohens_d_ci[2]), results$cohens_d_ci[2], NA),
            ifelse(!is.null(results$hedges_g), results$hedges_g, NA),
            ifelse(!is.null(results$hedges_g_ci[1]), results$hedges_g_ci[1], NA),
            ifelse(!is.null(results$hedges_g_ci[2]), results$hedges_g_ci[2], NA),
            ifelse(!is.null(results$glass_delta), results$glass_delta, NA),
            ifelse(!is.null(results$eta_squared), results$eta_squared, NA),
            ifelse(!is.null(results$partial_eta_squared), results$partial_eta_squared, NA),
            ifelse(!is.null(results$odds_ratio), results$odds_ratio, NA),
            ifelse(!is.null(results$odds_ratio_ci[1]), results$odds_ratio_ci[1], NA),
            ifelse(!is.null(results$odds_ratio_ci[2]), results$odds_ratio_ci[2], NA),
            ifelse(!is.null(results$pearson_r), results$pearson_r, NA),
            ifelse(!is.null(results$pearson_r_ci[1]), results$pearson_r_ci[1], NA),
            ifelse(!is.null(results$pearson_r_ci[2]), results$pearson_r_ci[2], NA)
          )
        )
        
        # Remove rows with all NAs
        effect_sizes <- effect_sizes[!is.na(effect_sizes$Value), ]
        
        # Write to file
        if (input$esFormat == "csv") {
          write.csv(export_data, file, row.names = FALSE)
        } else {
          writexl::write_xlsx(
            list("Data" = export_data, "Effect_Sizes" = effect_sizes),
            path = file
          )
        }
      }
    )
  })
}
