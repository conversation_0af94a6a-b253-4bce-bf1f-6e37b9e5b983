# Descriptive statistics calculation and output helpers

# 1. Data Upload Function
desc_stats_uploadData_func <- function(dsUserData) {
  ext <- tools::file_ext(dsUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(dsUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(dsUserData$datapath),
         xlsx = readxl::read_xlsx(dsUserData$datapath),
         txt = readr::read_tsv(dsUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

# 2. Main Results Function
desc_stats_results_func <- function(data) {
  tryCatch({
    if (is.null(data) || !is.data.frame(data)) {
      return(list(error = "Invalid data provided"))
    }

    # Return the data for further processing
    list(
      data = data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during descriptive statistics calculation:", e$message))
  })
}

# 3. HTML Output Function
desc_stats_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(p("Error:", results$error)))
  }

  tagList(
    h4("Descriptive Statistics Analysis"),
    p("Data successfully loaded and ready for analysis.")
  )
}

# 4. Summary HTML Function
desc_stats_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }

  data <- results$data
  if (is.null(data)) return(NULL)

  # Create summary for numeric columns
  numeric_cols <- sapply(data, is.numeric)
  if (any(numeric_cols)) {
    summary_data <- data[, numeric_cols, drop = FALSE]
    summary_stats <- do.call(rbind, lapply(names(summary_data), function(col) {
      dat <- na.omit(summary_data[[col]])
      if (length(dat) > 0) {
        data.frame(
          Variable = col,
          N = length(dat),
          Mean = round(mean(dat), 4),
          SD = round(sd(dat), 4),
          Min = round(min(dat), 4),
          Max = round(max(dat), 4)
        )
      }
    }))

    tagList(
      h4("Summary Statistics"),
      renderTable(summary_stats, digits = 4)
    )
  } else {
    tagList(p("No numeric variables found for summary statistics."))
  }
}

# 5. Plot Function
desc_stats_plot <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }

  data <- results$data
  if (is.null(data)) return(NULL)

  # Create a simple plot for the first numeric column
  numeric_cols <- sapply(data, is.numeric)
  if (any(numeric_cols)) {
    first_numeric <- names(data)[numeric_cols][1]
    dat <- na.omit(data[[first_numeric]])

    if (length(dat) > 1) {
      hist(dat, main = paste("Histogram of", first_numeric),
           xlab = first_numeric, col = "steelblue", breaks = 20)
    }
  }
}

# Keep existing helper functions
createDSColumn <- function(dat) ({
  sampSize <- length(dat)
  sampSum <- sum(dat)
  sumSquares <- sum(dat^2)
  xbar <- round(mean(dat),4)
  sampMode <- Modes(dat)
  if(sampMode == "No mode exists"){
    modeFreq <- paste("")
  } else{
    modeFreq <- paste("Each appears", attr(Mode(dat), "freq"), "times")
  }
  sampMin <- min(dat)
  quartiles <- GetQuartiles(dat)
  quartile1 <-  quartiles$q1
  sampMedian <- median(dat)
  quartile3 <-  quartiles$q3
  sampMax <- max(dat)
  sampIQR <- round(quartile3 - quartile1, 4)
  lowerFence <- round(quartile1 - (1.5*sampIQR), 4)
  upperFence <- round(quartile3 + (1.5*sampIQR), 4)
  numOutliers <- sum(dat < lowerFence) + sum(dat > upperFence)
  if(is.na(numOutliers) || numOutliers == 0) {
    outliers <- "There are no outliers."
  } else {
    outliers <- paste(as.character(GetOutliers(dat, lowerFence, upperFence)), collapse=", ")
  }
  sampRange <- Range(min(dat), max(dat))
  sampVar <- round(var(dat),4)
  sampMeanSE <- round(sd(dat)/sqrt(length(dat)), 4)
  sampStdDev <- sd(dat)
  if (sampStdDev < 0.0001) {
    formattedSD <- sprintf("%.4e", sampStdDev)
  } else {
    formattedSD <- sprintf("%.4f", sampStdDev)
  }
  coeffVar <- round(sampStdDev/xbar, 4)
  if (is.na(coeffVar)) {
    coeffVar <- "Coefficient of Variation is undefined for this data"
  } else if (is.infinite(coeffVar)) {
    coeffVar <- "Infinity"
  }
  # Always define sampSkewness and sampKurtosis
  sampSkewness <- NA
  sampKurtosis <- NA
  if(sampSize < 3){
    sampSkewness <- round(skewness(dat), 4)
  } else {
    sampSkewness <- round(skewness(dat), 4)
  }
  if(sampSize < 4){
    sampKurtosis <- round(kurtosis(dat), 4)
  } else {
    sampKurtosis <- round(kurtosis(dat), 4)
  }
  if(is.nan(sampSkewness)) {
    sampSkewness <- "Not enough variability or data points in the dataset."
  }
  if(is.nan(sampKurtosis)) {
    sampKurtosis <- "Not enough variability or data points in the dataset."
  }
  
  # Enhanced statistics
  sampGeometricMean <- if(all(dat > 0)) round(exp(mean(log(dat))), 4) else "Geometric mean requires positive values"
  sampHarmonicMean <- if(all(dat > 0)) round(sampSize / sum(1/dat), 4) else "Harmonic mean requires positive values"
  sampTrimmedMean <- round(mean(dat, trim = 0.1), 4)  # 10% trimmed mean
  sampWinsorizedMean <- round(mean(winsorize(dat, 0.1)), 4)  # 10% winsorized mean
  
  # Additional dispersion measures
  sampMAD <- round(mad(dat), 4)  # Median Absolute Deviation
  sampCV <- if(xbar != 0) round(sampStdDev/abs(xbar), 4) else "CV undefined (mean = 0)"
  
  # Normality test
  normality_test <- tryCatch({
    shapiro_result <- shapiro.test(dat)
    list(
      statistic = round(shapiro_result$statistic, 4),
      p_value = round(shapiro_result$p.value, 4),
      is_normal = shapiro_result$p.value > 0.05
    )
  }, error = function(e) {
    list(statistic = NA, p_value = NA, is_normal = NA)
  })
  
  # Confidence intervals
  t_critical <- qt(0.975, sampSize - 1)
  ci_lower <- round(xbar - t_critical * sampMeanSE, 4)
  ci_upper <- round(xbar + t_critical * sampMeanSE, 4)
  
  dfCol <- data.frame(Value = c(
    sampSize, sampSum, sumSquares, xbar, sampMode, modeFreq, sampMin, quartile1, 
    sampMedian, quartile3, sampMax, sampIQR, lowerFence, upperFence, numOutliers, 
    outliers, sampRange, formattedSD, sampVar, sampMeanSE, coeffVar, sampSkewness, 
    sampKurtosis, sampGeometricMean, sampHarmonicMean, sampTrimmedMean, sampWinsorizedMean,
    sampMAD, sampCV, ci_lower, ci_upper, normality_test$statistic, normality_test$p_value
  ))
  
  # Add additional information to the result
  attr(dfCol, "normality_test") <- normality_test
  attr(dfCol, "confidence_interval") <- c(ci_lower, ci_upper)
  attr(dfCol, "geometric_mean") <- sampGeometricMean
  attr(dfCol, "harmonic_mean") <- sampHarmonicMean
  
  dfCol
})

# Enhanced descriptive statistics with additional measures
enhanced_descriptive_stats <- function(dat) {
  if (length(dat) < 2) {
    stop("At least 2 observations are required for descriptive statistics")
  }
  
  # Basic statistics
  basic_stats <- createDSColumn(dat)
  
  # Additional percentiles
  percentiles <- quantile(dat, probs = c(0.01, 0.05, 0.10, 0.25, 0.50, 0.75, 0.90, 0.95, 0.99))
  
  # Robust statistics
  robust_stats <- list(
    median_absolute_deviation = mad(dat),
    interquartile_range = IQR(dat),
    trimmed_mean_10 = mean(dat, trim = 0.1),
    winsorized_mean_10 = mean(winsorize(dat, 0.1))
  )
  
  # Distribution shape
  shape_stats <- list(
    skewness = skewness(dat),
    kurtosis = kurtosis(dat),
    coefficient_of_variation = if(mean(dat) != 0) sd(dat) / abs(mean(dat)) else NA
  )
  
  # Outlier detection using multiple methods
  outliers <- list(
    iqr_method = boxplot.stats(dat)$out,
    z_score_method = dat[abs(scale(dat)) > 2.5],
    modified_z_score_method = dat[abs((dat - median(dat)) / mad(dat)) > 3.5]
  )
  
  # Normality assessment
  normality <- tryCatch({
    shapiro_test <- shapiro.test(dat)
    list(
      shapiro_w = shapiro_test$statistic,
      shapiro_p = shapiro_test$p.value,
      is_normal = shapiro_test$p.value > 0.05
    )
  }, error = function(e) {
    list(shapiro_w = NA, shapiro_p = NA, is_normal = NA)
  })
  
  # Confidence intervals
  n <- length(dat)
  mean_val <- mean(dat)
  se <- sd(dat) / sqrt(n)
  t_crit <- qt(0.975, n - 1)
  
  confidence_intervals <- list(
    mean_95_ci = c(mean_val - t_crit * se, mean_val + t_crit * se),
    median_95_ci = quantile(dat, c(0.025, 0.975)),
    variance_95_ci = c(
      (n-1) * var(dat) / qchisq(0.975, n-1),
      (n-1) * var(dat) / qchisq(0.025, n-1)
    )
  )
  
  # Return comprehensive results
  list(
    basic_statistics = basic_stats,
    percentiles = percentiles,
    robust_statistics = robust_stats,
    shape_statistics = shape_stats,
    outliers = outliers,
    normality = normality,
    confidence_intervals = confidence_intervals,
    sample_size = n,
    missing_values = sum(is.na(dat))
  )
}

# Helper function for winsorization
winsorize <- function(x, trim = 0.1) {
  if (trim <= 0 || trim >= 0.5) {
    stop("trim must be between 0 and 0.5")
  }
  
  n <- length(x)
  k <- floor(n * trim)
  
  if (k == 0) return(x)
  
  sorted_x <- sort(x)
  lower_bound <- sorted_x[k + 1]
  upper_bound <- sorted_x[n - k]
  
  winsorized_x <- x
  winsorized_x[x < lower_bound] <- lower_bound
  winsorized_x[x > upper_bound] <- upper_bound
  
  winsorized_x
}

# Enhanced outlier detection
detect_outliers <- function(dat, method = "iqr") {
  if (method == "iqr") {
    q1 <- quantile(dat, 0.25)
    q3 <- quantile(dat, 0.75)
    iqr <- q3 - q1
    lower_bound <- q1 - 1.5 * iqr
    upper_bound <- q3 + 1.5 * iqr
    outliers <- dat[dat < lower_bound | dat > upper_bound]
  } else if (method == "zscore") {
    z_scores <- abs(scale(dat))
    outliers <- dat[z_scores > 2.5]
  } else if (method == "modified_zscore") {
    median_val <- median(dat)
    mad_val <- mad(dat)
    modified_z_scores <- abs((dat - median_val) / mad_val)
    outliers <- dat[modified_z_scores > 3.5]
  } else {
    stop("Unknown outlier detection method")
  }
  
  list(
    outliers = outliers,
    outlier_indices = which(dat %in% outliers),
    outlier_count = length(outliers)
  )
} 