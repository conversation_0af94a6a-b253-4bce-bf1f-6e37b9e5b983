# Cochran-Mantel-Haenszel Test Server
# Stratified contingency tables

source("modules/calculations/cochran_mantel_haenszel.R")

CochranMantelHaenszelServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactive values
    cmh_data <- reactiveVal(NULL)
    cmh_results <- reactiveVal(NULL)
    
    # File upload reactive
    cmhUploadData <- eventReactive(input$cmhUserData, {
      handle_file_upload(input$cmhUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(cmhUploadData(), {
      data <- cmhUploadData()
      # Clear selectizeInputs and main panel
      updateSelectizeInput(session, 'cmhVariable1', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'cmhVariable2', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'cmhStratumVariable', choices = character(0), selected = NULL, server = TRUE)
      output$cmhResults <- renderUI({ NULL })
      
      # If data is valid, update choices
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'cmhVariable1', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'cmhVariable2', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'cmhStratumVariable', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    cmhValidationErrors <- reactive({
      errors <- c()
      
      if (input$cmhDataMethod == "Manual Entry") {
        # Manual entry validation
        if (is.null(input$cmhManualData) || input$cmhManualData == "") {
          errors <- c(errors, "Manual data entry is required.")
        } else {
          # Basic validation for manual entry
          tryCatch({
            data <- parse_cmh_data(input$cmhManualData)
            if (nrow(data) < 8) {
              errors <- c(errors, "At least 8 observations are required for CMH test.")
            }
            if (ncol(data) < 3) {
              errors <- c(errors, "At least 3 variables (stratum, var1, var2) are required.")
            }
          }, error = function(e) {
            errors <- c(errors, "Invalid manual data format. Please check your input.")
          })
        }
      } else {
        # File upload validation
        data <- cmhUploadData()
        if (is.null(data) || !is.data.frame(data)) {
          errors <- c(errors, "No data uploaded or file could not be read.")
          return(errors)
        }
        if (is.null(input$cmhVariable1) || input$cmhVariable1 == "") {
          errors <- c(errors, "Please select the first variable.")
        }
        if (is.null(input$cmhVariable2) || input$cmhVariable2 == "") {
          errors <- c(errors, "Please select the second variable.")
        }
        if (is.null(input$cmhStratumVariable) || input$cmhStratumVariable == "") {
          errors <- c(errors, "Please select a stratum variable.")
        }
        if (length(na.omit(data[c(input$cmhVariable1, input$cmhVariable2, input$cmhStratumVariable)])) < 8) {
          errors <- c(errors, "At least 8 non-missing observations are required.")
        }
      }
      
      errors
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goCmh, {
      output$cmhResults <- renderUI({
        errors <- cmhValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Cochran-Mantel-Haenszel Test", errors = errors)
        } else {
          tryCatch({
            # Prepare data
            if (input$cmhDataMethod == "Manual Entry") {
              # Parse manual entry data
              data <- parse_cmh_data(input$cmhManualData)
            } else {
              # Use uploaded data
              req(cmhUploadData(), input$cmhVariable1, input$cmhVariable2, input$cmhStratumVariable)
              
              data <- cmhUploadData()
              var1 <- input$cmhVariable1
              var2 <- input$cmhVariable2
              stratum_var <- input$cmhStratumVariable
              
              # Create contingency table
              data <- data[c(stratum_var, var1, var2)]
              names(data) <- c("Stratum", "Var1", "Var2")
              
              # Add count column (assuming each row represents one observation)
              data$Count <- 1
            }
            
            # Remove rows with missing values
            complete_cases <- complete.cases(data)
            if (sum(complete_cases) < nrow(data)) {
              warning("Removing rows with missing values")
              data <- data[complete_cases, ]
            }
            
            # Convert variables to factors
            data$Stratum <- as.factor(data$Stratum)
            data$Var1 <- as.factor(data$Var1)
            data$Var2 <- as.factor(data$Var2)
            
            # Check data structure
            if (length(unique(data$Stratum)) < 2) {
              stop("At least 2 strata are required")
            }
            
            if (length(unique(data$Var1)) < 2) {
              stop("At least 2 levels required for first variable")
            }
            
            if (length(unique(data$Var2)) < 2) {
              stop("At least 2 levels required for second variable")
            }
            
            # Perform Cochran-Mantel-Haenszel test
            results <- perform_cochran_mantel_haenszel(data, 
                                                      odds_ratio = input$cmhOddsRatio,
                                                      homogeneity_test = input$cmhHomogeneityTest,
                                                      effect_size = input$cmhEffectSize,
                                                      conf_level = input$cmhConfLevel)
            
            # Store results
            cmh_results(results)
            
            # Display results
            tagList(
              h3("Cochran-Mantel-Haenszel Test Results"),
              
              # Data summary
              h4("Data Summary"),
              renderTable({
                summary_table <- data.frame(
                  Statistic = c("Total Observations", "Number of Strata", "Missing Values"),
                  Value = c(results$n_observations, results$n_strata, results$n_missing)
                )
                summary_table
              }, rownames = FALSE),
              
              br(),
              
              # Main test results
              h4("Cochran-Mantel-Haenszel Test Results"),
              renderDataTable({
                test_table <- results$test_results
                datatable(test_table, 
                         options = list(pageLength = 10, scrollX = TRUE),
                         rownames = FALSE) %>%
                  formatRound(columns = c("Statistic", "p_value", "Effect_Size"), digits = 4)
              }),
              
              br(),
              
              # Stratum-specific results
              h4("Stratum-Specific Results"),
              renderDataTable({
                stratum_table <- results$stratum_results
                datatable(stratum_table, 
                         options = list(pageLength = 10, scrollX = TRUE),
                         rownames = FALSE) %>%
                  formatRound(columns = c("Odds_Ratio", "SE", "CI_Lower", "CI_Upper", "p_value"), digits = 4)
              }),
              
              br(),
              
              # Overall odds ratio
              if (!is.null(results$overall_odds_ratio)) tagList(
                h4("Overall Odds Ratio"),
                renderTable({
                  or_table <- results$overall_odds_ratio
                  or_table
                }, rownames = FALSE),
                br()
              ),
              
              # Homogeneity test
              if (!is.null(results$homogeneity_test)) tagList(
                h4("Homogeneity Test"),
                renderTable({
                  homogeneity_table <- results$homogeneity_test
                  homogeneity_table
                }, rownames = FALSE),
                br()
              ),
              
              # Contingency tables
              h4("Contingency Tables by Stratum"),
              renderDataTable({
                contingency_table <- results$contingency_tables
                datatable(contingency_table, 
                         options = list(pageLength = 10, scrollX = TRUE),
                         rownames = FALSE)
              }),
              
              br(),
              
              # Visualization
              h4("Visualization"),
              fluidRow(
                column(6,
                  renderPlot({
                    # Odds ratio plot by stratum
                    if (!is.null(results$stratum_results)) {
                      ggplot(results$stratum_results, aes(x = Stratum, y = Odds_Ratio)) +
                        geom_point(size = 3) +
                        geom_errorbar(aes(ymin = CI_Lower, ymax = CI_Upper), width = 0.2) +
                        geom_hline(yintercept = 1, color = "red", linetype = "dashed") +
                        labs(title = "Odds Ratios by Stratum",
                             x = "Stratum", y = "Odds Ratio") +
                        theme_minimal() +
                        theme(axis.text.x = element_text(angle = 45, hjust = 1))
                    }
                  })
                ),
                column(6,
                  renderPlot({
                    # Forest plot of odds ratios
                    if (!is.null(results$stratum_results)) {
                      ggplot(results$stratum_results, aes(x = Odds_Ratio, y = Stratum)) +
                        geom_point(size = 3) +
                        geom_errorbarh(aes(xmin = CI_Lower, xmax = CI_Upper), height = 0.2) +
                        geom_vline(xintercept = 1, color = "red", linetype = "dashed") +
                        labs(title = "Forest Plot of Odds Ratios",
                             x = "Odds Ratio", y = "Stratum") +
                        theme_minimal()
                    }
                  })
                )
              ),
              
              br(),
              
              # Educational content
              h4("Interpretation"),
              p(strong("Cochran-Mantel-Haenszel Test:"), "Tests for association between two categorical variables while controlling for a third variable (stratum)."),
              p(strong("Stratification:"), "Controls for confounding by analyzing the relationship within each stratum separately."),
              p(strong("Odds Ratio:"), "Measures the strength of association between the two variables."),
              p(strong("Homogeneity Test:"), "Tests whether the odds ratios are consistent across strata.")
            )
          }, error = function(e) {
            errorScreenUI(title = "Error in Cochran-Mantel-Haenszel Test", errors = e$message)
          })
        }
      })
    })
    
    # Helper function to parse numeric input
    parse_cmh_data <- function(input_text) {
      if (is.null(input_text) || input_text == "") {
        stop("Please enter data")
      }
      
      # Parse the input text into a data frame
      lines <- strsplit(input_text, "\n")[[1]]
      lines <- lines[lines != ""]
      
      if (length(lines) < 2) {
        stop("At least 2 lines required (header + data)")
      }
      
      # Parse header
      header <- strsplit(lines[1], ",")[[1]]
      header <- trimws(header)
      
      # Parse data
      data_lines <- lines[-1]
      data_matrix <- matrix(NA, nrow = length(data_lines), ncol = length(header))
      
      for (i in 1:length(data_lines)) {
        values <- strsplit(data_lines[i], ",")[[1]]
        values <- trimws(values)
        
        if (length(values) != length(header)) {
          stop(paste("Line", i + 1, "has incorrect number of values"))
        }
        
        data_matrix[i, ] <- values
      }
      
      data.frame(data_matrix, stringsAsFactors = FALSE)
    }
  })
} 