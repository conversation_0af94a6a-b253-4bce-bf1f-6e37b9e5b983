NetworkAnalysisServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    netData <- eventReactive(input$netUserData, {
      handle_file_upload(input$netUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(netData(), {
      data <- netData()
      if (!is.null(data) && is.data.frame(data)) {
        # Update metric choices based on data structure
        updateSelectInput(session, 'netMetric', choices = c("Degree", "Betweenness", "Closeness", "Eigenvector", "Clustering", "Community Detection"))
      }
    })
    
    # Validation errors reactive
    netValidationErrors <- reactive({
      errors <- c()
      data <- netData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (ncol(data) < 2) {
        errors <- c(errors, "Network data must have at least 2 columns (source and target).")
      }
      if (nrow(data) < 3) {
        errors <- c(errors, "Network data must have at least 3 connections.")
      }
      if (is.null(input$netMetric)) {
        errors <- c(errors, "Select a network metric to analyze.")
      }
      errors
    })
    
    # Network analysis reactive
    netResult <- eventReactive(input$goNet, {
      data <- netData()
      req(data, input$netMetric)
      
      # Check if igraph package is available
      if (!requireNamespace("igraph", quietly = TRUE)) {
        stop("Package 'igraph' is required for network analysis.")
      }
      
      # Perform network analysis
      network_analysis(data, input$netMetric)
    })
    
    # Error handling
    output$netError <- renderUI({
      errors <- netValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          netResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "Network Analysis Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$netModelSummary <- renderUI({
      req(netResult())
      res <- netResult()
      
      tagList(
        h4("Network Analysis Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Metric = c("Analysis Type", "Number of Nodes", "Number of Edges", "Network Density", "Average Degree"),
            Value = c(
              input$netMetric,
              res$n_nodes,
              res$n_edges,
              round(res$density, 4),
              round(res$avg_degree, 4)
            )
          )
        }),
        h4("Network Metrics"),
        renderTable({
          res$metrics_summary
        }),
        h4("Centrality Measures"),
        renderTable({
          res$centrality_measures
        })
      )
    })
    
    output$netPlot <- renderPlot({
      req(netResult())
      res <- netResult()
      
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # Network graph
      if (requireNamespace("igraph", quietly = TRUE)) {
        g <- igraph::graph_from_data_frame(netData(), directed = FALSE)
        plot(g, main = paste("Network Graph -", input$netMetric), 
             vertex.size = 8, vertex.label.cex = 0.8)
      }
      
      # Degree distribution
      if (!is.null(res$degree_dist)) {
        hist(res$degree_dist, main = "Degree Distribution", 
             xlab = "Degree", ylab = "Frequency", breaks = 10)
      }
      
      # Centrality plot
      if (!is.null(res$centrality_plot_data)) {
        plot(res$centrality_plot_data$degree, res$centrality_plot_data$betweenness,
             main = "Degree vs Betweenness Centrality",
             xlab = "Degree", ylab = "Betweenness", pch = 19)
      }
      
      # Community structure (if available)
      if (!is.null(res$community_structure)) {
        barplot(table(res$community_structure), main = "Community Sizes",
                xlab = "Community", ylab = "Number of Nodes")
      }
      
      par(mfrow = c(1, 1))
    })
    
    output$netDiagnostics <- renderUI({
      req(netResult())
      res <- netResult()
      
      tagList(
        h4("Network Diagnostics"),
        renderTable({
          data.frame(
            Metric = c("Diameter", "Average Path Length", "Clustering Coefficient", "Modularity"),
            Value = c(
              round(res$diameter, 4),
              round(res$avg_path_length, 4),
              round(res$clustering_coef, 4),
              round(res$modularity, 4)
            )
          )
        })
      )
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$netDataSummary <- renderUI({
      req(netData())
      data <- netData()
      
      tagList(
        h4("Network Data Summary"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "Unique Sources", "Unique Targets", "Total Connections"),
            Value = c(
              nrow(data),
              ncol(data),
              length(unique(data[[1]])),
              length(unique(data[[2]])),
              nrow(data)
            )
          )
        }),
        h4("Connection Statistics"),
        renderTable({
          # Calculate connection statistics
          all_nodes <- unique(c(data[[1]], data[[2]]))
          node_degrees <- table(c(data[[1]], data[[2]]))
          
          data.frame(
            Statistic = c("Total Unique Nodes", "Min Degree", "Max Degree", "Mean Degree", "Median Degree"),
            Value = c(
              length(all_nodes),
              min(node_degrees),
              max(node_degrees),
              round(mean(node_degrees), 2),
              median(node_degrees)
            )
          )
        })
      )
    })
    
    output$netAssumptions <- renderUI({
      req(netResult())
      res <- netResult()
      
      tagList(
        h4("Network Analysis Assumptions"),
        renderTable({
          data.frame(
            Assumption = c("Connected Network", "Undirected Graph", "No Self-loops", "No Multiple Edges"),
            Status = c(
              ifelse(res$is_connected, "Pass", "Fail"),
              "Pass",
              ifelse(res$has_self_loops, "Fail", "Pass"),
              ifelse(res$has_multiple_edges, "Fail", "Pass")
            ),
            Description = c(
              "Network is fully connected",
              "Graph is undirected",
              "No self-connections",
              "No duplicate connections"
            )
          )
        }),
        h4("Network Properties"),
        renderTable({
          data.frame(
            Property = c("Sparseness", "Scale-free", "Small-world", "Modular"),
            Value = c(
              ifelse(res$density < 0.1, "Sparse", "Dense"),
              ifelse(res$scale_free_exponent > 2, "Yes", "No"),
              ifelse(res$small_world_index > 1, "Yes", "No"),
              ifelse(res$modularity > 0.3, "Yes", "No")
            )
          )
        })
      )
    })
    
    output$netDiagnosticPlots <- renderPlot({
      req(netResult())
      res <- netResult()
      
      par(mfrow = c(2, 2))
      
      # Degree distribution (log-log)
      if (!is.null(res$degree_dist)) {
        degree_counts <- table(res$degree_dist)
        plot(as.numeric(names(degree_counts)), as.numeric(degree_counts), 
             log = "xy", main = "Degree Distribution (Log-Log)",
             xlab = "Degree", ylab = "Frequency", pch = 19)
      }
      
      # Clustering coefficient distribution
      if (!is.null(res$clustering_dist)) {
        hist(res$clustering_dist, main = "Clustering Coefficient Distribution",
             xlab = "Clustering Coefficient", ylab = "Frequency")
      }
      
      # Betweenness distribution
      if (!is.null(res$betweenness_dist)) {
        hist(res$betweenness_dist, main = "Betweenness Centrality Distribution",
             xlab = "Betweenness", ylab = "Frequency")
      }
      
      # Network growth (if temporal data available)
      if (!is.null(res$growth_data)) {
        plot(res$growth_data$time, res$growth_data$nodes, 
             type = "l", main = "Network Growth",
             xlab = "Time", ylab = "Number of Nodes")
      }
      
      par(mfrow = c(1, 1))
    })
    
    # Uploaded Data Tab Outputs
    output$netDataTable <- renderDT({
      req(netData())
      data <- netData()
      
      DT::datatable(
        data,
        options = list(
          pageLength = 10,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
        ),
        extensions = 'Buttons',
        filter = 'top',
        rownames = FALSE
      )
    })
    
    output$netDataInfo <- renderUI({
      req(netData())
      data <- netData()
      
      tagList(
        h4("Dataset Information"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "File Name"),
            Value = c(nrow(data), ncol(data), 
                     ifelse(!is.null(input$netUserData), input$netUserData$name, "Unknown"))
          )
        }),
        h4("Column Information"),
        renderTable({
          data.frame(
            Column = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Unique = sapply(data, function(x) length(unique(x)))
          )
        })
      )
    })
  })
} 