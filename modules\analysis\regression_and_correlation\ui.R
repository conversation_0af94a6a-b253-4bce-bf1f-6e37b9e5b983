regressionAndCorrelationUI <- function(id) {
  ns <- NS(id)
  tabsetPanel(
    tabPanel("Simple Linear Regression",
      sidebarLayout(
        sidebarPanel(simpleLinearRegressionSidebarUI(ns("slr"))),
        mainPanel(simpleLinearRegressionMainUI(ns("slr")))
      )
    ),
    tabPanel("Multiple Linear Regression",
      sidebarLayout(
        sidebarPanel(multipleLinearRegressionSidebarUI(ns("mlr"))),
        mainPanel(multipleLinearRegressionMainUI(ns("mlr")))
      )
    ),
    tabPanel("Logistic Regression",
      sidebarLayout(
        sidebarPanel(logisticRegressionSidebarUI(ns("logr"))),
        mainPanel(logisticRegressionMainUI(ns("logr")))
      )
    ),
    tabPanel("Robust Regression",
      sidebarLayout(
        sidebarPanel(RobustRegressionUI(ns("robust"))),
        mainPanel()
      )
    ),
    tabPanel("Generalized Linear Models",
      tabsetPanel(
        tabPanel("Poisson/NegBin Regression",
          sidebarLayout(
            sidebarPanel(PoissonRegressionUI(ns("pois"))),
            mainPanel()
          )
        ),
        tabPanel("Quasi-binomial/Quasi-Poisson",
          sidebarLayout(
            sidebarPanel(QuasiRegressionUI(ns("quasi"))),
            mainPanel()
          )
        ),
        tabPanel("Zero-inflated Models",
          sidebarLayout(
            sidebarPanel(ZeroInflatedUI(ns("zi"))),
            mainPanel()
          )
        )
      )
    ),
    tabPanel("Bayesian Regression",
      tabsetPanel(
        tabPanel("Bayesian Regression/ANOVA",
          sidebarLayout(
            sidebarPanel(BayesianRegressionUI(ns("bayesreg"))),
            mainPanel()
          )
        ),
        tabPanel("Bayesian Model Comparison",
          sidebarLayout(
            sidebarPanel(BayesianModelComparisonUI(ns("bmc"))),
            mainPanel()
          )
        )
      )
    ),
    tabPanel("Diagnostics & Preprocessing",
      tabsetPanel(
        tabPanel("Multiple Imputation (MICE)",
          sidebarLayout(
            sidebarPanel(MultipleImputationUI(ns("mice"))),
            mainPanel()
          )
        ),
        tabPanel("Outlier Detection",
          sidebarLayout(
            sidebarPanel(OutlierDetectionUI(ns("out"))),
            mainPanel()
          )
        ),
        tabPanel("Variable Transformation",
          sidebarLayout(
            sidebarPanel(VariableTransformationUI(ns("vt"))),
            mainPanel()
          )
        ),
        tabPanel("Durbin-Watson Test",
          sidebarLayout(
            sidebarPanel(durbinWatsonSidebarUI(ns("durbin_watson"))),
            mainPanel(durbinWatsonMainUI(ns("durbin_watson")))
          )
        ),
        tabPanel("Breusch-Pagan Test",
          sidebarLayout(
            sidebarPanel(breuschPaganSidebarUI(ns("breusch_pagan"))),
            mainPanel(breuschPaganMainUI(ns("breusch_pagan")))
          )
        )
      )
    ),
    tabPanel("Correlation & Visualization",
      tabsetPanel(
        tabPanel("Correlation Tests",
          sidebarLayout(
            sidebarPanel(CorrelationTestSidebarUI(ns("correlation"))),
            mainPanel(CorrelationTestMainUI(ns("correlation")))
          )
        ),
        tabPanel("Correlation Matrix Heatmaps",
          sidebarLayout(
            sidebarPanel(CorrHeatmapUI(ns("chm"))),
            mainPanel()
          )
        ),
        tabPanel("Pairwise Plot Matrix",
          sidebarLayout(
            sidebarPanel(PairwisePlotMatrixUI(ns("ppm"))),
            mainPanel()
          )
        )
      )
    )
  )
} 