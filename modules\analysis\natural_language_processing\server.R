NaturalLanguageProcessingServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    # Reactive values for storing results
    nlp_results <- reactiveVal(NULL)
    nlp_plots <- reactiveVal(NULL)
    
    # Data upload reactive
    nlpData <- eventReactive(input$nlpUserData, {
      handle_file_upload(input$nlpUserData)
    })

    # Update variable choices when data changes
    observeEvent(nlpData(), {
      data <- nlpData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectInput(session, "nlp_text_column", choices = names(data))
        updateSelectInput(session, "nlp_id_column", choices = c("None" = "none", names(data)))
        updateSelectInput(session, "nlp_group_column", choices = c("None" = "none", names(data)))
      }
    })

    # Run NLP analysis
    observeEvent(input$run_nlp, {
      req(nlpData(), input$nlp_text_column)
      
      tryCatch({
        # Prepare data
        text_data <- nlpData()
        text_column <- input$nlp_text_column
        id_column <- if(input$nlp_id_column != "none") input$nlp_id_column else NULL
        group_column <- if(input$nlp_group_column != "none") input$nlp_group_column else NULL
        
        # Perform analysis based on type
        if (input$nlp_analysis_type == "sentiment") {
          results <- perform_sentiment_analysis(text_data, text_column, id_column, group_column)
        } else if (input$nlp_analysis_type == "topic_modeling") {
          results <- perform_topic_modeling(text_data, text_column, id_column, group_column)
        } else if (input$nlp_analysis_type == "text_summarization") {
          results <- perform_text_summarization(text_data, text_column, id_column, group_column)
        } else if (input$nlp_analysis_type == "named_entity") {
          results <- perform_named_entity_recognition(text_data, text_column, id_column, group_column)
        } else if (input$nlp_analysis_type == "keyword_extraction") {
          results <- perform_keyword_extraction(text_data, text_column, id_column, group_column)
        } else if (input$nlp_analysis_type == "text_classification") {
          results <- perform_text_classification(text_data, text_column, id_column, group_column)
        }
        
        # Store results
        nlp_results(results)
        
        # Generate plots
        generate_nlp_plots(results)
        
      }, error = function(e) {
        showNotification(paste("Error in NLP analysis:", e$message), type = "error")
      })
    })

    # Sentiment Analysis
    perform_sentiment_analysis <- function(data, text_column, id_column, group_column) {
      # Simple sentiment analysis implementation
      texts <- data[[text_column]]
      
      # Basic sentiment scoring (placeholder)
      sentiment_scores <- sapply(texts, function(text) {
        positive_words <- c("good", "great", "excellent", "amazing", "wonderful", "love", "like", "happy")
        negative_words <- c("bad", "terrible", "awful", "hate", "dislike", "sad", "angry", "frustrated")
        
        text_lower <- tolower(text)
        positive_count <- sum(sapply(positive_words, function(word) grepl(word, text_lower)))
        negative_count <- sum(sapply(negative_words, function(word) grepl(word, text_lower)))
        
        score <- (positive_count - negative_count) / max(length(strsplit(text, " ")[[1]]), 1)
        return(score)
      })
      
      # Create results
      results <- data.frame(
        text = texts,
        sentiment_score = sentiment_scores,
        sentiment_label = ifelse(sentiment_scores > 0, "Positive", 
                                ifelse(sentiment_scores < 0, "Negative", "Neutral"))
      )
      
      if (!is.null(id_column)) {
        results$id <- data[[id_column]]
      }
      if (!is.null(group_column)) {
        results$group <- data[[group_column]]
      }
      
      return(list(
        method = "Sentiment Analysis",
        results = results,
        summary = summary(sentiment_scores),
        data = data
      ))
    }

    # Topic Modeling
    perform_topic_modeling <- function(data, text_column, id_column, group_column) {
      # Placeholder for topic modeling
      texts <- data[[text_column]]
      
      # Simple word frequency analysis
      all_words <- unlist(strsplit(paste(texts, collapse = " "), " "))
      word_freq <- table(all_words)
      top_words <- head(sort(word_freq, decreasing = TRUE), 20)
      
      return(list(
        method = "Topic Modeling",
        word_frequencies = top_words,
        n_topics = input$nlp_n_topics,
        data = data
      ))
    }

    # Text Summarization
    perform_text_summarization <- function(data, text_column, id_column, group_column) {
      # Placeholder for text summarization
      texts <- data[[text_column]]
      
      # Simple extractive summarization (first few sentences)
      summaries <- sapply(texts, function(text) {
        sentences <- strsplit(text, "[.!?]")[[1]]
        if (length(sentences) > 3) {
          return(paste(sentences[1:3], collapse = ". "))
        } else {
          return(text)
        }
      })
      
      return(list(
        method = "Text Summarization",
        summaries = summaries,
        original_texts = texts,
        data = data
      ))
    }

    # Named Entity Recognition
    perform_named_entity_recognition <- function(data, text_column, id_column, group_column) {
      # Placeholder for NER
      texts <- data[[text_column]]
      
      # Simple pattern matching for names, places, etc.
      entities <- list()
      for (i in seq_along(texts)) {
        text <- texts[i]
        # Extract capitalized words as potential entities
        words <- strsplit(text, " ")[[1]]
        capitalized <- words[grepl("^[A-Z]", words)]
        entities[[i]] <- capitalized
      }
      
      return(list(
        method = "Named Entity Recognition",
        entities = entities,
        texts = texts,
        data = data
      ))
    }

    # Keyword Extraction
    perform_keyword_extraction <- function(data, text_column, id_column, group_column) {
      # Placeholder for keyword extraction
      texts <- data[[text_column]]
      
      # Simple keyword extraction (words longer than 5 characters)
      keywords <- list()
      for (i in seq_along(texts)) {
        text <- texts[i]
        words <- strsplit(text, " ")[[1]]
        long_words <- words[nchar(words) > 5]
        keywords[[i]] <- unique(long_words)
      }
      
      return(list(
        method = "Keyword Extraction",
        keywords = keywords,
        texts = texts,
        data = data
      ))
    }

    # Text Classification
    perform_text_classification <- function(data, text_column, id_column, group_column) {
      # Placeholder for text classification
      texts <- data[[text_column]]
      
      # Simple rule-based classification
      classifications <- sapply(texts, function(text) {
        text_lower <- tolower(text)
        if (grepl("question|what|how|why|when|where", text_lower)) {
          return("Question")
        } else if (grepl("thank|thanks|appreciate", text_lower)) {
          return("Gratitude")
        } else if (grepl("problem|issue|error|bug", text_lower)) {
          return("Problem")
        } else {
          return("General")
        }
      })
      
      return(list(
        method = "Text Classification",
        classifications = classifications,
        texts = texts,
        data = data
      ))
    }

    # Generate plots
    generate_nlp_plots <- function(results) {
      plots <- list()
      
      if (results$method == "Sentiment Analysis") {
        # Sentiment distribution plot
        p1 <- ggplot(results$results, aes(x = sentiment_label, fill = sentiment_label)) +
          geom_bar() +
          labs(title = "Sentiment Distribution",
               x = "Sentiment", y = "Count") +
          theme_minimal() +
          theme(legend.position = "none")
        plots$sentiment_dist <- p1
        
        # Sentiment score histogram
        p2 <- ggplot(results$results, aes(x = sentiment_score)) +
          geom_histogram(bins = 20, fill = "steelblue", alpha = 0.7) +
          labs(title = "Sentiment Score Distribution",
               x = "Sentiment Score", y = "Frequency") +
          theme_minimal()
        plots$sentiment_hist <- p2
      }
      
      if (results$method == "Topic Modeling") {
        # Word frequency plot
        word_df <- data.frame(
          word = names(results$word_frequencies),
          frequency = as.numeric(results$word_frequencies)
        )
        
        p3 <- ggplot(head(word_df, 15), aes(x = reorder(word, frequency), y = frequency)) +
          geom_bar(stat = "identity", fill = "darkgreen") +
          coord_flip() +
          labs(title = "Most Frequent Words",
               x = "Word", y = "Frequency") +
          theme_minimal()
        plots$word_freq <- p3
      }
      
      nlp_plots(plots)
    }

    # Output summary
    output$nlp_summary <- renderPrint({
      req(nlp_results())
      results <- nlp_results()
      
      cat("NATURAL LANGUAGE PROCESSING SUMMARY\n")
      cat("==================================\n\n")
      cat("Method:", results$method, "\n")
      cat("Analysis Type:", input$nlp_analysis_type, "\n\n")
      
      if (results$method == "Sentiment Analysis") {
        cat("Sentiment Summary:\n")
        print(table(results$results$sentiment_label))
        cat("\nSentiment Score Statistics:\n")
        print(results$summary)
      }
      
      if (results$method == "Topic Modeling") {
        cat("Top Words:\n")
        print(head(results$word_frequencies, 10))
      }
      
      if (results$method == "Text Classification") {
        cat("Classification Results:\n")
        print(table(results$classifications))
      }
    })

    # Output plots
    output$nlp_plots <- renderPlot({
      req(nlp_plots())
      plots <- nlp_plots()
      if (length(plots) == 1) {
        print(plots[[1]])
      } else if (length(plots) > 1) {
        # Arrange multiple plots
        n_plots <- length(plots)
        n_cols <- min(2, n_plots)
        n_rows <- ceiling(n_plots / n_cols)
        do.call(grid.arrange, c(plots, ncol = n_cols))
      }
    })

    # Output results table
    output$nlp_results_table <- renderDataTable({
      req(nlp_results())
      results <- nlp_results()
      
      if (results$method == "Sentiment Analysis") {
        datatable(results$results, options = list(pageLength = 10))
      } else if (results$method == "Text Classification") {
        data.frame(
          Text = results$texts,
          Classification = results$classifications
        )
      } else {
        data.frame(Result = "Analysis completed")
      }
    })

    # Download results
    output$download_nlp_results <- downloadHandler(
      filename = function() {
        paste("nlp_analysis_results_", format(Sys.time(), "%Y%m%d_%H%M%S"), ".txt", sep = "")
      },
      content = function(file) {
        req(nlp_results())
        results <- nlp_results()
        sink(file)
        cat("NATURAL LANGUAGE PROCESSING RESULTS\n")
        cat("==================================\n\n")
        cat("Method:", results$method, "\n")
        cat("Analysis Type:", input$nlp_analysis_type, "\n\n")
        print(results)
        sink()
      }
    )

    # Reset functionality
    observeEvent(input$reset_nlp, {
      nlp_results(NULL)
      nlp_plots(NULL)
      updateSelectInput(session, "nlp_text_column", selected = "")
      updateSelectInput(session, "nlp_id_column", selected = "none")
      updateSelectInput(session, "nlp_group_column", selected = "none")
    })
  })
} 