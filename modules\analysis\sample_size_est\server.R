source('modules/calculations/sample_size_est.R')

sampSizeEstServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Sample size calculation reactive
    sampleSizeResult <- eventReactive(input$goSampleSize, {
      req(input$goSampleSize > 0)
      
      # Get parameters from UI
      type <- input$sampleSizeType
      alpha <- ifelse(is.null(input$sampleSizeAlpha), 0.05, input$sampleSizeAlpha)
      power <- ifelse(is.null(input$sampleSizePower), 0.8, input$sampleSizePower)
      
      # Calculate sample size based on type
      if (type == "One Mean") {
        mu <- ifelse(is.null(input$sampleSizeMu), 0, as.numeric(input$sampleSizeMu))
        sigma <- ifelse(is.null(input$sampleSizeSigma), 1, as.numeric(input$sampleSizeSigma))
        margin <- ifelse(is.null(input$sampleSizeMargin), 0.1, as.numeric(input$sampleSizeMargin))
        sample_size_estimation(type = "one_mean", alpha = alpha, power = power, 
                              mu = mu, sigma = sigma, margin = margin)
      } else if (type == "Two Means") {
        mu1 <- ifelse(is.null(input$sampleSizeMu1), 0, as.numeric(input$sampleSizeMu1))
        mu2 <- ifelse(is.null(input$sampleSizeMu2), 0.5, as.numeric(input$sampleSizeMu2))
        sigma <- ifelse(is.null(input$sampleSizeSigma), 1, as.numeric(input$sampleSizeSigma))
        sample_size_estimation(type = "two_means", alpha = alpha, power = power, 
                              mu1 = mu1, mu2 = mu2, sigma = sigma)
      } else if (type == "One Proportion") {
        p <- ifelse(is.null(input$sampleSizeP), 0.5, as.numeric(input$sampleSizeP))
        margin <- ifelse(is.null(input$sampleSizeMargin), 0.05, as.numeric(input$sampleSizeMargin))
        sample_size_estimation(type = "one_proportion", alpha = alpha, power = power, 
                              p = p, margin = margin)
      } else if (type == "Two Proportions") {
        p1 <- ifelse(is.null(input$sampleSizeP1), 0.5, as.numeric(input$sampleSizeP1))
        p2 <- ifelse(is.null(input$sampleSizeP2), 0.6, as.numeric(input$sampleSizeP2))
        sample_size_estimation(type = "two_proportions", alpha = alpha, power = power, 
                              p1 = p1, p2 = p2)
      } else {
        NULL
      }
    })
    
    # Render UI for sample size estimation-specific inputs
    output$ssInputs <- renderUI({
      switch(input$sampleSizeType,
        "One Mean" = tagList(
          numericInput(ns("sampleSizeAlpha"), "Significance Level (α)", value = 0.05, min = 0.001, max = 0.999, step = 0.001),
          numericInput(ns("sampleSizePower"), "Power (1-β)", value = 0.8, min = 0.1, max = 0.999, step = 0.001),
          numericInput(ns("sampleSizeMu"), "Population Mean (μ)", value = 0),
          numericInput(ns("sampleSizeSigma"), "Population Standard Deviation (σ)", value = 1, min = 0.001),
          numericInput(ns("sampleSizeMargin"), "Margin of Error", value = 0.1, min = 0.001)
        ),
        "Two Means" = tagList(
          numericInput(ns("sampleSizeAlpha"), "Significance Level (α)", value = 0.05, min = 0.001, max = 0.999, step = 0.001),
          numericInput(ns("sampleSizePower"), "Power (1-β)", value = 0.8, min = 0.1, max = 0.999, step = 0.001),
          numericInput(ns("sampleSizeMu1"), "Group 1 Mean (μ₁)", value = 0),
          numericInput(ns("sampleSizeMu2"), "Group 2 Mean (μ₂)", value = 0.5),
          numericInput(ns("sampleSizeSigma"), "Common Standard Deviation (σ)", value = 1, min = 0.001)
        ),
        "One Proportion" = tagList(
          numericInput(ns("sampleSizeAlpha"), "Significance Level (α)", value = 0.05, min = 0.001, max = 0.999, step = 0.001),
          numericInput(ns("sampleSizePower"), "Power (1-β)", value = 0.8, min = 0.1, max = 0.999, step = 0.001),
          numericInput(ns("sampleSizeP"), "Population Proportion (p)", value = 0.5, min = 0.001, max = 0.999, step = 0.001),
          numericInput(ns("sampleSizeMargin"), "Margin of Error", value = 0.05, min = 0.001, max = 0.5)
        ),
        "Two Proportions" = tagList(
          numericInput(ns("sampleSizeAlpha"), "Significance Level (α)", value = 0.05, min = 0.001, max = 0.999, step = 0.001),
          numericInput(ns("sampleSizePower"), "Power (1-β)", value = 0.8, min = 0.1, max = 0.999, step = 0.001),
          numericInput(ns("sampleSizeP1"), "Group 1 Proportion (p₁)", value = 0.5, min = 0.001, max = 0.999, step = 0.001),
          numericInput(ns("sampleSizeP2"), "Group 2 Proportion (p₂)", value = 0.6, min = 0.001, max = 0.999, step = 0.001)
        ),
        NULL
      )
    })
    
    # Render UI for sample size estimation-specific outputs
    output$ssOutputs <- renderUI({
      errors <- sampleSizeValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Error(s) in Sample Size Estimation", errors = errors)
      } else {
        tryCatch({ 
          res <- sampleSizeResult()
          if (is.null(res)) {
            errorScreenUI(title = "Sample Size Estimation Error", errors = "No results available")
          } else {
            tagList(
              tabsetPanel(
                id = ns("sampleSizeTabset"),
                selected = "Analysis",
                tabPanel(
                  id = ns("sampleSizeAnalysis"),
                  title = "Analysis",
                  titlePanel("Sample Size Estimation Results"),
                  br(),
                  h4("Sample Size Summary"),
                  tableOutput(ns('sampleSizeSummary')),
                  br(),
                  h4("Parameters Used"),
                  tableOutput(ns('sampleSizeParameters')),
                  br(),
                  h4("Interpretation"),
                  uiOutput(ns('sampleSizeInterpretation')),
                  br(),
                  h4("Recommendations"),
                  uiOutput(ns('sampleSizeRecommendations'))
                ),
                tabPanel(
                  id = ns("sampleSizeGuidelines"),
                  title = "Guidelines",
                  h4("Sample Size Guidelines"),
                  uiOutput(ns('sampleSizeGuidelines')),
                  br(),
                  h4("Effect Size Guidelines"),
                  uiOutput(ns('sampleSizeEffectSizeGuidelines')),
                  br(),
                  h4("Power Guidelines"),
                  uiOutput(ns('sampleSizePowerGuidelines'))
                ),
                tabPanel(
                  id = ns("sampleSizeExamples"),
                  title = "Examples",
                  h4("Example Calculations"),
                  uiOutput(ns('sampleSizeExamples')),
                  br(),
                  h4("Common Scenarios"),
                  uiOutput(ns('sampleSizeCommonScenarios'))
                )
              )
            )
          }
        }, error = function(e) {
          errorScreenUI(title = "Sample Size Estimation Error", errors = e$message)
        })
      }
    })
    
    # Validation errors reactive
    sampleSizeValidationErrors <- reactive({
      errors <- c()
      
      # Common validation
      if (is.null(input$sampleSizeAlpha) || input$sampleSizeAlpha <= 0 || input$sampleSizeAlpha >= 1) {
        errors <- c(errors, "Significance level must be between 0 and 1.")
      }
      if (is.null(input$sampleSizePower) || input$sampleSizePower <= 0 || input$sampleSizePower >= 1) {
        errors <- c(errors, "Power must be between 0 and 1.")
      }
      
      # Type-specific validation
      if (input$sampleSizeType == "One Mean") {
        if (is.null(input$sampleSizeSigma) || input$sampleSizeSigma <= 0) {
          errors <- c(errors, "Standard deviation must be positive.")
        }
        if (is.null(input$sampleSizeMargin) || input$sampleSizeMargin <= 0) {
          errors <- c(errors, "Margin of error must be positive.")
        }
      } else if (input$sampleSizeType == "Two Means") {
        if (is.null(input$sampleSizeSigma) || input$sampleSizeSigma <= 0) {
          errors <- c(errors, "Standard deviation must be positive.")
        }
      } else if (input$sampleSizeType == "One Proportion") {
        if (is.null(input$sampleSizeP) || input$sampleSizeP <= 0 || input$sampleSizeP >= 1) {
          errors <- c(errors, "Proportion must be between 0 and 1.")
        }
        if (is.null(input$sampleSizeMargin) || input$sampleSizeMargin <= 0 || input$sampleSizeMargin >= 1) {
          errors <- c(errors, "Margin of error must be between 0 and 1.")
        }
      } else if (input$sampleSizeType == "Two Proportions") {
        if (is.null(input$sampleSizeP1) || input$sampleSizeP1 <= 0 || input$sampleSizeP1 >= 1) {
          errors <- c(errors, "Proportion 1 must be between 0 and 1.")
        }
        if (is.null(input$sampleSizeP2) || input$sampleSizeP2 <= 0 || input$sampleSizeP2 >= 1) {
          errors <- c(errors, "Proportion 2 must be between 0 and 1.")
        }
      }
      
      errors
    })
    
    # Additional outputs for the enhanced tabs
    output$sampleSizeSummary <- renderTable({
      res <- sampleSizeResult()
      if (is.null(res)) return(NULL)
      
      data.frame(
        Parameter = c("Required Sample Size", "Power", "Significance Level", "Effect Size"),
        Value = c(res$sample_size, res$power, res$alpha, res$effect_size),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    output$sampleSizeParameters <- renderTable({
      res <- sampleSizeResult()
      if (is.null(res)) return(NULL)
      
      # Create parameter table based on type
      if (res$type == "one_mean") {
        data.frame(
          Parameter = c("Type", "Mean", "Standard Deviation", "Margin of Error"),
          Value = c(res$type, res$mu, res$sigma, res$margin),
          stringsAsFactors = FALSE
        )
      } else if (res$type == "two_means") {
        data.frame(
          Parameter = c("Type", "Mean 1", "Mean 2", "Standard Deviation"),
          Value = c(res$type, res$mu1, res$mu2, res$sigma),
          stringsAsFactors = FALSE
        )
      } else if (res$type == "one_proportion") {
        data.frame(
          Parameter = c("Type", "Proportion", "Margin of Error"),
          Value = c(res$type, res$p, res$margin),
          stringsAsFactors = FALSE
        )
      } else if (res$type == "two_proportions") {
        data.frame(
          Parameter = c("Type", "Proportion 1", "Proportion 2"),
          Value = c(res$type, res$p1, res$p2),
          stringsAsFactors = FALSE
        )
      }
    }, digits = 4)
    
    output$sampleSizeInterpretation <- renderUI({
      res <- sampleSizeResult()
      if (is.null(res)) return(NULL)
      
      tagList(
        p("Required sample size: ", round(res$sample_size, 0)),
        p("This sample size will provide ", round(res$power * 100, 1), "% power to detect the specified effect size."),
        p("The significance level is set at α = ", res$alpha),
        br(),
        p("Note: This is the minimum sample size required. Consider adding 10-20% for potential data loss.")
      )
    })
    
    output$sampleSizeRecommendations <- renderUI({
      tagList(
        h5("Sample Size Recommendations:"),
        p("• Add 10-20% to account for missing data"),
        p("• Consider practical constraints (time, cost, availability)"),
        p("• Ensure adequate power for your research question"),
        p("• Balance statistical requirements with practical feasibility"),
        br(),
        p("Note: These recommendations are general guidelines.")
      )
    })
    
    output$sampleSizeGuidelines <- renderUI({
      tagList(
        h5("Sample Size Guidelines:"),
        p("• Larger sample sizes increase power and precision"),
        p("• Smaller effect sizes require larger samples"),
        p("• Higher power requires larger samples"),
        p("• Lower significance levels require larger samples"),
        p("• Consider practical constraints when planning studies"),
        br(),
        p("Note: Use power analysis to determine optimal sample size.")
      )
    })
    
    output$sampleSizeEffectSizeGuidelines <- renderUI({
      tagList(
        h5("Effect Size Guidelines:"),
        p("For means (Cohen's d):"),
        p("• 0.2: Small effect"),
        p("• 0.5: Medium effect"),
        p("• 0.8: Large effect"),
        br(),
        p("For proportions:"),
        p("• 0.05: Small difference"),
        p("• 0.1: Medium difference"),
        p("• 0.2: Large difference")
      )
    })
    
    output$sampleSizePowerGuidelines <- renderUI({
      tagList(
        h5("Power Guidelines:"),
        p("• 0.8 (80%): Standard power level"),
        p("• 0.9 (90%): High power for important studies"),
        p("• 0.95 (95%): Very high power for critical studies"),
        p("• Below 0.5: Insufficient power"),
        br(),
        p("Note: Higher power requires larger sample sizes.")
      )
    })
    
    output$sampleSizeExamples <- renderUI({
      tagList(
        h5("Example Calculations:"),
        p("One Mean: Estimating population mean with specified precision"),
        p("Two Means: Comparing means between two groups"),
        p("One Proportion: Estimating population proportion"),
        p("Two Proportions: Comparing proportions between groups"),
        br(),
        p("Each calculation considers power, significance level, and effect size.")
      )
    })
    
    output$sampleSizeCommonScenarios <- renderUI({
      tagList(
        h5("Common Research Scenarios:"),
        p("• Clinical trials: Comparing treatment effects"),
        p("• Survey research: Estimating population parameters"),
        p("• Experimental studies: Testing hypotheses"),
        p("• Observational studies: Detecting associations"),
        br(),
        p("Choose the appropriate calculation based on your research design.")
      )
    })
  })
} 