SpectralAnalysisServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    specData <- eventReactive(input$specUserData, {
      handle_file_upload(input$specUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(specData(), {
      data <- specData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'specSeries', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    specValidationErrors <- reactive({
      errors <- c()
      data <- specData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$specSeries)) {
        errors <- c(errors, "Select a time series variable.")
      }
      if (!is.null(input$specSeries) && !is.numeric(data[[input$specSeries]])) {
        errors <- c(errors, "Time series variable must be numeric.")
      }
      if (!is.null(input$specSeries) && length(na.omit(data[[input$specSeries]])) < 50) {
        errors <- c(errors, "Time series must have at least 50 observations.")
      }
      errors
    })
    
    # Spectral analysis reactive
    specResult <- eventReactive(input$goSpec, {
      data <- specData()
      req(data, input$specSeries)
      
      # Remove missing values
      clean_data <- na.omit(data[[input$specSeries]])
      if (length(clean_data) < 50) {
        stop("Insufficient data after removing missing values.")
      }
      
      # Create time series object
      ts_data <- ts(clean_data)
      
      # Perform spectral analysis
      spectral_analysis(ts_data)
    })
    
    # Error handling
    output$specError <- renderUI({
      errors <- specValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          specResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "Spectral Analysis Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$specModelSummary <- renderUI({
      req(specResult())
      res <- specResult()
      
      tagList(
        h4("Spectral Analysis Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Metric = c("Analysis Method", "Observations", "Frequency Range", "Dominant Frequency", "Peak Power"),
            Value = c(
              "Periodogram Analysis",
              length(res$data),
              paste(round(min(res$frequencies), 4), "to", round(max(res$frequencies), 4)),
              round(res$dominant_freq, 4),
              round(res$peak_power, 4)
            )
          )
        }),
        h4("Frequency Analysis"),
        renderTable({
          res$frequency_peaks
        }),
        h4("Power Spectrum Statistics"),
        renderTable({
          data.frame(
            Metric = c("Mean Power", "Median Power", "Total Power", "Variance"),
            Value = c(
              round(mean(res$power_spectrum), 4),
              round(median(res$power_spectrum), 4),
              round(sum(res$power_spectrum), 4),
              round(var(res$power_spectrum), 4)
            )
          )
        })
      )
    })
    
    output$specPlot <- renderPlot({
      req(specResult())
      res <- specResult()
      
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # Power spectrum
      plot(res$frequencies, res$power_spectrum, type = "l", 
           main = "Power Spectral Density", 
           xlab = "Frequency", ylab = "Power")
      abline(v = res$dominant_freq, col = "red", lty = 2)
      
      # Periodogram
      plot(res$frequencies, res$periodogram, type = "l", 
           main = "Periodogram", 
           xlab = "Frequency", ylab = "Periodogram")
      
      # Cumulative periodogram
      plot(res$frequencies, cumsum(res$power_spectrum)/sum(res$power_spectrum), 
           type = "l", main = "Cumulative Power Spectrum", 
           xlab = "Frequency", ylab = "Cumulative Power")
      abline(h = 0.5, col = "red", lty = 2)
      
      # Log power spectrum
      plot(res$frequencies, log(res$power_spectrum + 1), type = "l", 
           main = "Log Power Spectrum", 
           xlab = "Frequency", ylab = "Log Power")
      
      par(mfrow = c(1, 1))
    })
    
    output$specDiagnostics <- renderUI({
      req(specResult())
      res <- specResult()
      
      tagList(
        h4("Spectral Diagnostics"),
        renderTable({
          data.frame(
            Metric = c("Nyquist Frequency", "Frequency Resolution", "Bandwidth", "Spectral Leakage"),
            Value = c(
              round(res$nyquist_freq, 4),
              round(res$freq_resolution, 4),
              round(res$bandwidth, 4),
              ifelse(res$spectral_leakage, "Present", "Minimal")
            )
          )
        })
      )
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$specDataSummary <- renderUI({
      req(specData(), input$specSeries)
      data <- specData()
      series_data <- data[[input$specSeries]]
      
      tagList(
        h4("Time Series Summary"),
        renderTable({
          summary_stats <- summary(series_data)
          data.frame(
            Statistic = names(summary_stats),
            Value = as.numeric(summary_stats)
          )
        }),
        h4("Missing Values"),
        renderTable({
          missing_count <- sum(is.na(series_data))
          total_count <- length(series_data)
          data.frame(
            Metric = c("Total Observations", "Missing Values", "Complete Cases", "Missing Percentage"),
            Value = c(total_count, missing_count, total_count - missing_count, 
                     round(missing_count/total_count * 100, 2))
          )
        })
      )
    })
    
    output$specAssumptions <- renderUI({
      req(specResult())
      res <- specResult()
      
      tagList(
        h4("Spectral Analysis Assumptions"),
        renderTable({
          # Stationarity test
          adf_test <- adf.test(res$data, alternative = "stationary")
          # Normality test
          norm_test <- shapiro.test(res$data)
          
          data.frame(
            Assumption = c("Stationarity (ADF Test)", "Normality (Shapiro-Wilk)"),
            Test_Statistic = c(round(adf_test$statistic, 4), round(norm_test$statistic, 4)),
            P_Value = c(round(adf_test$p.value, 4), round(norm_test$p.value, 4)),
            Decision = c(
              ifelse(adf_test$p.value < 0.05, "Pass", "Fail"),
              ifelse(norm_test$p.value > 0.05, "Pass", "Fail")
            )
          )
        }),
        h4("Time Series Characteristics"),
        renderTable({
          data.frame(
            Metric = c("Mean", "Standard Deviation", "Skewness", "Kurtosis", "Autocorrelation"),
            Value = c(
              round(mean(res$data, na.rm = TRUE), 4),
              round(sd(res$data, na.rm = TRUE), 4),
              round(skewness(res$data, na.rm = TRUE), 4),
              round(kurtosis(res$data, na.rm = TRUE), 4),
              round(acf(res$data, plot = FALSE)$acf[2], 4)
            )
          )
        })
      )
    })
    
    output$specDiagnosticPlots <- renderPlot({
      req(specResult())
      res <- specResult()
      
      par(mfrow = c(2, 2))
      
      # Time series plot
      plot(res$data, main = "Original Time Series", 
           ylab = "Value", xlab = "Time", type = "l")
      
      # ACF plot
      acf(res$data, main = "Autocorrelation Function", 
          lag.max = min(20, length(res$data) %/% 4))
      
      # Histogram of time series
      hist(res$data, main = "Histogram of Time Series", 
           xlab = "Value", freq = FALSE)
      curve(dnorm(x, mean = mean(res$data, na.rm = TRUE), 
                  sd = sd(res$data, na.rm = TRUE)), 
            add = TRUE, col = "red")
      
      # Q-Q plot
      qqnorm(res$data, main = "Q-Q Plot")
      qqline(res$data, col = "red")
      
      par(mfrow = c(1, 1))
    })
    
    # Uploaded Data Tab Outputs
    output$specDataTable <- renderDT({
      req(specData())
      data <- specData()
      
      DT::datatable(
        data,
        options = list(
          pageLength = 10,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
        ),
        extensions = 'Buttons',
        filter = 'top',
        rownames = FALSE
      )
    })
    
    output$specDataInfo <- renderUI({
      req(specData())
      data <- specData()
      
      tagList(
        h4("Dataset Information"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "File Name"),
            Value = c(nrow(data), ncol(data), 
                     ifelse(!is.null(input$specUserData), input$specUserData$name, "Unknown"))
          )
        }),
        h4("Variable Information"),
        renderTable({
          data.frame(
            Variable = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Unique = sapply(data, function(x) length(unique(x)))
          )
        })
      )
    })
  })
} 