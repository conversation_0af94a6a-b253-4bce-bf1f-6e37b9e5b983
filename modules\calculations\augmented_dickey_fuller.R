# Augmented Dickey-Fuller Test calculation and output helpers

augmented_dickey_fuller_uploadData_func <- function(adfUserData) {
  ext <- tools::file_ext(adfUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(adfUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(adfUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(adfUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(adfUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

augmented_dickey_fuller_results_func <- function(data, var, type = "drift", lag = 1) {
  tryCatch({
    if (!requireNamespace("urca", quietly = TRUE)) {
      stop("Package 'urca' needed for Augmented Dickey-Fuller test.")
    }
    
    x <- data[[var]]
    x <- x[!is.na(x)]
    
    if (length(x) < 4) {
      stop("At least 4 observations are required.")
    }
    
    test_result <- urca::ur.df(x, type = type, lags = lag)
    
    list(
      test = test_result,
      data = x,
      var = var,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Augmented Dickey-Fuller test calculation:", e$message))
  })
}

augmented_dickey_fuller_ht_html <- function(results, sigLvl) {
  test_stat <- results$test@teststat[1]
  crit_val <- results$test@cval[1, "5pct"]
  
  conclusion <- if (test_stat < crit_val) "stationary" else "non-stationary"
  
  withMathJax(tagList(
    h4("Augmented Dickey-Fuller Test"),
    p(sprintf("The time series is likely %s.", conclusion)),
    p(sprintf("Test Statistic: %.4f", test_stat)),
    p(sprintf("5%% Critical Value: %.4f", crit_val))
  ))
}

augmented_dickey_fuller_summary_html <- function(results) {
  tagList(
    h4("Test Summary"),
    renderPrint(summary(results$test))
  )
}

augmented_dickey_fuller_plot <- function(results) {
  plot_data <- data.frame(
    Time = 1:length(results$data),
    Value = results$data
  )
  
  ggplot(plot_data, aes(x = Time, y = Value)) +
    geom_line(alpha = 0.7) +
    labs(title = "Time Series Plot",
         x = "Time",
         y = results$var) +
    theme_minimal()
}