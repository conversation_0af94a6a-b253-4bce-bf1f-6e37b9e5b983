# UI Wrapper Function
ensembleMethodsUI <- function(id) {
  ns <- NS(id)
  
  sidebarLayout(
    sidebarPanel(
      selectInput(ns("ensemble_response"), "Response Variable:", choices = NULL),
      selectInput(ns("ensemble_predictors"), "Predictor Variables:", choices = NULL, multiple = TRUE),
      selectInput(ns("ensemble_test_split"), "Test Split Ratio:", 
                 choices = c("70/30" = 0.7, "80/20" = 0.8, "90/10" = 0.9), selected = 0.8),
      checkboxInput(ns("ensemble_cross_validation"), "Use Cross-Validation", value = TRUE),
      conditionalPanel(
        condition = paste0("input['", ns("ensemble_cross_validation"), "'] == true"),
        numericInput(ns("ensemble_cv_folds"), "Number of CV Folds:", value = 5, min = 2, max = 10, step = 1)
      ),
      selectInput(ns("ensemble_method"), "Ensemble Method:", 
                 choices = c("Random Forest" = "random_forest",
                            "Gradient Boosting" = "gradient_boosting",
                            "XGBoost" = "xgboost",
                            "LightGBM" = "lightgbm",
                            "Stacking" = "stacking",
                            "Bagging" = "bagging",
                            "Voting" = "voting")),
      conditionalPanel(
        condition = paste0("input['", ns("ensemble_method"), "'] == 'random_forest'"),
        numericInput(ns("ensemble_rf_ntree"), "Number of Trees:", value = 500, min = 100, max = 2000, step = 100),
        numericInput(ns("ensemble_rf_mtry"), "Variables per Split:", value = 3, min = 1, max = 10, step = 1),
        numericInput(ns("ensemble_rf_nodesize"), "Min Node Size:", value = 5, min = 1, max = 20, step = 1)
      ),
      conditionalPanel(
        condition = paste0("input['", ns("ensemble_method"), "'] == 'gradient_boosting'"),
        numericInput(ns("ensemble_gb_ntrees"), "Number of Trees:", value = 100, min = 50, max = 1000, step = 50),
        numericInput(ns("ensemble_gb_depth"), "Max Tree Depth:", value = 3, min = 1, max = 10, step = 1),
        numericInput(ns("ensemble_gb_shrinkage"), "Learning Rate:", value = 0.1, min = 0.01, max = 0.3, step = 0.01)
      ),
      conditionalPanel(
        condition = paste0("input['", ns("ensemble_method"), "'] == 'xgboost'"),
        numericInput(ns("ensemble_xgb_ntrees"), "Number of Trees:", value = 100, min = 50, max = 1000, step = 50),
        numericInput(ns("ensemble_xgb_depth"), "Max Tree Depth:", value = 3, min = 1, max = 10, step = 1),
        numericInput(ns("ensemble_xgb_eta"), "Learning Rate:", value = 0.1, min = 0.01, max = 0.3, step = 0.01),
        numericInput(ns("ensemble_xgb_subsample"), "Subsample Ratio:", value = 0.8, min = 0.5, max = 1.0, step = 0.1)
      ),
      conditionalPanel(
        condition = paste0("input['", ns("ensemble_method"), "'] == 'stacking'"),
        selectInput(ns("ensemble_stack_models"), "Base Models:", 
                   choices = c("Linear Regression", "Random Forest", "SVM", "Neural Network"),
                   multiple = TRUE, selected = c("Linear Regression", "Random Forest")),
        selectInput(ns("ensemble_stack_meta"), "Meta-Learner:", 
                   choices = c("Linear Regression" = "lm", "Ridge Regression" = "ridge", "Lasso" = "lasso"))
      ),
      selectInput(ns("ensemble_task"), "Task Type:", 
                 choices = c("Regression" = "regression", "Classification" = "classification")),
      conditionalPanel(
        condition = paste0("input['", ns("ensemble_task"), "'] == 'classification'"),
        selectInput(ns("ensemble_class_method"), "Classification Method:", 
                   choices = c("Random Forest" = "rf", "Gradient Boosting" = "gbm", "XGBoost" = "xgb"))
      ),
      checkboxInput(ns("ensemble_feature_importance"), "Calculate Feature Importance", value = TRUE),
      checkboxInput(ns("ensemble_partial_dependence"), "Partial Dependence Plots", value = FALSE),
      checkboxInput(ns("ensemble_hyperparameter_tuning"), "Hyperparameter Tuning", value = FALSE),
      conditionalPanel(
        condition = paste0("input['", ns("ensemble_hyperparameter_tuning"), "'] == true"),
        numericInput(ns("ensemble_tune_iterations"), "Tuning Iterations:", value = 20, min = 10, max = 100, step = 10),
        selectInput(ns("ensemble_tune_method"), "Tuning Method:", 
                   choices = c("Grid Search" = "grid", "Random Search" = "random", "Bayesian" = "bayesian"))
      ),
      numericInput(ns("ensemble_seed"), "Random Seed:", value = 123, min = 1, max = 9999, step = 1),
      checkboxInput(ns("ensemble_parallel"), "Use Parallel Processing", value = TRUE),
      numericInput(ns("ensemble_cores"), "Number of Cores:", value = 2, min = 1, max = 8, step = 1),
      actionButton(ns("run_ensemble"), "Run Ensemble Model", class = "btn-primary btn-lg"),
      actionButton(ns("reset_ensemble"), "Reset", class = "btn-secondary"),
      downloadButton(ns("download_ensemble_results"), "Download Results")
    ),
    mainPanel(
      tabsetPanel(
        tabPanel("Model Summary",
          verbatimTextOutput(ns("ensemble_summary")),
          plotOutput(ns("ensemble_performance_plot"), height = "400px")
        ),
        tabPanel("Feature Importance",
          plotOutput(ns("ensemble_importance_plot"), height = "500px"),
          dataTableOutput(ns("ensemble_importance_table"))
        ),
        tabPanel("Predictions",
          plotOutput(ns("ensemble_prediction_plot"), height = "400px"),
          dataTableOutput(ns("ensemble_prediction_table"))
        ),
        tabPanel("Diagnostics",
          plotOutput(ns("ensemble_diagnostic_plots"), height = "600px"),
          verbatimTextOutput(ns("ensemble_diagnostics"))
        ),
        tabPanel("Model Comparison",
          plotOutput(ns("ensemble_comparison_plot"), height = "500px"),
          dataTableOutput(ns("ensemble_comparison_table"))
        )
      )
    )
  )
} 