ClusterAnalysisServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    clustData <- eventReactive(input$clustUserData, {
      handle_file_upload(input$clustUserData)
    })
    
    observeEvent(clustData(), {
      data <- clustData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'clustVars', choices = names(data), server = TRUE)
      }
    })
    
    clustValidationErrors <- reactive({
      errors <- c()
      data <- clustData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$clustVars) || length(input$clustVars) < 2) {
        errors <- c(errors, "Select at least two variables for clustering.")
      }
      for (var in input$clustVars) {
        if (!is.numeric(data[[var]])) {
          errors <- c(errors, sprintf("Variable '%s' must be numeric.", var))
        }
      }
      errors
    })
    
    clustResult <- eventReactive(input$goClust, {
      data <- clustData()
      req(data, input$clustVars)
      
      # Get parameters from UI
      method <- ifelse(is.null(input$clustMethod), "kmeans", input$clustMethod)
      k <- ifelse(is.null(input$clustK), 3, input$clustK)
      
      cluster_analysis(data, input$clustVars, method = method, k = k)
    })
    
    output$clustError <- renderUI({
      tryCatch({ clustResult(); NULL }, error = function(e) errorScreenUI(title = "Cluster Analysis Error", errors = e$message))
    })
    
    # Show main results or error screen in main panel when Calculate is pressed
    observeEvent(input$goClust, {
      output$clustResultsUI <- renderUI({
        errors <- clustValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Cluster Analysis", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("clustTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("clustAnalysis"),
                title = "Analysis",
                titlePanel("Cluster Analysis Results"),
                br(),
                h4("Cluster Summary"),
                tableOutput(ns('clustResults')),
                br(),
                h4("Cluster Centers"),
                tableOutput(ns('clustCenters')),
                br(),
                h4("Overall Statistics"),
                tableOutput(ns('clustOverallStats')),
                br(),
                h4("Silhouette Score"),
                textOutput(ns('clustSilhouette')),
                h4("Calinski-Harabasz Index"),
                textOutput(ns('clustCHIndex')),
                h4("Method Used"),
                textOutput(ns('clustMethod')),
                br(),
                h4("Cluster Plot"),
                plotOutput(ns('clustPlot'), height = "400px"),
                br(),
                conditionalPanel(
                  condition = sprintf("input['%s'] == 'hierarchical'", ns('clustMethod')),
                  h4("Dendrogram"),
                  plotOutput(ns('clustDendrogram'), height = "400px")
                ),
                conditionalPanel(
                  condition = sprintf("input['%s'] == 'kmeans'", ns('clustMethod')),
                  h4("Elbow Plot (WSS)"),
                  plotOutput(ns('clustWSSPlot'), height = "400px")
                ),
                br(),
                h4("Data Summary"),
                textOutput(ns('clustDataSummary'))
              ),
              tabPanel(
                id = ns("clustAssignments"),
                title = "Cluster Assignments",
                h4("Data with Cluster Labels"),
                DT::DTOutput(ns('clustAssignmentTable'))
              ),
              tabPanel(
                id = ns("clustUploadedData"),
                title = "Uploaded Data",
                h4("Raw Uploaded Data"),
                DT::DTOutput(ns('clustRawDataTable'))
              )
            )
          )
        }
      })
    })
    
    # Cluster summary table
    output$clustResults <- renderTable({
      res <- clustResult()
      if (is.null(res)) return(NULL)
      res$summary
    }, rownames = FALSE, digits = 4)
    
    # Overall statistics
    output$clustOverallStats <- renderTable({
      res <- clustResult()
      if (is.null(res)) return(NULL)
      res$overall_stats
    }, rownames = FALSE, digits = 4)
    
    # Cluster centers
    output$clustCenters <- renderTable({
      res <- clustResult()
      if (is.null(res)) return(NULL)
      
      centers_df <- as.data.frame(res$centers)
      centers_df$Cluster <- rownames(centers_df)
      centers_df <- centers_df[, c("Cluster", names(centers_df)[-ncol(centers_df)])]
      centers_df
    }, rownames = FALSE, digits = 4)
    
    # Silhouette score
    output$clustSilhouette <- renderText({
      res <- clustResult()
      if (is.null(res) || is.na(res$silhouette_score)) return("Silhouette score not available (cluster package required)")
      paste("Average Silhouette Score:", round(res$silhouette_score, 4))
    })
    
    # Calinski-Harabasz index
    output$clustCHIndex <- renderText({
      res <- clustResult()
      if (is.null(res) || is.na(res$ch_index)) return("Calinski-Harabasz index not available (fpc package required)")
      paste("Calinski-Harabasz Index:", round(res$ch_index, 4))
    })
    
    # Method used
    output$clustMethod <- renderText({
      res <- clustResult()
      if (is.null(res)) return(NULL)
      paste("Clustering method used:", res$method)
    })
    
    # Cluster plot
    output$clustPlot <- renderPlot({
      res <- clustResult()
      if (is.null(res)) return(NULL)
      
      # Get original data
      data <- clustData()
      cluster_data <- data[, input$clustVars, drop = FALSE]
      
      # Add cluster assignments
      cluster_data$Cluster <- as.factor(res$clusters)
      
      # Create plot using first two variables
      if (ncol(cluster_data) >= 3) {
        var1 <- names(cluster_data)[1]
        var2 <- names(cluster_data)[2]
        
        ggplot2::ggplot(cluster_data, ggplot2::aes_string(x = var1, y = var2, color = "Cluster")) +
          ggplot2::geom_point(alpha = 0.7, size = 2) +
          ggplot2::stat_ellipse(ggplot2::aes(color = Cluster), type = "norm") +
          ggplot2::labs(title = paste("Cluster Plot -", res$method, "Method"),
                       x = var1, y = var2) +
          ggplot2::theme_minimal() +
          ggplot2::scale_color_discrete(name = "Cluster")
      } else {
        # If only one variable, create histogram
        ggplot2::ggplot(cluster_data, ggplot2::aes_string(x = names(cluster_data)[1], fill = "Cluster")) +
          ggplot2::geom_histogram(position = "identity", alpha = 0.7, bins = 30) +
          ggplot2::labs(title = paste("Cluster Distribution -", res$method, "Method"),
                       x = names(cluster_data)[1], y = "Count") +
          ggplot2::theme_minimal() +
          ggplot2::scale_fill_discrete(name = "Cluster")
      }
    })
    
    # Dendrogram (for hierarchical clustering)
    output$clustDendrogram <- renderPlot({
      res <- clustResult()
      if (is.null(res) || res$method != "hierarchical") return(NULL)
      
      # Plot dendrogram
      plot(res$fit, main = "Hierarchical Clustering Dendrogram", 
           xlab = "Observations", ylab = "Distance")
      abline(h = res$fit$height[length(res$fit$height) - input$clustK + 1], 
             col = "red", lty = 2)
    })
    
    # Within-cluster sum of squares plot (for k-means)
    output$clustWSSPlot <- renderPlot({
      res <- clustResult()
      if (is.null(res) || res$method != "kmeans") return(NULL)
      
      # Calculate WSS for different k values
      data <- clustData()
      cluster_data <- scale(data[, input$clustVars, drop = FALSE])
      
      wss_values <- numeric(10)
      for (k in 1:10) {
        set.seed(123)
        kmeans_result <- kmeans(cluster_data, centers = k, nstart = 25)
        wss_values[k] <- kmeans_result$tot.withinss
      }
      
      wss_data <- data.frame(k = 1:10, WSS = wss_values)
      
      ggplot2::ggplot(wss_data, ggplot2::aes(x = k, y = WSS)) +
        ggplot2::geom_line() +
        ggplot2::geom_point() +
        ggplot2::geom_vline(xintercept = input$clustK, color = "red", linetype = "dashed") +
        ggplot2::labs(title = "Elbow Plot - Within-Cluster Sum of Squares",
                     x = "Number of Clusters (k)", y = "Within-Cluster Sum of Squares") +
        ggplot2::theme_minimal()
    })
    
    # Data summary
    output$clustDataSummary <- renderText({
      res <- clustResult()
      if (is.null(res)) return(NULL)
      paste("Analysis performed on", res$n_observations, "observations and", length(input$clustVars), "variables.")
    })
    
    # Additional outputs for the enhanced tabs
    output$clustAssignmentTable <- DT::renderDT({
      res <- clustResult()
      data <- clustData()
      if (is.null(res) || is.null(data)) return(NULL)
      cluster_data <- data[, input$clustVars, drop = FALSE]
      cluster_data$Cluster <- as.factor(res$clusters)
      DT::datatable(cluster_data, options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), rownames = FALSE)
    })
    output$clustRawDataTable <- DT::renderDT({
      req(clustData())
      DT::datatable(clustData(), options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), rownames = FALSE)
    })
  })
} 