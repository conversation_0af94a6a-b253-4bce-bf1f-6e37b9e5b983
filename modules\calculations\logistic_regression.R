# Logistic Regression calculation and output helpers

logistic_regression_uploadData_func <- function(lrUserData) {
  ext <- tools::file_ext(lrUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(lrUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(lrUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(lrUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(lrUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

logistic_regression_results_func <- function(data, response_var, predictor_vars) {
  tryCatch({
    formula_str <- paste0("`", response_var, "` ~ ", paste0("`", predictor_vars, "`", collapse = " + "))
    model <- glm(as.formula(formula_str), data = data, family = binomial(link = "logit"))
    
    list(
      model = model,
      data = data,
      response_var = response_var,
      predictor_vars = predictor_vars,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Logistic Regression calculation:", e$message))
  })
}

logistic_regression_ht_html <- function(results) {
  # No single hypothesis test, summary is more informative
  tagList(
    h4("Logistic Regression Model"),
    p("See summary table for model coefficients and diagnostics.")
  )
}

logistic_regression_summary_html <- function(results) {
  model_summary <- summary(results$model)
  odds_ratios <- exp(coef(results$model))
  
  coef_df <- as.data.frame(model_summary$coefficients)
  coef_df$`Odds Ratio` <- odds_ratios
  
  tagList(
    h4("Coefficients"),
    renderTable(coef_df, digits = 4),
    h4("Model Summary"),
    renderPrint(model_summary)
  )
}

logistic_regression_plot <- function(results) {
  if (length(results$predictor_vars) == 1) {
    plot_data <- results$data
    plot_data$prob <- predict(results$model, type = "response")
    
    ggplot(plot_data, aes_string(x = results$predictor_vars[1], y = results$response_var)) +
      geom_point(alpha = 0.5) +
      geom_line(aes(y = prob), color = "blue") +
      labs(title = "Logistic Regression Curve",
           y = "Probability") +
      theme_minimal()
  }
}