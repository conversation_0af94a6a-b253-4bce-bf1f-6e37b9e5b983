RealTimeAnalyticsUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(
      selectInput(ns("data_source"), "Data Source",
                 choices = c("File Upload" = "file", "API Endpoint" = "api",
                           "Database Connection" = "database", "Streaming Service" = "stream")),
      conditionalPanel(
        condition = paste0("input['", ns("data_source"), "'] == 'file'"),
        fileInput(ns("data_file"), "Upload Data File", accept = c(".csv", ".json", ".parquet")),
        numericInput(ns("update_frequency"), "Update Frequency (seconds)", value = 5, min = 1, max = 3600)
      ),
      conditionalPanel(
        condition = paste0("input['", ns("data_source"), "'] == 'api'"),
        textInput(ns("api_url"), "API URL"),
        textInput(ns("api_key"), "API Key (optional)"),
        selectInput(ns("api_method"), "HTTP Method",
                   choices = c("GET" = "GET", "POST" = "POST", "PUT" = "PUT")),
        numericInput(ns("api_frequency"), "API Call Frequency (seconds)", value = 10, min = 1, max = 3600)
      ),
      conditionalPanel(
        condition = paste0("input['", ns("data_source"), "'] == 'database'"),
        selectInput(ns("db_type"), "Database Type",
                   choices = c("PostgreSQL" = "postgresql", "MySQL" = "mysql",
                             "SQLite" = "sqlite", "MongoDB" = "mongodb")),
        textInput(ns("db_host"), "Database Host"),
        textInput(ns("db_name"), "Database Name"),
        textInput(ns("db_user"), "Username"),
        passwordInput(ns("db_password"), "Password"),
        textInput(ns("db_query"), "SQL Query"),
        numericInput(ns("db_frequency"), "Query Frequency (seconds)", value = 30, min = 5, max = 3600)
      ),
      conditionalPanel(
        condition = paste0("input['", ns("data_source"), "'] == 'stream'"),
        selectInput(ns("stream_type"), "Streaming Service",
                   choices = c("Apache Kafka" = "kafka", "Apache Pulsar" = "pulsar",
                             "Amazon Kinesis" = "kinesis", "Google Pub/Sub" = "pubsub")),
        textInput(ns("stream_topic"), "Topic/Stream Name"),
        textInput(ns("stream_broker"), "Broker URL"),
        textInput(ns("stream_group"), "Consumer Group"),
        numericInput(ns("batch_size"), "Batch Size", value = 100, min = 1, max = 10000)
      ),
      selectInput(ns("analysis_type"), "Analysis Type",
                 choices = c("Real-time Monitoring" = "monitoring",
                           "Online Learning" = "online_learning",
                           "Anomaly Detection" = "anomaly",
                           "Trend Analysis" = "trend",
                           "Predictive Analytics" = "predictive",
                           "Dashboard Metrics" = "dashboard")),
      selectInput(ns("target_variable"), "Target Variable", choices = NULL),
      selectInput(ns("feature_variables"), "Feature Variables", choices = NULL, multiple = TRUE),
      actionButton(ns("start_streaming"), "Start Real-time Analysis", class = "btn-success"),
      actionButton(ns("stop_streaming"), "Stop Analysis", class = "btn-danger"),
      actionButton(ns("pause_streaming"), "Pause Analysis", class = "btn-warning"),
      actionButton(ns("reset_analysis"), "Reset Analysis", class = "btn-info"),
      downloadButton(ns("download_results"), "Download Results"),
      downloadButton(ns("export_model"), "Export Model")
    ),
    mainPanel(
      verbatimTextOutput(ns("connection_status")),
      verbatimTextOutput(ns("data_flow_status")),
      verbatimTextOutput(ns("model_status")),
      valueBoxOutput(ns("data_points_processed"), width = 3),
      valueBoxOutput(ns("current_throughput"), width = 3),
      valueBoxOutput(ns("model_accuracy"), width = 3),
      valueBoxOutput(ns("anomalies_detected"), width = 3),
      plotOutput(ns("real_time_plot"), height = "300px"),
      plotOutput(ns("performance_trend"), height = "250px"),
      plotOutput(ns("anomaly_plot"), height = "250px"),
      plotOutput(ns("learning_curve"), height = "250px"),
      plotOutput(ns("model_weights"), height = "250px"),
      plotOutput(ns("trend_plot"), height = "250px"),
      plotOutput(ns("forecast_plot"), height = "250px"),
      plotOutput(ns("prediction_plot"), height = "250px"),
      plotOutput(ns("prediction_accuracy"), height = "250px")
    )
  )
} 