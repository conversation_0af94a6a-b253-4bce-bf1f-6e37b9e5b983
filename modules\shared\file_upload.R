# Shared file upload helpers

handle_file_upload <- function(file_input) {
  if (is.null(file_input)) return(NULL)
  ext <- tolower(tools::file_ext(file_input$name))
  tryCatch({
    switch(ext,
      csv = readr::read_csv(file_input$datapath, show_col_types = FALSE),
      xls = readxl::read_xls(file_input$datapath),
      xlsx = readxl::read_xlsx(file_input$datapath),
      txt = readr::read_tsv(file_input$datapath, show_col_types = FALSE),
      NULL
    )
  }, error = function(e) {
    NULL
  })
} 