# Levene's/Bartlett's Test Server
source("modules/calculations/levene.R")
LeveneTestServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    leveneUploadData <- eventReactive(input$leveneUserData, {
      handle_file_upload(input$leveneUserData)
    })
    observeEvent(leveneUploadData(), {
      data <- leveneUploadData()
      updateSelectizeInput(session, 'leveneResponse', choices = names(data), server = TRUE)
      updateSelectizeInput(session, 'leveneGroup', choices = names(data), server = TRUE)
      output$leveneResults <- renderUI({
        if (!is.null(data) && is.data.frame(data)) {
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('levenePreviewTable'))
          )
        } else NULL
      })
      output$levenePreviewTable <- DT::renderDT({
        head(data, 20)
      })
    })
    leveneValidationErrors <- reactive({
      errors <- c()
      data <- leveneUploadData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$leveneResponse) || input$leveneResponse == "") {
        errors <- c(errors, "Please select a response variable.")
      }
      if (is.null(input$leveneGroup) || input$leveneGroup == "") {
        errors <- c(errors, "Please select a group/factor column.")
      }
      errors
    })
    observeEvent(input$goLevene, {
      output$leveneResults <- renderUI({
        errors <- leveneValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Levene's/Bartlett's Test", errors = errors)
        } else {
          data <- leveneUploadData()
          res <- calc_levene_bartlett(
            data,
            response = input$leveneResponse,
            group = input$leveneGroup,
            test_type = ifelse(input$leveneTestType == "Levene's Test", "Levene", "Bartlett")
          )
          
          tagList(
            tabsetPanel(
              id = ns("leveneTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("leveneAnalysis"),
                title = "Analysis",
                titlePanel("Levene's/Bartlett's Test Results"),
                br(),
                withMathJax(tagList(
                  h4('Hypotheses'),
                  p('$H_0$: All group variances are equal'),
                  p('$H_A$: At least one group variance differs'),
                  h4('Test Statistic'),
                  if (input$leveneTestType == "Levene's Test") {
                    p('$F = $', signif(res$`F value`[1], 4))
                  } else {
                    p('$K^2 = $', signif(res$statistic, 4))
                  },
                  h4('P-value Method'),
                  if (input$leveneTestType == "Levene's Test") {
                    p('$P = $', signif(res$`Pr(>F)`[1], 4))
                  } else {
                    p('$P = $', signif(res$p.value, 4))
                  },
                  h4('Conclusion'),
                  if ((input$leveneTestType == "Levene's Test" && res$`Pr(>F)`[1] < 0.05) || (input$leveneTestType != "Levene's Test" && res$p.value < 0.05)) {
                    p('Since $P < 0.05$, reject $H_0$.')
                  } else {
                    p('Since $P \\geq 0.05$, do not reject $H_0$.')
                  }
                )),
                br(),
                h4("Effect Size"),
                uiOutput(ns('leveneEffectSize')),
                br(),
                h4("Assumptions Check"),
                uiOutput(ns('leveneAssumptions'))
              ),
              tabPanel(
                id = ns("leveneDataSummary"),
                title = "Data Summary",
                h4("Descriptive Statistics by Group"),
                tableOutput(ns('leveneDescriptive')),
                br(),
                h4("Variance Comparison"),
                tableOutput(ns('leveneVariance'))
              ),
              tabPanel(
                id = ns("leveneUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('leveneDataTable'))
              )
            )
          )
        }
      })
    })
    
    # Additional outputs for the enhanced tabs
    output$leveneEffectSize <- renderUI({
      tagList(
        p("Effect size interpretation:"),
        p("- 0.0 to 0.1: Negligible"),
        p("- 0.1 to 0.3: Small"),
        p("- 0.3 to 0.5: Medium"),
        p("- 0.5 to 1.0: Large")
      )
    })
    
    output$leveneAssumptions <- renderUI({
      tagList(
        h5("Key Assumptions:"),
        p("1. Independent observations"),
        p("2. Normal distribution within groups"),
        p("3. Random sampling"),
        br(),
        p("Note: Levene's test is robust to departures from normality.")
      )
    })
    
    output$leveneDescriptive <- renderTable({
      data <- leveneUploadData()
      if (is.null(data)) return(NULL)
      
      response_var <- data[[input$leveneResponse]]
      group_var <- data[[input$leveneGroup]]
      
      desc_stats <- data.frame(
        Group = levels(as.factor(group_var)),
        N = tapply(response_var, group_var, length),
        Mean = tapply(response_var, group_var, mean),
        SD = tapply(response_var, group_var, sd),
        Variance = tapply(response_var, group_var, var),
        Min = tapply(response_var, group_var, min),
        Max = tapply(response_var, group_var, max)
      )
      desc_stats
    }, digits = 4)
    
    output$leveneVariance <- renderTable({
      data <- leveneUploadData()
      if (is.null(data)) return(NULL)
      
      response_var <- data[[input$leveneResponse]]
      group_var <- data[[input$leveneGroup]]
      
      variances <- tapply(response_var, group_var, var)
      variance_stats <- data.frame(
        Group = names(variances),
        Variance = variances,
        SD = sqrt(variances),
        stringsAsFactors = FALSE
      )
      variance_stats
    }, digits = 4)
    
    output$leveneDataTable <- DT::renderDT({
      req(leveneUploadData())
      DT::datatable(leveneUploadData(),
        options = list(pageLength = 25,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(leveneUploadData())))))
    })
  })
} 