library(testthat)
library(here)
source(here::here("modules/analysis/simple_linear_regression/server.R"))
source(here::here("modules/analysis/multiple_linear_regression/server.R"))
source(here::here("modules/analysis/logistic_regression/server.R"))
source(here::here("modules/analysis/desc_stats/server.R"))
source(here::here("modules/analysis/prob_dist/server.R"))
source(here::here("modules/analysis/sample_size_est/server.R"))

# These are mostly Shiny modules, so we test their calculation logic or helpers if possible

test_that("simpleLinearRegressionServer is a function", {
  expect_true(is.function(simpleLinearRegressionServer))
})

test_that("multipleLinearRegressionServer is a function", {
  expect_true(is.function(multipleLinearRegressionServer))
})

test_that("logisticRegressionServer is a function", {
  expect_true(is.function(logisticRegressionServer))
})

test_that("descStatsServer is a function", {
  expect_true(is.function(descStatsServer))
})

test_that("probDistServer is a function", {
  expect_true(is.function(probDistServer))
})

test_that("sampleSizeEstServer is a function", {
  expect_true(is.function(sampleSizeEstServer))
}) 