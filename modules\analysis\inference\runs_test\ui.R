runsTestSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(
      inputId = ns("rtUserData"),
      label   = strong("Upload your Data (.csv or .xls or .xlsx or .txt)"),
      accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
    ),
    div(
      id = ns("rtUploadInputs"),
      selectizeInput(
        inputId = ns("rtVariable"),
        label = strong("Select Variable"),
        choices = c(""),
        selected = NULL,
        options = list(placeholder = 'Select a column', onInitialize = I('function() { this.setValue(\"\"); }'))
      ),
      radioButtons(
        inputId = ns("rtMethod"),
        label = strong("Method"),
        choices = c("median", "mean", "custom"),
        selected = "median"
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'custom'", ns("rtMethod")),
        numericInput(
          inputId = ns("rtCustomValue"),
          label = strong("Custom Value"),
          value = 0,
          step = 0.1
        )
      ),
      radioButtons(
        inputId = ns("rtSigLvl"),
        label = strong("Significance Level (alpha)"),
        choices = c("10%", "5%", "1%"),
        selected = "5%",
        inline = TRUE
      )
    ),
    br(),
    actionButton(ns("goInference"), label = "Calculate", class = "act-btn")
  )
}

runsTestMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('runsTestResults'))
  )
}

runsTestUI <- function(id) {
  ns <- NS(id)
  tagList(
    runsTestSidebarUI(id),
    runsTestMainUI(id)
  )
} 