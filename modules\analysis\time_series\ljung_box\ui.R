ljungBoxUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(
      fileInput(
        inputId = ns("lbUserData"),
        label   = strong("Upload your Data (.csv or .xls or .xlsx or .txt)"),
        accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
      ),
      selectizeInput(
        inputId = ns("lbVariable"),
        label = strong("Select Variable"),
        choices = c(""),
        selected = NULL,
        options = list(placeholder = 'Select a column', onInitialize = I('function() { this.setValue(\"\"); }'))
      ),
      numericInput(
        inputId = ns("lbLag"),
        label = strong("Number of Lags"),
        value = 10,
        min = 1,
        max = 50,
        step = 1
      ),
      radioButtons(
        inputId = ns("lbSigLvl"),
        label = strong("Significance Level (alpha)"),
        choices = c("10%", "5%", "1%"),
        selected = "5%",
        inline = TRUE
      ),
      br(),
      actionButton(ns("goInference"), label = "Calculate", class = "act-btn")
    ),
    mainPanel(
      textOutput(ns('lbError')),
      tableOutput(ns('lbResults')),
      plotOutput(ns('lbPlot'))
    )
  )
} 