# Friedman Test UI
FriedmanTestSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("friedmanUserData"), "Upload your Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
    selectizeInput(ns("friedmanSubject"), "Subject ID Column", choices = NULL),
    selectizeInput(ns("friedmanResponse"), "Response Variable", choices = NULL),
    selectizeInput(ns("friedmanCondition"), "Condition/Time Column", choices = NULL),
    radioButtons(ns("friedmanSigLvl"), "Significance Level (alpha)", choices = c("10%", "5%", "1%"), selected = "5%", inline = TRUE),
    br(),
    actionButton(ns("goFriedman"), label = "Calculate", class = "act-btn")
  )
}

FriedmanTestMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('friedmanResults'))
  )
}

FriedmanTestUI <- function(id) {
  ns <- NS(id)
  tagList(
    FriedmanTestSidebarUI(id),
    FriedmanTestMainUI(id)
  )
} 