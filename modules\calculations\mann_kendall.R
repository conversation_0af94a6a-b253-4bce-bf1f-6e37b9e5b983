# Mann-<PERSON> Trend Test calculation and output helpers

mann_kendall_uploadData_func <- function(mkUserData) {
  ext <- tools::file_ext(mkUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(mkUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(mkUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(mkUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(mkUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

mann_kendall_results_func <- function(data, value_var, time_var = NULL, alternative = "two.sided") {
  tryCatch({
    if (!requireNamespace("<PERSON>", quietly = TRUE)) {
      stop("Package 'Kendall' needed for <PERSON><PERSON><PERSON> test.")
    }
    
    values <- data[[value_var]]
    
    if (is.null(time_var)) {
      time <- 1:length(values)
    } else {
      time <- data[[time_var]]
    }
    
    complete_cases <- complete.cases(time, values)
    time <- time[complete_cases]
    values <- values[complete_cases]
    
    if (length(values) < 3) {
      stop("At least 3 observations are required.")
    }
    
    test_result <- Kendall::MannKendall(values)
    
    list(
      test = test_result,
      data = data.frame(time = time, values = values),
      value_var = value_var,
      time_var = time_var,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Mann-Kendall test calculation:", e$message))
  })
}

mann_kendall_ht_html <- function(results, sigLvl) {
  test <- results$test
  p_value <- test$sl[1]
  
  conclusion <- if (p_value < sigLvl) "a significant trend" else "no significant trend"
  
  withMathJax(tagList(
    h4("Mann-Kendall Trend Test"),
    p(sprintf("There is %s in the data.", conclusion)),
    p(sprintf("Test Statistic (S): %.4f", test$S[1])),
    p(sprintf("P-value: %.4f", p_value))
  ))
}

mann_kendall_summary_html <- function(results) {
  test <- results$test
  
  desc_stats <- data.frame(
    Variable = results$value_var,
    N = length(results$data$values),
    Mean = mean(results$data$values),
    SD = sd(results$data$values)
  )
  
  tagList(
    h4("Test Details"),
    renderPrint(test),
    h4("Descriptive Statistics"),
    renderTable(desc_stats, digits = 4)
  )
}

mann_kendall_plot <- function(results) {
  ggplot(results$data, aes(x = time, y = values)) +
    geom_line(alpha = 0.7) +
    geom_point() +
    labs(title = "Time Series with Trend",
         x = ifelse(is.null(results$time_var), "Index", results$time_var),
         y = results$value_var) +
    theme_minimal()
}