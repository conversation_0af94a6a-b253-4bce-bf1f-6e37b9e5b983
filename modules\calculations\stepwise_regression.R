# Stepwise Regression calculation and output helpers

stepwise_regression_uploadData_func <- function(srUserData) {
  ext <- tools::file_ext(srUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(srUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(srUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(srUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(srUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

stepwise_regression_results_func <- function(data, response_var, predictor_vars, direction = "both") {
  tryCatch({
    all_vars <- c(response_var, predictor_vars)
    model_data <- data[complete.cases(data[all_vars]), ]
    
    initial_formula <- as.formula(paste0("`", response_var, "` ~ ", paste0("`", predictor_vars, "`", collapse = " + ")))
    null_formula <- as.formula(paste0("`", response_var, "` ~ 1"))
    
    model <- step(lm(initial_formula, data = model_data),
                  scope = list(lower = null_formula, upper = initial_formula),
                  direction = direction, trace = 0)
    
    list(
      model = model,
      data = model_data,
      response_var = response_var,
      predictor_vars = predictor_vars,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Stepwise Regression calculation:", e$message))
  })
}

stepwise_regression_ht_html <- function(results) {
  # No single hypothesis test, summary is more informative
  tagList(
    h4("Stepwise Regression Model"),
    p("See summary table for final model coefficients and diagnostics.")
  )
}

stepwise_regression_summary_html <- function(results) {
  model_summary <- summary(results$model)
  
  tagList(
    h4("Final Model Summary"),
    renderPrint(model_summary)
  )
}

stepwise_regression_plot <- function(results) {
  plot(results$model)
}