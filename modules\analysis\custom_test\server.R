# Custom Test Server
CustomTestServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    customUploadData <- eventReactive(input$customUserData, {
      handle_file_upload(input$customUserData)
    })
    
    observeEvent(customUploadData(), {
      data <- customUploadData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'customResponse', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'customPredictors', choices = names(data), server = TRUE)
      }
    })
    
    customTestValidationErrors <- reactive({
      errors <- c()
      data <- customUploadData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$customFormula) || input$customFormula == "") {
        errors <- c(errors, "Please enter a model formula.")
      }
      if (is.null(input$customResponse) || input$customResponse == "") {
        errors <- c(errors, "Please select a response variable.")
      }
      if (is.null(input$customPredictors) || length(input$customPredictors) == 0) {
        errors <- c(errors, "Please select at least one predictor variable.")
      }
      
      # Check variable types
      if (!is.null(input$customResponse) && !is.numeric(data[[input$customResponse]])) {
        errors <- c(errors, "Response variable must be numeric.")
      }
      for (var in input$customPredictors) {
        if (!is.numeric(data[[var]])) {
          errors <- c(errors, sprintf("Predictor variable '%s' must be numeric.", var))
        }
      }
      errors
    })
    
    customTestResult <- eventReactive(input$goCustomTest, {
      data <- customUploadData()
      req(data, input$customResponse, input$customPredictors, input$customFormula)
      
      # Get parameters from UI
      test_type <- ifelse(is.null(input$customTestType), "regression", input$customTestType)
      formula_str <- input$customFormula
      
      custom_test_analysis(data, input$customResponse, input$customPredictors, 
                          formula_str = formula_str, test_type = test_type)
    })
    
    observeEvent(input$goCustomTest, {
      output$customTestResults <- renderUI({
        errors <- customTestValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Custom Test", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("customTestTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("customTestAnalysis"),
                title = "Analysis",
                titlePanel("Custom Test Results"),
                br(),
                h4("Test Type"),
                textOutput(ns('customTestType')),
                h4("Model Formula"),
                textOutput(ns('customTestFormula')),
                h4("Model Summary"),
                tableOutput(ns('customTestSummary')),
                h4("Coefficients"),
                tableOutput(ns('customTestCoefficients')),
                h4("Test Statistics"),
                tableOutput(ns('customTestStatistics')),
                h4("Model Fit"),
                tableOutput(ns('customTestFit')),
                h4("Interpretation"),
                textOutput(ns('customTestInterpretation')),
                h4("Number of Observations"),
                textOutput(ns('customTestObservations')),
                h4("Number of Predictors"),
                textOutput(ns('customTestPredictors'))
              ),
              tabPanel(
                id = ns("customTestDiagnostics"),
                title = "Diagnostics",
                h4("Residuals vs Fitted Plot"),
                plotOutput(ns('customTestResidualPlot'), height = "300px"),
                h4("Q-Q Plot of Residuals"),
                plotOutput(ns('customTestQQPlot'), height = "300px"),
                h4("Diagnostic Summary"),
                textOutput(ns('customTestDiagnostics'))
              ),
              tabPanel(
                id = ns("customTestUploadedData"),
                title = "Uploaded Data",
                h4("Raw Uploaded Data"),
                DT::DTOutput(ns('customTestRawDataTable'))
              )
            )
          )
        }
      })
    })
    
    # Test type
    output$customTestType <- renderText({
      res <- customTestResult()
      if (is.null(res)) return(NULL)
      paste("Test type:", res$test_type)
    })
    
    # Model formula
    output$customTestFormula <- renderText({
      res <- customTestResult()
      if (is.null(res)) return(NULL)
      paste("Model formula:", res$formula)
    })
    
    # Model summary
    output$customTestSummary <- renderTable({
      res <- customTestResult()
      if (is.null(res)) return(NULL)
      res$model_summary
    }, rownames = FALSE, digits = 4)
    
    # Coefficients
    output$customTestCoefficients <- renderTable({
      res <- customTestResult()
      if (is.null(res)) return(NULL)
      res$coefficients
    }, rownames = FALSE, digits = 4)
    
    # Test statistics
    output$customTestStatistics <- renderTable({
      res <- customTestResult()
      if (is.null(res)) return(NULL)
      res$test_statistics
    }, rownames = FALSE, digits = 4)
    
    # Model fit
    output$customTestFit <- renderTable({
      res <- customTestResult()
      if (is.null(res)) return(NULL)
      res$model_fit
    }, rownames = FALSE, digits = 4)
    
    # Interpretation
    output$customTestInterpretation <- renderText({
      res <- customTestResult()
      if (is.null(res)) return(NULL)
      res$interpretation
    })
    
    # Number of observations
    output$customTestObservations <- renderText({
      res <- customTestResult()
      if (is.null(res)) return(NULL)
      paste("Number of observations:", res$n_observations)
    })
    
    # Number of predictors
    output$customTestPredictors <- renderText({
      res <- customTestResult()
      if (is.null(res)) return(NULL)
      paste("Number of predictors:", res$n_predictors)
    })
    
    # Residuals vs fitted plot
    output$customTestResidualPlot <- renderPlot({
      res <- customTestResult()
      if (is.null(res)) return(NULL)
      
      # Create residuals vs fitted plot
      plot(res$fitted_values, res$residuals, 
           main = "Residuals vs Fitted",
           xlab = "Fitted Values", ylab = "Residuals")
      abline(h = 0, col = "red", lty = 2)
    })
    
    # Q-Q plot
    output$customTestQQPlot <- renderPlot({
      res <- customTestResult()
      if (is.null(res)) return(NULL)
      
      # Create Q-Q plot
      qqnorm(res$residuals, main = "Q-Q Plot of Residuals")
      qqline(res$residuals, col = "red")
    })
    
    # Diagnostics summary
    output$customTestDiagnostics <- renderText({
      res <- customTestResult()
      if (is.null(res)) return(NULL)
      
      paste("Model diagnostics: ", res$diagnostics_summary,
            "\nResidual standard error: ", round(res$residual_se, 4),
            "\nR-squared: ", round(res$r_squared, 4),
            "\nAdjusted R-squared: ", round(res$adj_r_squared, 4))
    })
    
    # Raw data table
    output$customTestRawDataTable <- DT::renderDT({
      req(customUploadData())
      DT::datatable(customUploadData(), options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), rownames = FALSE)
    })
  })
} 