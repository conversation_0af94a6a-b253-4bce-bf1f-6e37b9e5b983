# Durbin-Watson Test calculation and output helpers

durbin_watson_uploadData_func <- function(dwUserData) {
  ext <- tools::file_ext(dwUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(dwUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(dwUserData$datapath),
         xlsx = readxl::read_xlsx(dwUserData$datapath),
         txt = readr::read_tsv(dwUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

durbin_watson_results_func <- function(data, response_var, predictor_vars) {
  tryCatch({
    all_vars <- c(response_var, predictor_vars)
    complete_data <- data[complete.cases(data[all_vars]), ]
    
    if (nrow(complete_data) < 3) {
      stop("At least 3 complete observations are required for Durb<PERSON>-<PERSON> test")
    }
    
    formula_str <- paste(response_var, "~", paste(predictor_vars, collapse = " + "))
    model <- lm(as.formula(formula_str), data = complete_data)
    
    test_result <- lmtest::dwtest(model)
    
    list(
      test = test_result,
      model = model,
      data = complete_data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Durbin-Watson test calculation:", e$message))
  })
}

durbin_watson_ht_html <- function(results, sigLvl) {
  test <- results$test
  p_value <- test$p.value
  
  conclusion <- if (p_value < sigLvl) {
    "Reject H0. There is evidence of autocorrelation in the residuals."
  } else {
    "Do not reject H0. There is no evidence of autocorrelation in the residuals."
  }
  
  withMathJax(tagList(
    h4("Durbin-Watson Test for Autocorrelation"),
    p("$H_0$: No first-order autocorrelation in residuals."),
    p("$H_A$: First-order autocorrelation exists in residuals."),
    p(sprintf("Test Statistic (DW): %.4f", test$statistic)),
    p(sprintf("P-value: %.4f", p_value)),
    p(strong("Conclusion: "), conclusion)
  ))
}

durbin_watson_summary_html <- function(results) {
  model_summary <- summary(results$model)
  
  tagList(
    h4("Linear Model Summary"),
    renderPrint(model_summary)
  )
}

durbin_watson_plot <- function(results) {
  plot_data <- data.frame(
    Index = 1:length(residuals(results$model)),
    Residuals = residuals(results$model)
  )
  
  ggplot(plot_data, aes(x = Index, y = Residuals)) +
    geom_point(alpha = 0.7) +
    geom_line(alpha = 0.5) +
    geom_hline(yintercept = 0, color = "red", linetype = "dashed") +
    labs(title = "Residuals vs. Observation Order",
         x = "Observation Index",
         y = "Residuals") +
    theme_minimal()
}