# Cochran's Q Test Server
source("modules/calculations/cochrans_q.R")
CochransQTestServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    cochransQUploadData <- eventReactive(input$cochransQUserData, {
      handle_file_upload(input$cochransQUserData)
    })
    
    observeEvent(cochransQUploadData(), {
      data <- cochransQUploadData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'cochransQSubject', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'cochransQBinaryVars', choices = names(data), server = TRUE)
      }
    })
    
    cochransQValidationErrors <- reactive({
      errors <- c()
      data <- cochransQUploadData()
      subject <- input$cochransQSubject
      binvars <- input$cochransQBinaryVars
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(subject) || !(subject %in% names(data))) {
        errors <- c(errors, "Please select a valid subject ID column.")
      }
      if (is.null(binvars) || length(binvars) < 3) {
        errors <- c(errors, "Please select at least three binary variables.")
      } else {
        for (col in binvars) {
          if (!all(data[[col]] %in% c(0, 1, NA))) {
            errors <- c(errors, sprintf("Column '%s' must be binary (0/1).", col))
          }
        }
      }
      errors
    })
    
    cochransQResult <- eventReactive(input$goCochransQ, {
      data <- cochransQUploadData()
      req(data, input$cochransQSubject, input$cochransQBinaryVars)
      
      binvars <- input$cochransQBinaryVars
      
      calc_cochrans_q_analysis(data, subject = input$cochransQSubject, binary_vars = binvars)
    })
    
    observeEvent(input$goCochransQ, {
      output$cochransQResults <- renderUI({
        errors <- cochransQValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Cochran's Q Test", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("cochransQTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("cochransQAnalysis"),
                title = "Analysis",
                titlePanel("Cochran's Q Test Results"),
                br(),
                h4("Test Overview"),
                textOutput(ns('cochransQOverview')),
                h4("Hypotheses"),
                uiOutput(ns('cochransQHypotheses')),
                h4("Test Statistics"),
                tableOutput(ns('cochransQStatistics')),
                h4("Proportions by Group"),
                tableOutput(ns('cochransQProportions')),
                h4("Effect Size"),
                tableOutput(ns('cochransQEffectSize')),
                h4("Decision"),
                textOutput(ns('cochransQDecision')),
                h4("Interpretation"),
                textOutput(ns('cochransQInterpretation')),
                h4("Number of Subjects"),
                textOutput(ns('cochransQSubjects')),
                h4("Number of Groups"),
                textOutput(ns('cochransQGroups'))
              ),
              tabPanel(
                id = ns("cochransQDiagnostics"),
                title = "Diagnostics",
                h4("Proportions Plot"),
                plotOutput(ns('cochransQProportionsPlot'), height = "300px"),
                h4("Response Patterns"),
                plotOutput(ns('cochransQPatternsPlot'), height = "300px"),
                h4("Diagnostic Summary"),
                textOutput(ns('cochransQDiagnostics'))
              ),
              tabPanel(
                id = ns("cochransQUploadedData"),
                title = "Uploaded Data",
                h4("Raw Uploaded Data"),
                DT::DTOutput(ns('cochransQRawDataTable'))
              )
            )
          )
        }
      })
    })
    
    # Test overview
    output$cochransQOverview <- renderText({
      res <- cochransQResult()
      if (is.null(res)) return(NULL)
      res$overview
    })
    
    # Hypotheses
    output$cochransQHypotheses <- renderUI({
      tagList(
        p("Null hypothesis: H₀: p₁ = p₂ = ... = pₖ"),
        p("Alternative hypothesis: Not all proportions are equal")
      )
    })
    
    # Test statistics
    output$cochransQStatistics <- renderTable({
      res <- cochransQResult()
      if (is.null(res)) return(NULL)
      res$test_statistics
    }, rownames = FALSE, digits = 4)
    
    # Proportions by group
    output$cochransQProportions <- renderTable({
      res <- cochransQResult()
      if (is.null(res)) return(NULL)
      res$proportions_by_group
    }, rownames = FALSE, digits = 4)
    
    # Effect size
    output$cochransQEffectSize <- renderTable({
      res <- cochransQResult()
      if (is.null(res)) return(NULL)
      res$effect_size
    }, rownames = FALSE, digits = 4)
    
    # Decision
    output$cochransQDecision <- renderText({
      res <- cochransQResult()
      if (is.null(res)) return(NULL)
      res$decision
    })
    
    # Interpretation
    output$cochransQInterpretation <- renderText({
      res <- cochransQResult()
      if (is.null(res)) return(NULL)
      res$interpretation
    })
    
    # Number of subjects
    output$cochransQSubjects <- renderText({
      res <- cochransQResult()
      if (is.null(res)) return(NULL)
      paste("Number of subjects:", res$n_subjects)
    })
    
    # Number of groups
    output$cochransQGroups <- renderText({
      res <- cochransQResult()
      if (is.null(res)) return(NULL)
      paste("Number of groups:", res$n_groups)
    })
    
    # Proportions plot
    output$cochransQProportionsPlot <- renderPlot({
      res <- cochransQResult()
      if (is.null(res)) return(NULL)
      
      # Create proportions comparison plot
      proportions_data <- res$proportions_by_group
      if (!is.null(proportions_data)) {
        barplot(proportions_data$Proportion,
                names.arg = proportions_data$Group,
                main = "Proportions by Group",
                ylab = "Proportion",
                col = "steelblue",
                ylim = c(0, 1))
      }
    })
    
    # Response patterns plot
    output$cochransQPatternsPlot <- renderPlot({
      res <- cochransQResult()
      if (is.null(res)) return(NULL)
      
      # Create response patterns visualization
      data <- cochransQUploadData()
      binvars <- input$cochransQBinaryVars
      
      if (!is.null(data) && !is.null(binvars)) {
        # Create a simple pattern count
        pattern_counts <- table(apply(data[, binvars], 1, paste, collapse = ""))
        
        barplot(pattern_counts,
                main = "Response Patterns",
                xlab = "Pattern", ylab = "Count",
                col = "lightgreen")
      }
    })
    
    # Diagnostics summary
    output$cochransQDiagnostics <- renderText({
      res <- cochransQResult()
      if (is.null(res)) return(NULL)
      
      paste("Test type: Cochran's Q Test for Related Samples",
            "\nSignificance level: ", res$alpha,
            "\nTest statistic (Q): ", round(res$statistic, 4),
            "\nP-value: ", round(res$p_value, 4),
            "\nDecision: ", res$decision)
    })
    
    # Raw data table
    output$cochransQRawDataTable <- DT::renderDT({
      req(cochransQUploadData())
      DT::datatable(cochransQUploadData(), options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), rownames = FALSE)
    })
  })
} 