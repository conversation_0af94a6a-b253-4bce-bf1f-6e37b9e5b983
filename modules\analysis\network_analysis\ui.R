NetworkAnalysisUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("netUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectInput(ns("netMetric"), "Network Metric", choices = c("Degree", "Betweenness", "Closeness", "Community Detection")),
        br(),
        actionButton(ns("goNet"), label = "Analyze Network", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("netError")),
        tableOutput(ns("netResults")),
        plotOutput(ns("netPlot"))
      )
    )
  )
} 