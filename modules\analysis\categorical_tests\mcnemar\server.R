# McNemar's Test Server
source("modules/calculations/mcnemar.R")
McNemarTestServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    mcnemarUploadData <- eventReactive(input$mcnemarUserData, {
      handle_file_upload(input$mcnemarUserData)
    })
    
    observeEvent(mcnemarUploadData(), {
      data <- mcnemarUploadData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'mcnemarBefore', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'mcnemarAfter', choices = names(data), server = TRUE)
      }
    })
    
    mcnemarValidationErrors <- reactive({
      errors <- c()
      data <- mcnemarUploadData()
      before <- input$mcnemarBefore
      after <- input$mcnemarAfter
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(before) || !(before %in% names(data))) {
        errors <- c(errors, "Please select a valid 'Before' column.")
      } else if (!all(data[[before]] %in% c(0, 1, NA))) {
        errors <- c(errors, sprintf("Column '%s' must be binary (0/1).", before))
      }
      if (is.null(after) || !(after %in% names(data))) {
        errors <- c(errors, "Please select a valid 'After' column.")
      } else if (!all(data[[after]] %in% c(0, 1, NA))) {
        errors <- c(errors, sprintf("Column '%s' must be binary (0/1).", after))
      }
      if (!is.null(before) && !is.null(after) && length(data[[before]]) != length(data[[after]])) {
        errors <- c(errors, "Both columns must have the same number of observations.")
      }
      errors
    })
    
    mcnemarResult <- eventReactive(input$goMcNemar, {
      data <- mcnemarUploadData()
      req(data, input$mcnemarBefore, input$mcnemarAfter)
      
      before <- data[[input$mcnemarBefore]]
      after <- data[[input$mcnemarAfter]]
      
      calc_mcnemar_analysis(before, after)
    })
    
    observeEvent(input$goMcNemar, {
      output$mcnemarResults <- renderUI({
        errors <- mcnemarValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in McNemar's Test", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("mcnemarTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("mcnemarAnalysis"),
                title = "Analysis",
                titlePanel("McNemar's Test Results"),
                br(),
                h4("Test Overview"),
                textOutput(ns('mcnemarOverview')),
                h4("Hypotheses"),
                uiOutput(ns('mcnemarHypotheses')),
                h4("Contingency Table"),
                tableOutput(ns('mcnemarContingency')),
                h4("Test Statistics"),
                tableOutput(ns('mcnemarStatistics')),
                h4("Effect Size"),
                tableOutput(ns('mcnemarEffectSize')),
                h4("Decision"),
                textOutput(ns('mcnemarDecision')),
                h4("Interpretation"),
                textOutput(ns('mcnemarInterpretation')),
                h4("Number of Observations"),
                textOutput(ns('mcnemarObservations'))
              ),
              tabPanel(
                id = ns("mcnemarDiagnostics"),
                title = "Diagnostics",
                h4("Change Pattern Visualization"),
                plotOutput(ns('mcnemarChangePlot'), height = "300px"),
                h4("Proportions Plot"),
                plotOutput(ns('mcnemarProportionsPlot'), height = "300px"),
                h4("Diagnostic Summary"),
                textOutput(ns('mcnemarDiagnostics'))
              ),
              tabPanel(
                id = ns("mcnemarUploadedData"),
                title = "Uploaded Data",
                h4("Raw Uploaded Data"),
                DT::DTOutput(ns('mcnemarRawDataTable'))
              )
            )
          )
        }
      })
    })
    
    # Test overview
    output$mcnemarOverview <- renderText({
      res <- mcnemarResult()
      if (is.null(res)) return(NULL)
      res$overview
    })
    
    # Hypotheses
    output$mcnemarHypotheses <- renderUI({
      tagList(
        p("Null hypothesis: H₀: p₀₁ = p₁₀"),
        p("Alternative hypothesis: H₁: p₀₁ ≠ p₁₀")
      )
    })
    
    # Contingency table
    output$mcnemarContingency <- renderTable({
      res <- mcnemarResult()
      if (is.null(res)) return(NULL)
      res$contingency_table
    }, rownames = TRUE, digits = 4)
    
    # Test statistics
    output$mcnemarStatistics <- renderTable({
      res <- mcnemarResult()
      if (is.null(res)) return(NULL)
      res$test_statistics
    }, rownames = FALSE, digits = 4)
    
    # Effect size
    output$mcnemarEffectSize <- renderTable({
      res <- mcnemarResult()
      if (is.null(res)) return(NULL)
      res$effect_size
    }, rownames = FALSE, digits = 4)
    
    # Decision
    output$mcnemarDecision <- renderText({
      res <- mcnemarResult()
      if (is.null(res)) return(NULL)
      res$decision
    })
    
    # Interpretation
    output$mcnemarInterpretation <- renderText({
      res <- mcnemarResult()
      if (is.null(res)) return(NULL)
      res$interpretation
    })
    
    # Number of observations
    output$mcnemarObservations <- renderText({
      res <- mcnemarResult()
      if (is.null(res)) return(NULL)
      paste("Number of observations:", res$n_observations)
    })
    
    # Change pattern plot
    output$mcnemarChangePlot <- renderPlot({
      res <- mcnemarResult()
      if (is.null(res)) return(NULL)
      
      # Create change pattern visualization
      data <- mcnemarUploadData()
      before <- data[[input$mcnemarBefore]]
      after <- data[[input$mcnemarAfter]]
      
      # Create a simple bar plot of change patterns
      change_patterns <- paste(before, "→", after)
      pattern_counts <- table(change_patterns)
      
      barplot(pattern_counts, 
              main = "Change Patterns (Before → After)",
              xlab = "Pattern", ylab = "Count",
              col = "steelblue")
    })
    
    # Proportions plot
    output$mcnemarProportionsPlot <- renderPlot({
      res <- mcnemarResult()
      if (is.null(res)) return(NULL)
      
      # Create proportions comparison plot
      data <- mcnemarUploadData()
      before <- data[[input$mcnemarBefore]]
      after <- data[[input$mcnemarAfter]]
      
      before_prop <- mean(before, na.rm = TRUE)
      after_prop <- mean(after, na.rm = TRUE)
      
      barplot(c(before_prop, after_prop),
              names.arg = c("Before", "After"),
              main = "Proportions Comparison",
              ylab = "Proportion",
              col = c("lightblue", "lightgreen"),
              ylim = c(0, 1))
    })
    
    # Diagnostics summary
    output$mcnemarDiagnostics <- renderText({
      res <- mcnemarResult()
      if (is.null(res)) return(NULL)
      
      paste("Test type: McNemar's Test for Paired Proportions",
            "\nSignificance level: ", res$alpha,
            "\nTest statistic: ", round(res$statistic, 4),
            "\nP-value: ", round(res$p_value, 4),
            "\nDecision: ", res$decision)
    })
    
    # Raw data table
    output$mcnemarRawDataTable <- DT::renderDT({
      req(mcnemarUploadData())
      DT::datatable(mcnemarUploadData(), options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), rownames = FALSE)
    })
  })
} 