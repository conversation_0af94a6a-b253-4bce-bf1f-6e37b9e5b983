# Repeated Measures ANOVA UI
RepeatedMeasuresAnovaSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("rmAnovaUserData"), "Upload your Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
    selectizeInput(ns("rmAnovaSubject"), "Subject ID Column", choices = NULL),
    selectizeInput(ns("rmAnovaResponse"), "Response Variable", choices = NULL),
    selectizeInput(ns("rmAnovaCondition"), "Condition/Time Column", choices = NULL),
    radioButtons(ns("rmAnovaSigLvl"), "Significance Level (alpha)", choices = c("10%", "5%", "1%"), selected = "5%", inline = TRUE),
    br(),
    actionButton(ns("goRepeatedMeasuresAnova"), label = "Calculate", class = "act-btn")
  )
}

RepeatedMeasuresAnovaMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('repeatedMeasuresAnovaResults'))
  )
}

RepeatedMeasuresAnovaUI <- function(id) {
  ns <- NS(id)
  tagList(
    RepeatedMeasuresAnovaSidebarUI(id),
    RepeatedMeasuresAnovaMainUI(id)
  )
} 