# Sign Test UI
# Non-parametric alternative to one-sample t-test

SignTestSidebarUI <- function(id) {
  ns <- NS(id)
  
  tagList(
    # Data Input Method
    radioButtons(
      inputId = ns("signTestDataMethod"),
      label = "Data Input Method:",
      choices = c("Manual Entry" = "Manual Entry", "Upload File" = "Upload File"),
      selected = "Manual Entry"
    ),
    
    # Manual Entry
    conditionalPanel(
      condition = "input.signTestDataMethod == 'Manual Entry'",
      ns = ns,
      textAreaInput(
        inputId = ns("signTestData"),
        label = "Enter Data (comma, space, or newline separated):",
        placeholder = "1, 2, 3, 4, 5",
        rows = 4
      )
    ),
    
    # File Upload
    conditionalPanel(
      condition = "input.signTestDataMethod == 'Upload File'",
      ns = ns,
      fileInput(
        inputId = ns("signTestUserData"),
        label = "Upload Data File:",
        accept = c(".csv", ".txt", ".xlsx", ".xls"),
        buttonLabel = "Browse Files",
        placeholder = "No file selected"
      ),
      selectizeInput(
        inputId = ns("signTestVariable"),
        label = "Select Variable:",
        choices = NULL,
        options = list(placeholder = "Select a variable...")
      )
    ),
    
    # Test Parameters
    numericInput(
      inputId = ns("signTestMedian"),
      label = "Hypothesized Median:",
      value = 0,
      step = 0.1
    ),
    
    selectInput(
      inputId = ns("signTestAlternative"),
      label = "Alternative Hypothesis:",
      choices = c(
        "Two-sided (≠)" = "two.sided",
        "Greater than (>)" = "greater",
        "Less than (<)" = "less"
      ),
      selected = "two.sided"
    ),
    
    numericInput(
      inputId = ns("signTestConfLevel"),
      label = "Confidence Level:",
      value = 0.95,
      min = 0.5,
      max = 0.99,
      step = 0.01
    ),
    
    actionButton(
      inputId = ns("goSignTest"),
      label = "Calculate Sign Test",
      class = "btn-primary",
      style = "width: 100%;"
    ),
    
    br(),
    br(),
    
    # Help text
    helpText(
      "The sign test is a non-parametric alternative to the one-sample t-test.",
      "It tests whether the median of a population equals a hypothesized value.",
      "It's robust to outliers and doesn't require normality assumptions."
    )
  )
}

SignTestMainUI <- function(id) {
  ns <- NS(id)
  
  tagList(
    titlePanel("Sign Test"),
    p("A non-parametric test for testing whether the median of a population equals a hypothesized value."),
    
    uiOutput(ns("signTestResults"))
  )
} 