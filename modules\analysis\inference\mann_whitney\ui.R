mannWhitneySidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(
      inputId = ns("mwUserData"),
      label   = strong("Upload your Data (.csv, .xls, .xlsx, .txt)"),
      accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
    ),
    selectizeInput(
      inputId = ns("mwNumeric"),
      label   = strong("Numeric Variable"),
      choices = c(""),
      selected = NULL,
      options = list(placeholder = 'Select a numeric variable')
    ),
    selectizeInput(
      inputId = ns("mwGroup"),
      label   = strong("Grouping Variable (2 groups)"),
      choices = c(""),
      selected = NULL,
      options = list(placeholder = 'Select a grouping variable')
    ),
    radioButtons(
      inputId = ns("mwAlternative"),
      label   = strong("Alternative Hypothesis"),
      choices = c("two.sided", "less", "greater"),
      selected = "two.sided",
      inline = TRUE
    ),
    sliderInput(
      inputId = ns("mwConfLevel"),
      label   = strong("Confidence Level"),
      min = 0.80, max = 0.99, value = 0.95, step = 0.01
    ),
    br(),
    actionButton(ns("goInference"), label = "Calculate", class = "act-btn")
  )
}

mannWhitneyMainUI <- function(id) {
  ns <- NS(id)
  uiOutput(ns('mannWhitneyResults'))
}