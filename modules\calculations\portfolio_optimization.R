# Portfolio Optimization calculation and output helpers

portfolio_optimization_uploadData_func <- function(poUserData) {
  ext <- tools::file_ext(poUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(poUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(poUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(poUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(poUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

portfolio_optimization_results_func <- function(data, return_cols) {
  tryCatch({
    if (!requireNamespace("PortfolioAnalytics", quietly = TRUE)) {
      stop("Package 'PortfolioAnalytics' is required.")
    }
    if (!requireNamespace("ROI", quietly = TRUE)) {
      stop("Package 'ROI' is required.")
    }
    if (!requireNamespace("ROI.plugin.quadprog", quietly = TRUE)) {
      stop("Package 'ROI.plugin.quadprog' is required.")
    }
    
    returns <- as.matrix(data[, return_cols])
    portf <- PortfolioAnalytics::portfolio.spec(assets = colnames(returns))
    portf <- PortfolioAnalytics::add.constraint(portf, type = "full_investment")
    portf <- PortfolioAnalytics::add.constraint(portf, type = "long_only")
    portf <- PortfolioAnalytics::add.objective(portf, type = "risk", name = "StdDev")
    portf <- PortfolioAnalytics::add.objective(portf, type = "return", name = "mean")
    
    opt <- PortfolioAnalytics::optimize.portfolio(R = returns, portfolio = portf, optimize_method = "ROI")
    
    list(
      opt = opt,
      data = data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Portfolio Optimization:", e$message))
  })
}

portfolio_optimization_ht_html <- function(results) {
  tagList(
    h4("Portfolio Optimization"),
    p("The optimal portfolio weights have been calculated to minimize risk for a given level of return.")
  )
}

portfolio_optimization_summary_html <- function(results) {
  weights <- round(PortfolioAnalytics::extractWeights(results$opt), 4)
  
  tagList(
    h4("Optimal Weights"),
    renderTable(as.data.frame(weights))
  )
}

portfolio_optimization_plot <- function(results) {
  if (!requireNamespace("ggplot2", quietly = TRUE)) {
    return(p("Package 'ggplot2' required for plotting."))
  }
  
  frontier <- PortfolioAnalytics::extractEfficientFrontier(results$opt, match.col = "StdDev", n.portfolios = 25)
  
  ggplot(frontier, aes(x = StdDev, y = mean)) +
    geom_line(color = "blue") +
    geom_point(color = "red") +
    labs(title = "Efficient Frontier", x = "Risk (StdDev)", y = "Return (Mean)") +
    theme_minimal()
}