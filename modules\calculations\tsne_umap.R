# Placeholder for t-SNE & UMAP calculation helpers if needed in future 

embedding_analysis <- function(data, vars, method = "tsne", dims = 2) {
  x <- as.matrix(data[, vars, drop = FALSE])
  if (method == "tsne") {
    if (!requireNamespace("Rtsne", quietly = TRUE)) stop("Package 'Rtsne' required.")
    fit <- Rtsne::Rtsne(x, dims = dims, check_duplicates = FALSE)
    plot_fun <- function() { plot(fit$Y, col = "blue", main = "t-SNE Embedding") }
    return(list(fit = fit, embedding = fit$Y, plot = plot_fun, error = NULL))
  } else if (method == "umap") {
    if (!requireNamespace("umap", quietly = TRUE)) stop("Package 'umap' required.")
    fit <- umap::umap(x, n_components = dims)
    plot_fun <- function() { plot(fit$layout, col = "blue", main = "UMAP Embedding") }
    return(list(fit = fit, embedding = fit$layout, plot = plot_fun, error = NULL))
  } else if (method == "pca") {
    fit <- prcomp(x, scale. = TRUE)
    explained <- summary(fit)$importance[2, 1:dims]
    plot_fun <- function() { biplot(fit, main = "PCA Biplot") }
    return(list(fit = fit, embedding = fit$x[, 1:dims], explained_variance = explained, plot = plot_fun, error = NULL))
  } else {
    stop("Unknown method.")
  }
} 