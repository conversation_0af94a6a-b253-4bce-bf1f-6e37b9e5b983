# Advanced Survival Analysis Functions

advanced_survival_analysis <- function(data, time, event, covariates = NULL, group = NULL, 
                                      analysis_type = "cox", competing_risks = FALSE) {
  tryCatch({
    # Prepare data
    if (!time %in% names(data)) {
      stop("Time variable not found in data")
    }
    
    if (!event %in% names(data)) {
      stop("Event variable not found in data")
    }
    
    # Remove rows with missing values
    vars_to_check <- c(time, event)
    if (!is.null(covariates)) vars_to_check <- c(vars_to_check, covariates)
    if (!is.null(group)) vars_to_check <- c(vars_to_check, group)
    
    complete_cases <- complete.cases(data[, vars_to_check, drop = FALSE])
    if (sum(complete_cases) < nrow(data)) {
      warning("Removing rows with missing values for survival analysis")
      data <- data[complete_cases, , drop = FALSE]
    }
    
    # Check if event variable is binary or has multiple levels
    event_values <- unique(data[[event]])
    if (length(event_values) < 2) {
      stop("Event variable must have at least 2 distinct values")
    }
    
    # Create survival object
    if (competing_risks && length(event_values) > 2) {
      # Competing risks analysis
      if (!requireNamespace("survival", quietly = TRUE)) {
        stop("Package 'survival' is required for competing risks analysis")
      }
      
      # Create competing risks object
      surv_obj <- survival::Surv(data[[time]], data[[event]])
      
      # Fit competing risks model
      if (!is.null(covariates)) {
        formula_str <- paste("surv_obj ~", paste(covariates, collapse = " + "))
        if (!is.null(group)) {
          formula_str <- paste(formula_str, "+ strata(", group, ")")
        }
        formula_obj <- as.formula(formula_str)
        fit <- survival::survreg(formula_obj, data = data, dist = "weibull")
      } else {
        fit <- survival::survfit(surv_obj ~ 1, data = data)
      }
      
      # Calculate cumulative incidence functions
      if (requireNamespace("cmprsk", quietly = TRUE)) {
        cif_fit <- cmprsk::crr(data[[time]], data[[event]], 
                               cov1 = if (!is.null(covariates)) as.matrix(data[, covariates, drop = FALSE]) else NULL)
        
        # Extract CIF results
        cif_results <- data.frame(
          Time = cif_fit$time,
          Event_Type = rep(names(cif_fit$est), each = length(cif_fit$time)),
          CIF = as.vector(cif_fit$est),
          SE = as.vector(cif_fit$var)
        )
      } else {
        cif_results <- NULL
      }
      
      # Event summary
      event_summary <- data.frame(
        Event_Type = names(table(data[[event]])),
        Count = as.numeric(table(data[[event]])),
        Percentage = round(as.numeric(table(data[[event]])) / nrow(data) * 100, 2)
      )
      
      result <- list(
        type = "competing_risks",
        fit = fit,
        cif_results = cif_results,
        event_summary = event_summary,
        n_events = length(event_values),
        n_observations = nrow(data)
      )
      
    } else {
      # Standard survival analysis
      
      # Create binary event indicator
      if (length(event_values) == 2) {
        # Binary event
        event_indicator <- as.numeric(data[[event]] == event_values[2])
      } else {
        # Multiple events, use first as reference
        event_indicator <- as.numeric(data[[event]] != event_values[1])
      }
      
      # Create survival object
      surv_obj <- survival::Surv(data[[time]], event_indicator)
      
      if (analysis_type == "cox") {
        # Cox proportional hazards model
        if (!is.null(covariates)) {
          formula_str <- paste("surv_obj ~", paste(covariates, collapse = " + "))
          if (!is.null(group)) {
            formula_str <- paste(formula_str, "+ strata(", group, ")")
          }
          formula_obj <- as.formula(formula_str)
          fit <- survival::coxph(formula_obj, data = data)
          
          # Model summary
          summary_fit <- summary(fit)
          
          # Extract coefficients
          coef_table <- data.frame(
            Variable = names(coef(fit)),
            Coefficient = round(coef(fit), 4),
            Exp_Coefficient = round(exp(coef(fit)), 4),
            Std_Error = round(summary_fit$coefficients[, "se(coef)"], 4),
            z_value = round(summary_fit$coefficients[, "z"], 4),
            p_value = round(summary_fit$coefficients[, "Pr(>|z|)"], 4),
            Lower_CI = round(exp(summary_fit$conf.int[, "lower .95"]), 4),
            Upper_CI = round(exp(summary_fit$conf.int[, "upper .95"]), 4)
          )
          
          # Model fit statistics
          fit_stats <- data.frame(
            Statistic = c("Likelihood Ratio Test", "Wald Test", "Score Test", "Concordance", "R-squared"),
            Value = c(round(summary_fit$logtest["test"], 4),
                     round(summary_fit$waldtest["test"], 4),
                     round(summary_fit$sctest["test"], 4),
                     round(summary_fit$concordance[1], 4),
                     round(summary_fit$rsq[1], 4)),
            p_value = c(round(summary_fit$logtest["pvalue"], 4),
                       round(summary_fit$waldtest["pvalue"], 4),
                       round(summary_fit$sctest["pvalue"], 4),
                       NA, NA)
          )
          
          # Proportional hazards assumption test
          ph_test <- survival::cox.zph(fit)
          ph_table <- data.frame(
            Variable = rownames(ph_test$table),
            Chi_Square = round(ph_test$table[, "chisq"], 4),
            p_value = round(ph_test$table[, "p"], 4)
          )
          
          # Baseline survival
          baseline_surv <- survival::survfit(fit)
          
        } else {
          # No covariates - Kaplan-Meier
          fit <- survival::survfit(surv_obj ~ 1, data = data)
          coef_table <- NULL
          fit_stats <- NULL
          ph_table <- NULL
          baseline_surv <- fit
        }
        
        # Survival curves
        if (!is.null(group)) {
          group_surv <- survival::survfit(surv_obj ~ data[[group]], data = data)
        } else {
          group_surv <- NULL
        }
        
        # Log-rank test
        if (!is.null(group)) {
          logrank_test <- survival::survdiff(surv_obj ~ data[[group]], data = data)
          logrank_results <- data.frame(
            Statistic = c("Chi-square", "Degrees of Freedom", "p-value"),
            Value = c(round(logrank_test$chisq, 4), logrank_test$n - 1, 
                     round(pchisq(logrank_test$chisq, logrank_test$n - 1, lower.tail = FALSE), 4))
          )
        } else {
          logrank_results <- NULL
        }
        
        result <- list(
          type = "cox",
          fit = fit,
          coefficients = coef_table,
          fit_statistics = fit_stats,
          proportional_hazards_test = ph_table,
          baseline_survival = baseline_surv,
          group_survival = group_surv,
          logrank_test = logrank_results,
          n_observations = nrow(data),
          n_events = sum(event_indicator),
          median_survival = if (!is.null(baseline_surv)) median(baseline_surv) else NULL
        )
        
      } else if (analysis_type == "parametric") {
        # Parametric survival models
        
        if (!is.null(covariates)) {
          formula_str <- paste("surv_obj ~", paste(covariates, collapse = " + "))
          if (!is.null(group)) {
            formula_str <- paste(formula_str, "+ strata(", group, ")")
          }
          formula_obj <- as.formula(formula_str)
          
          # Fit different parametric models
          models <- list()
          aic_values <- c()
          
          # Weibull
          tryCatch({
            weibull_fit <- survival::survreg(formula_obj, data = data, dist = "weibull")
            models$weibull <- weibull_fit
            aic_values["weibull"] <- AIC(weibull_fit)
          }, error = function(e) {
            models$weibull <- NULL
            aic_values["weibull"] <- NA
          })
          
          # Exponential
          tryCatch({
            exp_fit <- survival::survreg(formula_obj, data = data, dist = "exponential")
            models$exponential <- exp_fit
            aic_values["exponential"] <- AIC(exp_fit)
          }, error = function(e) {
            models$exponential <- NULL
            aic_values["exponential"] <- NA
          })
          
          # Log-normal
          tryCatch({
            lognorm_fit <- survival::survreg(formula_obj, data = data, dist = "lognormal")
            models$lognormal <- lognorm_fit
            aic_values["lognormal"] <- AIC(lognorm_fit)
          }, error = function(e) {
            models$lognormal <- NULL
            aic_values["lognormal"] <- NA
          })
          
          # Log-logistic
          tryCatch({
            loglog_fit <- survival::survreg(formula_obj, data = data, dist = "loglogistic")
            models$loglogistic <- loglog_fit
            aic_values["loglogistic"] <- AIC(loglog_fit)
          }, error = function(e) {
            models$loglogistic <- NULL
            aic_values["loglogistic"] <- NA
          })
          
          # Find best model
          best_model_name <- names(which.min(aic_values))
          best_model <- models[[best_model_name]]
          
          # Model comparison
          model_comparison <- data.frame(
            Model = names(aic_values),
            AIC = round(aic_values, 4),
            Delta_AIC = round(aic_values - min(aic_values, na.rm = TRUE), 4)
          )
          
          # Extract coefficients from best model
          summary_best <- summary(best_model)
          coef_table <- data.frame(
            Variable = names(coef(best_model)),
            Coefficient = round(coef(best_model), 4),
            Std_Error = round(summary_best$table[, "Std. Error"], 4),
            z_value = round(summary_best$table[, "z"], 4),
            p_value = round(summary_best$table[, "p"], 4)
          )
          
        } else {
          # No covariates
          models <- list()
          aic_values <- c()
          
          # Fit different distributions
          for (dist in c("weibull", "exponential", "lognormal", "loglogistic")) {
            tryCatch({
              fit <- survival::survreg(surv_obj ~ 1, data = data, dist = dist)
              models[[dist]] <- fit
              aic_values[dist] <- AIC(fit)
            }, error = function(e) {
              models[[dist]] <- NULL
              aic_values[dist] <- NA
            })
          }
          
          best_model_name <- names(which.min(aic_values))
          best_model <- models[[best_model_name]]
          coef_table <- NULL
          model_comparison <- data.frame(
            Model = names(aic_values),
            AIC = round(aic_values, 4),
            Delta_AIC = round(aic_values - min(aic_values, na.rm = TRUE), 4)
          )
        }
        
        result <- list(
          type = "parametric",
          best_model = best_model,
          best_model_name = best_model_name,
          all_models = models,
          model_comparison = model_comparison,
          coefficients = coef_table,
          n_observations = nrow(data),
          n_events = sum(event_indicator)
        )
        
      } else {
        stop("Unknown analysis type. Use 'cox' or 'parametric'")
      }
    }
    
    result
    
  }, error = function(e) {
    stop(paste("Advanced survival analysis failed:", e$message))
  })
}

# Advanced Survival Analysis calculation and output helpers

# 1. Data Upload Function
advanced_survival_uploadData_func <- function(asUserData) {
  ext <- tools::file_ext(asUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(asUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(asUserData$datapath),
         xlsx = readxl::read_xlsx(asUserData$datapath),
         txt = readr::read_tsv(asUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

# 2. Main Results Function (core calculations)
advanced_survival_results_func <- function(data, time, event, covariates = NULL, group = NULL, analysis_type = "cox", competing_risks = FALSE) {
  tryCatch({
    # Move the main calculation logic here
    results <- advanced_survival_analysis(data, time, event, covariates, group, analysis_type, competing_risks)
    results$data <- data
    results$error <- NULL
    results
  }, error = function(e) {
    list(error = paste("An error occurred during Advanced Survival Analysis calculation:", e$message))
  })
}

# 3. Hypothesis Test HTML Output
advanced_survival_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  tagList(
    h4("Advanced Survival Analysis"),
    p("See summary table for model details.")
  )
}

# 4. Summary Table HTML Output
advanced_survival_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  out <- list(h4("Advanced Survival Analysis Summary"))
  if (!is.null(results$fit_statistics)) {
    out <- c(out, h4("Model Fit Statistics"), renderTable(results$fit_statistics))
  }
  if (!is.null(results$coefficients)) {
    out <- c(out, h4("Coefficients"), renderTable(results$coefficients))
  }
  if (!is.null(results$proportional_hazards_test)) {
    out <- c(out, h4("Proportional Hazards Test"), renderTable(results$proportional_hazards_test))
  }
  if (!is.null(results$logrank_test)) {
    out <- c(out, h4("Log-rank Test"), renderTable(results$logrank_test))
  }
  if (!is.null(results$event_summary)) {
    out <- c(out, h4("Event Summary"), renderTable(results$event_summary))
  }
  if (!is.null(results$fit) && !is.null(attr(results$fit, "converged"))) {
    out <- c(out, paste("Converged:", attr(results$fit, "converged")))
  }
  if (!is.null(results$fit) && !is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

# 5. Plot Output
advanced_survival_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  if (!is.null(results$group_survival)) {
    plot(results$group_survival, col = 1:nlevels(as.factor(results$group_survival$strata)), lwd = 2, main = "Survival Curves by Group")
    legend("topright", legend = names(results$group_survival$strata), col = 1:nlevels(as.factor(results$group_survival$strata)), lwd = 2)
  } else if (!is.null(results$baseline_survival)) {
    plot(results$baseline_survival, main = "Baseline Survival Curve", lwd = 2)
  } else if (!is.null(results$cif_results)) {
    matplot(results$cif_results$Time, results$cif_results$CIF, type = 'l', lwd = 2, main = "Cumulative Incidence Functions", xlab = "Time", ylab = "CIF")
    legend("topright", legend = unique(results$cif_results$Event_Type), col = 1:length(unique(results$cif_results$Event_Type)), lty = 1)
  } else {
    plot.new(); title("No plot available.")
  }
} 