# Placeholder for Survival Trees UI
survivalTreesSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("stUserData"), "Upload your Data (.csv or .xls or .xlsx or .txt)", accept = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")),
    # TODO: Add Survival Trees-specific UI controls
    actionButton(ns("goST"), label = "Calculate", class = "act-btn")
  )
}

survivalTreesMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('stResults'))
  )
}

survivalTreesUI <- function(id) {
  ns <- NS(id)
  tagList(
    survivalTreesSidebarUI(id),
    survivalTreesMainUI(id),
    tabsetPanel(
      id = ns("stTabset"),
      selected = "Analysis",
      tabPanel(
        id    = ns("stAnalysis"),
        title = "Analysis",
        titlePanel("Survival Trees Analysis"),
        br(),
        uiOutput(ns('stHT')),
        br(),
        plotOutput(ns('stPlot'), width = "50%", height = "400px"),
        br(),
        uiOutput(ns('stConclusionOutput'))
      ),
      tabPanel(
        id    = ns("stData"),
        title = "Uploaded Data",
        uiOutput(ns("renderSTData"))
      )
    )
  )
} 