quantileRegressionServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactives
    qrUploadData <- eventReactive(input$qrUserData, {
      handle_file_upload(input$qrUserData)
    })
    
    qrResults <- reactive({
      data <- qrUploadData()
      if (is.null(data) || is.null(input$qrResponse) || is.null(input$qrPredictors)) {
        return(NULL)
      }
      
      # Only run if the selected columns exist in the data
      valid_response <- input$qrResponse %in% names(data)
      valid_predictors <- all(input$qrPredictors %in% names(data))
      
      if (!valid_response || !valid_predictors) {
        return(NULL)
      }
      
      qrResults_func(
        data,
        input$qrResponse,
        input$qrPredictors,
        input$qrQuantile,
        input$qrMethod,
        input$qrIntercept,
        input$qrConfLevel
      )
    })
    
    # Validation errors reactive
    qrValidationErrors <- reactive({
      errors <- c()
      data <- qrUploadData()
      
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$qrResponse) || input$qrResponse == "") {
        errors <- c(errors, "Please select a response variable.")
      }
      
      if (is.null(input$qrPredictors) || length(input$qrPredictors) == 0) {
        errors <- c(errors, "Please select at least one predictor variable.")
      }
      
      if (!is.null(input$qrResponse) && !is.null(input$qrPredictors) && 
          input$qrResponse %in% input$qrPredictors) {
        errors <- c(errors, "Response variable cannot be the same as a predictor variable.")
      }
      
      if (!is.null(input$qrResponse) && input$qrResponse %in% names(data)) {
        if (length(unique(data[[input$qrResponse]])) < 2) {
          errors <- c(errors, "Response variable must have at least two unique values.")
        }
      }
      
      if (!is.null(input$qrPredictors) && length(input$qrPredictors) > 0) {
        for (pred in input$qrPredictors) {
          if (pred %in% names(data) && length(unique(data[[pred]])) < 2) {
            errors <- c(errors, sprintf("Predictor variable '%s' must have at least two unique values.", pred))
          }
        }
      }
      
      errors
    })
    
    # Outputs
    output$qrModelSummary <- renderUI({
      results <- qrResults()
      if (is.null(results)) return(NULL)
      qrModelSummaryOutput(results)
    })
    
    output$qrCoefficients <- renderUI({
      results <- qrResults()
      if (is.null(results)) return(NULL)
      qrCoefficientsOutput(results)
    })
    
    output$qrDiagnostics <- renderUI({
      results <- qrResults()
      if (is.null(results)) return(NULL)
      qrDiagnosticsOutput(results)
    })
    
    output$qrPlot <- renderPlot({
      results <- qrResults()
      if (is.null(results)) return(NULL)
      qrPlotOutput(results)
    })
    
    output$qrDataPreview <- renderUI({
      req(qrUploadData())
      tagList(
        titlePanel("Data Preview"),
        br(),
        div(DT::DTOutput(ns("qrPreviewTable")), style = "width: 75%"),
        br()
      )
    })
    
    output$qrPreviewTable <- DT::renderDT({
      req(qrUploadData())
      DT::datatable(head(qrUploadData(), 20),
        options = list(pageLength = 10,
                       lengthMenu = list(c(10, 25, 50), c("10", "25", "50")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(qrUploadData())))))
    })
    
    # Update selectizeInput choices after file upload
    observeEvent(qrUploadData(), {
      data <- qrUploadData()
      
      # Clear selectizeInputs and main panel
      updateSelectizeInput(session, 'qrResponse', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'qrPredictors', choices = character(0), selected = NULL, server = TRUE)
      output$quantileRegressionResults <- renderUI({ NULL })
      
      # If data is valid, update choices and show preview
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'qrResponse', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'qrPredictors', choices = names(data), server = TRUE)
        output$quantileRegressionResults <- renderUI({
          tagList(
            h4('Data Preview'),
            DT::DTOutput(ns('qrPreviewTable'))
          )
        })
      }
    })
    
    # Show main results or error screen when Calculate is pressed
    observeEvent(input$goQuantileRegression, {
      output$quantileRegressionResults <- renderUI({
        errors <- qrValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Quantile Regression", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("qrTabset"),
              selected = "Model Summary",
              tabPanel(
                id = ns("qrSummary"),
                title = "Model Summary",
                titlePanel("Quantile Regression Results"),
                br(),
                uiOutput(ns('qrModelSummary')),
                br(),
                uiOutput(ns('qrCoefficients'))
              ),
              tabPanel(
                id = ns("qrDiagnostics"),
                title = "Diagnostics",
                titlePanel("Model Diagnostics"),
                br(),
                uiOutput(ns('qrDiagnostics')),
                br(),
                plotOutput(ns('qrPlot'), width = "100%", height = "600px")
              ),
              tabPanel(
                id = ns("qrData"),
                title = "Data Preview",
                uiOutput(ns("qrDataPreview"))
              )
            )
          )
        }
      })
    })
  })
} 