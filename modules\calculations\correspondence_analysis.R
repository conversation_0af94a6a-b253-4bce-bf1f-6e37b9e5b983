# Correspondence Analysis Calculation Functions

# Main correspondence analysis function
caResults_func <- function(data, row_var, col_var, method, n_dimensions, standardize, conf_level) {
  
  # Validate inputs
  if (!row_var %in% names(data)) {
    stop("Row variable not found in data")
  }
  
  if (!col_var %in% names(data)) {
    stop("Column variable not found in data")
  }
  
  if (row_var == col_var) {
    stop("Row and column variables must be different")
  }
  
  # Prepare data
  row_data <- data[[row_var]]
  col_data <- data[[col_var]]
  
  # Remove rows with missing values
  complete_cases <- complete.cases(row_data, col_data)
  row_data <- row_data[complete_cases]
  col_data <- col_data[complete_cases]
  
  if (length(row_data) < 10) {
    stop("Insufficient data for correspondence analysis")
  }
  
  # Create contingency table
  contingency_table <- table(row_data, col_data)
  
  if (nrow(contingency_table) < 2 || ncol(contingency_table) < 2) {
    stop("Contingency table must have at least 2 rows and 2 columns")
  }
  
  # Perform correspondence analysis based on method
  results <- switch(method,
    "Simple Correspondence Analysis" = perform_simple_ca(contingency_table, n_dimensions, standardize),
    "Multiple Correspondence Analysis" = perform_multiple_ca(data, c(row_var, col_var), n_dimensions, standardize),
    stop("Unknown correspondence analysis method: ", method)
  )
  
  # Add metadata
  results$method <- method
  results$row_var <- row_var
  results$col_var <- col_var
  results$n_dimensions <- n_dimensions
  results$standardize <- standardize
  results$conf_level <- conf_level
  results$n_observations <- length(row_data)
  results$contingency_table <- contingency_table
  results$data <- data[complete_cases, ]
  
  return(results)
}

# Simple Correspondence Analysis
perform_simple_ca <- function(contingency_table, n_dimensions, standardize) {
  
  # Calculate row and column totals
  row_totals <- rowSums(contingency_table)
  col_totals <- colSums(contingency_table)
  total_sum <- sum(contingency_table)
  
  # Calculate expected frequencies
  expected <- outer(row_totals, col_totals) / total_sum
  
  # Calculate chi-square residuals
  residuals <- (contingency_table - expected) / sqrt(expected)
  
  # Perform singular value decomposition
  svd_result <- svd(residuals)
  
  # Extract singular values and vectors
  singular_values <- svd_result$d
  u_matrix <- svd_result$u
  v_matrix <- svd_result$v
  
  # Calculate eigenvalues and proportion of variance
  eigenvalues <- singular_values^2
  total_inertia <- sum(eigenvalues)
  proportion_variance <- eigenvalues / total_inertia
  cumulative_variance <- cumsum(proportion_variance)
  
  # Calculate row and column coordinates
  row_coordinates <- u_matrix[, 1:n_dimensions, drop = FALSE] * singular_values[1:n_dimensions]
  col_coordinates <- v_matrix[, 1:n_dimensions, drop = FALSE] * singular_values[1:n_dimensions]
  
  # Standardize coordinates if requested
  if (standardize) {
    row_coordinates <- row_coordinates / sqrt(row_totals / total_sum)
    col_coordinates <- col_coordinates / sqrt(col_totals / total_sum)
  }
  
  # Calculate row and column masses
  row_masses <- row_totals / total_sum
  col_masses <- col_totals / total_sum
  
  # Calculate contributions
  row_contributions <- calculate_row_contributions(row_coordinates, row_masses, eigenvalues[1:n_dimensions])
  col_contributions <- calculate_col_contributions(col_coordinates, col_masses, eigenvalues[1:n_dimensions])
  
  # Calculate quality of representation
  row_quality <- calculate_quality_representation(row_coordinates, eigenvalues[1:n_dimensions])
  col_quality <- calculate_quality_representation(col_coordinates, eigenvalues[1:n_dimensions])
  
  return(list(
    eigenvalues = eigenvalues,
    proportion_variance = proportion_variance,
    cumulative_variance = cumulative_variance,
    row_coordinates = row_coordinates,
    col_coordinates = col_coordinates,
    row_masses = row_masses,
    col_masses = col_masses,
    row_contributions = row_contributions,
    col_contributions = col_contributions,
    row_quality = row_quality,
    col_quality = col_quality,
    total_inertia = total_inertia,
    residuals = residuals,
    method_name = "Simple Correspondence Analysis"
  ))
}

# Multiple Correspondence Analysis
perform_multiple_ca <- function(data, variables, n_dimensions, standardize) {
  
  # Create indicator matrix
  indicator_matrix <- create_indicator_matrix(data, variables)
  
  # Perform correspondence analysis on indicator matrix
  results <- perform_simple_ca(indicator_matrix, n_dimensions, standardize)
  
  # Add MCA-specific information
  results$method_name <- "Multiple Correspondence Analysis"
  results$variables <- variables
  results$indicator_matrix <- indicator_matrix
  
  return(results)
}

# Create indicator matrix for MCA
create_indicator_matrix <- function(data, variables) {
  
  # Create dummy variables for each categorical variable
  dummy_vars <- list()
  
  for (var in variables) {
    if (var %in% names(data)) {
      # Create dummy variables
      var_data <- data[[var]]
      unique_values <- unique(var_data)
      
      for (val in unique_values) {
        dummy_name <- paste0(var, "_", val)
        dummy_vars[[dummy_name]] <- as.numeric(var_data == val)
      }
    }
  }
  
  # Combine into matrix
  indicator_matrix <- do.call(cbind, dummy_vars)
  colnames(indicator_matrix) <- names(dummy_vars)
  
  return(indicator_matrix)
}

# Calculate row contributions
calculate_row_contributions <- function(row_coordinates, row_masses, eigenvalues) {
  
  n_dims <- ncol(row_coordinates)
  contributions <- matrix(0, nrow(row_coordinates), n_dims)
  
  for (i in 1:n_dims) {
    contributions[, i] <- (row_coordinates[, i]^2 * row_masses) / eigenvalues[i]
  }
  
  return(contributions)
}

# Calculate column contributions
calculate_col_contributions <- function(col_coordinates, col_masses, eigenvalues) {
  
  n_dims <- ncol(col_coordinates)
  contributions <- matrix(0, nrow(col_coordinates), n_dims)
  
  for (i in 1:n_dims) {
    contributions[, i] <- (col_coordinates[, i]^2 * col_masses) / eigenvalues[i]
  }
  
  return(contributions)
}

# Calculate quality of representation
calculate_quality_representation <- function(coordinates, eigenvalues) {
  
  n_dims <- ncol(coordinates)
  quality <- matrix(0, nrow(coordinates), n_dims)
  
  for (i in 1:n_dims) {
    quality[, i] <- coordinates[, i]^2 / eigenvalues[i]
  }
  
  return(quality)
}

# Output rendering functions

caModelSummaryOutput <- function(results) {
  renderUI({
    req(results)
    
    tagList(
      h4(paste("Results:", results$method_name)),
      br(),
      p(strong("Row Variable:"), results$row_var),
      p(strong("Column Variable:"), results$col_var),
      p(strong("Number of Dimensions:"), results$n_dimensions),
      p(strong("Number of Observations:"), results$n_observations),
      p(strong("Total Inertia:"), round(results$total_inertia, 4)),
      br(),
      h5("Contingency Table Dimensions"),
      p(strong("Rows:"), nrow(results$contingency_table)),
      p(strong("Columns:"), ncol(results$contingency_table)),
      br(),
      h5("Variance Explained"),
      p(strong("First Dimension:"), round(results$proportion_variance[1] * 100, 2), "%"),
      p(strong("Second Dimension:"), round(results$proportion_variance[2] * 100, 2), "%"),
      p(strong("Cumulative (2 dimensions):"), round(results$cumulative_variance[2] * 100, 2), "%")
    )
  })
}

caEigenvaluesOutput <- function(results) {
  renderUI({
    req(results)
    
    # Create eigenvalues table
    n_dims <- min(length(results$eigenvalues), 10)
    eigen_df <- data.frame(
      Dimension = paste0("Dim", 1:n_dims),
      Eigenvalue = round(results$eigenvalues[1:n_dims], 4),
      Proportion = round(results$proportion_variance[1:n_dims], 4),
      Cumulative = round(results$cumulative_variance[1:n_dims], 4),
      stringsAsFactors = FALSE
    )
    
    tagList(
      h4("Eigenvalues and Variance Explained"),
      br(),
      div(
        DT::datatable(eigen_df, options = list(pageLength = 10, searching = FALSE)),
        style = "width: 100%"
      ),
      br(),
      p(strong("Total Inertia:"), round(results$total_inertia, 4))
    )
  })
}

caCoordinatesOutput <- function(results) {
  renderUI({
    req(results)
    
    # Create row coordinates table
    row_coords_df <- as.data.frame(results$row_coordinates)
    row_coords_df$Row <- rownames(results$contingency_table)
    row_coords_df <- row_coords_df[, c("Row", colnames(results$row_coordinates))]
    
    # Create column coordinates table
    col_coords_df <- as.data.frame(results$col_coordinates)
    col_coords_df$Column <- colnames(results$contingency_table)
    col_coords_df <- col_coords_df[, c("Column", colnames(results$col_coordinates))]
    
    # Round coordinates
    for (col in colnames(results$row_coordinates)) {
      row_coords_df[[col]] <- round(row_coords_df[[col]], 4)
      col_coords_df[[col]] <- round(col_coords_df[[col]], 4)
    }
    
    tagList(
      h4("Row and Column Coordinates"),
      br(),
      h5("Row Coordinates"),
      div(
        DT::datatable(row_coords_df, options = list(pageLength = 10, searching = FALSE)),
        style = "width: 100%"
      ),
      br(),
      h5("Column Coordinates"),
      div(
        DT::datatable(col_coords_df, options = list(pageLength = 10, searching = FALSE)),
        style = "width: 100%"
      )
    )
  })
}

caContributionsOutput <- function(results) {
  renderUI({
    req(results)
    
    # Create row contributions table
    row_contrib_df <- as.data.frame(results$row_contributions)
    row_contrib_df$Row <- rownames(results$contingency_table)
    row_contrib_df <- row_contrib_df[, c("Row", colnames(results$row_contributions))]
    
    # Create column contributions table
    col_contrib_df <- as.data.frame(results$col_contributions)
    col_contrib_df$Column <- colnames(results$contingency_table)
    col_contrib_df <- col_contrib_df[, c("Column", colnames(results$col_contributions))]
    
    # Round contributions
    for (col in colnames(results$row_contributions)) {
      row_contrib_df[[col]] <- round(row_contrib_df[[col]], 4)
      col_contrib_df[[col]] <- round(col_contrib_df[[col]], 4)
    }
    
    tagList(
      h4("Contributions to Dimensions"),
      br(),
      h5("Row Contributions"),
      div(
        DT::datatable(row_contrib_df, options = list(pageLength = 10, searching = FALSE)),
        style = "width: 100%"
      ),
      br(),
      h5("Column Contributions"),
      div(
        DT::datatable(col_contrib_df, options = list(pageLength = 10, searching = FALSE)),
        style = "width: 100%"
      )
    )
  })
}

caPlotsOutput <- function(results) {
  renderUI({
    req(results)
    
    tagList(
      h4("Correspondence Analysis Plots"),
      br(),
      plotOutput(paste0("ca_scree_plot_", sample(1:1000, 1)), height = "400px"),
      br(),
      plotOutput(paste0("ca_biplot_", sample(1:1000, 1)), height = "400px")
    )
  })
}

# Correspondence Analysis calculation and output helpers

caResults_func <- function(si_iv_is_valid, data, row_var, col_var, n_dim) {
  req(si_iv_is_valid)
  req(data)
  req(row_var)
  req(col_var)
  
  results <- list()
  
  # Prepare data
  ca_data <- data[, c(row_var, col_var), drop = FALSE]
  ca_data <- na.omit(ca_data)
  
  # Create contingency table
  contingency_table <- table(ca_data[[row_var]], ca_data[[col_var]])
  
  # Perform correspondence analysis
  tryCatch({
    ca_result <- ca::ca(contingency_table, nd = n_dim)
    
    # Extract coordinates
    row_coords <- data.frame(
      Category = rownames(ca_result$rowcoord),
      ca_result$rowcoord,
      stringsAsFactors = FALSE
    )
    colnames(row_coords)[-1] <- paste0("Dim", 1:n_dim)
    
    col_coords <- data.frame(
      Category = rownames(ca_result$colcoord),
      ca_result$colcoord,
      stringsAsFactors = FALSE
    )
    colnames(col_coords)[-1] <- paste0("Dim", 1:n_dim)
    
    # Combine coordinates
    all_coords <- rbind(
      cbind(Type = "Row", row_coords),
      cbind(Type = "Column", col_coords)
    )
    
    # Extract contributions
    row_contrib <- data.frame(
      Category = rownames(ca_result$rowmass),
      Mass = round(ca_result$rowmass, 4),
      Inertia = round(ca_result$rowinertia, 4),
      stringsAsFactors = FALSE
    )
    
    col_contrib <- data.frame(
      Category = rownames(ca_result$colmass),
      Mass = round(ca_result$colmass, 4),
      Inertia = round(ca_result$colinertia, 4),
      stringsAsFactors = FALSE
    )
    
    # Combine contributions
    all_contrib <- rbind(
      cbind(Type = "Row", row_contrib),
      cbind(Type = "Column", col_contrib)
    )
    
    # Extract eigenvalues and variance explained
    eigenvalues <- ca_result$sv^2
    variance_explained <- eigenvalues / sum(eigenvalues) * 100
    cumulative_variance <- cumsum(variance_explained)
    
    results$ca_result <- ca_result
    results$coordinates <- all_coords
    results$contributions <- all_contrib
    results$eigenvalues <- eigenvalues
    results$variance_explained <- variance_explained
    results$cumulative_variance <- cumulative_variance
    results$contingency_table <- contingency_table
    results$data <- ca_data
    results$row_var <- row_var
    results$col_var <- col_var
    results$n_dim <- n_dim
    results$n_rows <- nrow(contingency_table)
    results$n_cols <- ncol(contingency_table)
    results$total_inertia <- sum(eigenvalues)
    
  }, error = function(e) {
    results$error <- e$message
  })
  
  return(results)
}

correspondenceAnalysisResults <- function(caResults_output, caSigLvl_input) {
  renderUI({
    req(caResults_output())
    results <- caResults_output()
    
    if (!is.null(results$error)) {
      return(tagList(
        h4("Error in Correspondence Analysis"),
        p(results$error)
      ))
    }
    
    tagList(
      h4("Correspondence Analysis Summary"),
      p(tags$b("Row Variable:"), results$row_var),
      p(tags$b("Column Variable:"), results$col_var),
      p(tags$b("Number of Dimensions:"), results$n_dim),
      p(tags$b("Number of Row Categories:"), results$n_rows),
      p(tags$b("Number of Column Categories:"), results$n_cols),
      br(),
      h4("Model Fit"),
      p(tags$b("Total Inertia:"), round(results$total_inertia, 4)),
      p(tags$b("Variance Explained by First 2 Dimensions:"), 
        round(sum(results$variance_explained[1:min(2, length(results$variance_explained))]), 1), "%"),
      br(),
      h4("Dimension Information"),
      lapply(1:results$n_dim, function(i) {
        p(sprintf("Dimension %d: %.3f (%.1f%%)", i, 
                  results$eigenvalues[i], 
                  results$variance_explained[i]))
      })
    )
  })
}

correspondenceAnalysisCoordinates <- function(caResults_output, caSigLvl_input) {
  renderUI({
    req(caResults_output())
    results <- caResults_output()
    
    if (!is.null(results$error)) {
      return(NULL)
    }
    
    tagList(
      h4("Coordinates Interpretation"),
      p("Coordinates represent the positions of row and column categories in the reduced dimensional space."),
      br(),
      h4("Distance Interpretation"),
      p("Categories that are close together in the plot are more similar in their association patterns."),
      p("Categories that are far apart have different association patterns."),
      br(),
      h4("Axis Interpretation"),
      p("The first dimension typically represents the strongest pattern of association in the data."),
      p("Subsequent dimensions represent additional, orthogonal patterns.")
    )
  })
}

correspondenceAnalysisContributions <- function(caResults_output, caSigLvl_input) {
  renderUI({
    req(caResults_output())
    results <- caResults_output()
    
    if (!is.null(results$error)) {
      return(NULL)
    }
    
    tagList(
      h4("Contributions Interpretation"),
      p("Mass represents the relative frequency of each category in the data."),
      p("Inertia represents the contribution of each category to the total variation in the data."),
      br(),
      h4("Mass vs Inertia"),
      p("High mass categories are common in the data but may not contribute much to the structure."),
      p("High inertia categories contribute significantly to the dimensional structure, even if they are rare.")
    )
  })
}

correspondenceAnalysisPlot <- function(caResults_output, caSigLvl_input) {
  renderPlot({
    req(caResults_output())
    results <- caResults_output()
    
    if (!is.null(results$error)) {
      return(NULL)
    }
    
    # Create biplot
    coords <- results$coordinates
    
    # Separate row and column coordinates
    row_coords <- coords[coords$Type == "Row", ]
    col_coords <- coords[coords$Type == "Column", ]
    
    # Create plot
    ggplot() +
      # Row points
      geom_point(data = row_coords, 
                 aes_string(x = "Dim1", y = "Dim2"), 
                 color = "blue", size = 3, alpha = 0.7) +
      geom_text(data = row_coords, 
                aes_string(x = "Dim1", y = "Dim2", label = "Category"), 
                color = "blue", hjust = -0.2, vjust = -0.2) +
      # Column points
      geom_point(data = col_coords, 
                 aes_string(x = "Dim1", y = "Dim2"), 
                 color = "red", size = 3, alpha = 0.7) +
      geom_text(data = col_coords, 
                aes_string(x = "Dim1", y = "Dim2", label = "Category"), 
                color = "red", hjust = -0.2, vjust = -0.2) +
      # Reference lines
      geom_hline(yintercept = 0, linetype = "dashed", alpha = 0.5) +
      geom_vline(xintercept = 0, linetype = "dashed", alpha = 0.5) +
      labs(title = "Correspondence Analysis Biplot",
           x = sprintf("Dimension 1 (%.1f%%)", results$variance_explained[1]),
           y = sprintf("Dimension 2 (%.1f%%)", results$variance_explained[2])) +
      theme_minimal() +
      theme(legend.position = "none")
  })
}

correspondenceAnalysisInterpretation <- function(caResults_output, caSigLvl_input) {
  renderUI({
    req(caResults_output())
    results <- caResults_output()
    
    if (!is.null(results$error)) {
      return(NULL)
    }
    
    tagList(
      h4("Correspondence Analysis Interpretation"),
      p("Correspondence analysis is a technique for analyzing the association between categorical variables."),
      br(),
      h4("Key Concepts"),
      tags$ul(
        tags$li("Inertia: Measures the total variation in the contingency table"),
        tags$li("Coordinates: Position categories in a low-dimensional space"),
        tags$li("Mass: Relative frequency of each category"),
        tags$li("Contributions: How much each category contributes to the structure")
      ),
      br(),
      h4("Applications"),
      tags$ul(
        tags$li("Market research"),
        tags$li("Social science research"),
        tags$li("Text analysis"),
        tags$li("Ecology and biology")
      ),
      br(),
      h4("Interpretation Guidelines"),
      p("Categories close together have similar association patterns."),
      p("Categories far apart have different association patterns."),
      p("The first dimension captures the strongest association pattern."),
      p("Subsequent dimensions capture additional, orthogonal patterns.")
    )
  })
} 