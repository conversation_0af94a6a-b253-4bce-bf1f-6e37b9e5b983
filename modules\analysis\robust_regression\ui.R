RobustRegressionSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("robustUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
    selectizeInput(ns("robustResponse"), "Response Variable", choices = NULL),
    selectizeInput(ns("robustPredictors"), "Predictor Variables", choices = NULL, multiple = TRUE),
    selectInput(ns("robustMethod"), "Method", choices = c("Huber M-estimator", "MM-estimator", "Least Trimmed Squares")),
    br(),
    actionButton(ns("goRobust"), label = "Run Robust Regression", class = "act-btn")
  )
}

RobustRegressionMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns("robustError")),
    tableOutput(ns("robustResults")),
    plotOutput(ns("robustPlot"))
  )
}

RobustRegressionUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(RobustRegressionSidebarUI(id)),
    mainPanel(RobustRegressionMainUI(id))
  )
} 