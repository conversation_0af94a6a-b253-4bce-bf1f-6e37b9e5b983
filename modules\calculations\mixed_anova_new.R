# Mixed ANOVA calculation and output helpers

mixedAnovaUploadData_func <- function(mixedAnovaUserData) {
  ext <- tools::file_ext(mixedAnovaUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(mixedAnovaUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(mixedAnovaUserData$datapath),
         xlsx = readxl::read_xlsx(mixedAnovaUserData$datapath),
         txt = readr::read_tsv(mixedAnovaUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

mixedAnovaResults_func <- function(si_iv_is_valid, mixedAnovaFormat, mixedAnovaUploadData_output, mixedAnovaSubjectVariable, mixedAnovaDependentVariable, mixedAnovaBetweenVariable, mixedAnovaWithinVariable) {
  req(si_iv_is_valid)
  results <- list()
  
  if (mixedAnovaFormat == "Upload File") {
    req(mixedAnovaSubjectVariable, mixedAnovaDependentVariable, mixedAnovaBetweenVariable, mixedAnovaWithinVariable)
    data <- mixedAnovaUploadData_output
    data <- data.frame(
      Subject = data[[mixedAnovaSubjectVariable]],
      Dependent = data[[mixedAnovaDependentVariable]],
      Between = data[[mixedAnovaBetweenVariable]],
      Within = data[[mixedAnovaWithinVariable]]
    )
  } else {
    stop("Mixed ANOVA requires uploaded data with subject, dependent, between, and within variables")
  }
  
  # Validate inputs
  required_cols <- c("Subject", "Dependent", "Between", "Within")
  missing_cols <- required_cols[!required_cols %in% names(data)]
  if (length(missing_cols) > 0) {
    stop("Missing required columns: ", paste(missing_cols, collapse = ", "))
  }
  
  # Data summary
  n_subjects <- length(unique(data$Subject))
  n_between_groups <- length(unique(data$Between))
  n_within_levels <- length(unique(data$Within))
  n_observations <- nrow(data)
  n_missing <- sum(is.na(data$Dependent))
  
  # Remove missing values
  data <- data[complete.cases(data), ]
  
  # Check minimum requirements
  if (n_subjects < 3) {
    stop("At least 3 subjects are required")
  }
  
  if (n_between_groups < 2) {
    stop("At least 2 levels required for between-subjects factor")
  }
  
  if (n_within_levels < 2) {
    stop("At least 2 levels required for within-subjects factor")
  }
  
  results$data <- data
  results$n_subjects <- n_subjects
  results$n_between_groups <- n_between_groups
  results$n_within_levels <- n_within_levels
  results$n_observations <- n_observations
  results$n_missing <- n_missing
  return(results)
}

perform_mixed_anova <- function(data, sphericity_test = TRUE, greenhouse_geisser = TRUE, 
                               huynh_feldt = TRUE, effect_size = TRUE, conf_level = 0.95) {
  
  # Validate inputs
  required_cols <- c("Subject", "Dependent", "Between", "Within")
  missing_cols <- required_cols[!required_cols %in% names(data)]
  if (length(missing_cols) > 0) {
    stop("Missing required columns: ", paste(missing_cols, collapse = ", "))
  }
  
  # Data summary
  n_subjects <- length(unique(data$Subject))
  n_between_groups <- length(unique(data$Between))
  n_within_levels <- length(unique(data$Within))
  n_observations <- nrow(data)
  n_missing <- sum(is.na(data$Dependent))
  
  # Remove missing values
  data <- data[complete.cases(data), ]
  
  # Check minimum requirements
  if (n_subjects < 3) {
    stop("At least 3 subjects are required")
  }
  
  if (n_between_groups < 2) {
    stop("At least 2 levels required for between-subjects factor")
  }
  
  if (n_within_levels < 2) {
    stop("At least 2 levels required for within-subjects factor")
  }
  
  # Calculate descriptive statistics
  desc_stats <- data %>%
    group_by(Between, Within) %>%
    summarise(
      Mean = mean(Dependent, na.rm = TRUE),
      SD = sd(Dependent, na.rm = TRUE),
      SE = SD / sqrt(n()),
      n = n(),
      .groups = 'drop'
    ) %>%
    mutate(
      CI_Lower = Mean - qt(1 - (1 - conf_level) / 2, n - 1) * SE,
      CI_Upper = Mean + qt(1 - (1 - conf_level) / 2, n - 1) * SE
    )
  
  # Prepare data for ANOVA
  # Reshape to wide format for within-subjects analysis
  data_wide <- data %>%
    pivot_wider(
      id_cols = c(Subject, Between),
      names_from = Within,
      values_from = Dependent
    )
  
  # Extract within-subjects data
  within_cols <- setdiff(names(data_wide), c("Subject", "Between"))
  within_data <- as.matrix(data_wide[within_cols])
  
  # Perform mixed ANOVA using aov
  formula_str <- paste("Dependent ~ Between * Within + Error(Subject/Within)")
  mixed_model <- aov(as.formula(formula_str), data = data)
  
  # Extract ANOVA results
  anova_summary <- summary(mixed_model)
  
  # Create ANOVA table
  anova_table <- data.frame(
    Source = character(),
    SS = numeric(),
    df = numeric(),
    MS = numeric(),
    F = numeric(),
    p_value = numeric(),
    Partial_Eta_Squared = numeric(),
    stringsAsFactors = FALSE
  )
  
  # Extract results from summary
  for (i in 1:length(anova_summary)) {
    if (is.data.frame(anova_summary[[i]])) {
      table_part <- anova_summary[[i]]
      table_part$Source <- rownames(table_part)
      table_part$Partial_Eta_Squared <- table_part$`Sum Sq` / (table_part$`Sum Sq` + table_part$`Sum Sq`[nrow(table_part)])
      
      # Rename columns
      names(table_part) <- c("df", "SS", "MS", "F", "p_value", "Source", "Partial_Eta_Squared")
      
      anova_table <- rbind(anova_table, table_part[, c("Source", "SS", "df", "MS", "F", "p_value", "Partial_Eta_Squared")])
    }
  }
  
  # Create results list
  results <- list(
    n_subjects = n_subjects,
    n_between_groups = n_between_groups,
    n_within_levels = n_within_levels,
    n_observations = n_observations,
    n_missing = n_missing,
    data = data,
    desc_stats = desc_stats,
    anova_table = anova_table,
    model = mixed_model
  )
  
  return(results)
}

mixedAnovaTable <- function(mixedAnovaResults_output) {
  renderTable({
    req(mixedAnovaResults_output())
    results <- mixedAnovaResults_output()
    
    # Perform mixed ANOVA
    test_result <- perform_mixed_anova(results$data)
    
    # Format ANOVA table
    anova_table <- test_result$anova_table
    anova_table$SS <- round(anova_table$SS, 4)
    anova_table$MS <- round(anova_table$MS, 4)
    anova_table$F <- round(anova_table$F, 4)
    anova_table$p_value <- ifelse(anova_table$p_value < 0.0001, "< 0.0001", round(anova_table$p_value, 4))
    anova_table$Partial_Eta_Squared <- round(anova_table$Partial_Eta_Squared, 4)
    
    anova_table
  }, rownames = FALSE)
}

mixedAnovaDescriptive <- function(mixedAnovaResults_output) {
  renderTable({
    req(mixedAnovaResults_output())
    results <- mixedAnovaResults_output()
    
    # Perform mixed ANOVA
    test_result <- perform_mixed_anova(results$data)
    
    # Format descriptive statistics
    desc_stats <- test_result$desc_stats
    desc_stats$Mean <- round(desc_stats$Mean, 4)
    desc_stats$SD <- round(desc_stats$SD, 4)
    desc_stats$SE <- round(desc_stats$SE, 4)
    desc_stats$CI_Lower <- round(desc_stats$CI_Lower, 4)
    desc_stats$CI_Upper <- round(desc_stats$CI_Upper, 4)
    
    desc_stats
  }, rownames = FALSE)
}

mixedAnovaInteractionPlot <- function(mixedAnovaResults_output) {
  renderPlot({
    req(mixedAnovaResults_output())
    results <- mixedAnovaResults_output()
    
    # Perform mixed ANOVA
    test_result <- perform_mixed_anova(results$data)
    
    # Create interaction plot
    ggplot(test_result$desc_stats, aes(x = Within, y = Mean, color = Between, group = Between)) +
      geom_line(size = 1) +
      geom_point(size = 3) +
      geom_errorbar(aes(ymin = Mean - SE, ymax = Mean + SE), width = 0.1) +
      labs(title = "Mixed ANOVA: Interaction Plot",
           x = "Within-Subjects Factor", y = "Mean", color = "Between-Subjects Factor") +
      theme_minimal() +
      theme(legend.position = "bottom")
  })
}

mixedAnovaBoxplot <- function(mixedAnovaResults_output) {
  renderPlot({
    req(mixedAnovaResults_output())
    results <- mixedAnovaResults_output()
    
    # Create boxplot
    ggplot(results$data, aes(x = interaction(Between, Within), y = Dependent, fill = Between)) +
      geom_boxplot(alpha = 0.7) +
      labs(title = "Mixed ANOVA: Response by Factor Combinations",
           x = "Between × Within", y = "Dependent Variable", fill = "Between Factor") +
      theme_minimal() +
      theme(axis.text.x = element_text(angle = 45, hjust = 1))
  })
} 