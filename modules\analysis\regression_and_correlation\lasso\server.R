# Lasso Regression Server
# Variable selection with L1 regularization

source("modules/calculations/lasso_regression.R")

LassoRegressionServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactive values
    lasso_data <- reactiveVal(NULL)
    lasso_results <- reactiveVal(NULL)
    
    # File upload reactive
    lassoUploadData <- eventReactive(input$lassoUserData, {
      handle_file_upload(input$lassoUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(lassoUploadData(), {
      data <- lassoUploadData()
      # Clear selectizeInputs and main panel
      updateSelectizeInput(session, 'lassoResponseVariable', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'lassoPredictorVariables', choices = character(0), selected = NULL, server = TRUE)
      output$lassoResults <- renderUI({ NULL })
      
      # If data is valid, update choices
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'lassoResponseVariable', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'lassoPredictorVariables', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    lassoValidationErrors <- reactive({
      errors <- c()
      
      if (input$lassoDataMethod == "Manual Entry") {
        # Manual entry validation
        if (is.null(input$lassoManualData) || input$lassoManualData == "") {
          errors <- c(errors, "Manual data entry is required.")
        } else {
          # Basic validation for manual entry
          tryCatch({
            data <- parse_lasso_data(input$lassoManualData)
            if (nrow(data) < 10) {
              errors <- c(errors, "At least 10 observations are required for lasso regression.")
            }
            if (ncol(data) < 2) {
              errors <- c(errors, "At least 1 predictor variable is required.")
            }
          }, error = function(e) {
            errors <- c(errors, "Invalid manual data format. Please check your input.")
          })
        }
      } else {
        # File upload validation
        data <- lassoUploadData()
        if (is.null(data) || !is.data.frame(data)) {
          errors <- c(errors, "No data uploaded or file could not be read.")
          return(errors)
        }
        if (is.null(input$lassoResponseVariable) || input$lassoResponseVariable == "") {
          errors <- c(errors, "Please select a response variable.")
        } else {
          var_data <- data[[input$lassoResponseVariable]]
          if (!is.numeric(var_data)) {
            errors <- c(errors, "Response variable must be numeric.")
          }
          if (length(na.omit(var_data)) < 10) {
            errors <- c(errors, "At least 10 non-missing observations are required.")
          }
        }
        if (is.null(input$lassoPredictorVariables) || length(input$lassoPredictorVariables) == 0) {
          errors <- c(errors, "Please select at least one predictor variable.")
        } else {
          for (var in input$lassoPredictorVariables) {
            var_data <- data[[var]]
            if (!is.numeric(var_data)) {
              errors <- c(errors, paste("Predictor variable", var, "must be numeric."))
            }
          }
        }
      }
      
      errors
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goLasso, {
      output$lassoResults <- renderUI({
        errors <- lassoValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Lasso Regression", errors = errors)
        } else {
          tryCatch({
            # Prepare data
            if (input$lassoDataMethod == "Manual Entry") {
              # Parse manual entry data
              data <- parse_lasso_data(input$lassoManualData)
            } else {
              # Use uploaded data
              req(lassoUploadData(), input$lassoResponseVariable, input$lassoPredictorVariables)
              
              data <- lassoUploadData()
              response_var <- input$lassoResponseVariable
              predictor_vars <- input$lassoPredictorVariables
              
              # Select relevant columns
              data <- data[c(response_var, predictor_vars)]
            }
            
            # Remove rows with missing values
            complete_cases <- complete.cases(data)
            if (sum(complete_cases) < nrow(data)) {
              warning("Removing rows with missing values")
              data <- data[complete_cases, ]
            }
            
            # Check data structure
            if (ncol(data) < 2) {
              stop("At least 1 predictor variable is required")
            }
            
            if (nrow(data) < 10) {
              stop("At least 10 observations are required")
            }
            
            # Perform lasso regression
            results <- perform_lasso_regression(data, 
                                              cv_folds = input$lassoCVFolds,
                                              lambda_seq = input$lassoLambdaSeq,
                                              conf_level = input$lassoConfLevel)
            
            # Store results
            lasso_results(results)
            
            # Display results
            tagList(
              # Summary statistics
              h3("Lasso Regression Results"),
              
              # Model information
              fluidRow(
                column(6,
                  h4("Model Information"),
                  p(strong("Optimal Lambda:"), round(results$optimal_lambda, 4)),
                  p(strong("Lambda (1SE):"), round(results$lambda_1se, 4)),
                  p(strong("Variables Selected:"), results$n_selected, "of", results$n_total),
                  p(strong("Selected Variables:"), paste(results$selected_variables, collapse = ", "))
                ),
                column(6,
                  h4("Model Performance"),
                  p(strong("R-squared:"), round(results$performance$r_squared, 4)),
                  p(strong("Adjusted R-squared:"), round(results$performance$adj_r_squared, 4)),
                  p(strong("RMSE:"), round(results$performance$rmse, 4)),
                  p(strong("MAE:"), round(results$performance$mae, 4))
                )
              ),
              
              br(),
              
              # Coefficient table
              h4("Coefficients"),
              renderDataTable({
                coef_table <- results$coefficients
                coef_table$Coefficient <- round(coef_table$Coefficient, 4)
                coef_table$Coefficient_1SE <- round(coef_table$Coefficient_1SE, 4)
                coef_table$CI_Lower <- round(coef_table$CI_Lower, 4)
                coef_table$CI_Upper <- round(coef_table$CI_Upper, 4)
                
                datatable(coef_table, 
                         options = list(pageLength = 10, scrollX = TRUE),
                         rownames = FALSE) %>%
                  formatStyle("Selected", 
                             backgroundColor = styleEqual(c(TRUE, FALSE), c("#d4edda", "#f8d7da")))
              }),
              
              br(),
              
              # Cross-validation plot
              if (!is.null(results$cv_result)) tagList(
                h4("Cross-Validation Results"),
                renderPlot({
                  plot(results$cv_result, main = "Cross-Validation for Lambda Selection")
                  abline(v = log(results$optimal_lambda), col = "red", lty = 2)
                  abline(v = log(results$lambda_1se), col = "blue", lty = 2)
                  legend("topright", 
                         legend = c("Optimal Lambda", "Lambda (1SE)"),
                         col = c("red", "blue"), lty = 2)
                })
              ),
              
              br(),
              
              # Coefficient path plot
              h4("Coefficient Paths"),
              renderPlot({
                lambda_seq <- exp(seq(log(0.001), log(10), length.out = 100))
                path_model <- glmnet::glmnet(results$X, results$y, alpha = 1, lambda = lambda_seq)
                
                plot(path_model, xvar = "lambda", main = "Coefficient Paths")
                abline(v = log(results$optimal_lambda), col = "red", lty = 2)
                abline(v = log(results$lambda_1se), col = "blue", lty = 2)
              }),
              
              br(),
              
              # Residuals plot
              h4("Model Diagnostics"),
              fluidRow(
                column(6,
                  renderPlot({
                    plot(results$predictions, results$residuals, 
                         main = "Residuals vs Fitted",
                         xlab = "Fitted Values", ylab = "Residuals")
                    abline(h = 0, col = "red", lty = 2)
                  })
                ),
                column(6,
                  renderPlot({
                    qqnorm(results$residuals, main = "Q-Q Plot of Residuals")
                    qqline(results$residuals, col = "red")
                  })
                )
              ),
              
              br(),
              
              # Educational content
              h4("Interpretation"),
              p(strong("Lasso Regression:"), "Uses L1 penalty to perform variable selection by shrinking some coefficients to exactly zero."),
              p(strong("Lambda:"), "Controls the strength of regularization. Larger values result in more variables being excluded."),
              p(strong("Cross-Validation:"), "Helps select the optimal lambda value that balances model fit and complexity."),
              p(strong("Variable Selection:"), "Variables with non-zero coefficients are considered important predictors.")
            )
          }, error = function(e) {
            errorScreenUI(title = "Error in Lasso Regression Analysis", errors = e$message)
          })
        }
      })
    })
    
    # Helper function to parse numeric input
    parse_lasso_data <- function(input_text) {
      if (is.null(input_text) || input_text == "") {
        stop("Please enter data")
      }
      
      # Parse the input text into a data frame
      lines <- strsplit(input_text, "\n")[[1]]
      lines <- lines[lines != ""]
      
      if (length(lines) < 2) {
        stop("At least 2 lines required (header + data)")
      }
      
      # Parse header
      header <- strsplit(lines[1], ",")[[1]]
      header <- trimws(header)
      
      # Parse data
      data_lines <- lines[-1]
      data_matrix <- matrix(NA, nrow = length(data_lines), ncol = length(header))
      
      for (i in 1:length(data_lines)) {
        values <- strsplit(data_lines[i], ",")[[1]]
        values <- trimws(values)
        values <- as.numeric(values)
        
        if (length(values) != length(header)) {
          stop(paste("Line", i + 1, "has incorrect number of values"))
        }
        
        data_matrix[i, ] <- values
      }
      
      data.frame(data_matrix, stringsAsFactors = FALSE)
    }
  })
} 