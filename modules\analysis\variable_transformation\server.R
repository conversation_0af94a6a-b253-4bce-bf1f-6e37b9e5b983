VariableTransformationServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    vtData <- eventReactive(input$vtUserData, {
      handle_file_upload(input$vtUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(vtData(), {
      data <- vtData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'vtVars', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    vtValidationErrors <- reactive({
      errors <- c()
      data <- vtData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$vtVars) || length(input$vtVars) == 0) {
        errors <- c(errors, "Select at least one variable to transform.")
      }
      
      # Check if selected variables are numeric
      if (!is.null(input$vtVars) && length(input$vtVars) > 0) {
        for (var in input$vtVars) {
          if (!is.numeric(data[[var]])) {
            errors <- c(errors, sprintf("Variable '%s' must be numeric.", var))
          }
        }
      }
      
      # Check method-specific requirements
      if (!is.null(input$vtMethod)) {
        if (input$vtMethod == "Log" || input$vtMethod == "Square Root") {
          for (var in input$vtVars) {
            if (any(data[[var]] <= 0, na.rm = TRUE)) {
              errors <- c(errors, sprintf("Variable '%s' contains non-positive values for %s transformation.", var, input$vtMethod))
            }
          }
        }
        if (input$vtMethod == "Box-Cox" && !requireNamespace("MASS", quietly = TRUE)) {
          errors <- c(errors, "Package 'MASS' is required for Box-Cox transformation.")
        }
        if (input$vtMethod == "Logit") {
          for (var in input$vtVars) {
            if (any(data[[var]] <= 0 | data[[var]] >= 1, na.rm = TRUE)) {
              errors <- c(errors, sprintf("Variable '%s' must be between 0 and 1 for logit transformation.", var))
            }
          }
        }
      }
      
      errors
    })
    
    # Variable transformation analysis reactive
    vtResult <- eventReactive(input$goVT, {
      data <- vtData()
      req(data, input$vtVars, input$vtMethod)
      
      # Remove rows with missing values
      complete_data <- data[complete.cases(data[, input$vtVars, drop = FALSE]), ]
      
      if (nrow(complete_data) < 2) {
        stop("Insufficient complete cases for transformation.")
      }
      
      # Perform variable transformation
      variable_transformation(complete_data, input$vtVars, input$vtMethod)
    })
    
    # Error handling
    output$vtError <- renderUI({
      errors <- vtValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          vtResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "Variable Transformation Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$vtModelSummary <- renderUI({
      req(vtResult())
      res <- vtResult()
      
      tagList(
        h4("Variable Transformation Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Metric = c("Transformation Method", "Number of Variables", "Number of Observations", "Transformation Applied"),
            Value = c(
              input$vtMethod,
              length(input$vtVars),
              res$n_observations,
              "Yes"
            )
          )
        }),
        h4("Transformation Quality Metrics"),
        renderTable({
          data.frame(
            Metric = c("Normality Improvement", "Skewness Reduction", "Variance Stabilization", "Outlier Impact"),
            Value = c(
              ifelse(!is.null(res$normality_improvement), round(res$normality_improvement, 4), "N/A"),
              ifelse(!is.null(res$skewness_reduction), round(res$skewness_reduction, 4), "N/A"),
              ifelse(!is.null(res$variance_stabilization), round(res$variance_stabilization, 4), "N/A"),
              ifelse(!is.null(res$outlier_impact), round(res$outlier_impact, 4), "N/A")
            )
          )
        })
      )
    })
    
    output$vtPlot <- renderPlot({
      req(vtResult())
      res <- vtResult()
      
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # Before and after comparison
      if (!is.null(res$original_data) && !is.null(res$transformed)) {
        for (i in 1:min(2, length(input$vtVars))) {
          var <- input$vtVars[i]
          if (var %in% names(res$original_data) && var %in% names(res$transformed)) {
            hist(res$original_data[[var]], main = paste("Original:", var), 
                 xlab = var, freq = FALSE, col = "lightblue")
            hist(res$transformed[[var]], main = paste("Transformed:", var), 
                 xlab = paste(input$vtMethod, var), freq = FALSE, col = "lightgreen")
          }
        }
      }
      
      # Q-Q plots
      if (!is.null(res$transformed)) {
        for (i in 1:min(2, length(input$vtVars))) {
          var <- input$vtVars[i]
          if (var %in% names(res$transformed)) {
            qqnorm(res$transformed[[var]], main = paste("Q-Q Plot:", input$vtMethod, var))
            qqline(res$transformed[[var]], col = "red")
          }
        }
      }
      
      par(mfrow = c(1, 1))
    })
    
    output$vtDiagnostics <- renderUI({
      req(vtResult())
      res <- vtResult()
      
      tagList(
        h4("Transformation Diagnostics"),
        renderTable({
          data.frame(
            Metric = c("Method", "Success", "Number of Variables", "Computational Time", "Transformation Quality"),
            Value = c(
              input$vtMethod,
              "Yes",
              length(input$vtVars),
              ifelse(!is.null(res$computation_time), paste(round(res$computation_time, 2), "seconds"), "N/A"),
              ifelse(!is.null(res$quality_score), round(res$quality_score, 4), "N/A")
            )
          )
        }),
        h4("Transformed Data Summary"),
        renderTable({
          if (!is.null(res$transformed_summary)) {
            res$transformed_summary
          } else {
            data.frame(
              Variable = input$vtVars,
              Original_Mean = sapply(input$vtVars, function(v) round(mean(res$original_data[[v]], na.rm = TRUE), 4)),
              Transformed_Mean = sapply(input$vtVars, function(v) round(mean(res$transformed[[v]], na.rm = TRUE), 4)),
              Original_SD = sapply(input$vtVars, function(v) round(sd(res$original_data[[v]], na.rm = TRUE), 4)),
              Transformed_SD = sapply(input$vtVars, function(v) round(sd(res$transformed[[v]], na.rm = TRUE), 4)),
              stringsAsFactors = FALSE
            )
          }
        })
      )
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$vtDataSummary <- renderUI({
      req(vtData(), input$vtVars)
      data <- vtData()
      vars <- input$vtVars
      
      tagList(
        h4("Dataset Summary"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "Variables to Transform", "Complete Cases"),
            Value = c(
              nrow(data),
              ncol(data),
              length(vars),
              sum(complete.cases(data[, vars]))
            )
          )
        }),
        h4("Variable Summary"),
        renderTable({
          data.frame(
            Variable = vars,
            Mean = sapply(vars, function(v) round(mean(data[[v]], na.rm = TRUE), 4)),
            SD = sapply(vars, function(v) round(sd(data[[v]], na.rm = TRUE), 4)),
            Min = sapply(vars, function(v) round(min(data[[v]], na.rm = TRUE), 4)),
            Max = sapply(vars, function(v) round(max(data[[v]], na.rm = TRUE), 4)),
            Skewness = sapply(vars, function(v) {
              x <- data[[v]]
              x <- x[!is.na(x)]
              if (length(x) > 2) {
                round((mean((x - mean(x))^3) / (sd(x)^3)), 4)
              } else {
                "N/A"
              }
            }),
            Missing = sapply(vars, function(v) sum(is.na(data[[v]]))),
            stringsAsFactors = FALSE
          )
        })
      )
    })
    
    output$vtAssumptions <- renderUI({
      req(vtResult())
      res <- vtResult()
      
      tagList(
        h4("Transformation Assumptions Check"),
        renderTable({
          data.frame(
            Assumption = c("Numeric Data", "Adequate Sample Size", "Valid Transformation", "Data Range Appropriate"),
            Status = c(
              "Pass",
              ifelse(res$n_observations >= 5, "Pass", "Fail"),
              "Pass",
              "Pass"
            ),
            Description = c(
              "Variables are measured on interval or ratio scale",
              "Sufficient observations for stable transformation",
              "Transformation method is appropriate for data type",
              "Data range is suitable for chosen transformation"
            )
          )
        }),
        h4("Transformation Guidelines"),
        renderTable({
          data.frame(
            Transformation = c("Log", "Square Root", "Box-Cox", "Z-score", "Min-Max", "Logit"),
            Use_When = c(
              "Data is right-skewed, positive values only",
              "Data is right-skewed, non-negative values",
              "Data needs normality, positive values",
              "Standardization needed, any numeric data",
              "Scaling to [0,1] range needed",
              "Proportions need to be transformed to real line"
            ),
            Assumptions = c(
              "Positive values only",
              "Non-negative values",
              "Positive values",
              "None",
              "None",
              "Values between 0 and 1"
            )
          )
        })
      )
    })
    
    output$vtDiagnosticPlots <- renderPlot({
      req(vtResult())
      res <- vtResult()
      
      par(mfrow = c(2, 2))
      
      # Original vs transformed comparison
      if (!is.null(res$original_data) && !is.null(res$transformed)) {
        for (i in 1:min(2, length(input$vtVars))) {
          var <- input$vtVars[i]
          if (var %in% names(res$original_data) && var %in% names(res$transformed)) {
            plot(res$original_data[[var]], res$transformed[[var]], 
                 main = paste("Original vs", input$vtMethod, "Transformed"),
                 xlab = paste("Original", var), ylab = paste(input$vtMethod, var), pch = 19)
          }
        }
      }
      
      # Q-Q plots for normality
      if (!is.null(res$transformed)) {
        for (i in 1:min(2, length(input$vtVars))) {
          var <- input$vtVars[i]
          if (var %in% names(res$transformed)) {
            qqnorm(res$transformed[[var]], main = paste("Q-Q Plot:", input$vtMethod, var))
            qqline(res$transformed[[var]], col = "red")
          }
        }
      }
      
      par(mfrow = c(1, 1))
    })
    
    # Uploaded Data Tab Outputs
    output$vtDataTable <- renderDT({
      req(vtData())
      data <- vtData()
      
      DT::datatable(
        data,
        options = list(
          pageLength = 10,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
        ),
        extensions = 'Buttons',
        filter = 'top',
        rownames = FALSE
      )
    })
    
    output$vtDataInfo <- renderUI({
      req(vtData())
      data <- vtData()
      
      tagList(
        h4("Dataset Information"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "File Name"),
            Value = c(nrow(data), ncol(data), 
                     ifelse(!is.null(input$vtUserData), input$vtUserData$name, "Unknown"))
          )
        }),
        h4("Variable Information"),
        renderTable({
          data.frame(
            Variable = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Unique = sapply(data, function(x) length(unique(x)))
          )
        })
      )
    })
    
    # Transformed Data Outputs
    output$vtTransformedData <- renderDT({
      req(vtResult())
      res <- vtResult()
      
      if (!is.null(res$transformed)) {
        DT::datatable(
          res$transformed,
          options = list(
            pageLength = 10,
            scrollX = TRUE,
            dom = 'Bfrtip',
            buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
          ),
          extensions = 'Buttons',
          filter = 'top',
          rownames = FALSE
        )
      } else {
        DT::datatable(
          data.frame(Message = "No transformed data available"),
          options = list(pageLength = 1),
          rownames = FALSE
        )
      }
    })
  })
} 