# Anderson-Darling Test calculation and output helpers

anderson_darling_uploadData_func <- function(adUserData) {
  ext <- tools::file_ext(adUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(adUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(adUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(adUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(adUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

anderson_darling_results_func <- function(data, var, distribution = "norm") {
  tryCatch({
    if (!requireNamespace("nortest", quietly = TRUE)) {
      stop("Package 'nortest' needed for Anderson-Darling test.")
    }
    
    x <- data[[var]]
    x <- x[!is.na(x)]
    
    if (length(x) < 3) {
      stop("At least 3 observations are required.")
    }
    
    test_result <- nortest::ad.test(x)
    
    list(
      test = test_result,
      data = x,
      var = var,
      distribution = distribution,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Anderson-Darling test calculation:", e$message))
  })
}

anderson_darling_ht_html <- function(results, sigLvl) {
  test <- results$test
  p_value <- test$p.value
  
  conclusion <- if (p_value < sigLvl) "does not follow a normal distribution" else "follows a normal distribution"
  
  withMathJax(tagList(
    h4("Anderson-Darling Test for Normality"),
    p(sprintf("The data likely %s.", conclusion)),
    p(sprintf("Test Statistic (A): %.4f", test$statistic)),
    p(sprintf("P-value: %.4f", p_value))
  ))
}

anderson_darling_summary_html <- function(results) {
  desc_stats <- data.frame(
    Variable = results$var,
    N = length(results$data),
    Mean = mean(results$data),
    SD = sd(results$data)
  )
  
  tagList(
    h4("Test Details"),
    renderPrint(results$test),
    h4("Descriptive Statistics"),
    renderTable(desc_stats, digits = 4)
  )
}

anderson_darling_plot <- function(results) {
  plot_data <- data.frame(
    Value = results$data
  )
  
  ggplot(plot_data, aes(sample = Value)) +
    stat_qq() +
    stat_qq_line() +
    labs(title = "Q-Q Plot for Normality",
         x = "Theoretical Quantiles",
         y = "Sample Quantiles") +
    theme_minimal()
}