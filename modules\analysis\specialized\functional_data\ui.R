# Placeholder for Functional Data Analysis UI
functionalDataSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("fdaUserData"), "Upload your Data (.csv or .xls or .xlsx or .txt)", accept = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")),
    uiOutput(ns("fdaColSelectors")),
    actionButton(ns("goFDA"), label = "Calculate", class = "act-btn")
  )
}

functionalDataMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('fdaHT')),
    plotOutput(ns('fdaPlot'), width = "50%", height = "400px"),
    uiOutput(ns('fdaConclusionOutput'))
  )
}

functionalDataUI <- function(id) {
  ns <- NS(id)
  tagList(
    functionalDataSidebarUI(id),
    functionalDataMainUI(id),
    tabsetPanel(
      id = ns("fdaTabset"),
      selected = "Analysis",
      tabPanel(
        id    = ns("fdaAnalysis"),
        title = "Functional Data Analysis",
        titlePanel("Functional Data Analysis"),
        br(),
        uiOutput(ns('fdaHT')),
        br(),
        plotOutput(ns('fdaPlot'), width = "50%", height = "400px"),
        br(),
        uiOutput(ns('fdaConclusionOutput'))
      ),
      tabPanel(
        id    = ns("fdaData"),
        title = "Uploaded Data",
        uiOutput(ns("renderFDAData"))
      )
    )
  )
} 