# Data Quality Assessment UI

dataQualitySidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    h4("Data Quality Assessment"),
    helpText("Upload a data file (CSV, TXT, XLS, XLSX) to assess missing values, duplicates, outliers, and data type consistency. Click 'Calculate' to view results."),
    fileInput(ns("dqUserData"), "Upload your Data", accept = c(".csv", ".txt", ".xls", ".xlsx")),
    actionButton(ns("goDQ"), label = "Calculate", class = "act-btn")
  )
}

dataQualityMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('dqHT')),
    br(),
    plotOutput(ns('dqPlot'), width = "60%", height = "300px"),
    br(),
    uiOutput(ns('dqConclusionOutput'))
  )
}

dataQualityUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(
      fileInput(ns("dqUserData"), "Upload your Data", accept = c(".csv", ".txt", ".xls", ".xlsx")),
      actionButton(ns("goDQ"), label = "Calculate", class = "act-btn")
    ),
    mainPanel(
      textOutput(ns('dqError')),
      plotOutput(ns('dqPlot'), width = "60%", height = "300px"),
      tableOutput(ns('dqSummary')),
      textOutput(ns('dqConclusion')),
      tableOutput(ns('dqData'))
    )
  )
} 