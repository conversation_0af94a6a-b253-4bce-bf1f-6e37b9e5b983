SpatialAnalysisServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    data <- reactiveVal(NULL)
    results <- reactiveVal(NULL)

    # Load data and update column choices
    observe({
      req(input$data_file)
      df <- read.csv(input$data_file$datapath, stringsAsFactors = FALSE)
      data(df)
      updateSelectInput(session, "variable", choices = names(df))
      updateSelectInput(session, "dependent_var", choices = names(df))
      updateSelectInput(session, "independent_vars", choices = names(df))
      updateSelectInput(session, "x_coord", choices = names(df))
      updateSelectInput(session, "y_coord", choices = names(df))
    })

    # Run spatial analysis
    observeEvent(input$run_analysis, {
      req(data(), input$analysis_type)
      df <- data()
      res <- switch(input$analysis_type,
        autocorrelation = spatial_autocorrelation_analysis(df, input$variable, input$x_coord, input$y_coord, input$autocorr_method, input$weight_matrix, input$k_neighbors, input$distance_threshold),
        regression = spatial_regression_analysis(df, input$dependent_var, input$independent_vars, input$x_coord, input$y_coord, input$regression_type, input$bandwidth, input$bandwidth_method),
        point_pattern = spatial_point_pattern_analysis(df, input$x_coord, input$y_coord, input$point_method, input$max_distance, input$n_simulations),
        clustering = spatial_clustering_analysis(df, input$x_coord, input$y_coord, input$clustering_method, input$n_clusters, input$eps),
        hotspot = spatial_hotspot_analysis(df, input$variable, input$x_coord, input$y_coord, input$hotspot_method, input$kernel_bandwidth, input$kernel_type),
        interpolation = spatial_interpolation_analysis(df, input$variable, input$x_coord, input$y_coord, input$interpolation_method, input$idw_power, input$variogram_model),
        buffer = spatial_buffer_analysis(df, input$x_coord, input$y_coord, input$buffer_distance, input$buffer_units, input$dissolve_buffers),
        overlay = spatial_overlay_analysis(df, input$x_coord, input$y_coord, input$crs),
        NULL
      )
      results(res)
    })

    # Output handlers (placeholders)
    output$spatial_map <- renderLeaflet({ req(results()); plot_spatial_map(results()) })
    output$autocorr_table <- renderTable({ req(results()); results()$autocorr_table })
    output$moran_scatter <- renderPlot({ req(results()); plot_moran_scatter(results()) })
    output$lisa_map <- renderPlot({ req(results()); plot_lisa_map(results()) })
    output$autocorr_plot <- renderPlot({ req(results()); plot_autocorr_plot(results()) })
    output$regression_summary <- renderTable({ req(results()); results()$regression_summary })
    output$regression_diagnostics <- renderPlot({ req(results()); plot_regression_diagnostics(results()) })
    output$spatial_residuals <- renderPlot({ req(results()); plot_spatial_residuals(results()) })
    output$regression_coefficients <- renderTable({ req(results()); results()$regression_coefficients })
    output$point_pattern_table <- renderTable({ req(results()); results()$point_pattern_table })
    output$ripley_plot <- renderPlot({ req(results()); plot_ripley_plot(results()) })
    output$nearest_neighbor_plot <- renderPlot({ req(results()); plot_nearest_neighbor_plot(results()) })
    output$quadrant_plot <- renderPlot({ req(results()); plot_quadrant_plot(results()) })
    output$clustering_summary <- renderTable({ req(results()); results()$clustering_summary })
    output$cluster_map <- renderPlot({ req(results()); plot_cluster_map(results()) })
    output$cluster_diagnostics <- renderPlot({ req(results()); plot_cluster_diagnostics(results()) })
    output$cluster_centers <- renderTable({ req(results()); results()$cluster_centers })
    output$hotspot_table <- renderTable({ req(results()); results()$hotspot_table })
    output$hotspot_map <- renderPlot({ req(results()); plot_hotspot_map(results()) })
    output$kernel_density <- renderPlot({ req(results()); plot_kernel_density(results()) })
    output$hotspot_significance <- renderPlot({ req(results()); plot_hotspot_significance(results()) })
    output$interpolation_summary <- renderTable({ req(results()); results()$interpolation_summary })
    output$interpolation_map <- renderPlot({ req(results()); plot_interpolation_map(results()) })
    output$variogram_plot <- renderPlot({ req(results()); plot_variogram_plot(results()) })
    output$cross_validation <- renderPlot({ req(results()); plot_cross_validation(results()) })
    output$buffer_summary <- renderTable({ req(results()); results()$buffer_summary })
    output$buffer_map <- renderPlot({ req(results()); plot_buffer_map(results()) })
    output$buffer_statistics <- renderTable({ req(results()); results()$buffer_statistics })
    output$spatial_statistics <- renderTable({ req(results()); results()$spatial_statistics })
    output$spatial_distribution <- renderPlot({ req(results()); plot_spatial_distribution(results()) })
    output$distance_analysis <- renderPlot({ req(results()); plot_distance_analysis(results()) })
    output$spatial_summary <- renderTable({ req(results()); results()$spatial_summary })

    # Download handlers
    output$download_results <- downloadHandler(
      filename = function() paste0("spatial_results_", Sys.Date(), ".csv"),
      content = function(file) { req(results()); write.csv(results()$output, file, row.names = FALSE) }
    )
    output$download_map <- downloadHandler(
      filename = function() paste0("spatial_map_", Sys.Date(), ".html"),
      content = function(file) { req(results()); save_spatial_map(results(), file) }
    )
  })
} 