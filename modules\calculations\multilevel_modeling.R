# Multilevel Modeling calculation and output helpers

multilevel_modeling_uploadData_func <- function(mlmUserData) {
  ext <- tools::file_ext(mlmUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(mlmUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(mlmUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(mlmUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(mlmUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

multilevel_modeling_results_func <- function(data, response, predictors, random_effects, ...) {
  tryCatch({
    
    results <- multilevel_modeling_analysis(data, response, predictors, random_effects, ...)
    
    list(
      results = results,
      data = data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Multilevel Modeling calculation:", e$message))
  })
}

multilevel_modeling_ht_html <- function(results) {
  icc_info <- results$results$icc
  icc_text <- paste("Intraclass Correlation Coefficient (ICC):", round(unlist(icc_info), 3), collapse = ", ")
  
  tagList(
    h4("Multilevel Model"),
    p(icc_text)
  )
}

multilevel_modeling_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  model <- results$results$model
  out <- list(
    h4("Fixed Effects"),
    renderTable(results$results$fixed_effects, rownames = TRUE),
    h4("Random Effects (Variance Components)"),
    renderPrint(results$results$random_effects)
  )
  # Add fit indices and warnings if available
  if (!is.null(model) && !is.null(attr(model, "converged"))) {
    out <- c(out, list(paste("Converged:", attr(model, "converged"))))
  }
  if (!is.null(model) && !is.null(warnings <- warnings())) {
    out <- c(out, list(h4("Warnings"), renderPrint(warnings)))
  }
  tagList(out)
}

multilevel_modeling_plot <- function(results) {
  generate_mlm_diagnostic_plots(results$results)
}

# --- Helper functions from original file ---

multilevel_modeling_analysis <- function(data, response, predictors, random_effects, 
                                        level2_predictors = NULL, random_slopes = FALSE,
                                        random_slope_vars = NULL, family = "gaussian",
                                        link = "identity", center_predictors = TRUE,
                                        scale_predictors = FALSE, alpha = 0.05) {
  
  if (!require(lme4, quietly = TRUE)) {
    stop("lme4 package is required for multilevel modeling")
  }
  
  model_data <- data
  
  if (center_predictors || scale_predictors) {
    for (pred in predictors) {
      if (is.numeric(model_data[[pred]])) {
        model_data[[pred]] <- scale(model_data[[pred]], 
                                   center = center_predictors, 
                                   scale = scale_predictors)
      }
    }
  }
  
  fixed_effects <- paste(predictors, collapse = " + ")
  random_effects_formula <- paste0("(", "1 | ", random_effects, ")", collapse = " + ")
  
  if (random_slopes && !is.null(random_slope_vars)) {
    random_slopes_formula <- paste0("(", random_slope_vars, " | ", random_effects, ")", collapse = " + ")
    random_effects_formula <- paste(random_effects_formula, random_slopes_formula, sep = " + ")
  }
  
  formula_str <- paste(response, "~", fixed_effects, "+", random_effects_formula)
  
  if (family == "gaussian") {
    model <- lmer(as.formula(formula_str), data = model_data)
  } else {
    family_fun <- get(family, mode = "function", envir = as.environment("package:stats"))
    model <- glmer(as.formula(formula_str), data = model_data, family = family_fun(link = link))
  }
  
  icc_results <- calculate_icc_multilevel(model, random_effects)
  
  fixed_effects_summary <- as.data.frame(summary(model)$coefficients)
  names(fixed_effects_summary) <- c("Estimate", "Std. Error", "t value", "Pr(>|t|)")
  
  list(
    model = model,
    formula = formula_str,
    fixed_effects = fixed_effects_summary,
    random_effects = VarCorr(model),
    icc = icc_results
  )
}

calculate_icc_multilevel <- function(model, random_effects) {
  vc <- as.data.frame(VarCorr(model))
  
  # Find residual variance
  resid_var <- vc[vc$grp == "Residual", "vcov"]
  
  # Find random intercept variances
  rand_vars <- vc[grepl("(Intercept)", vc$var1) & vc$grp %in% random_effects, "vcov"]
  
  # Calculate ICC
  icc <- sum(rand_vars) / (sum(rand_vars) + resid_var)
  
  return(icc)
}

generate_mlm_diagnostic_plots <- function(results) {
  if (!requireNamespace("sjPlot", quietly = TRUE)) {
    # Fallback: basic diagnostic plots
    model <- results$model
    if (!is.null(model)) {
      par(mfrow = c(1, 2))
      plot(fitted(model), residuals(model), main = "Residuals vs Fitted", xlab = "Fitted", ylab = "Residuals")
      abline(h = 0, col = "red")
      plot(fitted(model), results$data[[1]], main = "Observed vs Fitted", xlab = "Fitted", ylab = "Observed")
      abline(0, 1, col = "blue")
      par(mfrow = c(1, 1))
    } else {
      plot.new(); title("No model to plot.")
    }
    return(p("sjPlot not available. Showing basic diagnostic plots."))
  }
  sjPlot::plot_model(results$model, type = "diag")
}