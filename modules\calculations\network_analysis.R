# Network Analysis calculation and output helpers

network_analysis_uploadData_func <- function(netUserData) {
  ext <- tools::file_ext(netUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(netUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(netUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(netUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(netUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

network_analysis_results_func <- function(data, metric = "degree", directed = FALSE) {
  tryCatch({
    if (!requireNamespace("igraph", quietly = TRUE)) {
      stop("Package 'igraph' required.")
    }
    
    g <- igraph::graph_from_data_frame(data, directed = directed)
    
    res <- switch(metric,
      "degree" = igraph::degree(g),
      "betweenness" = igraph::betweenness(g),
      "closeness" = igraph::closeness(g),
      "eigenvector" = igraph::eigen_centrality(g)$vector,
      "clustering" = igraph::transitivity(g, type = "local"),
      "community" = igraph::membership(igraph::cluster_louvain(g)),
      stop("Unknown metric.")
    )
    
    list(
      graph = g,
      results = res,
      metric = metric,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Network Analysis:", e$message))
  })
}

network_analysis_ht_html <- function(results) {
  tagList(
    h4("Network Analysis"),
    p(paste("Metric calculated:", results$metric))
  )
}

network_analysis_summary_html <- function(results) {
  tagList(
    h4("Centrality/Community Results"),
    renderPrint(summary(results$results))
  )
}

network_analysis_plot <- function(results) {
  plot(results$graph, main = paste("Network -", results$metric))
}