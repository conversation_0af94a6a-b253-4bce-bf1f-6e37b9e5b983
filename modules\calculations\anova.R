# ANOVA calculation and output helpers

anova_uploadData_func <- function(anovaUserData) {
  ext <- tools::file_ext(anovaUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(anovaUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(anovaUserData$datapath),
         xlsx = readxl::read_xlsx(anovaUserData$datapath),
         txt = readr::read_tsv(anovaUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

anova_results_func <- function(data, format, multi_columns, response_var, factor_var) {
  tryCatch({
    # --- Data Preparation ---
    if (format == "Multiple") {
      anovaData <- stack(data[, multi_columns, drop = FALSE])
      factorCol <- "ind"
      factorNames <- levels(anovaData[, factorCol])
    } else {
      anovaData <- data
      colnames(anovaData)[colnames(anovaData) == factor_var] <- "ind"
      colnames(anovaData)[colnames(anovaData) == response_var] <- "values"
      anovaData <- anovaData %>% dplyr::mutate(ind = factor(ind))
      factorCol <- "ind"
      factorNames <- levels(anovaData$ind)
    }
    anovaData <- na.omit(anovaData)
    
    # --- Core Test ---
    anovaTest <- aov(formula = values ~ ind, data = anovaData)
    anova_summary <- anova(anovaTest)
    
    # --- Supplementary Calculations ---
    # Effect Size (Eta-squared)
    ss_total <- sum((anovaData$values - mean(anovaData$values))^2)
    ss_between <- sum(anova_summary$`Sum Sq`)
    eta_squared <- ss_between / ss_total
    
    # Descriptive Statistics
    desc_stats <- anovaData %>%
      dplyr::group_by(ind) %>%
      dplyr::summarise(
        N = n(),
        Mean = mean(values),
        SD = sd(values),
        SE = sd(values) / sqrt(n()),
        .groups = 'drop'
      )
      
    # Normality Test (Shapiro-Wilk)
    normality_test <- shapiro.test(residuals(anovaTest))
      
    # --- Return Results ---
    list(
      data = anovaData,
      count = nrow(anovaData),
      numFactors = length(factorNames),
      factorNames = factorNames,
      fit = anovaTest,
      test = anova_summary,
      effect_size = eta_squared,
      desc_stats = desc_stats,
      normality_test = normality_test,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during ANOVA calculation:", e$message))
  })
}

anova_render_ht_html <- function(results, sigLvl) {
  test_summary <- results$test
  critVal <- round(qf(1 - sigLvl, df1 = test_summary[1,"Df"], df2 = test_summary[2,"Df"]), 4)
  
  if(test_summary[1,"Pr(>F)"] < sigLvl) {
    reject <- "reject"
  } else {
    reject <- "do not reject"
  }
  
  # Effect size interpretation
  eta_squared <- results$effect_size
  effect_interp <- if (eta_squared < 0.01) {
    "Negligible effect size"
  } else if (eta_squared < 0.06) {
    "Small effect size"
  } else if (eta_squared < 0.14) {
    "Medium effect size"
  } else {
    "Large effect size"
  }
  
  # Conclusion
  conclusion_text <- if (reject == "reject") {
    "there is sufficient statistical evidence in support of the alternative hypothesis ($H_{a}$) that at least two means differ and post hoc tests are warranted."
  } else {
    "there is not enough statistical evidence in support of the alternative hypothesis ($H_{a}$) that at least two means differ."
  }
  
  withMathJax(
    tagList(
      h4("Hypotheses"),
      p(paste0("$H_{0}: ", paste(paste0("\\mu_{", results$factorNames, "}"), collapse = " = "), "$")),
      p("$H_{a}:$ At least two means differ"),
      p(sprintf("$\\alpha = %s$", sigLvl)),
      br(),
      h4("Effect Size"),
      p(sprintf("Eta-squared effect size: %.4f", eta_squared)),
      p(effect_interp),
      br(),
      h4("Assumptions Check"),
      p("1. Independent observations"),
      p("2. Normal distribution within groups"),
      p("3. Homogeneity of variances"),
      br(),
      h4("Conclusion"),
      p(sprintf("At the %.0f%% significance level, %s", sigLvl*100, conclusion_text))
    )
  )
}

anova_render_summary_html <- function(results) {
  anova_table <- as.data.frame(results$test)
  anova_table$Source <- rownames(anova_table)
  anova_table <- anova_table[, c("Source", "Df", "Sum Sq", "Mean Sq", "F value", "Pr(>F)")]
  
  normality_results <- data.frame(
    Test = "Shapiro-Wilk",
    Statistic = results$normality_test$statistic,
    P_value = results$normality_test$p.value
  )
  
  tagList(
    h4("Descriptive Statistics by Group"),
    renderTable(results$desc_stats, digits = 4),
    br(),
    h4("ANOVA Table"),
    renderTable(anova_table, digits = 4),
    br(),
    h4("Normality of Residuals"),
    renderTable(normality_results, digits = 4)
  )
}

anova_plot <- function(results) {
  ggplot(results$data, aes(x = ind, y = values, fill = ind)) +
    geom_boxplot(alpha = 0.7) +
    labs(title = "Boxplot of Response by Group",
         x = "Group",
         y = "Response") +
    theme_minimal() +
    theme(legend.position = "none")
}