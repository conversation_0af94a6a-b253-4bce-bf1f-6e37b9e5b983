# Statistical Decision Trees UI
decisionTreesUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(
      fileInput(ns("dtUserData"), "Upload your Data", accept = c(".csv", ".txt", ".xls", ".xlsx")),
      selectInput(ns("dtTargetCol"), "Target Variable", choices = NULL),
      selectInput(ns("dtPredictorCols"), "Predictor Variables", choices = NULL, multiple = TRUE),
      selectInput(ns("dtTreeType"), "Tree Type", choices = c("Classification" = "classification", "Regression" = "regression")),
      actionButton(ns("goDecisionTree"), label = "Run Decision Tree", class = "act-btn")
    ),
    mainPanel(
      plotOutput(ns('dtPlot')),
      verbatimTextOutput(ns('dtSummary')),
      textOutput(ns('dtError')),
      textOutput(ns('dtConclusion'))
    )
  )
} 