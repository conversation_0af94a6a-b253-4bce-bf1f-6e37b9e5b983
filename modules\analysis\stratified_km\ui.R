StratifiedKMUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("skmUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("skmTime"), "Time Variable", choices = NULL),
        selectizeInput(ns("skmEvent"), "Event Variable", choices = NULL),
        selectizeInput(ns("skmStrata"), "Strata Variable", choices = NULL),
        br(),
        actionButton(ns("goSKM"), label = "Plot Stratified KM", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("skmError")),
        plotOutput(ns("skmPlot")),
        verbatimTextOutput(ns("skmSummary"))
      )
    )
  )
} 