CCAUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("ccaUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("ccaXVars"), "X Variables (set 1)", choices = NULL, multiple = TRUE),
        selectizeInput(ns("ccaYVars"), "Y Variables (set 2)", choices = NULL, multiple = TRUE),
        br(),
        actionButton(ns("goCCA"), label = "Run CCA", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("ccaError")),
        tableOutput(ns("ccaCorrelations")),
        tableOutput(ns("ccaLoadings")),
        plotOutput(ns("ccaBiplot"))
      )
    )
  )
} 