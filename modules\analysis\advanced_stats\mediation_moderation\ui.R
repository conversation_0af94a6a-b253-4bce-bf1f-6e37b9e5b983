MediationModerationUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("mmUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("mmIV"), "Independent Variable", choices = NULL),
        selectizeInput(ns("mmDV"), "Dependent Variable", choices = NULL),
        selectizeInput(ns("mmMediator"), "Mediator Variable", choices = NULL),
        selectizeInput(ns("mmModerator"), "Moderator Variable (optional)", choices = NULL),
        textInput(ns("mmModel"), "Model Specification (optional)", value = ""),
        br(),
        actionButton(ns("goMM"), label = "Run Mediation/Moderation Analysis", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("mmError")),
        tableOutput(ns("mmSummary")),
        plotOutput(ns("mmPlot"))
      )
    )
  )
} 