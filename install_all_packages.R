# CougarStats Complete Package Installation Script
# This script installs ALL necessary packages for the CougarStats application
# Run this script to ensure all dependencies are properly installed

cat(paste0(rep("=", 60), collapse = ""), "\n")
cat("COUGARSTATS COMPLETE PACKAGE INSTALLATION\n")
cat(paste0(rep("=", 60), collapse = ""), "\n\n")

# Function to safely install packages with detailed feedback
install_package_safely <- function(package_name, repo = "https://cran.rstudio.com/", source = "CRAN") {
  cat(sprintf("Installing %s from %s... ", package_name, source))
  
  if (requireNamespace(package_name, quietly = TRUE)) {
    cat("✓ Already installed\n")
    return(TRUE)
  }
  
  tryCatch({
    install.packages(package_name, repos = repo, dependencies = TRUE, quiet = TRUE)
    
    # Verify installation
    if (requireNamespace(package_name, quietly = TRUE)) {
      cat("✓ Successfully installed\n")
      return(TRUE)
    } else {
      cat("✗ Installation failed (package not found after install)\n")
      return(FALSE)
    }
  }, error = function(e) {
    cat(sprintf("✗ Failed: %s\n", e$message))
    return(FALSE)
  })
}

# Function to install from Bioconductor
install_bioc_safely <- function(package_name) {
  cat(sprintf("Installing %s from Bioconductor... ", package_name))
  
  if (requireNamespace(package_name, quietly = TRUE)) {
    cat("✓ Already installed\n")
    return(TRUE)
  }
  
  tryCatch({
    if (!requireNamespace("BiocManager", quietly = TRUE)) {
      install.packages("BiocManager", repos = "https://cran.rstudio.com/", quiet = TRUE)
    }
    BiocManager::install(package_name, update = FALSE, ask = FALSE, quiet = TRUE)
    
    if (requireNamespace(package_name, quietly = TRUE)) {
      cat("✓ Successfully installed\n")
      return(TRUE)
    } else {
      cat("✗ Installation failed\n")
      return(FALSE)
    }
  }, error = function(e) {
    cat(sprintf("✗ Failed: %s\n", e$message))
    return(FALSE)
  })
}

# Function to install from GitHub
install_github_safely <- function(repo_name, package_name = NULL) {
  if (is.null(package_name)) {
    package_name <- strsplit(repo_name, "/")[[1]][2]
  }
  
  cat(sprintf("Installing %s from GitHub... ", package_name))
  
  if (requireNamespace(package_name, quietly = TRUE)) {
    cat("✓ Already installed\n")
    return(TRUE)
  }
  
  tryCatch({
    if (!requireNamespace("remotes", quietly = TRUE)) {
      install.packages("remotes", repos = "https://cran.rstudio.com/", quiet = TRUE)
    }
    remotes::install_github(repo_name, quiet = TRUE)
    
    if (requireNamespace(package_name, quietly = TRUE)) {
      cat("✓ Successfully installed\n")
      return(TRUE)
    } else {
      cat("✗ Installation failed\n")
      return(FALSE)
    }
  }, error = function(e) {
    cat(sprintf("✗ Failed: %s\n", e$message))
    return(FALSE)
  })
}

# Function to install packages with multiple fallback methods
install_with_fallbacks <- function(package_name, github_repo = NULL) {
  cat(sprintf("\n--- Installing %s ---\n", package_name))
  
  # Try CRAN first
  if (install_package_safely(package_name)) {
    return(TRUE)
  }
  
  # Try Bioconductor
  if (install_bioc_safely(package_name)) {
    return(TRUE)
  }
  
  # Try GitHub if repo provided
  if (!is.null(github_repo)) {
    if (install_github_safely(github_repo, package_name)) {
      return(TRUE)
    }
  }
  
  cat(sprintf("✗ Failed to install %s from all sources\n", package_name))
  return(FALSE)
}

# Define all required packages with their sources
cat("Defining package requirements...\n")

# Core packages (CRAN)
core_packages <- c(
  "shiny", "shinyjs", "shinyWidgets", "shinyMatrix", "shinyvalidate",
  "bslib", "shinythemes", "shinyDarkmode", "DT", "plotly", "leaflet", 
  "htmlwidgets", "dplyr", "readr", "readxl", "writexl", "MASS", 
  "rstatix", "DescTools", "ggplot2", "ggpubr", "ggsci", "ggResidpanel",
  "scatterplot3d", "networkD3", "visNetwork", "shinydashboard",
  "aplpack", "car", "colourpicker", "generics", "e1071", "markdown",
  "nortest", "xtable", "latex2exp", "thematic", "datamods", "magrittr",
  "olsrr", "sf", "spdep", "lme4", "nlme", "randomForest", "gbm", 
  "xgboost", "caret", "lavaan", "forecast", "igraph", "GGally", 
  "fmsb", "treemap"
)

# Analysis packages (CRAN)
analysis_packages <- c(
  "survival", "psych", "metafor", "mice", "VIM", "skimr", "poLCA", 
  "mirt", "mgcv", "glmnet", "robustbase", "pscl", "pwr", "FSA", 
  "polycor", "changepoint", "boot", "brms", "loo", "BayesFactor", 
  "bayestestR", "rstanarm", "mediation", "MatchIt", "heplots", 
  "ipred", "cluster", "fpc", "dbscan", "fields", "spgwr", "seasonal", 
  "Rtsne", "umap", "pryr", "performance", "sjPlot"
)

# Text mining packages (CRAN)
nlp_packages <- c("tm", "quanteda", "wordcloud", "topicmodels", "lda")

# Spatial packages (CRAN) - Updated to remove deprecated packages
spatial_packages <- c("sp", "gstat", "spatstat")

# Deprecated/removed packages that need alternatives
deprecated_packages <- list(
  "maptools" = list(
    status = "DEPRECATED", 
    alternative = "sf package provides similar functionality",
    note = "Use sf::st_read() instead of maptools::readShapePoly()"
  )
)

# Special packages that need alternative sources
special_packages <- list(
  "cmprsk" = list(github = "cran/cmprsk", bioc = TRUE),
  "shinyDarkmode" = list(github = "deepanshu88/shinyDarkmode"),
  "olsrr" = list(github = "rsquaredacademy/olsrr")
)

# Install core packages
cat("\n", paste0(rep("-", 50), collapse = ""), "\n")
cat("INSTALLING CORE PACKAGES\n")
cat(paste0(rep("-", 50), collapse = ""), "\n")

core_results <- sapply(core_packages, install_package_safely)
core_failed <- core_packages[!core_results]

# Install analysis packages
cat("\n", paste0(rep("-", 50), collapse = ""), "\n")
cat("INSTALLING ANALYSIS PACKAGES\n")
cat(paste0(rep("-", 50), collapse = ""), "\n")

analysis_results <- sapply(analysis_packages, install_package_safely)
analysis_failed <- analysis_packages[!analysis_results]

# Install NLP packages
cat("\n", paste0(rep("-", 50), collapse = ""), "\n")
cat("INSTALLING TEXT MINING PACKAGES\n")
cat(paste0(rep("-", 50), collapse = ""), "\n")

nlp_results <- sapply(nlp_packages, install_package_safely)
nlp_failed <- nlp_packages[!nlp_results]

# Install spatial packages
cat("\n", paste0(rep("-", 50), collapse = ""), "\n")
cat("INSTALLING SPATIAL PACKAGES\n")
cat(paste0(rep("-", 50), collapse = ""), "\n")

spatial_results <- sapply(spatial_packages, install_package_safely)
spatial_failed <- spatial_packages[!spatial_results]

# Install special packages with fallbacks
cat("\n", paste0(rep("-", 50), collapse = ""), "\n")
cat("INSTALLING SPECIAL PACKAGES\n")
cat(paste0(rep("-", 50), collapse = ""), "\n")

special_failed <- c()
for (pkg in names(special_packages)) {
  if (!install_with_fallbacks(pkg, special_packages[[pkg]]$github)) {
    special_failed <- c(special_failed, pkg)
  }
}

# Summary
cat("\n", paste0(rep("=", 60), collapse = ""), "\n")
cat("INSTALLATION SUMMARY\n")
cat(paste0(rep("=", 60), collapse = ""), "\n")

total_packages <- length(core_packages) + length(analysis_packages) + 
                  length(nlp_packages) + length(spatial_packages) + length(special_packages)
failed_packages <- c(core_failed, analysis_failed, nlp_failed, spatial_failed, special_failed)
successful_installations <- total_packages - length(failed_packages)

cat(sprintf("Total packages: %d\n", total_packages))
cat(sprintf("Successfully installed: %d\n", successful_installations))
cat(sprintf("Failed to install: %d\n", length(failed_packages)))

# Report on deprecated packages
if (length(deprecated_packages) > 0) {
  cat("\n", paste0(rep("=", 60), collapse = ""), "\n")
  cat("DEPRECATED PACKAGES INFORMATION\n")
  cat(paste0(rep("=", 60), collapse = ""), "\n")
  
  for (pkg in names(deprecated_packages)) {
    info <- deprecated_packages[[pkg]]
    cat(sprintf("\n%s: %s\n", pkg, info$status))
    cat(sprintf("Alternative: %s\n", info$alternative))
    cat(sprintf("Note: %s\n", info$note))
  }
}

if (length(failed_packages) > 0) {
  cat("\nFailed packages:\n")
  for (pkg in failed_packages) {
    cat(sprintf("  - %s\n", pkg))
  }
  
  cat("\n", paste0(rep("=", 60), collapse = ""), "\n")
  cat("TROUBLESHOOTING FAILED PACKAGES\n")
  cat(paste0(rep("=", 60), collapse = ""), "\n")
  
  for (pkg in failed_packages) {
    cat(sprintf("\nFor %s:\n", pkg))
    
    if (pkg == "cmprsk") {
      cat("  - Try: BiocManager::install('cmprsk')\n")
      cat("  - Try: remotes::install_github('cran/cmprsk')\n")
      cat("  - Download from: https://cran.r-project.org/src/contrib/Archive/cmprsk/\n")
    } else if (pkg %in% c("shinyDarkmode", "olsrr")) {
      cat("  - Try: remotes::install_github('deepanshu88/shinyDarkmode')\n")
      cat("  - Try: remotes::install_github('rsquaredacademy/olsrr')\n")
    } else if (pkg == "maptools") {
      cat("  - maptools is deprecated and no longer available\n")
      cat("  - Use sf package instead for spatial data handling\n")
      cat("  - Replace maptools::readShapePoly() with sf::st_read()\n")
    } else {
      cat(sprintf("  - Try: install.packages('%s')\n", pkg))
      cat("  - Check if package name is correct\n")
      cat("  - Update R to latest version\n")
    }
  }
}

# Test application startup
cat("\n", paste0(rep("=", 60), collapse = ""), "\n")
cat("TESTING APPLICATION STARTUP\n")
cat(paste0(rep("=", 60), collapse = ""), "\n")

tryCatch({
  # Test core packages
  library(shiny)
  library(ggplot2)
  library(dplyr)
  cat("✓ Core packages loaded successfully\n")
  
  # Test survival package
  library(survival)
  cat("✓ Survival analysis package loaded\n")
  
  # Test cmprsk if available
  if (requireNamespace("cmprsk", quietly = TRUE)) {
    library(cmprsk)
    cat("✓ Competing risks package loaded\n")
  } else {
    cat("⚠️  cmprsk not available - competing risks analysis disabled\n")
  }
  
  # Test spatial packages
  if (requireNamespace("sf", quietly = TRUE)) {
    library(sf)
    cat("✓ sf package loaded (replaces maptools)\n")
  } else {
    cat("⚠️  sf not available - spatial analysis may be limited\n")
  }
  
  # Test if global.R can be sourced
  if (file.exists("global.R")) {
    source("global.R", local = TRUE)
    cat("✓ global.R loaded successfully\n")
  } else {
    cat("⚠️  global.R not found in current directory\n")
  }
  
  cat("\n🎉 CougarStats should be ready to run!\n")
  
}, error = function(e) {
  cat(sprintf("❌ Error during testing: %s\n", e$message))
})

# Final instructions
cat("\n", paste0(rep("=", 60), collapse = ""), "\n")
cat("NEXT STEPS\n")
cat(paste0(rep("=", 60), collapse = ""), "\n")

if (length(failed_packages) == 0) {
  cat("✅ All packages installed successfully!\n")
  cat("You can now run CougarStats without issues.\n")
} else {
  cat("⚠️  Some packages failed to install.\n")
  cat("The application will work but some features may be limited.\n")
  cat("Try the troubleshooting steps above for failed packages.\n")
}

cat("\nTo run CougarStats:\n")
cat("1. Make sure you're in the CougarStats directory\n")
cat("2. Run: shiny::runApp()\n")
cat("3. Or run: source('app.R')\n")

cat("\nFor help with specific packages, see:\n")
cat("- CMPRSK_INSTALLATION.md (for cmprsk issues)\n")
cat("- ERROR_HANDLING_README.md (for general troubleshooting)\n")

cat("\n", paste0(rep("=", 60), collapse = ""), "\n")
cat("Installation script completed!\n")
cat(paste0(rep("=", 60), collapse = ""), "\n") 