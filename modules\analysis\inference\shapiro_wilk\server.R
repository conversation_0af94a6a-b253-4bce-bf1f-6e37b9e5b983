shapiroWilkServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactives
    swUploadData <- eventReactive(input$swUserData, {
      handle_file_upload(input$swUserData)
    })
    
    swResults <- reactive({
      data <- swUploadData()
      if (is.null(data) || is.null(input$swVariable) || input$swVariable == "") {
        return(NULL)
      }
      
      values <- data[[input$swVariable]]
      values <- values[!is.na(values)]
      
      if (length(values) < 3) return(NULL)
      
      list(values = values, n = length(values))
    })
    
    # Validation errors
    swValidationErrors <- reactive({
      errors <- c()
      data <- swUploadData()
      
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$swVariable) || input$swVariable == "") {
        errors <- c(errors, "Please select a variable.")
        return(errors)
      }
      
      values <- data[[input$swVariable]]
      if (length(na.omit(values)) < 3) {
        errors <- c(errors, "At least 3 non-missing values are required.")
      }
      
      errors
    })
    
    # Outputs
    output$swHT <- renderUI({
      results <- swResults()
      if (is.null(results)) return(NULL)
      shapiroWilkHT(swResults, reactive({input$swSigLvl}))
    })
    
    output$shapiroWilkPlot <- renderPlot({
      results <- swResults()
      if (is.null(results)) return(NULL)
      shapiroWilkPlot(swResults)
    })
    
    output$swConclusionOutput <- renderUI({
      results <- swResults()
      if (is.null(results)) return(NULL)
      swConclusion(swResults, reactive({input$swSigLvl}))
    })
    
    output$renderSWData <- renderUI({
      req(swUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("swInitialUploadTable")), style = "width: 75%"),
        br(),
        br()
      )
    })
    
    output$swInitialUploadTable <- DT::renderDT({
      req(swUploadData())
      DT::datatable(swUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(swUploadData())))))
    })
    
    # Update selectizeInput choices after file upload
    observeEvent(swUploadData(), {
      data <- swUploadData()
      updateSelectizeInput(session, 'swVariable', choices = character(0), selected = NULL, server = TRUE)
      output$shapiroWilkResults <- renderUI({ NULL })
      
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'swVariable', choices = names(data), server = TRUE)
        output$shapiroWilkResults <- renderUI({
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('swPreviewTable'))
          )
        })
        output$swPreviewTable <- DT::renderDT({
          head(data, 20)
        })
      }
    })
    
    # Show main results or error screen when Calculate is pressed
    observeEvent(input$goInference, {
      output$shapiroWilkResults <- renderUI({
        errors <- swValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Shapiro-Wilk Test", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("swTabset"),
              selected = "Analysis",
              tabPanel(
                id    = ns("sw"),
                title = "Analysis",
                titlePanel("Shapiro-Wilk Test for Normality"),
                br(),
                uiOutput(ns('swHT')),
                br(),
                plotOutput(ns('shapiroWilkPlot'), width = "50%", height = "400px"),
                br(),
                uiOutput(ns('swConclusionOutput'))
              ),
              tabPanel(
                id    = ns("swData"),
                title = "Uploaded Data",
                uiOutput(ns("renderSWData"))
              )
            )
          )
        }
      })
    })
  })
} 