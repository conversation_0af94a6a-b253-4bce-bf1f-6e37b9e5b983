# Discriminant Analysis Calculation Functions

# Main discriminant analysis function
daResults_func <- function(data, group, predictors, method, prior, cross_validation, conf_level) {
  
  # Validate inputs
  if (!group %in% names(data)) {
    stop("Group variable not found in data")
  }
  
  if (!all(predictors %in% names(data))) {
    stop("One or more predictor variables not found in data")
  }
  
  if (group %in% predictors) {
    stop("Group variable cannot be a predictor")
  }
  
  # Prepare data
  y <- data[[group]]
  X <- data[predictors]
  
  # Remove rows with missing values
  complete_cases <- complete.cases(y, X)
  y_clean <- y[complete_cases]
  X_clean <- X[complete_cases, , drop = FALSE]
  
  if (nrow(X_clean) < length(predictors) + 1) {
    stop("Insufficient data for discriminant analysis")
  }
  
  if (length(unique(y_clean)) < 2) {
    stop("At least 2 groups are required")
  }
  
  # Perform discriminant analysis based on method
  results <- switch(method,
    "Linear Discriminant Analysis (LDA)" = perform_lda(X_clean, y_clean, prior, cross_validation),
    "Quadratic Discriminant Analysis (QDA)" = perform_qda(X_clean, y_clean, prior, cross_validation),
    "Regularized Discriminant Analysis (RDA)" = perform_rda(X_clean, y_clean, prior, cross_validation),
    stop("Unknown discriminant method: ", method)
  )
  
  # Add metadata
  results$method <- method
  results$group <- group
  results$predictors <- predictors
  results$prior <- prior
  results$cross_validation <- cross_validation
  results$conf_level <- conf_level
  results$n_observations <- nrow(X_clean)
  results$n_predictors <- length(predictors)
  results$n_groups <- length(unique(y_clean))
  results$data <- data[complete_cases, ]
  
  return(results)
}

# Linear Discriminant Analysis
perform_lda <- function(X, y, prior, cross_validation) {
  
  tryCatch({
    if (requireNamespace("MASS", quietly = TRUE)) {
      # Use MASS package for LDA
      if (cross_validation) {
        model <- MASS::lda(y ~ ., data = data.frame(y = y, X), CV = TRUE, prior = rep(prior, length(unique(y))))
      } else {
        model <- MASS::lda(y ~ ., data = data.frame(y = y, X), prior = rep(prior, length(unique(y))))
      }
      
      # Extract results
      coefficients <- model$scaling
      means <- model$means
      prior_probs <- model$prior
      
      # Calculate classification results
      if (cross_validation) {
        predictions <- model$class
        posterior <- model$posterior
      } else {
        predictions <- predict(model, data.frame(X))$class
        posterior <- predict(model, data.frame(X))$posterior
      }
      
      # Calculate accuracy
      accuracy <- mean(predictions == y)
      
      # Create confusion matrix
      confusion_matrix <- table(Actual = y, Predicted = predictions)
      
      # Calculate group-specific accuracy
      group_accuracy <- diag(confusion_matrix) / rowSums(confusion_matrix)
      
      # Calculate Wilks' Lambda
      wilks_lambda <- calculate_wilks_lambda(X, y)
      
      return(list(
        model = model,
        coefficients = coefficients,
        means = means,
        prior_probs = prior_probs,
        predictions = predictions,
        posterior = posterior,
        accuracy = accuracy,
        confusion_matrix = confusion_matrix,
        group_accuracy = group_accuracy,
        wilks_lambda = wilks_lambda,
        method_name = "Linear Discriminant Analysis"
      ))
      
    } else {
      # Fallback implementation
      return(perform_basic_lda(X, y, prior))
    }
    
  }, error = function(e) {
    stop("Error in LDA: ", e$message)
  })
}

# Quadratic Discriminant Analysis
perform_qda <- function(X, y, prior, cross_validation) {
  
  tryCatch({
    if (requireNamespace("MASS", quietly = TRUE)) {
      # Use MASS package for QDA
      if (cross_validation) {
        model <- MASS::qda(y ~ ., data = data.frame(y = y, X), CV = TRUE, prior = rep(prior, length(unique(y))))
      } else {
        model <- MASS::qda(y ~ ., data = data.frame(y = y, X), prior = rep(prior, length(unique(y))))
      }
      
      # Extract results
      means <- model$means
      prior_probs <- model$prior
      
      # Calculate classification results
      if (cross_validation) {
        predictions <- model$class
        posterior <- model$posterior
      } else {
        predictions <- predict(model, data.frame(X))$class
        posterior <- predict(model, data.frame(X))$posterior
      }
      
      # Calculate accuracy
      accuracy <- mean(predictions == y)
      
      # Create confusion matrix
      confusion_matrix <- table(Actual = y, Predicted = predictions)
      
      # Calculate group-specific accuracy
      group_accuracy <- diag(confusion_matrix) / rowSums(confusion_matrix)
      
      return(list(
        model = model,
        means = means,
        prior_probs = prior_probs,
        predictions = predictions,
        posterior = posterior,
        accuracy = accuracy,
        confusion_matrix = confusion_matrix,
        group_accuracy = group_accuracy,
        method_name = "Quadratic Discriminant Analysis"
      ))
      
    } else {
      stop("MASS package required for QDA")
    }
    
  }, error = function(e) {
    stop("Error in QDA: ", e$message)
  })
}

# Regularized Discriminant Analysis
perform_rda <- function(X, y, prior, cross_validation) {
  
  tryCatch({
    if (requireNamespace("klaR", quietly = TRUE)) {
      # Use klaR package for RDA
      model <- klaR::rda(y ~ ., data = data.frame(y = y, X), gamma = 0.5, lambda = 0.5)
      
      # Extract results
      predictions <- predict(model, data.frame(X))$class
      posterior <- predict(model, data.frame(X))$posterior
      
      # Calculate accuracy
      accuracy <- mean(predictions == y)
      
      # Create confusion matrix
      confusion_matrix <- table(Actual = y, Predicted = predictions)
      
      # Calculate group-specific accuracy
      group_accuracy <- diag(confusion_matrix) / rowSums(confusion_matrix)
      
      return(list(
        model = model,
        predictions = predictions,
        posterior = posterior,
        accuracy = accuracy,
        confusion_matrix = confusion_matrix,
        group_accuracy = group_accuracy,
        method_name = "Regularized Discriminant Analysis"
      ))
      
    } else {
      stop("klaR package required for RDA")
    }
    
  }, error = function(e) {
    stop("Error in RDA: ", e$message)
  })
}

# Basic LDA implementation (fallback)
perform_basic_lda <- function(X, y, prior) {
  
  # Calculate group means
  groups <- unique(y)
  group_means <- lapply(groups, function(g) colMeans(X[y == g, , drop = FALSE]))
  names(group_means) <- groups
  
  # Calculate pooled covariance matrix
  n_groups <- length(groups)
  n_vars <- ncol(X)
  pooled_cov <- matrix(0, n_vars, n_vars)
  
  for (g in groups) {
    group_data <- X[y == g, , drop = FALSE]
    n_group <- nrow(group_data)
    group_cov <- cov(group_data) * (n_group - 1)
    pooled_cov <- pooled_cov + group_cov
  }
  
  pooled_cov <- pooled_cov / (nrow(X) - n_groups)
  
  # Calculate discriminant coefficients
  inv_cov <- solve(pooled_cov)
  coefficients <- matrix(0, n_vars, n_groups - 1)
  
  for (i in 1:(n_groups - 1)) {
    diff_means <- group_means[[i + 1]] - group_means[[1]]
    coefficients[, i] <- inv_cov %*% diff_means
  }
  
  # Simple classification
  predictions <- rep(groups[1], nrow(X))
  for (i in 1:nrow(X)) {
    scores <- rep(0, n_groups)
    for (j in 1:n_groups) {
      scores[j] <- t(coefficients[, 1]) %*% (as.numeric(X[i, ]) - group_means[[j]])
    }
    predictions[i] <- groups[which.max(scores)]
  }
  
  # Calculate accuracy
  accuracy <- mean(predictions == y)
  
  # Create confusion matrix
  confusion_matrix <- table(Actual = y, Predicted = predictions)
  
  return(list(
    coefficients = coefficients,
    group_means = group_means,
    pooled_covariance = pooled_cov,
    predictions = predictions,
    accuracy = accuracy,
    confusion_matrix = confusion_matrix,
    method_name = "Basic Linear Discriminant Analysis"
  ))
}

# Calculate Wilks' Lambda
calculate_wilks_lambda <- function(X, y) {
  
  # Total sum of squares
  total_mean <- colMeans(X)
  total_ss <- t(X - total_mean) %*% (X - total_mean)
  
  # Within-group sum of squares
  groups <- unique(y)
  within_ss <- matrix(0, ncol(X), ncol(X))
  
  for (g in groups) {
    group_data <- X[y == g, , drop = FALSE]
    group_mean <- colMeans(group_data)
    group_ss <- t(group_data - group_mean) %*% (group_data - group_mean)
    within_ss <- within_ss + group_ss
  }
  
  # Wilks' Lambda
  wilks_lambda <- det(within_ss) / det(total_ss)
  
  return(wilks_lambda)
}

# Output rendering functions

daModelSummaryOutput <- function(results) {
  renderUI({
    req(results)
    
    tagList(
      h4(paste("Results:", results$method_name)),
      br(),
      p(strong("Number of Groups:"), results$n_groups),
      p(strong("Number of Predictors:"), results$n_predictors),
      p(strong("Number of Observations:"), results$n_observations),
      p(strong("Overall Accuracy:"), round(results$accuracy, 4)),
      br(),
      h5("Group-Specific Accuracy"),
      div(
        DT::datatable(
          data.frame(
            Group = names(results$group_accuracy),
            Accuracy = round(results$group_accuracy, 4),
            stringsAsFactors = FALSE
          ),
          options = list(pageLength = 10, searching = FALSE, paging = FALSE),
          rownames = FALSE
        ),
        style = "width: 50%"
      ),
      if (!is.null(results$wilks_lambda)) {
        tagList(
          br(),
          p(strong("Wilks' Lambda:"), round(results$wilks_lambda, 4))
        )
      }
    )
  })
}

daClassificationOutput <- function(results) {
  renderUI({
    req(results)
    
    # Create confusion matrix table
    confusion_df <- as.data.frame(results$confusion_matrix)
    confusion_df$Actual <- as.character(confusion_df$Actual)
    confusion_df$Predicted <- as.character(confusion_df$Predicted)
    
    tagList(
      h4("Classification Results"),
      br(),
      h5("Confusion Matrix"),
      div(
        DT::datatable(confusion_df, options = list(pageLength = 10, searching = FALSE)),
        style = "width: 100%"
      ),
      br(),
      p(strong("Overall Classification Accuracy:"), round(results$accuracy * 100, 2), "%")
    )
  })
}

daCoefficientsOutput <- function(results) {
  renderUI({
    req(results)
    
    if (!is.null(results$coefficients)) {
      # Create coefficients table
      coef_df <- as.data.frame(results$coefficients)
      coef_df$Variable <- rownames(coef_df)
      coef_df <- coef_df[, c("Variable", colnames(results$coefficients))]
      
      # Round coefficients
      for (col in colnames(results$coefficients)) {
        coef_df[[col]] <- round(coef_df[[col]], 4)
      }
      
      tagList(
        h4("Discriminant Function Coefficients"),
        br(),
        div(
          DT::datatable(coef_df, options = list(pageLength = 20, searching = FALSE)),
          style = "width: 100%"
        ),
        br(),
        p(em("Note: Coefficients represent the contribution of each variable to the discriminant function."))
      )
    } else {
      p("Coefficients not available for this method.")
    }
  })
}

daPlotsOutput <- function(results) {
  renderUI({
    req(results)
    
    tagList(
      h4("Discriminant Analysis Plots"),
      br(),
      plotOutput(paste0("da_scatter_plot_", sample(1:1000, 1)), height = "400px"),
      br(),
      plotOutput(paste0("da_accuracy_plot_", sample(1:1000, 1)), height = "400px")
    )
  })
}

# Discriminant Analysis calculation and output helpers

# 1. Data Upload Function
discriminant_analysis_uploadData_func <- function(daUserData) {
  ext <- tools::file_ext(daUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(daUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(daUserData$datapath),
         xlsx = readxl::read_xlsx(daUserData$datapath),
         txt = readr::read_tsv(daUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

# 2. Main Results Function (core calculations)
discriminant_analysis_results_func <- function(data, group, predictors, method, prior, cross_validation, conf_level) {
  tryCatch({
    results <- daResults_func(data, group, predictors, method, prior, cross_validation, conf_level)
    results$data <- data
    results$error <- NULL
    results
  }, error = function(e) {
    list(error = paste("An error occurred during Discriminant Analysis calculation:", e$message))
  })
}

# 3. Hypothesis Test HTML Output
discriminant_analysis_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  tagList(
    h4("Discriminant Analysis"),
    p("See summary table for model details.")
  )
}

# 4. Summary Table HTML Output
discriminant_analysis_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  out <- list(h4("Discriminant Analysis Summary"))
  if (!is.null(results$coefficients)) {
    out <- c(out, h4("Coefficients"), renderTable(results$coefficients))
  }
  if (!is.null(results$means)) {
    out <- c(out, h4("Group Means"), renderTable(results$means))
  }
  if (!is.null(results$confusion_matrix)) {
    out <- c(out, h4("Confusion Matrix"), renderTable(results$confusion_matrix))
  }
  if (!is.null(results$group_accuracy)) {
    out <- c(out, h4("Group Accuracy"), renderTable(as.data.frame(results$group_accuracy)))
  }
  if (!is.null(results$wilks_lambda)) {
    out <- c(out, h4("Wilks' Lambda"), renderPrint(results$wilks_lambda))
  }
  if (!is.null(results$model) && !is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

# 5. Plot Output
discriminant_analysis_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  if (!is.null(results$model) && inherits(results$model, "lda")) {
    # Discriminant function plot
    plot(results$model)
  }
  if (!is.null(results$confusion_matrix)) {
    # Confusion matrix heatmap
    cm <- results$confusion_matrix
    image(as.matrix(cm), main = "Confusion Matrix Heatmap", xlab = "Predicted", ylab = "Actual", col = heat.colors(10))
    axis(1, at = seq(0, 1, length.out = ncol(cm)), labels = colnames(cm))
    axis(2, at = seq(0, 1, length.out = nrow(cm)), labels = rownames(cm))
  }
  # ROC curve if possible
  if (!is.null(results$posterior) && ncol(results$posterior) == 2) {
    if (requireNamespace("pROC", quietly = TRUE)) {
      roc_obj <- pROC::roc(as.numeric(results$predictions), results$posterior[,2])
      plot(roc_obj, main = "ROC Curve")
    }
  }
}

discriminantAnalysisResults <- function(daResults_output, daSigLvl_input) {
  renderUI({
    req(daResults_output())
    results <- daResults_output()
    
    if (!is.null(results$error)) {
      return(tagList(
        h4("Error in Discriminant Analysis"),
        p(results$error)
      ))
    }
    
    tagList(
      h4("Discriminant Analysis Summary"),
      p(tags$b("Method:"), results$method),
      p(tags$b("Number of Groups:"), results$n_groups),
      p(tags$b("Number of Predictors:"), results$n_predictors),
      p(tags$b("Number of Observations:"), results$n_observations),
      br(),
      h4("Model Performance"),
      p(tags$b("Classification Accuracy:"), round(results$accuracy * 100, 2), "%"),
      br(),
      h4("Model Formula"),
      p(results$formula),
      br(),
      h4("Group Information"),
      p("The analysis aims to find linear combinations of predictor variables that best separate the groups.")
    )
  })
}

discriminantAnalysisCoefficients <- function(daResults_output, daSigLvl_input) {
  renderUI({
    req(daResults_output())
    results <- daResults_output()
    
    if (!is.null(results$error) || is.null(results$coefficients)) {
      return(tagList(
        h4("Discriminant Functions"),
        p("Coefficients are not available for QDA or when the analysis fails.")
      ))
    }
    
    tagList(
      h4("Discriminant Function Coefficients"),
      p("The coefficients show the contribution of each predictor variable to the discriminant functions."),
      p("Larger absolute values indicate greater importance of that variable in separating the groups."),
      br(),
      h4("Interpretation"),
      p("Each discriminant function is a linear combination of the predictor variables that maximizes the separation between groups.")
    )
  })
}

discriminantAnalysisPredictions <- function(daResults_output, daSigLvl_input) {
  renderUI({
    req(daResults_output())
    results <- daResults_output()
    
    if (!is.null(results$error)) {
      return(NULL)
    }
    
    tagList(
      h4("Classification Results"),
      p("The table shows the actual group membership and predicted group membership for each observation."),
      br(),
      h4("Accuracy Interpretation"),
      p(sprintf("Overall classification accuracy: %.2f%%", results$accuracy * 100)),
      p("This represents the proportion of observations correctly classified by the model."),
      br(),
      h4("Posterior Probabilities"),
      p("Posterior probabilities show the probability that each observation belongs to each group based on the model.")
    )
  })
}

discriminantAnalysisClassification <- function(daResults_output, daSigLvl_input) {
  renderUI({
    req(daResults_output())
    results <- daResults_output()
    
    if (!is.null(results$error)) {
      return(NULL)
    }
    
    tagList(
      h4("Classification Summary"),
      p("Discriminant analysis is used to classify observations into predefined groups based on predictor variables."),
      br(),
      h4("Key Results"),
      p(sprintf("The %s model achieved %.2f%% classification accuracy.", 
                results$method, results$accuracy * 100)),
      br(),
      h4("Model Comparison"),
      if (results$method == "LDA") {
        p("Linear Discriminant Analysis assumes equal covariance matrices across groups and is more robust with smaller sample sizes.")
      } else {
        p("Quadratic Discriminant Analysis allows different covariance matrices across groups and may provide better fit with larger sample sizes.")
      },
      br(),
      h4("Applications"),
      tags$ul(
        tags$li("Medical diagnosis"),
        tags$li("Credit scoring"),
        tags$li("Market segmentation"),
        tags$li("Species classification")
      )
    )
  })
} 