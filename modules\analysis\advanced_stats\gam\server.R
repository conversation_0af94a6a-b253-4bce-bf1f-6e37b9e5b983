GAMServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    gamData <- eventReactive(input$gamUserData, {
      handle_file_upload(input$gamUserData)
    })
    
    observeEvent(gamData(), {
      data <- gamData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'gamResponse', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'gamPredictors', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'gamSmooths', choices = names(data), server = TRUE)
      }
    })
    
    gamValidationErrors <- reactive({
      errors <- c()
      data <- gamData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$gamResponse)) {
        errors <- c(errors, "Select a response variable.")
      }
      if (is.null(input$gamPredictors) || length(input$gamPredictors) < 1) {
        errors <- c(errors, "Select at least one predictor variable.")
      }
      if (!is.null(input$gamResponse) && !is.numeric(data[[input$gamResponse]])) {
        errors <- c(errors, "Response variable must be numeric.")
      }
      for (var in c(input$gamPredictors, input$gamSmooths)) {
        if (!is.null(var) && !is.numeric(data[[var]])) {
          errors <- c(errors, sprintf("Variable '%s' must be numeric.", var))
        }
      }
      errors
    })
    
    gamResult <- eventReactive(input$goGAM, {
      data <- gamData()
      req(data, input$gamResponse, input$gamPredictors)
      
      # Get parameters from UI
      family <- ifelse(is.null(input$gamFamily), "gaussian", input$gamFamily)
      smooth_terms <- ifelse(is.null(input$gamSmooths), NULL, input$gamSmooths)
      
      gam_analysis(data, input$gamResponse, input$gamPredictors, 
                  smooth_terms = smooth_terms, family = family)
    })
    
    observeEvent(input$goGAM, {
      output$gamResultsUI <- renderUI({
        errors <- gamValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in GAM", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("gamTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("gamAnalysis"),
                title = "Analysis",
                titlePanel("Generalized Additive Model Results"),
                br(),
                h4("Model Formula"),
                textOutput(ns('gamFormula')),
                h4("Family Used"),
                textOutput(ns('gamFamily')),
                h4("Coefficients"),
                tableOutput(ns('gamCoefficients')),
                h4("Smooth Terms"),
                tableOutput(ns('gamSmoothTerms')),
                h4("Fit Statistics"),
                tableOutput(ns('gamFitStats')),
                h4("Effect Sizes / Residual Stats"),
                tableOutput(ns('gamResidualStats')),
                h4("Number of Observations"),
                textOutput(ns('gamObservations')),
                h4("Number of Predictors"),
                textOutput(ns('gamPredictors')),
                h4("Number of Smooth Terms"),
                textOutput(ns('gamSmoothCount'))
              ),
              tabPanel(
                id = ns("gamDiagnostics"),
                title = "Model Diagnostics",
                h4("Residuals vs Fitted Plot"),
                plotOutput(ns('gamResidualPlot'), height = "300px"),
                h4("Q-Q Plot of Residuals"),
                plotOutput(ns('gamQQPlot'), height = "300px"),
                h4("Observed vs Fitted Plot"),
                plotOutput(ns('gamObservedFittedPlot'), height = "300px"),
                h4("Smooth Term Plots"),
                plotOutput(ns('gamSmoothPlots'), height = "300px"),
                h4("Diagnostics Summary"),
                textOutput(ns('gamDiagnostics'))
              ),
              tabPanel(
                id = ns("gamUploadedData"),
                title = "Uploaded Data",
                h4("Raw Uploaded Data"),
                DT::DTOutput(ns('gamRawDataTable'))
              )
            )
          )
        }
      })
    })
    output$gamRawDataTable <- DT::renderDT({
      req(gamData())
      DT::datatable(gamData(), options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), rownames = FALSE)
    })
  })
} 