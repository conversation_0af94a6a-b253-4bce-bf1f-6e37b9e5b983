discriminantAnalysisSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(
      inputId = ns("daUserData"),
      label   = strong("Upload your Data (.csv or .xls or .xlsx or .txt)"),
      accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
    ),
    div(
      id = ns("daUploadInputs"),
      selectizeInput(
        inputId = ns("daGroup"),
        label = strong("Group Variable"),
        choices = c(""),
        selected = NULL,
        options = list(placeholder = 'Select group variable', onInitialize = I('function() { this.setValue(""); }'))
      ),
      selectizeInput(
        inputId = ns("daPredictors"),
        label = strong("Predictor Variables"),
        choices = c(""),
        multiple = TRUE,
        selected = NULL,
        options = list(hideSelected = FALSE, placeholder = 'Select predictor variables', onInitialize = I('function() { this.setValue(""); }'))
      )
    ),
    radioButtons(
      inputId = ns("daMethod"),
      label = strong("Discriminant Method"),
      choices = c("Linear Discriminant Analysis (LDA)", "Quadratic Discriminant Analysis (QDA)"),
      selected = "Linear Discriminant Analysis (LDA)"
    ),
    radioButtons(
      inputId = ns("daSigLvl"),
      label = strong("Significance Level (alpha)"),
      choices  = c("10%", "5%", "1%"),
      selected = "5%",
      inline   = TRUE
    ),
    br(),
    actionButton(ns("goDiscriminantAnalysis"), label = "Calculate", class = "act-btn")
  )
}

discriminantAnalysisMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('discriminantAnalysisResults'))
  )
}

discriminantAnalysisUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(discriminantAnalysisSidebarUI(id)),
    mainPanel(discriminantAnalysisMainUI(id))
  )
} 