# Longitudinal Analysis Server Logic

longitudinalAnalysisServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    # Reactive values for storing results
    longitudinal_results <- reactiveVal(NULL)
    longitudinal_plots <- reactiveVal(NULL)
    
    # Data upload reactive
    longData <- eventReactive(input$longUserData, {
      handle_file_upload(input$longUserData)
    })

    # Update variable choices when data changes
    observeEvent(longData(), {
      data <- longData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectInput(session, "long_response", choices = names(data))
        updateSelectInput(session, "long_time", choices = names(data))
        updateSelectInput(session, "long_subject", choices = names(data))
        updateSelectInput(session, "long_predictors", choices = names(data), multiple = TRUE)
        updateSelectInput(session, "long_covariates", choices = names(data), multiple = TRUE)
        updateSelectInput(session, "long_group", choices = c("None" = "none", names(data)), selected = "none")
        updateSelectInput(session, "long_var1", choices = names(data))
        updateSelectInput(session, "long_var2", choices = names(data))
      }
    })

    # Run longitudinal analysis
    observeEvent(input$run_longitudinal, {
      req(longData(), input$long_response, input$long_time, input$long_subject)
      
      tryCatch({
        # Prepare data
        model_data <- longData()
        response_var <- input$long_response
        time_var <- input$long_time
        subject_var <- input$long_subject
        predictors <- input$long_predictors
        covariates <- input$long_covariates
        group_var <- if(input$long_group != "none") input$long_group else NULL
        
        # Handle missing data
        if (input$long_handle_missing) {
          if (input$long_missing_method == "listwise") {
            model_data <- na.omit(model_data)
          } else if (input$long_missing_method == "imputation") {
            if (require(mice, quietly = TRUE)) {
              imp <- mice(model_data, m = input$long_imputations)
              model_data <- complete(imp)
            }
          }
        }
        
        # Scale predictors if requested
        if (input$long_scale_predictors && !is.null(predictors)) {
          for (pred in predictors) {
            if (is.numeric(model_data[[pred]])) {
              model_data[[pred]] <- scale(model_data[[pred]])
            }
          }
        }
        
        # Fit model based on analysis type
        if (input$long_analysis_type == "growth_curve") {
          results <- fit_growth_curve_model(model_data, response_var, time_var, subject_var,
                                           predictors, covariates, group_var)
        } else if (input$long_analysis_type == "latent_growth") {
          results <- fit_latent_growth_model(model_data, response_var, time_var, subject_var,
                                            covariates)
        } else if (input$long_analysis_type == "autoregressive") {
          results <- fit_autoregressive_model(model_data, response_var, time_var, subject_var,
                                             predictors)
        } else if (input$long_analysis_type == "cross_lagged") {
          results <- fit_cross_lagged_model(model_data, input$long_var1, input$long_var2,
                                           time_var, subject_var, covariates)
        } else if (input$long_analysis_type == "time_series") {
          results <- fit_time_series_model(model_data, response_var, time_var, subject_var)
        } else if (input$long_analysis_type == "mixed_growth") {
          results <- fit_mixed_growth_model(model_data, response_var, time_var, subject_var,
                                           predictors, covariates, group_var)
        }
        
        # Store results
        longitudinal_results(results)
        
        # Generate plots
        generate_longitudinal_plots(results, model_data, response_var, time_var, subject_var)
        
      }, error = function(e) {
        showNotification(paste("Error in longitudinal analysis:", e$message), type = "error")
      })
    })

    # Growth curve model
    fit_growth_curve_model <- function(data, response, time, subject, predictors, covariates, group) {
      
      # Build formula
      fixed_effects <- time
      
      if (input$long_growth_type == "quadratic") {
        data$time_sq <- data[[time]]^2
        fixed_effects <- paste(time, "+ time_sq")
      } else if (input$long_growth_type == "cubic") {
        data$time_sq <- data[[time]]^2
        data$time_cub <- data[[time]]^3
        fixed_effects <- paste(time, "+ time_sq + time_cub")
      }
      
      if (!is.null(predictors)) {
        fixed_effects <- paste(fixed_effects, "+", paste(predictors, collapse = " + "))
      }
      if (!is.null(covariates)) {
        fixed_effects <- paste(fixed_effects, "+", paste(covariates, collapse = " + "))
      }
      if (!is.null(group)) {
        fixed_effects <- paste(fixed_effects, "+", group)
      }
      
      # Random effects
      random_effects <- paste("(1|", subject, ")")
      if (input$long_random_slopes) {
        random_effects <- paste(random_effects, "+ (", time, "|", subject, ")")
      }
      
      formula_str <- paste(response, "~", fixed_effects, "+", random_effects)
      
      # Fit model
      if (input$long_family == "gaussian") {
        model <- lmer(as.formula(formula_str), data = data)
      } else {
        family_fun <- switch(input$long_family,
                            "binomial" = binomial(link = input$long_link),
                            "poisson" = poisson(link = input$long_link))
        model <- glmer(as.formula(formula_str), data = data, family = family_fun)
      }
      
      return(list(
        model = model,
        method = "Growth Curve Modeling",
        formula = formula_str,
        growth_type = input$long_growth_type,
        data = data
      ))
    }

    # Latent growth model
    fit_latent_growth_model <- function(data, response, time, subject, covariates) {
      
      # Reshape to wide format
      wide_data <- reshape(data, 
                           idvar = subject, 
                           timevar = time, 
                           direction = "wide")
      
      # Create model syntax
      time_points <- unique(data[[time]])
      model_syntax <- paste0(response, " ~ 1")
      
      if (input$long_latent_shape == "linear") {
        model_syntax <- paste0(response, " ~ 1 + ", time)
      } else if (input$long_latent_shape == "quadratic") {
        model_syntax <- paste0(response, " ~ 1 + ", time, " + ", time, "2")
      }
      
      if (!is.null(covariates)) {
        for (cov in covariates) {
          model_syntax <- paste0(model_syntax, "\n", response, " ~ ", cov)
        }
      }
      
      # Fit model
      if (input$long_latent_classes == 1) {
        model <- growth(model_syntax, data = wide_data)
      } else {
        model <- growth(model_syntax, data = wide_data, mixture = input$long_latent_classes)
      }
      
      return(list(
        model = model,
        method = "Latent Growth Modeling",
        shape = input$long_latent_shape,
        n_classes = input$long_latent_classes,
        data = wide_data
      ))
    }

    # Autoregressive model
    fit_autoregressive_model <- function(data, response, time, subject, predictors) {
      
      # Sort data
      data <- data[order(data[[subject]], data[[time]]), ]
      
      # Build formula
      fixed_effects <- response
      if (!is.null(predictors)) {
        fixed_effects <- paste(fixed_effects, "~", paste(predictors, collapse = " + "))
      } else {
        fixed_effects <- paste(fixed_effects, "~ 1")
      }
      
      # Correlation structure
      ar_order <- input$long_ar_order
      ma_order <- if(input$long_ma_terms) input$long_ma_order else 0
      
      if (ma_order > 0) {
        cor_structure <- paste0("corARMA(", ar_order, ",", ma_order, ")")
      } else {
        cor_structure <- paste0("corAR", ar_order)
      }
      
      # Fit model
      model <- gls(as.formula(fixed_effects), 
                   data = data,
                   correlation = eval(parse(text = paste0(cor_structure, "(~", time, "|", subject, ")"))))
      
      return(list(
        model = model,
        method = "Autoregressive Modeling",
        ar_order = ar_order,
        ma_order = ma_order,
        data = data
      ))
    }

    # Cross-lagged model
    fit_cross_lagged_model <- function(data, var1, var2, time, subject, covariates) {
      
      # Sort data
      data <- data[order(data[[subject]], data[[time]]), ]
      
      # Create lagged variables
      data$var1_lag <- ave(data[[var1]], data[[subject]], FUN = function(x) c(NA, x[-length(x)]))
      data$var2_lag <- ave(data[[var2]], data[[subject]], FUN = function(x) c(NA, x[-length(x)]))
      
      # Build formulas
      formula1 <- paste(var1, "~ var1_lag + var2_lag")
      formula2 <- paste(var2, "~ var1_lag + var2_lag")
      
      if (!is.null(covariates)) {
        formula1 <- paste(formula1, "+", paste(covariates, collapse = " + "))
        formula2 <- paste(formula2, "+", paste(covariates, collapse = " + "))
      }
      
      # Fit models
      model1 <- lm(as.formula(formula1), data = data)
      model2 <- lm(as.formula(formula2), data = data)
      
      return(list(
        model1 = model1,
        model2 = model2,
        method = "Cross-Lagged Modeling",
        var1 = var1,
        var2 = var2,
        data = data
      ))
    }

    # Time series model
    fit_time_series_model <- function(data, response, time, subject) {
      
      # Aggregate by time if multiple subjects
      if (length(unique(data[[subject]])) > 1) {
        time_data <- aggregate(data[[response]], by = list(time = data[[time]]), FUN = mean)
        names(time_data)[2] <- response
      } else {
        time_data <- data[c(time, response)]
        names(time_data) <- c("time", response)
      }
      
      # Fit ARIMA model
      ts_data <- ts(time_data[[response]])
      model <- auto.arima(ts_data, seasonal = input$long_seasonal)
      
      return(list(
        model = model,
        method = "Time Series Modeling",
        seasonal = input$long_seasonal,
        data = time_data
      ))
    }

    # Mixed growth model
    fit_mixed_growth_model <- function(data, response, time, subject, predictors, covariates, group) {
      
      # Similar to growth curve but with more complex random effects
      fixed_effects <- time
      
      if (input$long_mixed_shape == "quadratic") {
        data$time_sq <- data[[time]]^2
        fixed_effects <- paste(time, "+ time_sq")
      } else if (input$long_mixed_shape == "cubic") {
        data$time_sq <- data[[time]]^2
        data$time_cub <- data[[time]]^3
        fixed_effects <- paste(time, "+ time_sq + time_cub")
      }
      
      if (!is.null(predictors)) {
        fixed_effects <- paste(fixed_effects, "+", paste(predictors, collapse = " + "))
      }
      if (!is.null(covariates)) {
        fixed_effects <- paste(fixed_effects, "+", paste(covariates, collapse = " + "))
      }
      if (!is.null(group)) {
        fixed_effects <- paste(fixed_effects, "+", group)
      }
      
      # Complex random effects
      random_effects <- paste("(1 +", time, "|", subject, ")")
      if (!is.null(group)) {
        random_effects <- paste(random_effects, "+ (1|", group, ")")
      }
      
      formula_str <- paste(response, "~", fixed_effects, "+", random_effects)
      
      # Fit model
      model <- lmer(as.formula(formula_str), data = data)
      
      return(list(
        model = model,
        method = "Mixed Growth Modeling",
        formula = formula_str,
        shape = input$long_mixed_shape,
        data = data
      ))
    }

    # Generate plots
    generate_longitudinal_plots <- function(results, data, response, time, subject) {
      plots <- list()
      
      # Individual trajectories
      if (input$long_individual_plots) {
        p1 <- ggplot(data, aes_string(x = time, y = response, group = subject)) +
          geom_line(alpha = 0.3) +
          geom_point(alpha = 0.5) +
          labs(title = "Individual Trajectories",
               x = "Time", y = "Response") +
          theme_minimal()
        plots$individual <- p1
      }
      
      # Mean trajectory
      if (input$long_mean_plots) {
        mean_data <- aggregate(data[[response]], by = list(time = data[[time]]), FUN = mean)
        names(mean_data)[2] <- "mean_response"
        
        p2 <- ggplot(mean_data, aes(x = time, y = mean_response)) +
          geom_line(size = 1.5, color = "blue") +
          geom_point(size = 3, color = "red") +
          labs(title = "Mean Trajectory",
               x = "Time", y = "Mean Response") +
          theme_minimal()
        plots$mean <- p2
      }
      
      # Growth curves by group
      if (input$long_group_plots && !is.null(input$long_group) && input$long_group != "none") {
        group_data <- aggregate(data[[response]], 
                               by = list(time = data[[time]], group = data[[input$long_group]]), 
                               FUN = mean)
        names(group_data)[3] <- "mean_response"
        
        p3 <- ggplot(group_data, aes_string(x = "time", y = "mean_response", color = "group")) +
          geom_line(size = 1.2) +
          geom_point(size = 2) +
          labs(title = "Growth Curves by Group",
               x = "Time", y = "Mean Response", color = "Group") +
          theme_minimal()
        plots$group <- p3
      }
      
      # Residual plots
      if (input$long_residual_plots && !is.null(results$model)) {
        if (inherits(results$model, "lmerMod") || inherits(results$model, "glmerMod")) {
          residuals <- residuals(results$model)
          fitted <- fitted(results$model)
          
          p4 <- ggplot(data.frame(residuals = residuals, fitted = fitted), 
                      aes(x = fitted, y = residuals)) +
            geom_point(alpha = 0.6) +
            geom_hline(yintercept = 0, color = "red", linetype = "dashed") +
            geom_smooth(method = "loess", se = FALSE) +
            labs(title = "Residuals vs Fitted Values",
                 x = "Fitted Values", y = "Residuals") +
            theme_minimal()
          plots$residuals <- p4
        }
      }
      
      longitudinal_plots(plots)
    }

    # Output summary
    output$longitudinal_summary <- renderPrint({
      req(longitudinal_results())
      results <- longitudinal_results()
      
      cat("LONGITUDINAL ANALYSIS SUMMARY\n")
      cat("============================\n\n")
      cat("Method:", results$method, "\n")
      cat("Analysis Type:", input$long_analysis_type, "\n\n")
      
      if (!is.null(results$formula)) {
        cat("Formula:\n")
        cat(results$formula, "\n\n")
      }
      
      cat("Model Summary:\n")
      if (inherits(results$model, "list")) {
        # Multiple models (e.g., cross-lagged)
        for (i in seq_along(results$model)) {
          if (inherits(results$model[[i]], "lm") || inherits(results$model[[i]], "lmerMod")) {
            cat("\nModel", i, ":\n")
            print(summary(results$model[[i]]))
          }
        }
      } else {
        # Single model
        print(summary(results$model))
      }
      
      if (!is.null(results$data)) {
        cat("\nData Summary:\n")
        cat("Number of observations:", nrow(results$data), "\n")
        cat("Number of subjects:", length(unique(results$data[[input$long_subject]])), "\n")
        cat("Time points:", length(unique(results$data[[input$long_time]])), "\n")
      }
    })

    # Output plots
    output$longitudinal_plots <- renderPlot({
      req(longitudinal_plots())
      plots <- longitudinal_plots()
      if (length(plots) == 1) {
        print(plots[[1]])
      } else if (length(plots) > 1) {
        # Arrange multiple plots
        n_plots <- length(plots)
        n_cols <- min(2, n_plots)
        n_rows <- ceiling(n_plots / n_cols)
        do.call(grid.arrange, c(plots, ncol = n_cols))
      }
    })

    # Parameter estimates table
    output$longitudinal_parameters <- renderDataTable({
      req(longitudinal_results())
      results <- longitudinal_results()
      
      if (!is.null(results$model)) {
        if (inherits(results$model, "lmerMod") || inherits(results$model, "glmerMod")) {
          # Extract fixed effects
          coef_table <- summary(results$model)$coefficients
          coef_df <- data.frame(
            Parameter = rownames(coef_table),
            Estimate = round(coef_table[, "Estimate"], 4),
            Std_Error = round(coef_table[, "Std. Error"], 4),
            t_value = round(coef_table[, "t value"], 4),
            p_value = round(coef_table[, "Pr(>|t|)"], 4)
          )
          return(coef_df)
        } else if (inherits(results$model, "lm")) {
          # Extract coefficients
          coef_table <- summary(results$model)$coefficients
          coef_df <- data.frame(
            Parameter = rownames(coef_table),
            Estimate = round(coef_table[, "Estimate"], 4),
            Std_Error = round(coef_table[, "Std. Error"], 4),
            t_value = round(coef_table[, "t value"], 4),
            p_value = round(coef_table[, "Pr(>|t|)"], 4)
          )
          return(coef_df)
        }
      }
      
      # Fallback
      data.frame(
        Parameter = "No parameters available",
        Estimate = NA,
        Std_Error = NA,
        t_value = NA,
        p_value = NA
      )
    })

    # Model comparison table
    output$longitudinal_comparison_table <- renderDataTable({
      req(longitudinal_results())
      results <- longitudinal_results()
      
      # Create comparison table
      if (!is.null(results$model)) {
        if (inherits(results$model, "lmerMod") || inherits(results$model, "glmerMod")) {
          # AIC, BIC, log-likelihood
          comparison_df <- data.frame(
            Model = results$method,
            AIC = round(AIC(results$model), 2),
            BIC = round(BIC(results$model), 2),
            Log_Likelihood = round(logLik(results$model), 2),
            Deviance = round(deviance(results$model), 2)
          )
          return(comparison_df)
        } else if (inherits(results$model, "lm")) {
          comparison_df <- data.frame(
            Model = results$method,
            AIC = round(AIC(results$model), 2),
            BIC = round(BIC(results$model), 2),
            R_squared = round(summary(results$model)$r.squared, 4),
            Adj_R_squared = round(summary(results$model)$adj.r.squared, 4)
          )
          return(comparison_df)
        }
      }
      
      # Fallback
      data.frame(
        Model = "No model available",
        AIC = NA,
        BIC = NA,
        Log_Likelihood = NA,
        Deviance = NA
      )
    })

    # Prediction table
    output$longitudinal_prediction_table <- renderDataTable({
      req(longitudinal_results())
      results <- longitudinal_results()
      
      if (!is.null(results$model) && !is.null(results$data)) {
        # Generate predictions
        if (inherits(results$model, "lmerMod") || inherits(results$model, "glmerMod")) {
          pred_data <- data.frame(
            Subject = results$data[[input$long_subject]],
            Time = results$data[[input$long_time]],
            Observed = results$data[[input$long_response]],
            Predicted = round(fitted(results$model), 4),
            Residual = round(residuals(results$model), 4)
          )
          return(pred_data)
        } else if (inherits(results$model, "lm")) {
          pred_data <- data.frame(
            Subject = if(input$long_subject %in% names(results$data)) results$data[[input$long_subject]] else 1:nrow(results$data),
            Time = if(input$long_time %in% names(results$data)) results$data[[input$long_time]] else 1:nrow(results$data),
            Observed = results$data[[input$long_response]],
            Predicted = round(fitted(results$model), 4),
            Residual = round(residuals(results$model), 4)
          )
          return(pred_data)
        }
      }
      
      # Fallback
      data.frame(
        Subject = "No data available",
        Time = NA,
        Observed = NA,
        Predicted = NA,
        Residual = NA
      )
    })

    # Download results
    output$download_longitudinal_results <- downloadHandler(
      filename = function() {
        paste("longitudinal_analysis_results_", format(Sys.time(), "%Y%m%d_%H%M%S"), ".txt", sep = "")
      },
      content = function(file) {
        req(longitudinal_results())
        results <- longitudinal_results()
        sink(file)
        cat("LONGITUDINAL ANALYSIS RESULTS\n")
        cat("============================\n\n")
        cat("Method:", results$method, "\n")
        cat("Analysis Type:", input$long_analysis_type, "\n\n")
        if (!is.null(results$formula)) {
          cat("Formula:", results$formula, "\n\n")
        }
        cat("Model Summary:\n")
        print(summary(results$model))
        sink()
      }
    )

    # Reset functionality
    observeEvent(input$reset_longitudinal, {
      longitudinal_results(NULL)
      longitudinal_plots(NULL)
      updateSelectInput(session, "long_response", selected = "")
      updateSelectInput(session, "long_time", selected = "")
      updateSelectInput(session, "long_subject", selected = "")
      updateSelectInput(session, "long_predictors", selected = "")
      updateSelectInput(session, "long_covariates", selected = "")
      updateSelectInput(session, "long_group", selected = "none")
    })
  })
} 