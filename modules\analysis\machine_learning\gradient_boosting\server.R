# Gradient Boosting Server
gbServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Upload and parse data
    gbUploadData <- reactive({
      req(input$gbUserData)
      ext <- tools::file_ext(input$gbUserData$name)
      if (ext %in% c("csv", "txt")) {
        read.csv(input$gbUserData$datapath)
      } else if (ext %in% c("xls", "xlsx")) {
        if (!requireNamespace("readxl", quietly = TRUE)) stop("Install 'readxl' for Excel support.")
        readxl::read_excel(input$gbUserData$datapath)
      } else {
        stop("Unsupported file type.")
      }
    })

    # UI for selecting response, predictors, and parameters
    output$gbColSelectors <- renderUI({
      df <- gbUploadData()
      req(df)
      tagList(
        selectInput(ns("gbResponseCol"), "Response Variable", choices = names(df)),
        selectInput(ns("gbPredictors"), "Predictors", choices = names(df), multiple = TRUE),
        numericInput(ns("gbNrounds"), "Number of Rounds (nrounds)", 100, min = 10),
        numericInput(ns("gbEta"), "Learning Rate (eta)", 0.1, min = 0.01, max = 1, step = 0.01)
      )
    })

    # Run Gradient Boosting analysis
    gbResults <- eventReactive(input$goGB, {
      df <- gbUploadData()
      req(input$gbResponseCol, input$gbPredictors)
      gbResults_func(
        data = df,
        response = input$gbResponseCol,
        predictors = input$gbPredictors,
        nrounds = input$gbNrounds,
        eta = input$gbEta
      )
    })

    # Outputs
    output$gbHT <- renderUI({
      results <- gbResults()
      if (is.null(results)) return(NULL)
      tagList(
        tags$p(paste("Accuracy:", round(results$accuracy, 4))),
        DT::DTOutput(ns("gbImportanceTable"))
      )
    })
    output$gbImportanceTable <- DT::renderDT({
      results <- gbResults()
      if (is.null(results)) return(NULL)
      DT::datatable(results$importance)
    })
    output$gbPlot <- renderPlot({
      results <- gbResults()
      if (is.null(results) || is.null(results$plot)) return(NULL)
      results$plot
    })
    output$gbConclusionOutput <- renderUI({
      results <- gbResults()
      if (is.null(results)) return(NULL)
      tags$p("Gradient Boosting model fitted. See accuracy, feature importance, and plot above.")
    })
    output$renderGBData <- renderUI({
      req(gbUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("gbInitialUploadTable")), style = "width: 75%"),
        br(),
        br(),
        uiOutput(ns("gbColSelectors"))
      )
    })
    output$gbInitialUploadTable <- DT::renderDT({
      req(gbUploadData())
      DT::datatable(gbUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(gbUploadData())))))
    })
  })
} 