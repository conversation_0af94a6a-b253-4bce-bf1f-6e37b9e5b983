probDistUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        shinyjs::useShinyjs(),
        div(
          id = ns("inputPanel"),
          HTML("<label class='si-label'><b>Distribution</b></label>"),
          radioButtons(
            inputId  = ns("probability"),
            label    = NULL,
            choices  = c("Contingency Table", "Binomial", "Poisson", "Hypergeometric", "Negative Binomial", "Normal"),
            selected = "Binomial",
            inline   = FALSE
          ),
          # Placeholders for conditional panels for each distribution
          uiOutput(ns("distInputs")),
          br(),
          actionButton(
            inputId = ns("goProb"),
            label   = "Calculate",
            class   = "act-btn"
          )
        )
      ),
      mainPanel(
        uiOutput(ns("distOutputs"))
      )
    )
  )
} 