ClusterAnalysisUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("clustUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("clustVars"), "Variables for Clustering", choices = NULL, multiple = TRUE),
        selectInput(ns("clustMethod"), "Clustering Method", choices = c("K-means", "Hierarchical")),
        numericInput(ns("clustK"), "Number of Clusters (k)", value = 3, min = 2, step = 1),
        br(),
        actionButton(ns("goClust"), label = "Run Clustering", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("clustError")),
        plotOutput(ns("clustDendrogram")),
        plotOutput(ns("clustPlot")),
        plotOutput(ns("clustSilhouette"))
      )
    )
  )
} 