source("modules/calculations/anova.R")

AnovaInferenceServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # --- Reactives ---
    anovaUploadData <- eventReactive(input$anovaUserData, {
      handle_file_upload(input$anovaUserData)
    })
    
    anovaValidationErrors <- reactive({
      errors <- c()
      data <- anovaUploadData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (input$anovaFormat == "Multiple") {
        cols <- input$anovaMultiColumns
        if (is.null(cols) || length(cols) < 2) {
          errors <- c(errors, "Please select at least two columns for multi-column analysis.")
        } else {
          for (col_name in cols) {
            if (!is.numeric(data[[col_name]])) {
              errors <- c(errors, paste("Column", col_name, "must be numeric."))
            }
            if (length(unique(data[[col_name]])) < 2) {
              errors <- c(errors, paste("Column", col_name, "must have more than one unique value."))
            }
          }
        }
      } else { # Stacked format
        response_var <- input$anovaResponse
        factor_var <- input$anovaFactors
        if (is.null(response_var) || response_var == "") {
          errors <- c(errors, "Please select a response variable.")
        } else if (!is.numeric(data[[response_var]])) {
          errors <- c(errors, paste("Response variable", response_var, "must be numeric."))
        }
        if (is.null(factor_var) || factor_var == "") {
          errors <- c(errors, "Please select a factor variable.")
        }
      }
      errors
    })
    
    anovaResults <- eventReactive(input$goInference, {
      if (length(anovaValidationErrors()) > 0) {
        return(NULL)
      }
      anova_results_func(
        data = anovaUploadData(),
        format = input$anovaFormat,
        multi_columns = input$anovaMultiColumns,
        response_var = input$anovaResponse,
        factor_var = input$anovaFactors
      )
    })
    
    # --- Observers ---
    observeEvent(anovaUploadData(), {
      data <- anovaUploadData()
      updateSelectizeInput(session, 'anovaMultiColumns', choices = names(data), server = TRUE)
      updateSelectizeInput(session, 'anovaResponse', choices = names(data), server = TRUE)
      updateSelectizeInput(session, 'anovaFactors', choices = names(data), server = TRUE)
      
      output$anovaResults <- renderUI({
        if (!is.null(data) && is.data.frame(data)) {
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('anovaPreviewTable'))
          )
        }
      })
      output$anovaPreviewTable <- DT::renderDT({
        head(data, 20)
      })
    })
    
    observeEvent(input$goInference, {
      output$anovaResults <- renderUI({
        errors <- anovaValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in ANOVA", errors = errors)
        } else {
          results <- anovaResults()
          req(results)
          
          # Check for errors from the calculation function
          if (!is.null(results$error)) {
            errorScreenUI(title = "Calculation Error", errors = results$error)
          } else {
            sigLvl <- as.numeric(sub("%", "", input$anovaSigLvl)) / 100
            
            tagList(
              tabsetPanel(
                id = ns("anovaTabset"),
                selected = "Analysis",
                tabPanel(
                  id = ns("anovaAnalysis"),
                  title = "Analysis",
                  titlePanel("ANOVA Results"),
                  br(),
                  anova_render_ht_html(results, sigLvl)
                ),
                tabPanel(
                  id = ns("anovaDataSummary"),
                  title = "Data Summary",
                  anova_render_summary_html(results)
                ),
                tabPanel(
                  id = ns("anovaUploadedData"),
                  title = "Uploaded Data",
                  h4("Raw Data"),
                  DT::DTOutput(ns('anovaDataTable'))
                )
              )
            )
          }
        }
      })
      
      output$anovaDataTable <- DT::renderDT({
        req(anovaUploadData())
        DT::datatable(anovaUploadData(),
          options = list(pageLength = 25,
                         lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                         columnDefs = list(list(className = 'dt-center', targets = 0:ncol(anovaUploadData())))))
      })
    })
  })
}