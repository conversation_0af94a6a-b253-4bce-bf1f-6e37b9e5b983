# GAM calculation and output helpers

gam_uploadData_func <- function(gamUserData) {
  ext <- tools::file_ext(gamUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(gamUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(gamUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(gamUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(gamUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

gam_results_func <- function(data, response_var, predictor_vars, smooth_terms = NULL, family = "gaussian") {
  tryCatch({
    if (!requireNamespace("mgcv", quietly = TRUE)) {
      stop("Package 'mgcv' is required for GAM analysis.")
    }
    
    all_vars <- c(response_var, predictor_vars)
    model_data <- data[complete.cases(data[all_vars]), ]
    
    smooth_formula <- if (!is.null(smooth_terms)) paste0("s(", smooth_terms, ")", collapse = " + ") else NULL
    linear_predictors <- setdiff(predictor_vars, smooth_terms)
    
    formula_parts <- c(linear_predictors, smooth_formula)
    formula_str <- paste0("`", response_var, "` ~ ", paste(formula_parts, collapse = " + "))
    
    fit <- mgcv::gam(as.formula(formula_str), data = model_data, family = family, method = "REML")
    
    list(
      fit = fit,
      data = model_data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during GAM calculation:", e$message))
  })
}

gam_ht_html <- function(results) {
  # No single hypothesis test, summary is more informative
  tagList(
    h4("Generalized Additive Model (GAM)"),
    p("See summary table for model coefficients and diagnostics.")
  )
}

gam_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  out <- list(
    h4("Model Summary"),
    renderPrint(summary(results$fit))
  )
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

gam_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  plot(results$fit, pages = 1, main = "GAM Smooth Terms")
  # Residuals plot
  resids <- residuals(results$fit)
  plot(resids, type = 'h', main = 'GAM Residuals', ylab = 'Residuals')
}