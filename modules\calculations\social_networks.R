# Social Network Analysis calculations for CougarStats

#' Social Network Analysis Calculation
#' @param data Data frame with at least two columns (source, target)
#' @param source_col Name of source column
#' @param target_col Name of target column
#' @return List with summary and plot function
socialNetworksResults_func <- function(data, source_col, target_col) {
  if (!requireNamespace("igraph", quietly = TRUE)) stop("Please install the 'igraph' package.")
  if (!all(c(source_col, target_col) %in% names(data))) stop("Source/target columns not found in data.")
  edges <- data[, c(source_col, target_col)]
  g <- igraph::graph_from_data_frame(edges, directed = TRUE)

  # Summary statistics
  n_nodes <- igraph::gorder(g)
  n_edges <- igraph::gsize(g)
  deg <- igraph::degree(g)
  dens <- igraph::edge_density(g)
  centr <- igraph::betweenness(g)
  summ <- list(
    nodes = n_nodes,
    edges = n_edges,
    density = dens,
    degree = summary(deg),
    betweenness = summary(centr)
  )

  # Plot function
  plotfun <- function() {
    igraph::plot.igraph(g, vertex.size = 20, vertex.label.cex = 0.8,
                        edge.arrow.size = 0.5, main = "Social Network Graph")
  }

  list(summary = summ, plot = plotfun)
} 