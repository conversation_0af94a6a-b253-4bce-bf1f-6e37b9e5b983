# Correlation Test Server
CorrelationTestServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive with error handling
    corrUploadData <- eventReactive(input$corrUserData, {
      tryCatch({
        data <- handle_file_upload(input$corrUserData)
        
        # Validate uploaded data
        errors <- validate_data(data, min_rows = 2)
        if (length(errors) > 0) {
          log_error(paste("Data validation failed:", paste(errors, collapse = "; ")), "correlation_upload")
          return(NULL)
        }
        
        log_info("Data uploaded successfully", "correlation_upload")
        return(data)
        
      }, error = function(e) {
        log_error(e$message, "correlation_upload")
        return(NULL)
      })
    })
    
    # Update variable choices when data is uploaded
    observeEvent(corrUploadData(), {
      data <- corrUploadData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'corrXVar', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'corrYVar', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'corrVariables', choices = names(data), server = TRUE, multiple = TRUE)
      }
    })
    
    # Get correlation data reactive with validation
    corrData <- reactive({
      tryCatch({
        if (input$corrDataMethod == "Manual Entry") {
          # Validate manual entry
          x_errors <- validate_input(input$corrX, type = "character", required = TRUE)
          y_errors <- validate_input(input$corrY, type = "character", required = TRUE)
          
          if (length(x_errors) > 0 || length(y_errors) > 0) {
            log_error(paste("Manual entry validation failed:", paste(c(x_errors, y_errors), collapse = "; ")), "correlation_data")
            return(NULL)
          }
          
          x_vals <- createNumLst(input$corrX)
          y_vals <- createNumLst(input$corrY)
          
          # Validate parsed values
          if (length(x_vals) != length(y_vals)) {
            log_error("X and Y values have different lengths", "correlation_data")
            return(NULL)
          }
          
          if (length(x_vals) < 2) {
            log_error("At least 2 observations required", "correlation_data")
            return(NULL)
          }
          
          data.frame(X = x_vals, Y = y_vals)
          
        } else {
          # File upload method
          req(corrUploadData(), input$corrXVar, input$corrYVar)
          data <- corrUploadData()
          
          # Validate selected variables
          if (!input$corrXVar %in% names(data) || !input$corrYVar %in% names(data)) {
            log_error("Selected variables not found in data", "correlation_data")
            return(NULL)
          }
          
          x_vals <- data[[input$corrXVar]]
          y_vals <- data[[input$corrYVar]]
          
          # Validate variable types
          if (!is.numeric(x_vals) || !is.numeric(y_vals)) {
            log_error("Selected variables must be numeric", "correlation_data")
            return(NULL)
          }
          
          data.frame(X = x_vals, Y = y_vals)
        }
        
      }, error = function(e) {
        log_error(e$message, "correlation_data")
        return(NULL)
      })
    })
    
    # Enhanced correlation analysis using new calculations with error handling
    corrAnalysis <- reactive({
      req(corrData())
      data <- corrData()
      
      if (is.null(data)) return(NULL)
      
      # Remove missing values
      complete_data <- data[complete.cases(data), ]
      
      if (nrow(complete_data) < 2) {
        log_error("Insufficient complete cases for analysis", "correlation_analysis")
        return(NULL)
      }
      
      # Monitor performance
      monitor_performance({
        # Determine correlation method
        method <- tolower(input$corrType)
        
        # Validate method
        valid_methods <- c("pearson", "spearman", "kendall")
        if (!method %in% valid_methods) {
          log_error(paste("Invalid correlation method:", method), "correlation_analysis")
          return(NULL)
        }
        
        # Perform comprehensive correlation analysis
        result <- tryCatch({
          correlation_analysis(
            x = complete_data$X,
            y = complete_data$Y,
            method = method,
            conf_level = input$corrConfLevel,
            alternative = input$corrAlternative
          )
        }, error = function(e) {
          log_error(paste("Correlation analysis failed:", e$message), "correlation_analysis")
          return(NULL)
        })
        
        if (is.null(result)) return(NULL)
        
        # Add additional correlation methods for comparison
        if (method != "pearson") {
          result$pearson <- tryCatch({
            correlation_analysis(
              x = complete_data$X,
              y = complete_data$Y,
              method = "pearson",
              conf_level = input$corrConfLevel,
              alternative = input$corrAlternative
            )
          }, error = function(e) {
            log_warning(paste("Pearson correlation failed:", e$message), "correlation_analysis")
            return(NULL)
          })
        }
        
        if (method != "spearman") {
          result$spearman <- tryCatch({
            correlation_analysis(
              x = complete_data$X,
              y = complete_data$Y,
              method = "spearman",
              conf_level = input$corrConfLevel,
              alternative = input$corrAlternative
            )
          }, error = function(e) {
            log_warning(paste("Spearman correlation failed:", e$message), "correlation_analysis")
            return(NULL)
          })
        }
        
        if (method != "kendall") {
          result$kendall <- tryCatch({
            correlation_analysis(
              x = complete_data$X,
              y = complete_data$Y,
              method = "kendall",
              conf_level = input$corrConfLevel,
              alternative = input$corrAlternative
            )
          }, error = function(e) {
            log_warning(paste("Kendall correlation failed:", e$message), "correlation_analysis")
            return(NULL)
          })
        }
        
        # Add data to result
        result$data <- complete_data
        result$method <- method
        
        return(result)
        
      }, "Correlation Analysis")
    })
    
    # Multiple variable correlation analysis with error handling
    multiCorrAnalysis <- reactive({
      req(corrUploadData(), input$corrVariables)
      data <- corrUploadData()
      variables <- input$corrVariables
      
      if (length(variables) < 2) {
        log_error("At least 2 variables required for multiple correlation", "multi_correlation")
        return(NULL)
      }
      
      # Select only numeric variables
      numeric_vars <- variables[sapply(data[variables], is.numeric)]
      if (length(numeric_vars) < 2) {
        log_error("At least 2 numeric variables required", "multi_correlation")
        return(NULL)
      }
      
      # Use the new multiple correlation function with error handling
      tryCatch({
        monitor_performance({
          multiple_correlation_analysis(
            data = data[numeric_vars],
            methods = c("pearson", "spearman", "kendall"),
            conf_level = input$corrConfLevel,
            p_adjust = input$corrPAdjust
          )
        }, "Multiple Correlation Analysis")
        
      }, error = function(e) {
        log_error(paste("Multiple correlation analysis failed:", e$message), "multi_correlation")
        
        # Fallback to basic correlation matrix
        tryCatch({
          cor_matrix <- cor(data[numeric_vars], use = "complete.obs")
          list(
            correlation_matrix = cor_matrix,
            method = "pearson",
            variables = numeric_vars
          )
        }, error = function(e2) {
          log_error(paste("Fallback correlation matrix failed:", e2$message), "multi_correlation")
          return(NULL)
        })
      })
    })

    # Enhanced validation errors reactive
    corrValidationErrors <- reactive({
      errors <- c()
      
      tryCatch({
        if (input$corrDataMethod == "Manual Entry") {
          # Manual entry validation
          if (is.null(input$corrX) || input$corrX == "") {
            errors <- c(errors, "X values are required for manual entry.")
          }
          if (is.null(input$corrY) || input$corrY == "") {
            errors <- c(errors, "Y values are required for manual entry.")
          }
          if (!is.null(input$corrX) && !is.null(input$corrY) && input$corrX != "" && input$corrY != "") {
            x_vals <- createNumLst(input$corrX)
            y_vals <- createNumLst(input$corrY)
            if (length(x_vals) != length(y_vals)) {
              errors <- c(errors, "X and Y values must have the same number of observations.")
            }
            if (length(x_vals) < 2) {
              errors <- c(errors, "At least 2 observations are required.")
            }
            if (sd(x_vals) == 0) {
              errors <- c(errors, "X values must have variance (not all the same).")
            }
            if (sd(y_vals) == 0) {
              errors <- c(errors, "Y values must have variance (not all the same).")
            }
          }
        } else {
          # File upload validation
          data <- corrUploadData()
          if (is.null(data) || !is.data.frame(data)) {
            errors <- c(errors, "No data uploaded or file could not be read.")
            return(errors)
          }
          if (is.null(input$corrXVar) || input$corrXVar == "") {
            errors <- c(errors, "Please select an X variable.")
          }
          if (is.null(input$corrYVar) || input$corrYVar == "") {
            errors <- c(errors, "Please select a Y variable.")
          }
          if (!is.null(input$corrXVar) && !is.null(input$corrYVar) && 
              input$corrXVar != "" && input$corrYVar != "") {
            if (input$corrXVar == input$corrYVar) {
              errors <- c(errors, "X and Y variables must be different.")
            }
            x_vals <- data[[input$corrXVar]]
            y_vals <- data[[input$corrYVar]]
            if (!is.numeric(x_vals) || !is.numeric(y_vals)) {
              errors <- c(errors, "Both X and Y variables must be numeric.")
            }
            if (length(x_vals) != length(y_vals)) {
              errors <- c(errors, "X and Y variables must have the same number of observations.")
            }
            if (length(x_vals) < 2) {
              errors <- c(errors, "At least 2 observations are required.")
            }
            if (sd(x_vals, na.rm = TRUE) == 0) {
              errors <- c(errors, "X variable must have variance (not all the same).")
            }
            if (sd(y_vals, na.rm = TRUE) == 0) {
              errors <- c(errors, "Y variable must have variance (not all the same).")
            }
          }
        }
        
        # Log validation errors if any
        if (length(errors) > 0) {
          log_warning(paste("Validation errors:", paste(errors, collapse = "; ")), "correlation_validation")
        }
        
        errors
        
      }, error = function(e) {
        log_error(paste("Validation error check failed:", e$message), "correlation_validation")
        c("An error occurred during validation. Please try again.")
      })
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goCorr, {
      output$corrResults <- renderUI({
        errors <- corrValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Correlation Test", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("corrTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("corrAnalysis"),
                title = "Analysis",
                titlePanel("Correlation Analysis Results"),
                br(),
                h4("Correlation Coefficients"),
                tableOutput(ns('corrCoefficients')),
                br(),
                h4("Correlation Test Results"),
                tableOutput(ns('corrTestResults')),
                br(),
                h4("Effect Size and Interpretation"),
                uiOutput(ns('corrEffectSize')),
                br(),
                h4("Scatter Plot"),
                plotOutput(ns('corrScatterPlot'), height = "400px"),
                br(),
                h4("Interpretation"),
                uiOutput(ns('corrInterpretation'))
              ),
              tabPanel(
                id = ns("corrDataSummary"),
                title = "Data Summary/Diagnostics",
                h4("Descriptive Statistics"),
                tableOutput(ns('corrDescriptive')),
                br(),
                h4("Correlation Matrix"),
                tableOutput(ns('corrMatrix')),
                br(),
                h4("Assumptions Check"),
                uiOutput(ns('corrAssumptions')),
                br(),
                h4("Diagnostic Plots"),
                plotOutput(ns('corrDiagnosticPlots'), height = "600px")
              ),
              tabPanel(
                id = ns("corrMultiple"),
                title = "Multiple Correlations",
                h4("Multiple Variable Correlation Matrix"),
                tableOutput(ns('corrMultipleMatrix')),
                br(),
                h4("Correlation Heatmap"),
                plotOutput(ns('corrHeatmap'), height = "500px")
              ),
              tabPanel(
                id = ns("corrUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('corrDataTable'))
              )
            )
          )
        }
      })
    })
    
    # Correlation coefficients table
    output$corrCoefficients <- renderTable({
      req(corrAnalysis())
      analysis <- corrAnalysis()
      
      if (is.null(analysis)) {
        return(data.frame(
          Method = c("Pearson", "Spearman", "Kendall"),
          Correlation = rep("N/A", 3),
          P_value = rep("N/A", 3),
          CI_Lower = rep("N/A", 3),
          CI_Upper = rep("N/A", 3),
          stringsAsFactors = FALSE
        ))
      }
      
      # Check if using new correlation functions
      if ("correlation" %in% names(analysis)) {
        # New format
        results <- data.frame(
          Method = c("Pearson", "Spearman", "Kendall"),
          Correlation = c(
            ifelse(!is.null(analysis$pearson), round(analysis$pearson$correlation, 4), "N/A"),
            ifelse(!is.null(analysis$spearman), round(analysis$spearman$correlation, 4), "N/A"),
            ifelse(!is.null(analysis$kendall), round(analysis$kendall$correlation, 4), "N/A")
          ),
          P_value = c(
            ifelse(!is.null(analysis$pearson), round(analysis$pearson$p_value, 4), "N/A"),
            ifelse(!is.null(analysis$spearman), round(analysis$spearman$p_value, 4), "N/A"),
            ifelse(!is.null(analysis$kendall), round(analysis$kendall$p_value, 4), "N/A")
          ),
          CI_Lower = c(
            ifelse(!is.null(analysis$pearson), round(analysis$pearson$ci_lower, 4), "N/A"),
            ifelse(!is.null(analysis$spearman), round(analysis$spearman$ci_lower, 4), "N/A"),
            ifelse(!is.null(analysis$kendall), round(analysis$kendall$ci_lower, 4), "N/A")
          ),
          CI_Upper = c(
            ifelse(!is.null(analysis$pearson), round(analysis$pearson$ci_upper, 4), "N/A"),
            ifelse(!is.null(analysis$spearman), round(analysis$spearman$ci_upper, 4), "N/A"),
            ifelse(!is.null(analysis$kendall), round(analysis$kendall$ci_upper, 4), "N/A")
          ),
          stringsAsFactors = FALSE
        )
      } else {
        # Original format
        results <- data.frame(
          Method = c("Pearson", "Spearman", "Kendall"),
          Correlation = c(
            ifelse(!is.null(analysis$pearson), round(analysis$pearson$estimate, 4), "N/A"),
            ifelse(!is.null(analysis$spearman), round(analysis$spearman$estimate, 4), "N/A"),
            ifelse(!is.null(analysis$kendall), round(analysis$kendall$estimate, 4), "N/A")
          ),
          P_value = c(
            ifelse(!is.null(analysis$pearson), round(analysis$pearson$p.value, 4), "N/A"),
            ifelse(!is.null(analysis$spearman), round(analysis$spearman$p.value, 4), "N/A"),
            ifelse(!is.null(analysis$kendall), round(analysis$kendall$p.value, 4), "N/A")
          ),
          CI_Lower = rep("N/A", 3),
          CI_Upper = rep("N/A", 3),
          stringsAsFactors = FALSE
        )
      }
      results
    }, digits = 4)
    
    # Correlation test results table
    output$corrTestResults <- renderTable({
      req(corrAnalysis())
      analysis <- corrAnalysis()
      
      if (is.null(analysis)) {
        return(data.frame(
          Test = c("Pearson's r", "Spearman's rho", "Kendall's tau"),
          Statistic = rep("N/A", 3),
          P_value = rep("N/A", 3),
          DF = rep("N/A", 3),
          stringsAsFactors = FALSE
        ))
      }
      
      # Check if using new correlation functions
      if ("correlation" %in% names(analysis)) {
        # New format
        results <- data.frame(
          Test = c("Pearson's r", "Spearman's rho", "Kendall's tau"),
          Statistic = c(
            ifelse(!is.null(analysis$pearson), round(analysis$pearson$statistic, 4), "N/A"),
            ifelse(!is.null(analysis$spearman), round(analysis$spearman$statistic, 4), "N/A"),
            ifelse(!is.null(analysis$kendall), round(analysis$kendall$statistic, 4), "N/A")
          ),
          P_value = c(
            ifelse(!is.null(analysis$pearson), round(analysis$pearson$p_value, 4), "N/A"),
            ifelse(!is.null(analysis$spearman), round(analysis$spearman$p_value, 4), "N/A"),
            ifelse(!is.null(analysis$kendall), round(analysis$kendall$p_value, 4), "N/A")
          ),
          DF = c(
            ifelse(!is.null(analysis$pearson), analysis$pearson$df, "N/A"),
            ifelse(!is.null(analysis$spearman), "N/A", "N/A"),
            ifelse(!is.null(analysis$kendall), "N/A", "N/A")
          ),
          stringsAsFactors = FALSE
        )
      } else {
        # Original format
        results <- data.frame(
          Test = c("Pearson's r", "Spearman's rho", "Kendall's tau"),
          Statistic = c(
            ifelse(!is.null(analysis$pearson), round(analysis$pearson$statistic, 4), "N/A"),
            ifelse(!is.null(analysis$spearman), round(analysis$spearman$statistic, 4), "N/A"),
            ifelse(!is.null(analysis$kendall), round(analysis$kendall$statistic, 4), "N/A")
          ),
          P_value = c(
            ifelse(!is.null(analysis$pearson), round(analysis$pearson$p.value, 4), "N/A"),
            ifelse(!is.null(analysis$spearman), round(analysis$spearman$p.value, 4), "N/A"),
            ifelse(!is.null(analysis$kendall), round(analysis$kendall$p.value, 4), "N/A")
          ),
          DF = rep("N/A", 3),
          stringsAsFactors = FALSE
        )
      }
      results
    }, digits = 4)
    
    # Effect size and interpretation
    output$corrEffectSize <- renderUI({
      req(corrAnalysis())
      analysis <- corrAnalysis()
      
      if (is.null(analysis)) {
        return(tagList(
          p("Effect size interpretation will appear here.")
        ))
      }
      
      # Get correlation coefficient for the selected method
      method <- analysis$method
      if ("correlation" %in% names(analysis)) {
        cor_value <- analysis$correlation
        p_value <- analysis$p_value
      } else {
        cor_value <- if (method == "pearson" && !is.null(analysis$pearson)) analysis$pearson$estimate else
                     if (method == "spearman" && !is.null(analysis$spearman)) analysis$spearman$estimate else
                     if (method == "kendall" && !is.null(analysis$kendall)) analysis$kendall$estimate else 0
        
        p_value <- if (method == "pearson" && !is.null(analysis$pearson)) analysis$pearson$p.value else
                   if (method == "spearman" && !is.null(analysis$spearman)) analysis$spearman$p.value else
                   if (method == "kendall" && !is.null(analysis$kendall)) analysis$kendall$p.value else 1
      }
      
      # Calculate effect size interpretation
      abs_cor <- abs(cor_value)
      effect_size <- if (abs_cor < 0.1) "Negligible" else
                     if (abs_cor < 0.3) "Small" else
                     if (abs_cor < 0.5) "Medium" else "Large"
      
      # Calculate r-squared
      r_squared <- cor_value^2
      
      # Calculate Cohen's guidelines
      cohen_interpretation <- if (abs_cor < 0.1) "Negligible (Cohen's d < 0.1)" else
                             if (abs_cor < 0.3) "Small (Cohen's d = 0.1-0.3)" else
                             if (abs_cor < 0.5) "Medium (Cohen's d = 0.3-0.5)" else "Large (Cohen's d > 0.5)"
      
      tagList(
        p(strong("Correlation coefficient (r): "), round(cor_value, 4)),
        p(strong("R-squared (r²): "), round(r_squared, 4), " - This means ", round(r_squared * 100, 1), "% of the variance in Y is explained by X"),
        p(strong("Effect size: "), effect_size, " (", cohen_interpretation, ")"),
        p(strong("P-value: "), round(p_value, 4)),
        br(),
        p(strong("Effect Size Guidelines (Cohen, 1988):")),
        p("- 0.0 to 0.1: Negligible"),
        p("- 0.1 to 0.3: Small"),
        p("- 0.3 to 0.5: Medium"),
        p("- 0.5 to 1.0: Large")
      )
    })
    
    # Scatter plot
    output$corrScatterPlot <- renderPlot({
      req(corrAnalysis())
      analysis <- corrAnalysis()
      
      if (is.null(analysis)) {
        plot.new()
        text(0.5, 0.5, "Scatter plot will appear here")
        return()
      }
      
      data <- analysis$data
      
      # Get correlation coefficient for the selected method
      method <- analysis$method
      if ("correlation" %in% names(analysis)) {
        cor_value <- analysis$correlation
      } else {
        cor_value <- if (method == "pearson" && !is.null(analysis$pearson)) analysis$pearson$estimate else
                     if (method == "spearman" && !is.null(analysis$spearman)) analysis$spearman$estimate else
                     if (method == "kendall" && !is.null(analysis$kendall)) analysis$kendall$estimate else 0
      }
      
      # Create scatter plot
      plot(data$X, data$Y, 
           main = paste("Scatter Plot with", stringr::str_to_title(method), "Correlation"),
           xlab = ifelse(input$corrDataMethod == "Manual Entry", "X", input$corrXVar),
           ylab = ifelse(input$corrDataMethod == "Manual Entry", "Y", input$corrYVar),
           pch = 19, col = "blue", cex = 1.2)
      
      # Add correlation line
      if (method == "pearson") {
        abline(lm(Y ~ X, data = data), col = "red", lwd = 2)
      }
      
      # Add correlation coefficient text
      text(par("usr")[1] + 0.1 * diff(par("usr")[1:2]), 
           par("usr")[4] - 0.1 * diff(par("usr")[3:4]),
           paste("r =", round(cor_value, 4)), 
           pos = 4, cex = 1.2, font = 2)
    })
    
    # Diagnostic plots
    output$corrDiagnosticPlots <- renderPlot({
      req(corrAnalysis())
      analysis <- corrAnalysis()
      
      if (is.null(analysis)) {
        plot.new()
        text(0.5, 0.5, "Diagnostic plots will appear here")
        return()
      }
      
      data <- analysis$data
      
      # Create diagnostic plots
      par(mfrow = c(2, 2))
      
      # 1. Q-Q plot for normality
      qqnorm(data$X, main = "Q-Q Plot for X Variable")
      qqline(data$X, col = "red")
      
      qqnorm(data$Y, main = "Q-Q Plot for Y Variable")
      qqline(data$Y, col = "red")
      
      # 2. Residual plot (if linear relationship)
      if (analysis$method == "pearson") {
        lm_fit <- lm(Y ~ X, data = data)
        plot(fitted(lm_fit), residuals(lm_fit), 
             main = "Residual Plot",
             xlab = "Fitted Values", ylab = "Residuals")
        abline(h = 0, col = "red", lty = 2)
      } else {
        plot(data$X, data$Y, main = "Data Distribution", xlab = "X", ylab = "Y")
      }
      
      # 3. Boxplot for outliers
      boxplot(data$X, data$Y, names = c("X", "Y"), 
              main = "Boxplot for Outlier Detection",
              col = c("lightblue", "lightgreen"))
    })
    
    # Multiple correlation matrix
    output$corrMultipleMatrix <- renderTable({
      req(multiCorrAnalysis())
      analysis <- multiCorrAnalysis()
      
      if (is.null(analysis)) {
        return(data.frame(Message = "No multiple correlation analysis available"))
      }
      
      if ("correlation_matrix" %in% names(analysis)) {
        # New format
        cor_matrix <- analysis$correlation_matrix
        result <- as.data.frame(cor_matrix)
        result$Variable <- rownames(result)
        result <- result[, c("Variable", colnames(cor_matrix))]
        rownames(result) <- NULL
        return(result)
      } else {
        # Fallback
        return(data.frame(Message = "Correlation matrix not available"))
      }
    }, digits = 4)
    
    # Correlation heatmap
    output$corrHeatmap <- renderPlot({
      req(multiCorrAnalysis())
      analysis <- multiCorrAnalysis()
      
      if (is.null(analysis) || !("correlation_matrix" %in% names(analysis))) {
        plot.new()
        text(0.5, 0.5, "Correlation heatmap will appear here")
        return()
      }
      
      cor_matrix <- analysis$correlation_matrix
      
      # Create heatmap
      heatmap(cor_matrix, 
              main = "Correlation Heatmap",
              col = colorRampPalette(c("blue", "white", "red"))(100),
              symm = TRUE,
              margins = c(10, 10))
    })
    
    # Interpretation
    output$corrInterpretation <- renderUI({
      req(corrAnalysis())
      analysis <- corrAnalysis()
      
      if (is.null(analysis)) {
        return(tagList(
          p("Correlation interpretation will appear here."),
          p("Strength of correlation:"),
          p("- 0.0 to 0.1: Negligible"),
          p("- 0.1 to 0.3: Small"),
          p("- 0.3 to 0.5: Medium"),
          p("- 0.5 to 1.0: Large")
        ))
      }
      
      # Get correlation coefficient for the selected method
      method <- analysis$method
      if ("correlation" %in% names(analysis)) {
        cor_value <- analysis$correlation
        p_value <- analysis$p_value
      } else {
        cor_value <- if (method == "pearson" && !is.null(analysis$pearson)) analysis$pearson$estimate else
                     if (method == "spearman" && !is.null(analysis$spearman)) analysis$spearman$estimate else
                     if (method == "kendall" && !is.null(analysis$kendall)) analysis$kendall$estimate else 0
        
        p_value <- if (method == "pearson" && !is.null(analysis$pearson)) analysis$pearson$p.value else
                   if (method == "spearman" && !is.null(analysis$spearman)) analysis$spearman$p.value else
                   if (method == "kendall" && !is.null(analysis$kendall)) analysis$kendall$p.value else 1
      }
      
      # Determine strength and significance
      abs_cor <- abs(cor_value)
      strength <- if (abs_cor < 0.1) "Negligible" else
                  if (abs_cor < 0.3) "Small" else
                  if (abs_cor < 0.5) "Medium" else "Large"
      
      direction <- if (cor_value > 0) "positive" else "negative"
      significance <- if (p_value < 0.001) "highly significant (p < 0.001)" else
                      if (p_value < 0.01) "very significant (p < 0.01)" else
                      if (p_value < 0.05) "significant (p < 0.05)" else "not significant"
      
      tagList(
        p(strong("Correlation coefficient: "), round(cor_value, 4)),
        p(strong("P-value: "), round(p_value, 4)),
        p(strong("Interpretation: "), "The correlation is ", strength, " and ", direction, "."),
        p(strong("Significance: "), "The correlation is ", significance, "."),
        br(),
        p(strong("Strength of correlation:")),
        p("- 0.0 to 0.1: Negligible"),
        p("- 0.1 to 0.3: Small"),
        p("- 0.3 to 0.5: Medium"),
        p("- 0.5 to 1.0: Large")
      )
    })
    
    # Descriptive statistics
    output$corrDescriptive <- renderTable({
      req(corrAnalysis())
      analysis <- corrAnalysis()
      
      if (is.null(analysis)) {
        return(data.frame(
          Variable = c("X", "Y"),
          Mean = rep("N/A", 2),
          SD = rep("N/A", 2),
          Min = rep("N/A", 2),
          Max = rep("N/A", 2),
          N = rep("N/A", 2),
          stringsAsFactors = FALSE
        ))
      }
      
      data <- analysis$data
      
      stats <- data.frame(
        Variable = c("X", "Y"),
        Mean = c(round(mean(data$X, na.rm = TRUE), 4), round(mean(data$Y, na.rm = TRUE), 4)),
        SD = c(round(sd(data$X, na.rm = TRUE), 4), round(sd(data$Y, na.rm = TRUE), 4)),
        Min = c(round(min(data$X, na.rm = TRUE), 4), round(min(data$Y, na.rm = TRUE), 4)),
        Max = c(round(max(data$X, na.rm = TRUE), 4), round(max(data$Y, na.rm = TRUE), 4)),
        N = c(sum(!is.na(data$X)), sum(!is.na(data$Y))),
        stringsAsFactors = FALSE
      )
      stats
    }, digits = 4)
    
    # Correlation matrix
    output$corrMatrix <- renderTable({
      req(corrAnalysis())
      analysis <- corrAnalysis()
      
      if (is.null(analysis)) {
        matrix_data <- matrix(c("1.000", "N/A", "N/A", "1.000"), nrow = 2)
        colnames(matrix_data) <- c("X", "Y")
        rownames(matrix_data) <- c("X", "Y")
        return(as.data.frame(matrix_data))
      }
      
      data <- analysis$data
      cor_matrix <- cor(data, use = "complete.obs")
      
      result <- data.frame(
        Variable = c("X", "Y"),
        X = c(round(cor_matrix[1,1], 4), round(cor_matrix[2,1], 4)),
        Y = c(round(cor_matrix[1,2], 4), round(cor_matrix[2,2], 4)),
        stringsAsFactors = FALSE
      )
      names(result) <- c("Variable", "X", "Y")
      result
    })
    
    # Assumptions check
    output$corrAssumptions <- renderUI({
      req(corrAnalysis())
      analysis <- corrAnalysis()
      
      if (is.null(analysis)) {
        return(tagList(
          h5("Key Assumptions:"),
          p("1. Variables are continuous (for Pearson)"),
          p("2. Linear relationship (for Pearson)"),
          p("3. Bivariate normal distribution (for Pearson)"),
          p("4. Independent observations"),
          p("5. No outliers")
        ))
      }
      
      data <- analysis$data
      
      # Check for outliers using IQR method
      x_outliers <- boxplot.stats(data$X)$out
      y_outliers <- boxplot.stats(data$Y)$out
      outlier_check <- if (length(x_outliers) > 0 || length(y_outliers) > 0) {
        paste("Outliers detected: X =", length(x_outliers), ", Y =", length(y_outliers))
      } else {
        "No outliers detected"
      }
      
      tagList(
        h5("Key Assumptions:"),
        p("1. Variables are continuous (for Pearson)"),
        p("2. Linear relationship (for Pearson)"),
        p("3. Bivariate normal distribution (for Pearson)"),
        p("4. Independent observations"),
        p("5. No outliers"),
        br(),
        p(strong("Outlier check: "), outlier_check)
      )
    })
    
    # Data table output
    output$corrDataTable <- DT::renderDT({
      if (input$corrDataMethod == "Manual Entry") {
        req(corrData())
        DT::datatable(corrData(),
          options = list(pageLength = 25,
                         lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                         columnDefs = list(list(className = 'dt-center', targets = 0:ncol(corrData())))))
      } else {
        req(corrUploadData())
        DT::datatable(corrUploadData(),
          options = list(pageLength = 25,
                         lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                         columnDefs = list(list(className = 'dt-center', targets = 0:ncol(corrUploadData())))))
      }
    })
  })
} 