# Test Suite for Machine Learning Modules
# Tests: Supervised Learning, Unsupervised Learning, Model Comparison, Feature Selection

library(testthat)

# Test Supervised Machine Learning Module
test_that("Supervised Machine Learning Module", {
  # Load test data
  data <- load_test_data("supervised_ml")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 3)
  
  # Test supervised learning
  if (exists("supervised_ml_analysis")) {
    # Test classification
    result_class <- supervised_ml_analysis(data, names(data)[1], names(data)[2:min(4, ncol(data))], 
                                         task = "classification", method = "random_forest")
    expect_true(is.list(result_class))
    expect_true("model" %in% names(result_class))
    expect_true("predictions" %in% names(result_class))
    expect_true("accuracy" %in% names(result_class))
    
    # Test regression
    result_reg <- supervised_ml_analysis(data, names(data)[1], names(data)[2:min(4, ncol(data))], 
                                       task = "regression", method = "linear_regression")
    expect_true(is.list(result_reg))
    expect_true("model" %in% names(result_reg))
    expect_true("predictions" %in% names(result_reg))
    expect_true("r_squared" %in% names(result_reg))
  }
  
  cat("  ✓ Supervised ML: Classification and regression tested\n")
})

# Test Unsupervised Machine Learning Module
test_that("Unsupervised Machine Learning Module", {
  # Load test data
  data <- load_test_data("unsupervised_ml")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 3)
  
  # Test unsupervised learning
  if (exists("unsupervised_ml_analysis")) {
    # Test clustering
    result_cluster <- unsupervised_ml_analysis(data, names(data), task = "clustering", method = "kmeans", k = 3)
    expect_true(is.list(result_cluster))
    expect_true("clusters" %in% names(result_cluster))
    expect_true("centers" %in% names(result_cluster))
    
    # Test dimensionality reduction
    result_dim <- unsupervised_ml_analysis(data, names(data), task = "dimensionality_reduction", method = "pca")
    expect_true(is.list(result_dim))
    expect_true("reduced_data" %in% names(result_dim))
    expect_true("explained_variance" %in% names(result_dim))
  }
  
  cat("  ✓ Unsupervised ML: Clustering and dimensionality reduction tested\n")
})

# Test Model Comparison Module
test_that("Model Comparison Module", {
  # Load test data
  data <- load_test_data("model_comparison")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 3)
  
  # Test model comparison
  if (exists("model_comparison_analysis")) {
    methods <- c("linear_regression", "random_forest", "svm")
    result <- model_comparison_analysis(data, names(data)[1], names(data)[2:min(4, ncol(data))], methods)
    expect_true(is.list(result))
    expect_true("comparison_table" %in% names(result))
    expect_true("best_model" %in% names(result))
    expect_true("performance_metrics" %in% names(result))
  }
  
  cat("  ✓ Model Comparison: Multiple model evaluation tested\n")
})

# Test Feature Selection Module
test_that("Feature Selection Module", {
  # Load test data
  data <- load_test_data("feature_selection")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 4)
  
  # Test feature selection
  if (exists("feature_selection_analysis")) {
    # Test filter methods
    result_filter <- feature_selection_analysis(data, names(data)[1], names(data)[2:min(6, ncol(data))], 
                                              method = "filter", filter_type = "correlation")
    expect_true(is.list(result_filter))
    expect_true("selected_features" %in% names(result_filter))
    expect_true("feature_scores" %in% names(result_filter))
    
    # Test wrapper methods
    result_wrapper <- feature_selection_analysis(data, names(data)[1], names(data)[2:min(6, ncol(data))], 
                                                method = "wrapper", wrapper_type = "forward")
    expect_true(is.list(result_wrapper))
    expect_true("selected_features" %in% names(result_wrapper))
    expect_true("performance_history" %in% names(result_wrapper))
  }
  
  cat("  ✓ Feature Selection: Filter and wrapper methods tested\n")
})

# Test Advanced Survival Analysis Module
test_that("Advanced Survival Analysis Module", {
  # Load test data
  data <- load_test_data("advanced_survival")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 3)
  
  # Test advanced survival analysis
  if (exists("advanced_survival_analysis")) {
    result <- advanced_survival_analysis(data, names(data)[1], names(data)[2], names(data)[3])
    expect_true(is.list(result))
    expect_true("survival_curves" %in% names(result))
    expect_true("hazard_functions" %in% names(result))
    expect_true("model_comparison" %in% names(result))
  }
  
  cat("  ✓ Advanced Survival: Complex survival modeling tested\n")
})

# Test Interactive Dashboards Module
test_that("Interactive Dashboards Module", {
  # Load test data
  data <- load_test_data("interactive_dashboards")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test dashboard functionality
  if (exists("interactive_dashboard_analysis")) {
    result <- interactive_dashboard_analysis(data, names(data))
    expect_true(is.list(result))
    expect_true("dashboard_components" %in% names(result))
    expect_true("interactive_plots" %in% names(result))
  }
  
  cat("  ✓ Interactive Dashboards: Dashboard functionality tested\n")
})

# Test Bootstrap Analysis Module
test_that("Bootstrap Analysis Module", {
  # Load test data
  data <- load_test_data("bootstrap")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test bootstrap analysis
  if (exists("bootstrap_analysis")) {
    result <- bootstrap_analysis(data, names(data)[1], names(data)[2], n_bootstrap = 1000)
    expect_true(is.list(result))
    expect_true("bootstrap_samples" %in% names(result))
    expect_true("confidence_intervals" %in% names(result))
    expect_true("bias_correction" %in% names(result))
  }
  
  cat("  ✓ Bootstrap Analysis: Resampling methods tested\n")
})

# Test Permutation Tests Module
test_that("Permutation Tests Module", {
  # Load test data
  data <- load_test_data("permutation_tests")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test permutation tests
  if (exists("permutation_tests_analysis")) {
    result <- permutation_tests_analysis(data, names(data)[1], names(data)[2], n_permutations = 1000)
    expect_true(is.list(result))
    expect_true("permutation_distribution" %in% names(result))
    expect_true("p_value" %in% names(result))
    expect_true("observed_statistic" %in% names(result))
  }
  
  cat("  ✓ Permutation Tests: Nonparametric hypothesis testing tested\n")
})

# Test Custom Test Module (ML context)
test_that("Custom Test Module (ML context)", {
  # Load test data
  data <- load_test_data("custom_test")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test custom test in ML context
  if (exists("custom_test_analysis")) {
    result <- custom_test_analysis(data, names(data)[1], names(data)[2], 
                                  formula_str = "y ~ x", test_type = "ml_model")
    expect_true(is.list(result))
    expect_true("model_performance" %in% names(result))
    expect_true("cross_validation" %in% names(result))
  }
  
  cat("  ✓ Custom Test (ML): Custom ML model testing tested\n")
})

# Test Simulation Module (ML context)
test_that("Simulation Module (ML context)", {
  # Load test data
  data <- load_test_data("simulation")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test simulation in ML context
  if (exists("simulation_analysis")) {
    result <- simulation_analysis(n_sim = 100, n_obs = 100, n_features = 5, 
                                 model_type = "classification", noise_level = 0.1)
    expect_true(is.list(result))
    expect_true("simulated_datasets" %in% names(result))
    expect_true("model_performance" %in% names(result))
    expect_true("performance_distribution" %in% names(result))
  }
  
  cat("  ✓ Simulation (ML): ML model simulation tested\n")
})

# Test Data Summarization Module (ML context)
test_that("Data Summarization Module (ML context)", {
  # Load test data
  data <- load_test_data("data_summarization")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 3)
  
  # Test data summarization for ML
  if (exists("data_summarization_analysis")) {
    result <- data_summarization_analysis(data, ml_context = TRUE)
    expect_true(is.list(result))
    expect_true("feature_summary" %in% names(result))
    expect_true("data_quality_report" %in% names(result))
    expect_true("ml_readiness_assessment" %in% names(result))
  }
  
  cat("  ✓ Data Summarization (ML): ML data preparation tested\n")
})

# Test Missingness Visualization Module (ML context)
test_that("Missingness Visualization Module (ML context)", {
  # Load test data
  data <- load_test_data("missingness_viz")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test missingness analysis for ML
  if (exists("missingness_analysis")) {
    result <- missingness_analysis(data, ml_context = TRUE)
    expect_true(is.list(result))
    expect_true("missing_patterns" %in% names(result))
    expect_true("imputation_recommendations" %in% names(result))
    expect_true("ml_impact_assessment" %in% names(result))
  }
  
  cat("  ✓ Missingness Visualization (ML): ML missing data analysis tested\n")
})

# Test Outlier Detection Module (ML context)
test_that("Outlier Detection Module (ML context)", {
  # Load test data
  data <- load_test_data("outlier_detection")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test outlier detection for ML
  if (exists("outlier_detection_analysis")) {
    result <- outlier_detection_analysis(data, names(data)[1], ml_context = TRUE)
    expect_true(is.list(result))
    expect_true("outliers" %in% names(result))
    expect_true("ml_impact_analysis" %in% names(result))
    expect_true("treatment_recommendations" %in% names(result))
  }
  
  cat("  ✓ Outlier Detection (ML): ML outlier analysis tested\n")
})

# Test Variable Transformation Module (ML context)
test_that("Variable Transformation Module (ML context)", {
  # Load test data
  data <- load_test_data("variable_transformation")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 2)
  
  # Test variable transformation for ML
  if (exists("variable_transformation_analysis")) {
    result <- variable_transformation_analysis(data, names(data)[1], "standardization", ml_context = TRUE)
    expect_true(is.list(result))
    expect_true("transformed_data" %in% names(result))
    expect_true("transformation_impact" %in% names(result))
    expect_true("ml_compatibility" %in% names(result))
  }
  
  cat("  ✓ Variable Transformation (ML): ML data preprocessing tested\n")
})

# Test Pairwise Plot Matrix Module (ML context)
test_that("Pairwise Plot Matrix Module (ML context)", {
  # Load test data
  data <- load_test_data("pairwise_plot_matrix")
  
  # Test basic functionality
  expect_true(is.data.frame(data))
  expect_true(ncol(data) >= 3)
  
  # Test pairwise plots for ML
  if (exists("pairwise_plot_analysis")) {
    result <- pairwise_plot_analysis(data, names(data)[1:min(4, ncol(data))], ml_context = TRUE)
    expect_true(is.list(result))
    expect_true("correlation_matrix" %in% names(result))
    expect_true("feature_relationships" %in% names(result))
    expect_true("ml_insights" %in% names(result))
  }
  
  cat("  ✓ Pairwise Plot Matrix (ML): ML feature analysis tested\n")
})

cat("Machine Learning Modules: All tests completed\n") 