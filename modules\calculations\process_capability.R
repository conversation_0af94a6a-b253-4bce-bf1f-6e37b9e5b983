# Process Capability Analysis calculation and output helpers

process_capability_uploadData_func <- function(pcUserData, variable) {
  tryCatch(
    {
      if (is.null(pcUserData) || is.null(variable)) {
        return(NULL)
      }
      
      # Read data based on file type
      if (grepl("\\.csv$", pcUserData$name, ignore.case = TRUE)) {
        df <- read.csv(pcUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.tsv$", pcUserData$name, ignore.case = TRUE)) {
        df <- read.tsv(pcUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.xlsx$", pcUserData$name, ignore.case = TRUE)) {
        df <- readxl::read_excel(pcUserData$datapath)
      } else {
        stop("Unsupported file type. Please upload a CSV, TSV, or XLSX file.")
      }
      
      if (!variable %in% names(df)) {
        stop(paste("Variable '", variable, "' not found in the uploaded file.", sep = ""))
      }
      
      # Return the selected variable as a numeric vector
      return(as.numeric(df[[variable]]))
    },
    error = function(e) {
      # Return a list with an error message
      return(list(error = paste("Error reading data:", e$message)))
    }
  )
}

process_capability_results_func <- function(data, lsl, usl) {
  tryCatch({
    if (is.null(data) || length(data) == 0) {
      stop("No data provided for analysis.")
    }
    
    if (!requireNamespace("qcc", quietly = TRUE)) {
      stop("Package 'qcc' is required for Process Capability analysis.")
    }
    
    # Perform process capability analysis
    pc <- qcc::process.capability(qcc::qcc(data, type = "xbar.one"), spec.limits = c(lsl, usl))
    
    list(
      pc_results = pc,
      summary = summary(pc),
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Process Capability analysis:", e$message))
  })
}

process_capability_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(
      h4("Error"),
      p(results$error)
    ))
  }
  
  tagList(
    h4("Process Capability Analysis")
  )
}

process_capability_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  
  renderPrint({
    results$summary
  })
}

process_capability_plot <- function(results) {
  if (!is.null(results$error) || is.null(results$pc_results)) {
    return(NULL)
  }
  
  plot(results$pc_results)
}