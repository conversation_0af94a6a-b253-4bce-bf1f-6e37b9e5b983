# One Sample Inference UI
# Extracted and modularized from statInfr.R

OneSampleInferenceSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    radioButtons(
      inputId      = ns("popuParameter"),
      label        = strong("Parameter of Interest"),
      choiceValues = list("Population Mean", "Population Standard Deviation", "Population Proportion"),
      choiceNames  = list(HTML("Population Mean (\\( \\mu \\)) "), HTML("Population Standard Deviation (\\( \\sigma\\)) "), HTML("Population Proportion (\\( p\\))")),
      selected     = "Population Mean",
      inline       = FALSE
    ),
    conditionalPanel(
      ns = ns,
      condition = "input.popuParameter == 'Population Mean'",
      radioButtons(
        inputId      = ns("dataAvailability"),
        label        = strong("Data Availability"),
        choiceValues = list("Summarized Data", "Enter Raw Data", "Upload Data"),
        choiceNames  = list("Summarized Data", "Enter Raw Data", "Upload Data"),
        selected     = "Summarized Data",
        inline       = TRUE
      ),
      conditionalPanel(
        ns = ns,
        condition = "input.dataAvailability == 'Summarized Data'",
        numericInput(ns("sampleSize"), strong("Sample Size (n)"), 18, min = 1, step = 1),
        numericInput(ns("sampleMean"), strong("Sample Mean (\u0305x)"), 103.5375, step = 0.00001),
        radioButtons(ns("sigmaKnown"), strong("Is Population Standard Deviation (sigma) known?"),
          choiceValues = list("Known", "Unknown"),
          choiceNames = list("Known", "Unknown"),
          selected = "Known", inline = TRUE
        ),
        conditionalPanel(
          ns = ns,
          condition = "input.sigmaKnown == 'Known'",
          numericInput(ns("popuSD"), strong("Population Standard Deviation (sigma) Value"), 8.25, min = 0.00001, step = 0.00001)
        ),
        conditionalPanel(
          ns = ns,
          condition = "input.sigmaKnown == 'Unknown'",
          numericInput(ns("sampSD"), strong("Sample Standard Deviation (s) Value"), 4.78, min = 0.00001, step = 0.00001)
        )
      ),
      conditionalPanel(
        ns = ns,
        condition = "input.dataAvailability == 'Enter Raw Data'",
        textAreaInput(ns("sample1"), strong("Sample"), "202, 210, 215, 220, 220, 224, 225, 228, 228, 228", placeholder = "Enter values separated by a comma with decimals as points", rows = 3),
        radioButtons(ns("sigmaKnownRaw"), strong("Population Standard Deviation (sigma)"),
          choiceValues = list("rawKnown", "rawUnknown"),
          choiceNames = list("Known", "Unknown"),
          selected = "rawUnknown", inline = TRUE
        ),
        conditionalPanel(
          ns = ns,
          condition = "input.sigmaKnownRaw == 'rawKnown'",
          numericInput(ns("popuSDRaw"), strong("Population Standard Deviation (sigma) Value"), 8.25, min = 0.00001, step = 0.00001)
        )
      ),
      conditionalPanel(
        ns = ns,
        condition = "input.dataAvailability == 'Upload Data'",
        fileInput(ns("oneMeanUserData"), strong("Upload your Data (.csv or .xls or .xlsx or .txt)"), accept = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")),
        selectizeInput(ns("oneMeanVariable"), strong("Choose a Column for Analysis"), choices = c(""), options = list(placeholder = 'Select a column', onInitialize = I('function() { this.setValue(\"\"); }'))),
        radioButtons(ns("sigmaKnownUpload"), strong("Population Standard Deviation (sigma)"),
          choiceValues = list("Known", "Unknown"),
          choiceNames = list("Known", "Unknown"),
          selected = "Unknown", inline = TRUE
        ),
        conditionalPanel(
          ns = ns,
          condition = "input.sigmaKnownUpload == 'Known'",
          numericInput(ns("popuSDUpload"), strong("Population Standard Deviation (sigma) Value"), 5, min = 0.00001, step = 0.00001)
        )
      )
    ),
    conditionalPanel(
      ns = ns,
      condition = "input.popuParameter == 'Population Proportion'",
      numericInput(ns("numSuccesses"), strong("Number of Successes (x)"), 1087, min = 0, step = 1),
      numericInput(ns("numTrials"), strong("Number of Trials (n)"), 1430, min = 1, step = 1)
    ),
    conditionalPanel(
      ns = ns,
      condition = "input.popuParameter == 'Population Standard Deviation'",
      numericInput(ns("SSDSampleSize"), strong("Sample Size (n)"), 30, min = 2, step = 1),
      numericInput(ns("SSDStdDev"), strong("Sample Standard Deviation (s)"), 12.23, min = 0.00001, step = 0.00001)
    )
  )
}

OneSampleInferenceMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('oneSampleResults'))
  )
} 