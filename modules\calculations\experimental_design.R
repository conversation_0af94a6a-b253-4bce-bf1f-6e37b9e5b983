# Experimental Design Tools Calculation Functions

# Main experimental design function
edResults_func <- function(tool, test_type, effect_size, power, alpha, sample_size, 
                          effect_size_power, alpha_power, factors, levels, runs,
                          total_subjects, groups, stratified, blocks, treatments, block_type) {
  
  # Perform analysis based on selected tool
  results <- switch(tool,
    "Sample Size Calculator" = calculate_sample_size(test_type, effect_size, power, alpha),
    "Power Analysis" = calculate_power_analysis(sample_size, effect_size_power, alpha_power),
    "Design Efficiency" = calculate_design_efficiency(factors, levels, runs),
    "Randomization" = generate_randomization(total_subjects, groups, stratified),
    "Blocking Design" = generate_blocking_design(blocks, treatments, block_type),
    stop("Unknown experimental design tool: ", tool)
  )
  
  # Add metadata
  results$tool <- tool
  results$parameters <- list(
    test_type = test_type,
    effect_size = effect_size,
    power = power,
    alpha = alpha,
    sample_size = sample_size,
    effect_size_power = effect_size_power,
    alpha_power = alpha_power,
    factors = factors,
    levels = levels,
    runs = runs,
    total_subjects = total_subjects,
    groups = groups,
    stratified = stratified,
    blocks = blocks,
    treatments = treatments,
    block_type = block_type
  )
  
  return(results)
}

# Sample Size Calculator
calculate_sample_size <- function(test_type, effect_size, power, alpha) {
  
  # Calculate sample size based on test type
  sample_size_result <- switch(test_type,
    "One-Sample t-test" = calculate_one_sample_size(effect_size, power, alpha),
    "Two-Sample t-test" = calculate_two_sample_size(effect_size, power, alpha),
    "Paired t-test" = calculate_paired_sample_size(effect_size, power, alpha),
    "One-Way ANOVA" = calculate_anova_sample_size(effect_size, power, alpha),
    "Two-Way ANOVA" = calculate_two_way_anova_size(effect_size, power, alpha),
    "Chi-Square Test" = calculate_chi_square_size(effect_size, power, alpha),
    "Correlation" = calculate_correlation_size(effect_size, power, alpha),
    stop("Unknown test type: ", test_type)
  )
  
  return(list(
    test_type = test_type,
    effect_size = effect_size,
    power = power,
    alpha = alpha,
    sample_size = sample_size_result$sample_size,
    per_group = sample_size_result$per_group,
    total_sample_size = sample_size_result$total_sample_size,
    method = sample_size_result$method
  ))
}

# One-sample t-test sample size
calculate_one_sample_size <- function(effect_size, power, alpha) {
  
  # Use power.t.test for one-sample t-test
  result <- power.t.test(
    d = effect_size,
    power = power,
    sig.level = alpha,
    type = "one.sample"
  )
  
  return(list(
    sample_size = ceiling(result$n),
    per_group = ceiling(result$n),
    total_sample_size = ceiling(result$n),
    method = "One-sample t-test power analysis"
  ))
}

# Two-sample t-test sample size
calculate_two_sample_size <- function(effect_size, power, alpha) {
  
  # Use power.t.test for two-sample t-test
  result <- power.t.test(
    d = effect_size,
    power = power,
    sig.level = alpha,
    type = "two.sample"
  )
  
  return(list(
    sample_size = ceiling(result$n),
    per_group = ceiling(result$n),
    total_sample_size = ceiling(result$n) * 2,
    method = "Two-sample t-test power analysis"
  ))
}

# Paired t-test sample size
calculate_paired_sample_size <- function(effect_size, power, alpha) {
  
  # Use power.t.test for paired t-test
  result <- power.t.test(
    d = effect_size,
    power = power,
    sig.level = alpha,
    type = "paired"
  )
  
  return(list(
    sample_size = ceiling(result$n),
    per_group = ceiling(result$n),
    total_sample_size = ceiling(result$n),
    method = "Paired t-test power analysis"
  ))
}

# One-way ANOVA sample size
calculate_anova_sample_size <- function(effect_size, power, alpha) {
  
  # Convert Cohen's d to f (effect size for ANOVA)
  f <- effect_size / 2
  
  # Use pwr.anova.test for one-way ANOVA
  if (requireNamespace("pwr", quietly = TRUE)) {
    result <- pwr::pwr.anova.test(
      f = f,
      power = power,
      sig.level = alpha,
      k = 3  # Assume 3 groups
    )
    
    return(list(
      sample_size = ceiling(result$n),
      per_group = ceiling(result$n),
      total_sample_size = ceiling(result$n) * 3,
      method = "One-way ANOVA power analysis"
    ))
  } else {
    # Fallback calculation
    n_per_group <- ceiling((2 * (qnorm(1 - alpha/2) + qnorm(power))^2) / effect_size^2)
    return(list(
      sample_size = n_per_group,
      per_group = n_per_group,
      total_sample_size = n_per_group * 3,
      method = "One-way ANOVA (approximate)"
    ))
  }
}

# Two-way ANOVA sample size
calculate_two_way_anova_size <- function(effect_size, power, alpha) {
  
  # Simplified calculation for two-way ANOVA
  f <- effect_size / 2
  n_per_group <- ceiling((2 * (qnorm(1 - alpha/2) + qnorm(power))^2) / effect_size^2)
  
  return(list(
    sample_size = n_per_group,
    per_group = n_per_group,
    total_sample_size = n_per_group * 4,  # Assume 2x2 design
    method = "Two-way ANOVA (approximate)"
  ))
}

# Chi-square test sample size
calculate_chi_square_size <- function(effect_size, power, alpha) {
  
  # Use pwr.chisq.test for chi-square test
  if (requireNamespace("pwr", quietly = TRUE)) {
    result <- pwr::pwr.chisq.test(
      w = effect_size,
      power = power,
      sig.level = alpha,
      df = 1  # Assume 2x2 contingency table
    )
    
    return(list(
      sample_size = ceiling(result$N),
      per_group = ceiling(result$N / 4),  # Assume equal cell sizes
      total_sample_size = ceiling(result$N),
      method = "Chi-square test power analysis"
    ))
  } else {
    # Fallback calculation
    n_total <- ceiling((2 * (qnorm(1 - alpha/2) + qnorm(power))^2) / effect_size^2)
    return(list(
      sample_size = n_total,
      per_group = ceiling(n_total / 4),
      total_sample_size = n_total,
      method = "Chi-square test (approximate)"
    ))
  }
}

# Correlation sample size
calculate_correlation_size <- function(effect_size, power, alpha) {
  
  # Use pwr.r.test for correlation
  if (requireNamespace("pwr", quietly = TRUE)) {
    result <- pwr::pwr.r.test(
      r = effect_size,
      power = power,
      sig.level = alpha
    )
    
    return(list(
      sample_size = ceiling(result$n),
      per_group = ceiling(result$n),
      total_sample_size = ceiling(result$n),
      method = "Correlation power analysis"
    ))
  } else {
    # Fallback calculation
    n <- ceiling((2 * (qnorm(1 - alpha/2) + qnorm(power))^2) / (0.5 * log((1 + effect_size) / (1 - effect_size)))^2)
    return(list(
      sample_size = n,
      per_group = n,
      total_sample_size = n,
      method = "Correlation (approximate)"
    ))
  }
}

# Power Analysis
calculate_power_analysis <- function(sample_size, effect_size, alpha) {
  
  # Calculate power for different test types
  power_results <- list()
  
  # One-sample t-test power
  power_one_sample <- power.t.test(
    n = sample_size,
    d = effect_size,
    sig.level = alpha,
    type = "one.sample"
  )$power
  
  # Two-sample t-test power
  power_two_sample <- power.t.test(
    n = sample_size,
    d = effect_size,
    sig.level = alpha,
    type = "two.sample"
  )$power
  
  # Paired t-test power
  power_paired <- power.t.test(
    n = sample_size,
    d = effect_size,
    sig.level = alpha,
    type = "paired"
  )$power
  
  return(list(
    sample_size = sample_size,
    effect_size = effect_size,
    alpha = alpha,
    power_one_sample = power_one_sample,
    power_two_sample = power_two_sample,
    power_paired = power_paired,
    method = "Power analysis for multiple test types"
  ))
}

# Design Efficiency
calculate_design_efficiency <- function(factors, levels, runs) {
  
  # Calculate design efficiency metrics
  total_combinations <- levels^factors
  efficiency <- runs / total_combinations
  
  # Calculate resolution (for factorial designs)
  if (runs >= 2^factors) {
    resolution <- floor(log2(runs / factors))
  } else {
    resolution <- 0
  }
  
  # Calculate confounding patterns
  confounding <- calculate_confounding(factors, runs)
  
  return(list(
    factors = factors,
    levels = levels,
    runs = runs,
    total_combinations = total_combinations,
    efficiency = efficiency,
    resolution = resolution,
    confounding = confounding,
    method = "Design efficiency analysis"
  ))
}

# Calculate confounding patterns
calculate_confounding <- function(factors, runs) {
  
  # Simplified confounding calculation
  if (runs >= 2^factors) {
    return("No confounding")
  } else {
    return("Some confounding present")
  }
}

# Randomization
generate_randomization <- function(total_subjects, groups, stratified) {
  
  # Generate random assignment
  set.seed(123)  # For reproducibility
  
  if (stratified) {
    # Stratified randomization
    subjects_per_group <- ceiling(total_subjects / groups)
    assignments <- rep(1:groups, each = subjects_per_group)
    assignments <- assignments[1:total_subjects]
    assignments <- sample(assignments)
  } else {
    # Simple randomization
    assignments <- sample(rep(1:groups, ceiling(total_subjects / groups)))[1:total_subjects]
  }
  
  # Create assignment table
  assignment_table <- data.frame(
    Subject = 1:total_subjects,
    Group = assignments,
    stringsAsFactors = FALSE
  )
  
  # Calculate group sizes
  group_sizes <- table(assignments)
  
  return(list(
    total_subjects = total_subjects,
    groups = groups,
    stratified = stratified,
    assignment_table = assignment_table,
    group_sizes = group_sizes,
    method = ifelse(stratified, "Stratified randomization", "Simple randomization")
  ))
}

# Blocking Design
generate_blocking_design <- function(blocks, treatments, block_type) {
  
  set.seed(123)  # For reproducibility
  
  if (block_type == "Complete Block") {
    # Complete block design
    design <- expand.grid(Block = 1:blocks, Treatment = 1:treatments)
    design$Subject <- 1:nrow(design)
    
  } else if (block_type == "Incomplete Block") {
    # Incomplete block design (simplified)
    subjects_per_block <- treatments - 1
    total_subjects <- blocks * subjects_per_block
    
    design <- data.frame(
      Subject = 1:total_subjects,
      Block = rep(1:blocks, each = subjects_per_block),
      Treatment = sample(rep(1:treatments, ceiling(total_subjects / treatments)))[1:total_subjects],
      stringsAsFactors = FALSE
    )
    
  } else if (block_type == "Latin Square") {
    # Latin square design
    if (blocks != treatments) {
      stop("Latin square requires equal number of blocks and treatments")
    }
    
    # Generate Latin square
    latin_square <- matrix(0, blocks, treatments)
    for (i in 1:blocks) {
      latin_square[i, ] <- sample(1:treatments)
    }
    
    design <- data.frame(
      Subject = 1:(blocks * treatments),
      Block = rep(1:blocks, each = treatments),
      Treatment = as.vector(latin_square),
      stringsAsFactors = FALSE
    )
  }
  
  return(list(
    blocks = blocks,
    treatments = treatments,
    block_type = block_type,
    design = design,
    method = paste(block_type, "design")
  ))
}

# Output rendering functions

edResultsOutput <- function(results) {
  renderUI({
    req(results)
    
    switch(results$tool,
      "Sample Size Calculator" = edSampleSizeOutput(results),
      "Power Analysis" = edPowerAnalysisOutput(results),
      "Design Efficiency" = edDesignEfficiencyOutput(results),
      "Randomization" = edRandomizationOutput(results),
      "Blocking Design" = edBlockingDesignOutput(results)
    )
  })
}

edSampleSizeOutput <- function(results) {
  renderUI({
    tagList(
      h4("Sample Size Calculation Results"),
      br(),
      p(strong("Test Type:"), results$test_type),
      p(strong("Effect Size (Cohen's d):"), results$effect_size),
      p(strong("Power (1 - β):"), results$power),
      p(strong("Significance Level (α):"), results$alpha),
      br(),
      h5("Sample Size Requirements"),
      p(strong("Sample Size per Group:"), results$sample_size),
      p(strong("Total Sample Size:"), results$total_sample_size),
      br(),
      p(em("Note: Sample sizes are rounded up to ensure adequate power."))
    )
  })
}

edPowerAnalysisOutput <- function(results) {
  renderUI({
    tagList(
      h4("Power Analysis Results"),
      br(),
      p(strong("Sample Size per Group:"), results$sample_size),
      p(strong("Effect Size:"), results$effect_size),
      p(strong("Significance Level:"), results$alpha),
      br(),
      h5("Power for Different Test Types"),
      p(strong("One-Sample t-test:"), round(results$power_one_sample, 4)),
      p(strong("Two-Sample t-test:"), round(results$power_two_sample, 4)),
      p(strong("Paired t-test:"), round(results$power_paired, 4))
    )
  })
}

edDesignEfficiencyOutput <- function(results) {
  renderUI({
    tagList(
      h4("Design Efficiency Results"),
      br(),
      p(strong("Number of Factors:"), results$factors),
      p(strong("Levels per Factor:"), results$levels),
      p(strong("Number of Runs:"), results$runs),
      br(),
      h5("Efficiency Metrics"),
      p(strong("Total Combinations:"), results$total_combinations),
      p(strong("Design Efficiency:"), round(results$efficiency, 4)),
      p(strong("Resolution:"), results$resolution),
      p(strong("Confounding:"), results$confounding)
    )
  })
}

edRandomizationOutput <- function(results) {
  renderUI({
    tagList(
      h4("Randomization Results"),
      br(),
      p(strong("Total Subjects:"), results$total_subjects),
      p(strong("Number of Groups:"), results$groups),
      p(strong("Method:"), results$method),
      br(),
      h5("Group Sizes"),
      div(
        DT::datatable(
          data.frame(
            Group = names(results$group_sizes),
            Size = as.numeric(results$group_sizes),
            stringsAsFactors = FALSE
          ),
          options = list(pageLength = 10, searching = FALSE, paging = FALSE),
          rownames = FALSE
        ),
        style = "width: 50%"
      ),
      br(),
      h5("Assignment Table"),
      div(
        DT::datatable(results$assignment_table, options = list(pageLength = 20)),
        style = "width: 100%"
      )
    )
  })
}

edBlockingDesignOutput <- function(results) {
  renderUI({
    tagList(
      h4("Blocking Design Results"),
      br(),
      p(strong("Number of Blocks:"), results$blocks),
      p(strong("Number of Treatments:"), results$treatments),
      p(strong("Design Type:"), results$block_type),
      br(),
      h5("Design Layout"),
      div(
        DT::datatable(results$design, options = list(pageLength = 20)),
        style = "width: 100%"
      )
    )
  })
}

edPlotsOutput <- function(results) {
  renderUI({
    req(results)
    
    tagList(
      h4("Experimental Design Plots"),
      br(),
      plotOutput(paste0("ed_plot_", sample(1:1000, 1)), height = "400px")
    )
  })
}

# Experimental Design calculation and output helpers

# 1. Data Upload Function
experimental_design_uploadData_func <- function(edUserData) {
  ext <- tools::file_ext(edUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(edUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(edUserData$datapath),
         xlsx = readxl::read_xlsx(edUserData$datapath),
         txt = readr::read_tsv(edUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

# 2. Main Results Function (core calculations)
experimental_design_results_func <- function(data, tool, ...) {
  tryCatch({
    args <- list(...)
    # Move the main calculation logic here
    results <- switch(tool,
      "Sample Size Calculator" = do.call(calculate_sample_size, args),
      "Power Analysis" = do.call(calculate_power_analysis, args),
      "Design Efficiency" = do.call(calculate_design_efficiency, args),
      "Randomization" = do.call(generate_randomization, args),
      "Blocking Design" = do.call(generate_blocking_design, args),
      stop("Unknown experimental design tool: ", tool)
    )
    results$tool <- tool
    results$data <- data
    results$error <- NULL
    results
  }, error = function(e) {
    list(error = paste("An error occurred during Experimental Design calculation:", e$message))
  })
}

# 3. Hypothesis Test HTML Output
experimental_design_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  tagList(
    h4(paste("Experimental Design Tool:", results$tool)),
    p("See summary table for details.")
  )
}

# 4. Summary Table HTML Output
experimental_design_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  out <- list(h4("Experimental Design Summary"), renderPrint(results))
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

# 5. Plot Output
experimental_design_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  # Power curve plot if available
  if (!is.null(results$tool) && results$tool == "Power Analysis" && !is.null(results$parameters)) {
    n_seq <- seq(5, 200, by = 1)
    power_seq <- sapply(n_seq, function(n) {
      tryCatch({
        power.t.test(n = n, d = results$parameters$effect_size_power, sig.level = results$parameters$alpha_power, type = "two.sample")$power
      }, error = function(e) NA)
    })
    plot(n_seq, power_seq, type = "l", col = "blue", lwd = 2, xlab = "Sample Size (n)", ylab = "Power", main = "Power Curve")
    abline(h = results$parameters$power, col = "red", lty = 2)
    legend("bottomright", legend = c("Power Curve", "Target Power"), col = c("blue", "red"), lty = c(1,2))
    return()
  }
  # Randomization diagram (if available)
  if (!is.null(results$tool) && results$tool == "Randomization" && !is.null(results$randomization)) {
    barplot(table(results$randomization), main = "Randomization Diagram", col = "skyblue")
    return()
  }
  # Block design visual (if available)
  if (!is.null(results$tool) && results$tool == "Blocking Design" && !is.null(results$blocking_design)) {
    image(as.matrix(results$blocking_design), main = "Block Design Visual", col = heat.colors(10))
    return()
  }
  plot.new(); title("No plot available.")
} 