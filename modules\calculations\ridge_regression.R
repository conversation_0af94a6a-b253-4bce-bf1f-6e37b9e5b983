# Ridge Regression calculation and output helpers

ridge_regression_uploadData_func <- function(rrUserData, response_var, predictor_vars) {
  tryCatch(
    {
      if (is.null(rrUserData) || is.null(response_var) || is.null(predictor_vars)) {
        return(NULL)
      }
      
      # Read data based on file type
      if (grepl("\\.csv$", rrUserData$name, ignore.case = TRUE)) {
        df <- read.csv(rrUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.tsv$", rrUserData$name, ignore.case = TRUE)) {
        df <- read.tsv(rrUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.xlsx$", rrUserData$name, ignore.case = TRUE)) {
        df <- readxl::read_excel(rrUserData$datapath)
      } else {
        stop("Unsupported file type. Please upload a CSV, TSV, or XLSX file.")
      }
      
      # Check for required columns
      required_cols <- c(response_var, predictor_vars)
      if (!all(required_cols %in% names(df))) {
        missing_cols <- required_cols[!required_cols %in% names(df)]
        stop(paste("The following columns were not found in the uploaded file:", paste(missing_cols, collapse = ", ")))
      }
      
      return(df)
    },
    error = function(e) {
      return(list(error = paste("Error reading data:", e$message)))
    }
  )
}

ridge_regression_results_func <- function(data, response_var, predictor_vars, use_cv = TRUE, cv_folds = 10) {
  tryCatch({
    if (is.null(data) || nrow(data) == 0) {
      stop("No data provided for analysis.")
    }
    
    if (!requireNamespace("glmnet", quietly = TRUE)) {
      stop("Package 'glmnet' needed for ridge regression.")
    }
    
    X <- as.matrix(data[predictor_vars])
    y <- data[[response_var]]
    
    if (use_cv) {
      cv_fit <- glmnet::cv.glmnet(X, y, alpha = 0, nfolds = cv_folds)
      best_lambda <- cv_fit$lambda.min
      model <- glmnet::glmnet(X, y, alpha = 0, lambda = best_lambda)
    } else {
      model <- glmnet::glmnet(X, y, alpha = 0)
      cv_fit <- NULL
    }
    
    list(
      model = model,
      cv_fit = cv_fit,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Ridge regression calculation:", e$message))
  })
}

ridge_regression_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  tagList(
    h4("Ridge Regression Model"),
    p("See summary and plots for model details.")
  )
}

ridge_regression_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  
  coefs <- coef(results$model)
  coef_df <- data.frame(
    Variable = rownames(coefs),
    Coefficient = as.numeric(coefs)
  )
  
  tagList(
    h4("Coefficients"),
    renderTable(coef_df, digits = 4),
    if (!is.null(results$cv_fit)) {
      tagList(
        h4("Cross-Validation Results"),
        p(paste("Optimal Lambda:", results$cv_fit$lambda.min))
      )
    }
  )
}

ridge_regression_plot <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  
  if (!is.null(results$cv_fit)) {
    plot(results$cv_fit)
  } else {
    plot(results$model, xvar = "lambda", label = TRUE)
  }
}