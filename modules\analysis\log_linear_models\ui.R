# Log-linear Models UI
# Multi-way contingency tables

LogLinearModelsUI <- function(id) {
  ns <- NS(id)
  
  sidebarLayout(
    sidebarPanel(
      width = 3,
      # Data Input Method
      radioButtons(
        inputId = ns("logLinearDataMethod"),
        label = "Data Input Method:",
        choices = c("Upload File" = "Upload File", "Manual Entry" = "Manual Entry"),
        selected = "Upload File"
      ),
      # File Upload
      conditionalPanel(
        condition = "input.logLinearDataMethod == 'Upload File'",
        ns = ns,
        fileInput(
          inputId = ns("logLinearUserData"),
          label = "Upload Data File:",
          accept = c(".csv", ".txt", ".xlsx", ".xls"),
          buttonLabel = "Browse Files",
          placeholder = "No file selected"
        ),
        selectizeInput(
          inputId = ns("logLinearVariables"),
          label = "Select Categorical Variables:",
          choices = NULL,
          multiple = TRUE,
          options = list(placeholder = "Select categorical variables...")
        ),
        selectizeInput(
          inputId = ns("logLinearCountVariable"),
          label = "Select Count Variable (optional):",
          choices = NULL,
          options = list(placeholder = "Select count variable...")
        )
      ),
      # Manual Entry
      conditionalPanel(
        condition = "input.logLinearDataMethod == 'Manual Entry'",
        ns = ns,
        textAreaInput(
          inputId = ns("logLinearManualData"),
          label = "Enter Contingency Table Data (comma-separated):",
          placeholder = "Var1,Var2,Var3,Count\nA,X,1,10\nA,X,2,5\nA,Y,1,8\nA,Y,2,12",
          rows = 10
        )
      ),
      # Analysis Options
      h5("Model Options"),
      checkboxInput(
        inputId = ns("logLinearHierarchical"),
        label = "Use Hierarchical Models",
        value = TRUE
      ),
      checkboxInput(
        inputId = ns("logLinearModelSelection"),
        label = "Perform Model Selection",
        value = TRUE
      ),
      selectInput(
        inputId = ns("logLinearSelectionCriterion"),
        label = "Model Selection Criterion:",
        choices = c("AIC" = "aic", "BIC" = "bic", "Likelihood Ratio" = "lr"),
        selected = "aic"
      ),
      checkboxInput(
        inputId = ns("logLinearResiduals"),
        label = "Calculate Residuals",
        value = TRUE
      ),
      numericInput(
        inputId = ns("logLinearConfLevel"),
        label = "Confidence Level:",
        value = 0.95,
        min = 0.5,
        max = 0.99,
        step = 0.01
      ),
      actionButton(
        inputId = ns("goLogLinear"),
        label = "Fit Log-linear Models",
        class = "btn-primary",
        style = "width: 100%;"
      ),
      br(),
      br(),
      # Help text
      helpText(
        "Log-linear models analyze associations in contingency tables.",
        "Hierarchical models include all lower-order terms.",
        "Model selection helps find the best fitting model.",
        "Residuals help identify poorly fitted cells."
      )
    ),
    mainPanel(
      width = 9,
      uiOutput(ns("logLinearResults"))
    )
  )
} 