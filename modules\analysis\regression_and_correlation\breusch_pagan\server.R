breuschPaganServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactives
    bpUploadData <- eventReactive(input$bpUserData, {
      handle_file_upload(input$bpUserData)
    })
    
    bpResults <- reactive({
      data <- bpUploadData()
      if (is.null(data) || is.null(input$bpResponse) || input$bpResponse == "" ||
          is.null(input$bpPredictors) || length(input$bpPredictors) == 0) {
        return(NULL)
      }
      
      # Check if all selected variables exist in the data
      all_vars <- c(input$bpResponse, input$bpPredictors)
      if (!all(all_vars %in% names(data))) {
        return(NULL)
      }
      
      # Remove rows with missing values
      complete_data <- data[complete.cases(data[all_vars]), ]
      
      if (nrow(complete_data) < 3) return(NULL)
      
      list(data = complete_data, response = input$bpResponse, 
           predictors = input$bpPredictors, n = nrow(complete_data))
    })
    
    # Validation errors
    bpValidationErrors <- reactive({
      errors <- c()
      data <- bpUploadData()
      
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$bpResponse) || input$bpResponse == "") {
        errors <- c(errors, "Please select a response variable.")
        return(errors)
      }
      
      if (is.null(input$bpPredictors) || length(input$bpPredictors) == 0) {
        errors <- c(errors, "Please select at least one predictor variable.")
        return(errors)
      }
      
      # Check if variables exist in data
      all_vars <- c(input$bpResponse, input$bpPredictors)
      missing_vars <- all_vars[!all_vars %in% names(data)]
      if (length(missing_vars) > 0) {
        errors <- c(errors, sprintf("Variables not found in data: %s", paste(missing_vars, collapse = ", ")))
      }
      
      # Check for sufficient observations
      if (!is.null(input$bpResponse) && !is.null(input$bpPredictors) && 
          length(input$bpPredictors) > 0) {
        complete_data <- data[complete.cases(data[all_vars]), ]
        if (nrow(complete_data) < 3) {
          errors <- c(errors, "At least 3 complete observations are required.")
        }
      }
      
      errors
    })
    
    # Outputs
    output$bpHT <- renderUI({
      results <- bpResults()
      if (is.null(results)) return(NULL)
      breuschPaganHT(bpResults, reactive({input$bpSigLvl}))
    })
    
    output$breuschPaganPlot <- renderPlot({
      results <- bpResults()
      if (is.null(results)) return(NULL)
      breuschPaganPlot(bpResults)
    })
    
    output$bpConclusionOutput <- renderUI({
      results <- bpResults()
      if (is.null(results)) return(NULL)
      bpConclusion(bpResults, reactive({input$bpSigLvl}))
    })
    
    output$renderBPData <- renderUI({
      req(bpUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("bpInitialUploadTable")), style = "width: 75%"),
        br(),
        br()
      )
    })
    
    output$bpInitialUploadTable <- DT::renderDT({
      req(bpUploadData())
      DT::datatable(bpUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(bpUploadData())))))
    })
    
    # Update selectizeInput choices after file upload
    observeEvent(bpUploadData(), {
      data <- bpUploadData()
      updateSelectizeInput(session, 'bpResponse', choices = character(0), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'bpPredictors', choices = character(0), selected = NULL, server = TRUE)
      output$breuschPaganResults <- renderUI({ NULL })
      
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'bpResponse', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'bpPredictors', choices = names(data), server = TRUE)
        output$breuschPaganResults <- renderUI({
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('bpPreviewTable'))
          )
        })
        output$bpPreviewTable <- DT::renderDT({
          head(data, 20)
        })
      }
    })
    
    # Show main results or error screen when Calculate is pressed
    observeEvent(input$goInference, {
      output$breuschPaganResults <- renderUI({
        errors <- bpValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Breusch-Pagan Test", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("bpTabset"),
              selected = "Analysis",
              tabPanel(
                id    = ns("bp"),
                title = "Analysis",
                titlePanel("Breusch-Pagan Test for Heteroscedasticity"),
                br(),
                uiOutput(ns('bpHT')),
                br(),
                plotOutput(ns('breuschPaganPlot'), width = "50%", height = "400px"),
                br(),
                uiOutput(ns('bpConclusionOutput'))
              ),
              tabPanel(
                id    = ns("bpData"),
                title = "Uploaded Data",
                uiOutput(ns("renderBPData"))
              )
            )
          )
        }
      })
    })
  })
} 