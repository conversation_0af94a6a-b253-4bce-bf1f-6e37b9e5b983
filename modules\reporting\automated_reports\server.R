# Automated Reporting Server
automatedReportsServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns

    # Upload and parse data
    reportUploadData <- reactive({
      req(input$reportUserData)
      ext <- tools::file_ext(input$reportUserData$name)
      if (ext %in% c("csv", "txt")) {
        read.csv(input$reportUserData$datapath)
      } else if (ext %in% c("xls", "xlsx")) {
        if (!requireNamespace("readxl", quietly = TRUE)) stop("Install 'readxl' for Excel support.")
        readxl::read_excel(input$reportUserData$datapath)
      } else {
        stop("Unsupported file type.")
      }
    })

    # Run Automated Reporting
    reportResults <- eventReactive(input$goReport, {
      df <- reportUploadData()
      title <- input$reportTitle
      automatedReportsResults_func(
        data = df,
        report_title = title
      )
    })

    # Outputs
    output$reportContent <- renderUI({
      results <- reportResults()
      if (is.null(results)) return(NULL)
      tagList(
        tags$p(paste("Report generated:", results$title)),
        downloadButton(ns("downloadReport"), "Download HTML Report")
      )
    })
    output$downloadReport <- downloadHandler(
      filename = function() {
        paste0("CougarStats_Report_", Sys.Date(), ".html")
      },
      content = function(file) {
        results <- reportResults()
        file.copy(results$report_path, file)
      }
    )
    output$reportConclusion <- renderUI({
      results <- reportResults()
      if (is.null(results)) return(NULL)
      tags$p("Automated report generation complete. Download your report above.")
    })
  })
} 