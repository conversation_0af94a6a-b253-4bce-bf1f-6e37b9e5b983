# Survival Analysis Calculation Helper

# Main survival analysis function
perform_survival_analysis <- function(data, time_var, event_var, group_var = NULL, 
                                    covariates = NULL, method = "kaplan_meier",
                                    conf.level = 0.95, test_type = "log_rank") {
  
  # Check required packages
  if (!requireNamespace("survival", quietly = TRUE)) {
    stop("Package 'survival' required for survival analysis")
  }
  
  # Validate input
  if (!time_var %in% names(data)) {
    stop("Time variable not found in data")
  }
  if (!event_var %in% names(data)) {
    stop("Event variable not found in data")
  }
  
  # Create survival object
  surv_obj <- survival::Surv(data[[time_var]], data[[event_var]])
  
  # Perform analysis based on method
  results <- switch(method,
    "kaplan_meier" = perform_kaplan_meier_analysis(surv_obj, data, group_var, conf.level),
    "cox_regression" = perform_cox_regression_analysis(surv_obj, data, covariates, conf.level),
    "parametric" = perform_parametric_survival_analysis(surv_obj, data, group_var, covariates),
    "competing_risks" = perform_competing_risks_analysis(data, time_var, event_var, group_var),
    "frailty" = perform_frailty_analysis(surv_obj, data, group_var, covariates),
    stop("Unknown survival analysis method: ", method)
  )
  
  # Add metadata
  results$method <- method
  results$time_var <- time_var
  results$event_var <- event_var
  results$group_var <- group_var
  results$covariates <- covariates
  results$conf.level <- conf.level
  results$n_events <- sum(data[[event_var]], na.rm = TRUE)
  results$n_total <- nrow(data)
  
  return(results)
}

# Kaplan-Meier analysis
perform_kaplan_meier_analysis <- function(surv_obj, data, group_var = NULL, conf.level = 0.95) {
  
  # Fit Kaplan-Meier model
  if (is.null(group_var)) {
    # No grouping variable
    km_fit <- survival::survfit(surv_obj ~ 1, conf.type = "log-log")
    
    # Calculate summary statistics
    summary_stats <- summary(km_fit, times = quantile(data[[names(surv_obj)[1]]], probs = c(0.25, 0.5, 0.75)))
    
    results <- list(
      model = km_fit,
      summary = summary_stats,
      median_survival = summary(km_fit)$table["median"],
      mean_survival = summary(km_fit)$table["rmean"],
      survival_rates = summary(km_fit)$surv,
      confidence_intervals = summary(km_fit)$conf.int,
      time_points = summary(km_fit)$time
    )
    
  } else {
    # With grouping variable
    if (!group_var %in% names(data)) {
      stop("Group variable not found in data")
    }
    
    km_fit <- survival::survfit(surv_obj ~ data[[group_var]], conf.type = "log-log")
    
    # Perform log-rank test
    logrank_test <- survival::survdiff(surv_obj ~ data[[group_var]])
    
    # Calculate summary statistics by group
    summary_stats <- summary(km_fit)
    
    # Extract group-specific statistics
    group_stats <- list()
    for (i in 1:length(km_fit$strata)) {
      group_name <- names(km_fit$strata)[i]
      group_indices <- km_fit$strata[i]
      
      group_stats[[group_name]] <- list(
        median_survival = summary(km_fit)$table[group_name, "median"],
        mean_survival = summary(km_fit)$table[group_name, "rmean"],
        n_events = summary(km_fit)$table[group_name, "events"],
        n_total = summary(km_fit)$table[group_name, "n"]
      )
    }
    
    results <- list(
      model = km_fit,
      summary = summary_stats,
      group_statistics = group_stats,
      logrank_test = logrank_test,
      p_value = 1 - pchisq(logrank_test$chisq, length(logrank_test$n) - 1),
      survival_rates = summary(km_fit)$surv,
      confidence_intervals = summary(km_fit)$conf.int,
      time_points = summary(km_fit)$time,
      groups = names(km_fit$strata)
    )
  }
  
  return(results)
}

# Cox proportional hazards regression
perform_cox_regression_analysis <- function(surv_obj, data, covariates = NULL, conf.level = 0.95) {
  
  # Create formula
  if (is.null(covariates)) {
    formula <- surv_obj ~ 1
  } else {
    # Validate covariates
    missing_covars <- covariates[!covariates %in% names(data)]
    if (length(missing_covars) > 0) {
      stop("Covariates not found in data: ", paste(missing_covars, collapse = ", "))
    }
    
    formula <- as.formula(paste("surv_obj ~", paste(covariates, collapse = " + ")))
  }
  
  # Fit Cox model
  cox_fit <- survival::coxph(formula, data = data)
  
  # Extract results
  cox_summary <- summary(cox_fit, conf.int = conf.level)
  
  # Test proportional hazards assumption
  ph_test <- survival::cox.zph(cox_fit)
  
  # Calculate hazard ratios and confidence intervals
  hazard_ratios <- exp(cox_summary$coefficients[, "coef"])
  ci_lower <- exp(cox_summary$conf.int[, "lower .95"])
  ci_upper <- exp(cox_summary$conf.int[, "upper .95"])
  
  # Create results table
  results_table <- data.frame(
    Variable = rownames(cox_summary$coefficients),
    Coefficient = cox_summary$coefficients[, "coef"],
    Hazard_Ratio = hazard_ratios,
    SE = cox_summary$coefficients[, "se(coef)"],
    Z = cox_summary$coefficients[, "z"],
    P_value = cox_summary$coefficients[, "Pr(>|z|)"],
    CI_Lower = ci_lower,
    CI_Upper = ci_upper,
    stringsAsFactors = FALSE
  )
  
  # Calculate model fit statistics
  model_fit <- list(
    log_likelihood = cox_fit$loglik[2],
    aic = AIC(cox_fit),
    concordance = cox_fit$concordance[1],
    n_events = cox_fit$nevent,
    n_total = cox_fit$n
  )
  
  results <- list(
    model = cox_fit,
    summary = cox_summary,
    results_table = results_table,
    proportional_hazards_test = ph_test,
    model_fit = model_fit,
    hazard_ratios = hazard_ratios,
    confidence_intervals = cbind(ci_lower, ci_upper)
  )
  
  return(results)
}

# Parametric survival analysis
perform_parametric_survival_analysis <- function(surv_obj, data, group_var = NULL, covariates = NULL) {
  
  if (!requireNamespace("survival", quietly = TRUE)) {
    stop("Package 'survival' required for parametric survival analysis")
  }
  
  # Create formula
  if (is.null(covariates) && is.null(group_var)) {
    formula <- surv_obj ~ 1
  } else {
    vars <- c()
    if (!is.null(group_var)) vars <- c(vars, group_var)
    if (!is.null(covariates)) vars <- c(vars, covariates)
    formula <- as.formula(paste("surv_obj ~", paste(vars, collapse = " + ")))
  }
  
  # Fit different parametric models
  models <- list()
  
  # Weibull model
  tryCatch({
    weibull_fit <- survival::survreg(formula, data = data, dist = "weibull")
    models$weibull <- weibull_fit
  }, error = function(e) {
    models$weibull <- NULL
  })
  
  # Exponential model
  tryCatch({
    exp_fit <- survival::survreg(formula, data = data, dist = "exponential")
    models$exponential <- exp_fit
  }, error = function(e) {
    models$exponential <- NULL
  })
  
  # Log-normal model
  tryCatch({
    lognormal_fit <- survival::survreg(formula, data = data, dist = "lognormal")
    models$lognormal <- lognormal_fit
  }, error = function(e) {
    models$lognormal <- NULL
  })
  
  # Log-logistic model
  tryCatch({
    loglogistic_fit <- survival::survreg(formula, data = data, dist = "loglogistic")
    models$loglogistic <- loglogistic_fit
  }, error = function(e) {
    models$loglogistic <- NULL
  })
  
  # Compare models using AIC
  aic_values <- sapply(models, AIC)
  best_model <- names(which.min(aic_values))
  
  # Extract results from best model
  if (!is.null(models[[best_model]])) {
    best_fit <- models[[best_model]]
    best_summary <- summary(best_fit)
    
    # Calculate predicted survival times
    predicted_survival <- predict(best_fit, type = "response")
    
    results <- list(
      models = models,
      best_model = best_model,
      best_fit = best_fit,
      best_summary = best_summary,
      aic_values = aic_values,
      predicted_survival = predicted_survival,
      distribution = best_fit$dist
    )
  } else {
    results <- list(
      models = models,
      error = "No parametric models could be fitted"
    )
  }
  
  return(results)
}

# Competing risks analysis
perform_competing_risks_analysis <- function(data, time_var, event_var, group_var = NULL) {
  
  if (!requireNamespace("cmprsk", quietly = TRUE)) {
    warning("Package 'cmprsk' not available. Competing risks analysis cannot be performed.")
    return(list(
      error = "Package 'cmprsk' required for competing risks analysis",
      available = FALSE,
      message = "Please install cmprsk package from Bioconductor or GitHub to enable competing risks analysis"
    ))
  }
  
  # Validate input
  if (!time_var %in% names(data)) {
    stop("Time variable not found in data")
  }
  if (!event_var %in% names(data)) {
    stop("Event variable not found in data")
  }
  
  # Extract time and event data
  time_data <- data[[time_var]]
  event_data <- data[[event_var]]
  
  # Perform competing risks analysis
  if (is.null(group_var)) {
    # No grouping
    cr_fit <- cmprsk::crr(time_data, event_data)
    
    results <- list(
      model = cr_fit,
      cumulative_incidence = cr_fit$est,
      time_points = cr_fit$time,
      n_events = table(event_data),
      available = TRUE
    )
    
  } else {
    # With grouping
    if (!group_var %in% names(data)) {
      stop("Group variable not found in data")
    }
    
    group_data <- data[[group_var]]
    unique_groups <- unique(group_data)
    
    cr_results <- list()
    for (group in unique_groups) {
      group_indices <- group_data == group
      group_time <- time_data[group_indices]
      group_event <- event_data[group_indices]
      
      cr_fit <- cmprsk::crr(group_time, group_event)
      cr_results[[as.character(group)]] <- cr_fit
    }
    
    # Perform Gray's test
    gray_test <- cmprsk::cmprsk(time_data, event_data, group_data)
    
    results <- list(
      models = cr_results,
      gray_test = gray_test,
      groups = unique_groups,
      n_events_by_group = tapply(event_data, group_data, table),
      available = TRUE
    )
  }
  
  return(results)
}

# Frailty models
perform_frailty_analysis <- function(surv_obj, data, group_var = NULL, covariates = NULL) {
  
  if (!requireNamespace("survival", quietly = TRUE)) {
    stop("Package 'survival' required for frailty analysis")
  }
  
  # Create formula
  if (is.null(covariates) && is.null(group_var)) {
    stop("Either group_var or covariates must be specified for frailty analysis")
  }
  
  vars <- c()
  if (!is.null(group_var)) {
    vars <- c(vars, paste0("frailty(", group_var, ")"))
  }
  if (!is.null(covariates)) {
    vars <- c(vars, covariates)
  }
  
  formula <- as.formula(paste("surv_obj ~", paste(vars, collapse = " + ")))
  
  # Fit frailty model
  frailty_fit <- survival::coxph(formula, data = data)
  
  # Extract results
  frailty_summary <- summary(frailty_fit)
  
  # Calculate frailty variance
  frailty_variance <- frailty_fit$history$theta
  
  results <- list(
    model = frailty_fit,
    summary = frailty_summary,
    frailty_variance = frailty_variance,
    formula = formula
  )
  
  return(results)
}

# Calculate survival statistics
calculate_survival_statistics <- function(surv_obj, data, group_var = NULL) {
  
  # Basic survival statistics
  n_total <- length(surv_obj)
  n_events <- sum(surv_obj[, 2], na.rm = TRUE)
  n_censored <- n_total - n_events
  
  # Calculate median survival
  km_fit <- survival::survfit(surv_obj ~ 1)
  median_survival <- summary(km_fit)$table["median"]
  
  # Calculate mean survival
  mean_survival <- summary(km_fit)$table["rmean"]
  
  # Calculate survival rates at different time points
  time_points <- quantile(data[[names(surv_obj)[1]]], probs = c(0.25, 0.5, 0.75), na.rm = TRUE)
  survival_rates <- summary(km_fit, times = time_points)$surv
  
  # Group-specific statistics
  group_stats <- NULL
  if (!is.null(group_var)) {
    if (group_var %in% names(data)) {
      group_data <- data[[group_var]]
      unique_groups <- unique(group_data)
      
      group_stats <- list()
      for (group in unique_groups) {
        group_indices <- group_data == group
        group_surv <- surv_obj[group_indices, ]
        
        group_km <- survival::survfit(group_surv ~ 1)
        group_stats[[as.character(group)]] <- list(
          n_total = sum(group_indices),
          n_events = sum(group_surv[, 2], na.rm = TRUE),
          median_survival = summary(group_km)$table["median"],
          mean_survival = summary(group_km)$table["rmean"]
        )
      }
    }
  }
  
  results <- list(
    n_total = n_total,
    n_events = n_events,
    n_censored = n_censored,
    median_survival = median_survival,
    mean_survival = mean_survival,
    survival_rates = survival_rates,
    time_points = time_points,
    group_statistics = group_stats
  )
  
  return(results)
}

# Test proportional hazards assumption
test_proportional_hazards <- function(cox_model) {
  
  if (!requireNamespace("survival", quietly = TRUE)) {
    stop("Package 'survival' required for proportional hazards testing")
  }
  
  # Perform Schoenfeld test
  schoenfeld_test <- survival::cox.zph(cox_model)
  
  # Extract test statistics
  test_results <- data.frame(
    Variable = rownames(schoenfeld_test$table),
    Chi_Square = schoenfeld_test$table[, "chisq"],
    DF = schoenfeld_test$table[, "df"],
    P_value = schoenfeld_test$table[, "p"],
    stringsAsFactors = FALSE
  )
  
  # Global test
  global_test <- data.frame(
    Variable = "Global",
    Chi_Square = sum(schoenfeld_test$table[, "chisq"]),
    DF = sum(schoenfeld_test$table[, "df"]),
    P_value = 1 - pchisq(sum(schoenfeld_test$table[, "chisq"]), sum(schoenfeld_test$table[, "df"])),
    stringsAsFactors = FALSE
  )
  
  results <- list(
    schoenfeld_test = schoenfeld_test,
    test_results = test_results,
    global_test = global_test,
    assumption_violated = any(test_results$P_value < 0.05)
  )
  
  return(results)
}

# Calculate hazard ratios
calculate_hazard_ratios <- function(cox_model, conf.level = 0.95) {
  
  # Extract coefficients and standard errors
  coef_summary <- summary(cox_model)
  coefficients <- coef_summary$coefficients[, "coef"]
  se_coef <- coef_summary$coefficients[, "se(coef)"]
  
  # Calculate hazard ratios
  hazard_ratios <- exp(coefficients)
  
  # Calculate confidence intervals
  z_crit <- qnorm((1 + conf.level) / 2)
  ci_lower <- exp(coefficients - z_crit * se_coef)
  ci_upper <- exp(coefficients + z_crit * se_coef)
  
  # Create results table
  results_table <- data.frame(
    Variable = names(coefficients),
    Coefficient = coefficients,
    Hazard_Ratio = hazard_ratios,
    SE = se_coef,
    CI_Lower = ci_lower,
    CI_Upper = ci_upper,
    stringsAsFactors = FALSE
  )
  
  return(results_table)
}

# Generate survival curves
generate_survival_curves <- function(surv_obj, data, group_var = NULL, time_points = NULL) {
  
  # Fit Kaplan-Meier model
  if (is.null(group_var)) {
    km_fit <- survival::survfit(surv_obj ~ 1)
  } else {
    if (!group_var %in% names(data)) {
      stop("Group variable not found in data")
    }
    km_fit <- survival::survfit(surv_obj ~ data[[group_var]])
  }
  
  # Extract survival data
  survival_data <- data.frame(
    Time = km_fit$time,
    Survival = km_fit$surv,
    Lower_CI = km_fit$lower,
    Upper_CI = km_fit$upper,
    stringsAsFactors = FALSE
  )
  
  # Add group information if available
  if (!is.null(group_var) && group_var %in% names(data)) {
    survival_data$Group <- rep(names(km_fit$strata), km_fit$strata)
  }
  
  # Calculate survival at specific time points if provided
  if (!is.null(time_points)) {
    survival_at_times <- summary(km_fit, times = time_points)
    
    time_specific_data <- data.frame(
      Time = time_points,
      Survival = survival_at_times$surv,
      Lower_CI = survival_at_times$lower,
      Upper_CI = survival_at_times$upper,
      stringsAsFactors = FALSE
    )
    
    if (!is.null(group_var) && group_var %in% names(data)) {
      time_specific_data$Group <- rep(names(km_fit$strata), each = length(time_points))
    }
  } else {
    time_specific_data <- NULL
  }
  
  results <- list(
    model = km_fit,
    survival_data = survival_data,
    time_specific_data = time_specific_data,
    groups = if (!is.null(group_var) && group_var %in% names(data)) names(km_fit$strata) else NULL
  )
  
  return(results)
} 