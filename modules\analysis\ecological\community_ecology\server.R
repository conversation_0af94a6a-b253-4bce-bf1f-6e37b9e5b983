# Community Ecology Server
communityEcologyServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns

    # Upload and parse data
    ceUploadData <- reactive({
      req(input$ceUserData)
      ext <- tools::file_ext(input$ceUserData$name)
      if (ext %in% c("csv", "txt")) {
        read.csv(input$ceUserData$datapath)
      } else if (ext %in% c("xls", "xlsx")) {
        if (!requireNamespace("readxl", quietly = TRUE)) stop("Install 'readxl' for Excel support.")
        readxl::read_excel(input$ceUserData$datapath)
      } else {
        stop("Unsupported file type.")
      }
    })

    # UI for selecting species columns
    output$ceColSelectors <- renderUI({
      df <- ceUploadData()
      req(df)
      tagList(
        selectInput(ns("ceSpeciesCols"), "Species Columns (Select 2+)", choices = names(df), multiple = TRUE)
      )
    })

    # Run Community Ecology analysis
    ceResults <- eventReactive(input$goCE, {
      df <- ceUploadData()
      req(input$ceSpeciesCols)
      communityEcologyResults_func(
        data = df,
        species_cols = input$ceSpeciesCols
      )
    })

    # Outputs
    output$ceHT <- renderUI({
      results <- ceResults()
      if (is.null(results)) return(NULL)
      verbatimTextOutput(ns("ceSummary"))
    })
    output$ceSummary <- renderPrint({
      results <- ceResults()
      if (is.null(results)) return(NULL)
      print(results$summary)
    })
    output$cePlot <- renderPlot({
      results <- ceResults()
      if (is.null(results) || is.null(results$plot)) return(NULL)
      results$plot
    })
    output$ceConclusionOutput <- renderUI({
      results <- ceResults()
      if (is.null(results)) return(NULL)
      tags$p("Community ecology analysis complete. See diversity indices and plot above.")
    })
    output$renderCEData <- renderUI({
      req(ceUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("ceInitialUploadTable")), style = "width: 75%"),
        br(),
        br(),
        uiOutput(ns("ceColSelectors"))
      )
    })
    output$ceInitialUploadTable <- DT::renderDT({
      req(ceUploadData())
      DT::datatable(ceUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(ceUploadData())))))
    })
  })
} 