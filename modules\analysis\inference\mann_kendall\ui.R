mannKendallSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(
      inputId = ns("mkUserData"),
      label   = strong("Upload your Data (.csv or .xls or .xlsx or .txt)"),
      accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
    ),
    div(
      id = ns("mkUploadInputs"),
      radioButtons(
        inputId = ns("mkFormat"),
        label   = strong("Data Format"),
        choiceNames = c("Time series in single column", "Time and value in two columns"),
        choiceValues = c("Single", "Paired")
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Single'", ns("mkFormat")),
        selectizeInput(
          inputId = ns("mkValues"),
          label = strong("Select values column"),
          choices = c(""),
          selected = NULL,
          options = list(placeholder = 'Select a column', onInitialize = I('function() { this.setValue(\"\"); }'))
        )
      ),
      conditionalPanel(
        condition = sprintf("input['%s'] == 'Paired'", ns("mkFormat")),
        selectizeInput(
          inputId = ns("mkTime"),
          label = strong("Time Variable"),
          choices = c(""),
          selected = NULL,
          options = list(placeholder = 'Select time column', onInitialize = I('function() { this.setValue(\"\"); }'))
        ),
        selectizeInput(
          inputId = ns("mkValues"),
          label = strong("Values Variable"),
          choices = c(""),
          selected = NULL,
          options = list(placeholder = 'Select values column', onInitialize = I('function() { this.setValue(\"\"); }'))
        )
      )
    ),
    radioButtons(
      inputId = ns("mkAlternative"),
      label = strong("Alternative Hypothesis"),
      choices  = c("two.sided", "increasing", "decreasing"),
      selected = "two.sided",
      inline   = TRUE
    ),
    radioButtons(
      inputId = ns("mkSigLvl"),
      label = strong("Significance Level (alpha)"),
      choices  = c("10%", "5%", "1%"),
      selected = "5%",
      inline   = TRUE
    ),
    br(),
    actionButton(ns("goInference"), label = "Calculate", class = "act-btn")
  )
}

mannKendallMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('mannKendallResults'))
  )
}

mannKendallUI <- function(id) {
  ns <- NS(id)
  tagList(
    mannKendallSidebarUI(id),
    mannKendallMainUI(id)
  )
} 