# Jonckheere-Terpstra Test Server
# Non-parametric test for ordered alternatives

source("modules/calculations/jonckheere_terpstra.R")

JonckheereTerpstraServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    jtUploadData <- eventReactive(input$jtUserData, {
      handle_file_upload(input$jtUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(jtUploadData(), {
      data <- jtUploadData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'jtResponseVariable', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'jtGroupVariable', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    jtValidationErrors <- reactive({
      errors <- c()
      
      if (input$jtDataMethod == "Manual Entry") {
        # Manual entry validation
        if (is.null(input$jtData) || input$jtData == "") {
          errors <- c(errors, "Data values are required for manual entry.")
        } else {
          data_vals <- createNumLst(input$jtData)
          if (length(data_vals) < 6) {
            errors <- c(errors, "At least 6 observations are required.")
          }
        }
        
        if (is.null(input$jtGroups) || input$jtGroups == "") {
          errors <- c(errors, "Group labels are required for manual entry.")
        } else {
          group_vals <- createNumLst(input$jtGroups)
          if (length(group_vals) != length(createNumLst(input$jtData))) {
            errors <- c(errors, "Number of group labels must match number of data values.")
          }
        }
      } else {
        # File upload validation
        data <- jtUploadData()
        if (is.null(data) || !is.data.frame(data)) {
          errors <- c(errors, "No data uploaded or file could not be read.")
          return(errors)
        }
        if (is.null(input$jtResponseVariable) || input$jtResponseVariable == "") {
          errors <- c(errors, "Please select a response variable.")
        } else {
          var_data <- data[[input$jtResponseVariable]]
          if (!is.numeric(var_data)) {
            errors <- c(errors, "Response variable must be numeric.")
          }
          if (length(na.omit(var_data)) < 6) {
            errors <- c(errors, "At least 6 non-missing observations are required.")
          }
        }
        if (is.null(input$jtGroupVariable) || input$jtGroupVariable == "") {
          errors <- c(errors, "Please select a group variable.")
        }
      }
      
      errors
    })
    
    # Jonckheere-Terpstra analysis reactive
    jtResult <- reactive({
      req(input$jtDataMethod)
      
      if (input$jtDataMethod == "Manual Entry") {
        data_vals <- createNumLst(input$jtData)
        group_vals <- createNumLst(input$jtGroups)
      } else {
        data <- jtUploadData()
        req(data, input$jtResponseVariable, input$jtGroupVariable)
        data_vals <- data[[input$jtResponseVariable]]
        group_vals <- data[[input$jtGroupVariable]]
      }
      
      alternative <- input$jtAlternative
      conf_level <- input$jtConfLevel
      
      # Perform Jonckheere-Terpstra test
      result <- perform_jonckheere_terpstra(data_vals, group_vals, alternative, conf_level)
      
      result
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goJT, {
      output$jtResults <- renderUI({
        errors <- jtValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Jonckheere-Terpstra Test", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("jtTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("jtAnalysis"),
                title = "Analysis",
                titlePanel("Jonckheere-Terpstra Test Results"),
                br(),
                uiOutput(ns('jtSummary')),
                br(),
                h4("Test Details"),
                uiOutput(ns('jtDetails')),
                br(),
                h4("Assumptions and Interpretation"),
                uiOutput(ns('jtAssumptions'))
              ),
              tabPanel(
                id = ns("jtDataSummary"),
                title = "Data Summary",
                h4("Group Statistics"),
                tableOutput(ns('jtGroupStats')),
                br(),
                h4("Data Distribution by Group"),
                plotOutput(ns('jtBoxplot'), height = "400px"),
                br(),
                h4("Trend Analysis"),
                tableOutput(ns('jtTrendAnalysis'))
              ),
              tabPanel(
                id = ns("jtUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('jtViewUpload'))
              )
            )
          )
        }
      })
    })
    
    # Analysis Tab Outputs
    output$jtSummary <- renderUI({
      req(jtResult())
      result <- jtResult()
      
      tagList(
        h4("Jonckheere-Terpstra Test Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Statistic = c("Total Sample Size", "Number of Groups", "Alternative Hypothesis", 
                         "Test Statistic (JT)", "Expected Value", "Variance", "Z-score", 
                         "P-value", "Significance Level", "Decision"),
            Value = c(
              result$total_n,
              result$k,
              result$alternative,
              round(result$test_statistic, 4),
              round(result$expected_value, 4),
              round(result$variance, 4),
              round(result$z_score, 4),
              ifelse(result$p_value < 0.0001, "< 0.0001", round(result$p_value, 4)),
              paste0((1 - result$conf_level) * 100, "%"),
              ifelse(result$p_value < (1 - result$conf_level), "Reject H₀", "Fail to reject H₀")
            )
          )
        }),
        br(),
        p(strong("Conclusion:"), 
          ifelse(result$p_value < (1 - result$conf_level),
                 "There is sufficient evidence to reject the null hypothesis of no trend.",
                 "There is insufficient evidence to reject the null hypothesis of no trend."))
      )
    })
    
    output$jtDetails <- renderUI({
      req(jtResult())
      result <- jtResult()
      
      tagList(
        h5("Hypotheses:"),
        p("H₀: No trend across groups"),
        p("Hₐ: ", 
          ifelse(result$alternative == "two.sided", "Trend exists (two-sided)", 
                 ifelse(result$alternative == "greater", "Increasing trend", "Decreasing trend"))),
        br(),
        h5("Test Statistic:"),
        p("JT = sum of Mann-Whitney U statistics for all pairs = ", round(result$test_statistic, 4)),
        p("Expected value under H₀ = ", round(result$expected_value, 4)),
        p("Variance under H₀ = ", round(result$variance, 4)),
        br(),
        h5("Z-score:"),
        p("Z = (JT - E[JT]) / √Var[JT] = ", round(result$z_score, 4)),
        br(),
        h5("P-value:"),
        p("P-value = ", ifelse(result$p_value < 0.0001, "< 0.0001", round(result$p_value, 4)))
      )
    })
    
    output$jtAssumptions <- renderUI({
      req(jtResult())
      result <- jtResult()
      
      tagList(
        h5("Key Assumptions:"),
        p("1. Data are independent within and between groups"),
        p("2. Data are continuous (or at least ordinal)"),
        p("3. Groups have similar shapes (not necessarily normal)"),
        p("4. Groups are ordered in the hypothesized direction"),
        br(),
        h5("Advantages:"),
        p("• Robust to outliers"),
        p("• No assumption of normality"),
        p("• Tests for ordered alternatives"),
        p("• More powerful than Kruskal-Wallis for trend detection"),
        br(),
        h5("Disadvantages:"),
        p("• Requires ordered groups"),
        p("• Less powerful than parametric alternatives when assumptions are met"),
        p("• Sensitive to group ordering"),
        br(),
        h5("When to Use:"),
        p("• Testing for trends across ordered groups"),
        p("• Non-normal data"),
        p("• Ordinal response variable"),
        p("• Dose-response studies")
      )
    })
    
    # Data Summary Tab Outputs
    output$jtGroupStats <- renderTable({
      req(jtResult())
      result <- jtResult()
      
      result$group_stats
    }, digits = 4)
    
    output$jtBoxplot <- renderPlot({
      req(jtResult())
      result <- jtResult()
      
      # Create data frame for plotting
      plot_data <- data.frame(
        value = result$data,
        group = result$groups
      )
      
      boxplot(value ~ group, data = plot_data, 
              main = "Boxplot by Group", 
              xlab = "Group", ylab = "Value",
              col = "#4F81BD", border = "darkblue")
    })
    
    output$jtTrendAnalysis <- renderTable({
      req(jtResult())
      result <- jtResult()
      
      result$trend_analysis
    }, digits = 4)
    
    # Uploaded Data Tab Output
    output$jtViewUpload <- DT::renderDT({
      req(jtUploadData())
      DT::datatable(jtUploadData(), 
                   options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), 
                   rownames = FALSE)
    })
  })
} 