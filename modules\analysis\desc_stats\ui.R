descStatsUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        shinyjs::useShinyjs(),
        div(id = ns("inputPanel"),
            radioButtons(
              inputId      = ns("dataInput"),
              label        = strong("Data"),
              choiceValues = list("Enter Raw Data", "Upload Data"),
              choiceNames  = list("Enter Raw Data", "Upload Data"),
              selected     = "Enter Raw Data",
              inline       = TRUE),
            conditionalPanel(
              condition = sprintf("input['%s'] == 'Enter Raw Data'", ns("dataInput")),
              textAreaInput(
                inputId     = ns("descriptiveStat"),
                label       = strong("Sample"),
                value       = "2.14,   2.09,   2.65,   3.56,   5.55,   5.00,   5.55,   8.09,   10.79",
                placeholder = "Enter values separated by a comma with decimals as points",
                rows        = 3)
            ),
            conditionalPanel(
              condition = sprintf("input['%s'] == 'Upload Data'", ns("dataInput")),
              fileInput(
                inputId = ns("dsUserData"),
                label   = strong("Upload your Data (.csv or .xls or .xlsx or .txt)"),
                accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
              ),
              selectInput(
                inputId = ns("dsUploadVars"),
                label   = strong("Select Variable(s)"),
                choices = NULL,
                multiple = TRUE
              )
            ),
            shinyWidgets::pickerInput(
              inputId  = ns("dsTableFilters"),
              label    = strong("Statistics"),
              choices  = list(
                Descriptives          = c("Observations", "Sum", "Sum of Squares", "Mean", "Mode"),
                'Five Number Summary' = c("Minimum", "First Quartile (Q1)", "Second Quartile or Median (Q2)", "Third Quartile (Q3)", "Maximum"),
                Outliers              = c("IQR", "Potential Outliers"),
                Dispersion            = c("Range", "Sample Standard Deviation", "Sample Variance", "Standard Error of the Mean", "Coefficient of Variation"),
                Distribution          = c("Skewness", "Kurtosis")),
              selected = c("Observations", "Mean", "Minimum", "First Quartile (Q1)", "Second Quartile or Median (Q2)", "Third Quartile (Q3)", "Maximum", "Sample Standard Deviation"),
              options  = shinyWidgets::pickerOptions(
                actionsBox = TRUE,
                selectedTextFormat = 'count',
                style = "btn-outline-primary",
                hideDisabled = TRUE),
              multiple = TRUE),
            br(),
            selectizeInput(
              inputId  = ns("dsGraphOptions"),
              label    = strong("Graphs"),
              choices  = c("Boxplot", "Histogram", "Stem and Leaf Plot"),
              selected = c("Boxplot"),
              multiple = TRUE,
              options  = list(hideSelected = FALSE, placeholder = 'Select graph(s) to display')),
            br(),
            actionButton(
              inputId = ns("goDescpStats"),
              label   = "Calculate",
              class   = "act-btn"),
            actionButton(
              inputId = ns("resetAll"),
              label   = "Reset Values",
              class   = "act-btn")
        )
      ),
      shinyjs::hidden(
        mainPanel(
          div(id = ns("descriptiveStatsMP"),
            uiOutput(ns("renderDescrStats")),
            div(id = ns("outputPanel"),
              tabsetPanel(
                id       = ns("dsTabset"),
                selected = "Descriptive Statistics",
                tabPanel(
                  id    = ns("dsTable"),
                  title = "Descriptive Statistics",
                  value = "Descriptive Statistics",
                  withMathJax(),
                  conditionalPanel(
                    condition = sprintf("input['%s'] == ''", ns("dsTableFilters")),
                    br(),
                    p("Select one or more items from the Statistics menu to see more information.")
                  ),
                  conditionalPanel(
                    condition = sprintf("input['%s'] != ''", ns("dsTableFilters")),
                    DTOutput(ns("dsTableData"))
                  ),
                  br(),
                  conditionalPanel(
                    condition = sprintf("input['%s'].indexOf('First Quartile (Q1)') > -1 | input['%s'].indexOf('Third Quartile (Q3)') > -1 | input['%s'].indexOf('IQR') > -1 | input['%s'].indexOf('Potential Outliers') > -1", ns("dsTableFilters"), ns("dsTableFilters"), ns("dsTableFilters"), ns("dsTableFilters")),
                    helpText("* Note: Quartiles are calculated by excluding the median on both sides.")),
                  br(),
                  br(),
                  conditionalPanel(
                    condition = sprintf("input['%s'].indexOf('Mean') > -1 | input['%s'].indexOf('Sample Standard Deviation') > -1", ns("dsTableFilters"), ns("dsTableFilters")),
                    fluidRow(
                      column(
                        width = 4,
                        br(),
                        DTOutput(ns("sampleDataTable")),
                        br(),
                        br()),
                      column(
                        width = 8,
                        conditionalPanel(
                          condition = sprintf("input['%s'].indexOf('Mean') > -1", ns("dsTableFilters")),
                          withMathJax(),
                          titlePanel(tags$u("Sample Mean")),
                          br(),
                          uiOutput(ns("dsMeanCalc")),
                          br()),
                        conditionalPanel(
                          condition = sprintf("input['%s'].indexOf('Sample Standard Deviation') > -1", ns("dsTableFilters")),
                          withMathJax(),
                          titlePanel(tags$u("Sample Standard Deviation")),
                          br(),
                          uiOutput(ns("dsSDCalc")),
                          br(),
                          br(),
                          br()))
                    )
                  )
                ),
                tabPanel(
                  id    = ns("dsGraphs"),
                  title = "Graphs",
                  value = "Graphs",
                  uiOutput(ns("renderDSBoxplot")),
                  uiOutput(ns("renderDSHistogram")),
                  verbatimTextOutput(ns("dsStemLeaf"))
                ),
                tabPanel(
                  id    = ns("dsUploadData"),
                  title = "Uploaded Data",
                  value = "Uploaded Data",
                  uiOutput(ns("renderDSData"))
                )
              )
            )
          )
        )
      )
    )
  )
} 