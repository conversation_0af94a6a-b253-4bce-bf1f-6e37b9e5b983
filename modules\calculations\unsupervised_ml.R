run_unsupervised_ml <- function(data, vars, method = "kmeans", clusters = 3) {
  tryCatch({
    x <- as.matrix(data[, vars, drop = FALSE])
    if (method == "kmeans") {
      fit <- kmeans(x, centers = clusters)
      plot_obj <- function() { plot(x, col = fit$cluster, main = "K-means Clustering"); points(fit$centers, col = 1:clusters, pch = 8, cex = 2) }
      list(method = "kmeans", clusters = fit$cluster, centers = fit$centers, plot = plot_obj, error = NULL)
    } else if (method == "hclust") {
      d <- dist(x)
      fit <- hclust(d)
      cluster_assign <- cutree(fit, k = clusters)
      plot_obj <- function() { plot(fit, main = "Hierarchical Clustering"); rect.hclust(fit, k = clusters, border = 2:5) }
      list(method = "hclust", clusters = cluster_assign, dendrogram = fit, plot = plot_obj, error = NULL)
    } else if (method == "pca") {
      fit <- prcomp(x, scale. = TRUE)
      plot_obj <- function() { biplot(fit, main = "PCA Biplot") }
      list(method = "pca", pca = fit, plot = plot_obj, error = NULL)
    } else {
      stop("Unknown method.")
    }
  }, error = function(e) {
    list(clusters = NULL, centers = NULL, plot = NULL, error = e$message)
  })
} 