BayesianRegressionSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("bayesUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
    selectInput(ns("bayesModelType"), "Model Type", choices = c("Bayesian Linear Regression", "Bayesian ANOVA")),
    selectizeInput(ns("bayesResponse"), "Response Variable", choices = NULL),
    selectizeInput(ns("bayesPredictors"), "Predictor Variables", choices = NULL, multiple = TRUE),
    br(),
    actionButton(ns("goBayes"), label = "Run Bayesian Model", class = "act-btn")
  )
}

BayesianRegressionMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns("bayesError")),
    tableOutput(ns("bayesResults")),
    plotOutput(ns("bayesPlot"))
  )
}

BayesianRegressionUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(BayesianRegressionSidebarUI(id)),
    mainPanel(BayesianRegressionMainUI(id))
  )
} 