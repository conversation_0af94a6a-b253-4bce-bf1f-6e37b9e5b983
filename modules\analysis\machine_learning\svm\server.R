# Placeholder for SVM Server
svmServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Upload and parse data
    svmUploadData <- reactive({
      req(input$svmUserData)
      ext <- tools::file_ext(input$svmUserData$name)
      if (ext %in% c("csv", "txt")) {
        read.csv(input$svmUserData$datapath)
      } else if (ext %in% c("xls", "xlsx")) {
        if (!requireNamespace("readxl", quietly = TRUE)) stop("Install 'readxl' for Excel support.")
        readxl::read_excel(input$svmUserData$datapath)
      } else {
        stop("Unsupported file type.")
      }
    })

    # UI for selecting columns and SVM params
    output$svmColSelectors <- renderUI({
      df <- svmUploadData()
      req(df)
      tagList(
        selectInput(ns("svmResponseCol"), "Response Variable", choices = names(df)),
        selectInput(ns("svmPredictors"), "Predictors", choices = names(df), multiple = TRUE),
        selectInput(ns("svmType"), "SVM Type", choices = c("C-classification", "nu-classification", "eps-regression", "nu-regression"), selected = "C-classification"),
        selectInput(ns("svmKernel"), "Kernel", choices = c("radial", "linear", "polynomial", "sigmoid"), selected = "radial")
      )
    })

    # Run SVM analysis
    svmResults <- eventReactive(input$goSVM, {
      df <- svmUploadData()
      req(input$svmResponseCol, input$svmPredictors)
      modules::calculations::svmResults_func(
        data = df,
        response = input$svmResponseCol,
        predictors = input$svmPredictors,
        type = input$svmType,
        kernel = input$svmKernel
      )
    })

    # Outputs
    output$svmHT <- renderUI({
      results <- svmResults()
      if (is.null(results)) return(NULL)
      tagList(
        verbatimTextOutput(ns("svmSummary")),
        tags$p(paste("Accuracy:", round(results$accuracy, 4)))
      )
    })
    output$svmSummary <- renderPrint({
      results <- svmResults()
      if (is.null(results)) return(NULL)
      print(results$summary)
    })
    output$svmPlot <- renderPlot({
      results <- svmResults()
      if (is.null(results) || is.null(results$plot)) return(NULL)
      results$plot
    })
    output$svmConclusionOutput <- renderUI({
      results <- svmResults()
      if (is.null(results)) return(NULL)
      tags$p("SVM model fitted. See summary and plot above.")
    })
    output$renderSVMData <- renderUI({
      req(svmUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("svmInitialUploadTable")), style = "width: 75%"),
        br(),
        br(),
        uiOutput(ns("svmColSelectors"))
      )
    })
    output$svmInitialUploadTable <- DT::renderDT({
      req(svmUploadData())
      DT::datatable(svmUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(svmUploadData())))))
    })
  })
} 