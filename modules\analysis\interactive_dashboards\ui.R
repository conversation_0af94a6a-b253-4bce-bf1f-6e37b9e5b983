InteractiveDashboardsUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("dashUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectInput(ns("dashType"), "Dashboard Type", choices = c("Summary", "Exploratory", "Custom")),
        br(),
        actionButton(ns("goDash"), label = "Generate Dashboard", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("dashError")),
        uiOutput(ns("dashOutput"))
      )
    )
  )
} 