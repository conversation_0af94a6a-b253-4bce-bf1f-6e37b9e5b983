# Multilevel Modeling Server Logic

multilevelModelingServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    # Reactive values for storing results
    mlm_results <- reactiveVal(NULL)
    mlm_plots <- reactiveVal(NULL)
    
    # Data upload reactive
    mlmData <- eventReactive(input$mlmUserData, {
      handle_file_upload(input$mlmUserData)
    })

    # Update variable choices when data is uploaded
    observeEvent(mlmData(), {
      data <- mlmData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectInput(session, "mlm_response", choices = names(data))
        updateSelectInput(session, "mlm_predictors", choices = names(data))
        updateSelectInput(session, "mlm_random_effects", choices = names(data))
        updateSelectInput(session, "mlm_level2_predictors", choices = names(data))
        updateSelectInput(session, "mlm_random_slope_vars", choices = names(data))
      }
    })

    # Run multilevel model
    observeEvent(input$run_mlm, {
      req(mlmData(), input$mlm_response, input$mlm_predictors, input$mlm_random_effects)
      tryCatch({
        # Prepare data
        model_data <- mlmData()
        # Center/scale predictors if requested
        if (input$mlm_center_predictors || input$mlm_scale_predictors) {
          for (pred in input$mlm_predictors) {
            if (is.numeric(model_data[[pred]])) {
              if (input$mlm_center_predictors) {
                model_data[[pred]] <- scale(model_data[[pred]], center = TRUE, scale = input$mlm_scale_predictors)
              }
            }
          }
        }
        # Build formula
        fixed_effects <- paste(input$mlm_predictors, collapse = " + ")
        random_effects <- paste("(1|", input$mlm_random_effects, ")", collapse = " + ")
        if (input$mlm_random_slopes && length(input$mlm_random_slope_vars) > 0) {
          random_slopes <- paste("(", input$mlm_random_slope_vars, "|", input$mlm_random_effects, ")", collapse = " + ")
          random_effects <- paste(random_effects, random_slopes, sep = " + ")
        }
        formula_str <- paste(input$mlm_response, "~", fixed_effects, "+", random_effects)
        # Fit model
        if (input$mlm_family == "gaussian") {
          model <- lmer(as.formula(formula_str), data = model_data)
        } else {
          # For non-Gaussian families, use glmer
          family_fun <- switch(input$mlm_family,
                              "binomial" = binomial(link = input$mlm_link),
                              "poisson" = poisson(link = input$mlm_link),
                              "gamma" = Gamma(link = input$mlm_link))
          model <- glmer(as.formula(formula_str), data = model_data, family = family_fun)
        }
        # Calculate ICC
        icc_results <- NULL
        if (input$mlm_icc) {
          icc_results <- calculate_icc(model, input$mlm_random_effects)
        }
        # Store results
        results <- list(
          model = model,
          formula = formula_str,
          icc = icc_results,
          data = model_data,
          family = input$mlm_family,
          link = input$mlm_link
        )
        mlm_results(results)
        # Generate plots
        generate_mlm_plots(results)
      }, error = function(e) {
        showNotification(paste("Error in multilevel modeling:", e$message), type = "error")
      })
    })

    # Generate plots
    generate_mlm_plots <- function(results) {
      req(results)
      plots <- list()
      # Residual plots
      if (input$mlm_residuals) {
        p1 <- ggplot(data.frame(
          fitted = fitted(results$model),
          residuals = residuals(results$model)
        ), aes(x = fitted, y = residuals)) +
          geom_point() +
          geom_hline(yintercept = 0, color = "red", linetype = "dashed") +
          geom_smooth(method = "loess", se = FALSE) +
          labs(title = "Residuals vs Fitted Values",
               x = "Fitted Values", y = "Residuals") +
          theme_minimal()
        plots$residuals <- p1
      }
      # Q-Q plot
      if (input$mlm_qq_plot) {
        p2 <- ggplot(data.frame(residuals = residuals(results$model)), aes(sample = residuals)) +
          stat_qq() +
          stat_qq_line() +
          labs(title = "Q-Q Plot of Residuals",
               x = "Theoretical Quantiles", y = "Sample Quantiles") +
          theme_minimal()
        plots$qq <- p2
      }
      # Random effects plot
      if (input$mlm_random_effects_plot) {
        re_data <- ranef(results$model)
        if (length(re_data) > 0) {
          re_df <- data.frame(
            group = rep(names(re_data[[1]]), each = nrow(re_data[[1]])),
            effect = as.vector(re_data[[1]])
          )
          p3 <- ggplot(re_df, aes(x = group, y = effect)) +
            geom_boxplot() +
            geom_jitter(width = 0.2, alpha = 0.6) +
            labs(title = "Random Effects by Group",
                 x = "Group", y = "Random Effect") +
            theme_minimal() +
            theme(axis.text.x = element_text(angle = 45, hjust = 1))
          plots$random_effects <- p3
        }
      }
      # ICC plot
      if (input$mlm_icc && !is.null(results$icc)) {
        icc_df <- data.frame(
          level = names(results$icc),
          icc = unlist(results$icc)
        )
        p4 <- ggplot(icc_df, aes(x = level, y = icc)) +
          geom_bar(stat = "identity", fill = "steelblue") +
          labs(title = "Intraclass Correlation Coefficients",
               x = "Level", y = "ICC") +
          theme_minimal() +
          theme(axis.text.x = element_text(angle = 45, hjust = 1))
        plots$icc <- p4
      }
      mlm_plots(plots)
    }

    # Calculate ICC
    calculate_icc <- function(model, random_effects) {
      # Extract variance components
      vc <- VarCorr(model)
      icc_values <- list()
      for (re in random_effects) {
        if (re %in% names(vc)) {
          # Calculate ICC for each random effect
          var_re <- attr(vc[[re]], "stddev")^2
          var_resid <- attr(vc, "sc")^2
          icc_values[[re]] <- var_re / (var_re + var_resid)
        }
      }
      return(icc_values)
    }

    # Output summary
    output$mlm_summary <- renderPrint({
      req(mlm_results())
      results <- mlm_results()
      cat("MULTILEVEL MODEL SUMMARY\n")
      cat("========================\n\n")
      cat("Formula:\n")
      cat(results$formula, "\n\n")
      cat("Model Family:", results$family, "\n")
      cat("Link Function:", results$link, "\n\n")
      cat("Fixed Effects:\n")
      print(summary(results$model)$coefficients)
      cat("\nRandom Effects:\n")
      print(VarCorr(results$model))
      if (!is.null(results$icc)) {
        cat("\nIntraclass Correlation Coefficients:\n")
        for (level in names(results$icc)) {
          cat(level, ":", round(results$icc[[level]], 4), "\n")
        }
      }
      cat("\nModel Fit Statistics:\n")
      cat("AIC:", round(AIC(results$model), 3), "\n")
      cat("BIC:", round(BIC(results$model), 3), "\n")
      cat("Log-Likelihood:", round(logLik(results$model), 3), "\n")
    })

    # Output plots
    output$mlm_plots <- renderPlot({
      req(mlm_plots())
      plots <- mlm_plots()
      if (length(plots) == 1) {
        print(plots[[1]])
      } else if (length(plots) > 1) {
        # Arrange multiple plots
        n_plots <- length(plots)
        n_cols <- min(2, n_plots)
        n_rows <- ceiling(n_plots / n_cols)
        do.call(grid.arrange, c(plots, ncol = n_cols))
      }
    })

    # Download results
    output$download_mlm_results <- downloadHandler(
      filename = function() {
        paste("multilevel_model_results_", format(Sys.time(), "%Y%m%d_%H%M%S"), ".txt", sep = "")
      },
      content = function(file) {
        req(mlm_results())
        results <- mlm_results()
        sink(file)
        cat("MULTILEVEL MODEL RESULTS\n")
        cat("========================\n\n")
        cat("Formula:", results$formula, "\n\n")
        cat("Model Summary:\n")
        print(summary(results$model))
        if (!is.null(results$icc)) {
          cat("\nICC Values:\n")
          print(results$icc)
        }
        sink()
      }
    )

    # Reset functionality
    observeEvent(input$reset_mlm, {
      mlm_results(NULL)
      mlm_plots(NULL)
      updateSelectInput(session, "mlm_response", selected = "")
      updateSelectInput(session, "mlm_predictors", selected = "")
      updateSelectInput(session, "mlm_random_effects", selected = "")
      updateSelectInput(session, "mlm_level2_predictors", selected = "")
      updateSelectInput(session, "mlm_random_slope_vars", selected = "")
    })
  })
} 