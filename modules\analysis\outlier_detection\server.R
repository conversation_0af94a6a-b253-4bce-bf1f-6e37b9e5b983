OutlierDetectionServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    outData <- eventReactive(input$outUserData, {
      handle_file_upload(input$outUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(outData(), {
      data <- outData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'outVars', choices = names(data), server = TRUE)
      }
    })
    
    outResult <- eventReactive(input$goOut, {
      data <- outData()
      req(data, input$outVars)
      outlier_detection(data, input$outVars)
    })
    
    observeEvent(input$goOut, {
      output$outError <- renderUI({
        tryCatch({ 
          res <- outResult()
          if (is.null(res)) {
            errorScreenUI(title = "Outlier Detection Error", errors = "No results available")
          } else {
            NULL
          }
        }, error = function(e) {
          errorScreenUI(title = "Outlier Detection Error", errors = e$message)
        })
      })
      
      output$outResults <- renderUI({
        res <- outResult()
        if (is.null(res)) return(NULL)
        
        tagList(
          tabsetPanel(
            id = ns("outTabset"),
            selected = "Analysis",
            tabPanel(
              id = ns("outAnalysis"),
              title = "Analysis",
              titlePanel("Outlier Detection Results"),
              br(),
              h4("Outlier Summary"),
              tableOutput(ns('outSummary')),
              br(),
              h4("Outlier Details"),
              tableOutput(ns('outDetails')),
              br(),
              h4("Statistical Tests"),
              uiOutput(ns('outStatisticalTests')),
              br(),
              h4("Recommendations"),
              uiOutput(ns('outRecommendations'))
            ),
            tabPanel(
              id = ns("outVisualizations"),
              title = "Visualizations",
              h4("Boxplots"),
              plotOutput(ns('outBoxplot'), height = "500px"),
              br(),
              h4("Scatter Plots"),
              plotOutput(ns('outScatterplot'), height = "500px"),
              br(),
              h4("Q-Q Plots"),
              plotOutput(ns('outQQPlot'), height = "500px")
            ),
            tabPanel(
              id = ns("outData"),
              title = "Data",
              h4("Data with Outliers Highlighted"),
              DT::DTOutput(ns('outDataTable')),
              br(),
              h4("Outlier-Free Data"),
              DT::DTOutput(ns('outCleanDataTable'))
            )
          )
        )
      })
    })
    
    # Additional outputs for the enhanced tabs
    output$outSummary <- renderTable({
      res <- outResult()
      if (is.null(res)) return(NULL)
      res$summary
    }, digits = 4)
    
    output$outDetails <- renderTable({
      res <- outResult()
      if (is.null(res)) return(NULL)
      
      # Create detailed outlier table
      if (!is.null(res$outliers) && nrow(res$outliers) > 0) {
        res$outliers
      } else {
        data.frame(Message = "No outliers detected", stringsAsFactors = FALSE)
      }
    }, digits = 4)
    
    output$outStatisticalTests <- renderUI({
      res <- outResult()
      if (is.null(res)) return(NULL)
      
      tagList(
        h5("Outlier Detection Methods Used:"),
        p("• Boxplot method (IQR-based)"),
        p("• Z-score method"),
        p("• Modified Z-score method"),
        p("• Grubbs' test"),
        br(),
        p("Note: Different methods may identify different outliers.")
      )
    })
    
    output$outRecommendations <- renderUI({
      res <- outResult()
      if (is.null(res)) return(NULL)
      
      outlier_count <- ifelse(!is.null(res$outliers), nrow(res$outliers), 0)
      
      tagList(
        h5("Outlier Handling Recommendations:"),
        if (outlier_count == 0) {
          p("No outliers detected. Data appears to be clean.")
        } else {
          tagList(
            p("Number of outliers detected: ", outlier_count),
            p("• Investigate outliers for data entry errors"),
            p("• Consider the context and domain knowledge"),
            p("• Decide whether to remove, transform, or keep outliers"),
            p("• Document your outlier handling decisions")
          )
        },
        br(),
        p("Note: Outlier removal should be justified and documented.")
      )
    })
    
    output$outBoxplot <- renderPlot({
      res <- outResult()
      if (is.null(res)) return(NULL)
      
      data <- outData()
      if (is.null(data) || is.null(input$outVars)) return(NULL)
      
      # Create boxplots for each variable
      boxplot(data[, input$outVars, drop = FALSE], 
              main = "Boxplots for Outlier Detection",
              col = "lightblue",
              border = "darkblue")
    })
    
    output$outScatterplot <- renderPlot({
      res <- outResult()
      if (is.null(res)) return(NULL)
      
      data <- outData()
      if (is.null(data) || length(input$outVars) < 2) return(NULL)
      
      # Create scatter plot for first two variables
      plot(data[[input$outVars[1]]], data[[input$outVars[2]]],
           xlab = input$outVars[1], ylab = input$outVars[2],
           main = "Scatter Plot for Outlier Detection",
           pch = 19, col = "blue")
    })
    
    output$outQQPlot <- renderPlot({
      res <- outResult()
      if (is.null(res)) return(NULL)
      
      data <- outData()
      if (is.null(data) || is.null(input$outVars)) return(NULL)
      
      # Create Q-Q plot for first variable
      qqnorm(data[[input$outVars[1]]], main = paste("Q-Q Plot for", input$outVars[1]))
      qqline(data[[input$outVars[1]]], col = "red")
    })
    
    output$outDataTable <- DT::renderDT({
      req(outData())
      data <- outData()
      
      # Highlight outliers in the data table
      DT::datatable(data,
        options = list(pageLength = 25,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(data)))))
    })
    
    output$outCleanDataTable <- DT::renderDT({
      req(outData(), outResult())
      data <- outData()
      res <- outResult()
      
      # Remove outliers if any were detected
      if (!is.null(res$outliers) && nrow(res$outliers) > 0) {
        # Create clean data (this would need to be implemented in the calculation function)
        clean_data <- data  # Placeholder
        DT::datatable(clean_data,
          options = list(pageLength = 25,
                         lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                         columnDefs = list(list(className = 'dt-center', targets = 0:ncol(clean_data)))))
      } else {
        DT::datatable(data,
          options = list(pageLength = 25,
                         lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                         columnDefs = list(list(className = 'dt-center', targets = 0:ncol(data)))))
      }
    })
  })
} 