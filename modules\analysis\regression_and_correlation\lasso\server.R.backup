# Lasso Regression Server
# Variable selection and regularization with L1 penalty

LassoRegressionServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    
    # Reactive values
    lasso_data <- reactiveVal(NULL)
    lasso_results <- reactiveVal(NULL)
    
    # Handle file upload
    observeEvent(input$lassoUserData, {
      req(input$lassoUserData)
      
      tryCatch({
        # Read the uploaded file
        file_ext <- tools::file_ext(input$lassoUserData$name)
        
        if (file_ext %in% c("csv", "txt")) {
          data <- read.csv(input$lassoUserData$datapath, stringsAsFactors = FALSE)
        } else if (file_ext %in% c("xlsx", "xls")) {
          data <- readxl::read_excel(input$lassoUserData$datapath)
        } else {
          stop("Unsupported file format")
        }
        
        lasso_data(data)
        
        # Update variable choices
        updateSelectizeInput(session, "lassoResponseVariable", 
                           choices = names(data), selected = NULL)
        updateSelectizeInput(session, "lassoPredictorVariables", 
                           choices = names(data), selected = NULL)
        
      }, error = function(e) {
        showNotification(paste("Error reading file:", e$message), type = "error")
      })
    })
    
    # Perform Lasso Regression
    observeEvent(input$goLasso, {
      req(input$goLasso)
      
      tryCatch({
        # Prepare data
        if (input$lassoDataMethod == "Manual Entry") {
          # Parse manual entry data
          response_data <- parse_numeric_input(input$lassoResponseData)
          predictor_data <- parse_numeric_input(input$lassoPredictorData)
          
          if (length(response_data) != length(predictor_data)) {
            stop("Response and predictor data must have the same length")
          }
          
          data <- data.frame(
            Response = response_data,
            Predictor = predictor_data
          )
        } else {
          # Use uploaded data
          req(lasso_data(), input$lassoResponseVariable, input$lassoPredictorVariables)
          
          data <- lasso_data()
          response_var <- input$lassoResponseVariable
          predictor_vars <- input$lassoPredictorVariables
          
          # Select relevant columns
          data <- data[c(response_var, predictor_vars)]
          names(data)[1] <- "Response"
        }
        
        # Remove rows with missing values
        complete_cases <- complete.cases(data)
        if (sum(complete_cases) < nrow(data)) {
          warning("Removing rows with missing values")
          data <- data[complete_cases, ]
        }
        
        if (nrow(data) < 10) {
          stop("At least 10 observations are required for lasso regression")
        }
        
        # Prepare predictor matrix and response vector
        if (ncol(data) == 2) {
          # Single predictor case
          X <- as.matrix(data[, -1, drop = FALSE])
          y <- data$Response
          predictor_names <- names(data)[-1]
        } else {
          # Multiple predictors case
          X <- as.matrix(data[, -1])
          y <- data$Response
          predictor_names <- names(data)[-1]
        }
        
        # Standardize predictors
        X_scaled <- scale(X)
        
        # Perform lasso regression
        if (input$lassoCrossValidation) {
          # Cross-validation for lambda selection
          cv_folds <- input$lassoCVFolds
          
          # Create lambda sequence
          lambda_seq <- exp(seq(log(0.001), log(10), length.out = 100))
          
          # Perform cross-validation
          cv_result <- glmnet::cv.glmnet(X_scaled, y, alpha = 1, 
                                        lambda = lambda_seq, 
                                        nfolds = cv_folds)
          
          optimal_lambda <- cv_result$lambda.min
          lambda_1se <- cv_result$lambda.1se
          
          # Fit model with optimal lambda
          lasso_model <- glmnet::glmnet(X_scaled, y, alpha = 1, lambda = optimal_lambda)
          
          # Also fit with 1SE lambda
          lasso_model_1se <- glmnet::glmnet(X_scaled, y, alpha = 1, lambda = lambda_1se)
          
        } else {
          # Use specified lambda
          lambda <- input$lassoLambda
          lasso_model <- glmnet::glmnet(X_scaled, y, alpha = 1, lambda = lambda)
          optimal_lambda <- lambda
          lambda_1se <- lambda
          cv_result <- NULL
          lasso_model_1se <- lasso_model
        }
        
        # Extract coefficients
        coefficients <- coef(lasso_model)[-1]  # Remove intercept
        coefficients_1se <- coef(lasso_model_1se)[-1]
        intercept <- coef(lasso_model)[1]
        
        # Calculate predictions
        y_pred <- predict(lasso_model, newx = X_scaled)
        residuals <- y - y_pred
        
        # Calculate performance metrics
        mse <- mean(residuals^2)
        rmse <- sqrt(mse)
        mae <- mean(abs(residuals))
        r_squared <- 1 - sum(residuals^2) / sum((y - mean(y))^2)
        adj_r_squared <- 1 - (1 - r_squared) * (nrow(data) - 1) / (nrow(data) - ncol(X) - 1)
        
        # Variable selection results
        selected_vars <- predictor_names[coefficients != 0]
        n_selected <- length(selected_vars)
        n_total <- length(predictor_names)
        
        # Create coefficient table
        coef_table <- data.frame(
          Variable = c("Intercept", predictor_names),
          Coefficient = c(intercept, coefficients),
          Coefficient_1SE = c(coef(lasso_model_1se)[1], coefficients_1se),
          stringsAsFactors = FALSE
        )
        
        # Add selection status
        coef_table$Selected <- c(TRUE, coefficients != 0)
        coef_table$Selected_1SE <- c(TRUE, coefficients_1se != 0)
        
        # Calculate confidence intervals (bootstrap)
        set.seed(123)
        n_bootstrap <- 1000
        bootstrap_coefs <- matrix(0, n_bootstrap, length(coefficients) + 1)
        
        for (i in 1:n_bootstrap) {
          # Bootstrap sample
          indices <- sample(1:nrow(data), replace = TRUE)
          X_boot <- X_scaled[indices, , drop = FALSE]
          y_boot <- y[indices]
          
          # Fit lasso on bootstrap sample
          boot_model <- glmnet::glmnet(X_boot, y_boot, alpha = 1, lambda = optimal_lambda)
          bootstrap_coefs[i, ] <- as.vector(coef(boot_model))
        }
        
        # Calculate confidence intervals
        ci_level <- input$lassoConfLevel
        alpha <- 1 - ci_level
        ci_lower <- apply(bootstrap_coefs, 2, quantile, alpha/2)
        ci_upper <- apply(bootstrap_coefs, 2, quantile, 1 - alpha/2)
        
        coef_table$CI_Lower <- ci_lower
        coef_table$CI_Upper <- ci_upper
        
        # Create results list
        results <- list(
          data = data,
          X = X_scaled,
          y = y,
          lasso_model = lasso_model,
          lasso_model_1se = lasso_model_1se,
          cv_result = cv_result,
          coefficients = coef_table,
          optimal_lambda = optimal_lambda,
          lambda_1se = lambda_1se,
          selected_variables = selected_vars,
          n_selected = n_selected,
          n_total = n_total,
          performance = list(
            mse = mse,
            rmse = rmse,
            mae = mae,
            r_squared = r_squared,
            adj_r_squared = adj_r_squared
          ),
          predictions = y_pred,
          residuals = residuals,
          conf_level = ci_level
        )
        
        lasso_results(results)
        
      }, error = function(e) {
        showNotification(paste("Error in lasso regression:", e$message), type = "error")
      })
    })
    
    # Render results
    output$lassoResults <- renderUI({
      req(lasso_results())
      results <- lasso_results()
      
      tagList(
        # Summary statistics
        h3("Lasso Regression Results"),
        
        # Model information
        fluidRow(
          column(6,
            h4("Model Information"),
            p(strong("Optimal Lambda:"), round(results$optimal_lambda, 4)),
            p(strong("Lambda (1SE):"), round(results$lambda_1se, 4)),
            p(strong("Variables Selected:"), results$n_selected, "of", results$n_total),
            p(strong("Selected Variables:"), paste(results$selected_variables, collapse = ", "))
          ),
          column(6,
            h4("Model Performance"),
            p(strong("R-squared:"), round(results$performance$r_squared, 4)),
            p(strong("Adjusted R-squared:"), round(results$performance$adj_r_squared, 4)),
            p(strong("RMSE:"), round(results$performance$rmse, 4)),
            p(strong("MAE:"), round(results$performance$mae, 4))
          )
        ),
        
        br(),
        
        # Coefficient table
        h4("Coefficients"),
        renderDataTable({
          coef_table <- results$coefficients
          coef_table$Coefficient <- round(coef_table$Coefficient, 4)
          coef_table$Coefficient_1SE <- round(coef_table$Coefficient_1SE, 4)
          coef_table$CI_Lower <- round(coef_table$CI_Lower, 4)
          coef_table$CI_Upper <- round(coef_table$CI_Upper, 4)
          
          datatable(coef_table, 
                   options = list(pageLength = 10, scrollX = TRUE),
                   rownames = FALSE) %>%
            formatStyle("Selected", 
                       backgroundColor = styleEqual(c(TRUE, FALSE), c("#d4edda", "#f8d7da")))
        }),
        
        br(),
        
        # Cross-validation plot
        if (!is.null(results$cv_result)) {
          h4("Cross-Validation Results"),
          renderPlot({
            plot(results$cv_result, main = "Cross-Validation for Lambda Selection")
            abline(v = log(results$optimal_lambda), col = "red", lty = 2)
            abline(v = log(results$lambda_1se), col = "blue", lty = 2)
            legend("topright", 
                   legend = c("Optimal Lambda", "Lambda (1SE)"),
                   col = c("red", "blue"), lty = 2)
          })
        },
        
        br(),
        
        # Coefficient path plot
        h4("Coefficient Paths"),
        renderPlot({
          lambda_seq <- exp(seq(log(0.001), log(10), length.out = 100))
          path_model <- glmnet::glmnet(results$X, results$y, alpha = 1, lambda = lambda_seq)
          
          plot(path_model, xvar = "lambda", main = "Coefficient Paths")
          abline(v = log(results$optimal_lambda), col = "red", lty = 2)
          abline(v = log(results$lambda_1se), col = "blue", lty = 2)
        }),
        
        br(),
        
        # Residuals plot
        h4("Model Diagnostics"),
        fluidRow(
          column(6,
            renderPlot({
              plot(results$predictions, results$residuals, 
                   main = "Residuals vs Fitted",
                   xlab = "Fitted Values", ylab = "Residuals")
              abline(h = 0, col = "red", lty = 2)
            })
          ),
          column(6,
            renderPlot({
              qqnorm(results$residuals, main = "Q-Q Plot of Residuals")
              qqline(results$residuals, col = "red")
            })
          )
        ),
        
        br(),
        
        # Educational content
        h4("Interpretation"),
        p(strong("Lasso Regression:"), "Uses L1 penalty to perform variable selection by shrinking some coefficients to exactly zero."),
        p(strong("Lambda:"), "Controls the strength of regularization. Larger values result in more variables being excluded."),
        p(strong("Cross-Validation:"), "Helps select the optimal lambda value that balances model fit and complexity."),
        p(strong("Variable Selection:"), "Variables with non-zero coefficients are considered important predictors.")
      )
    })
    
    # Helper function to parse numeric input
    parse_numeric_input <- function(input_text) {
      if (is.null(input_text) || input_text == "") {
        stop("Please enter data")
      }
      
      # Split by commas, spaces, or newlines
      values <- unlist(strsplit(input_text, "[,\\s\\n]+"))
      values <- values[values != ""]
      
      # Convert to numeric
      numeric_values <- as.numeric(values)
      
      if (any(is.na(numeric_values))) {
        stop("All values must be numeric")
      }
      
      return(numeric_values)
    }
  })
} 