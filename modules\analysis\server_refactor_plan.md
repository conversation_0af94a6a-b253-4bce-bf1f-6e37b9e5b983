# Server Refactor Plan: Calculation File Integration

## Objective
Ensure all `server.R` files in `modules/analysis/` delegate all statistical logic to their corresponding calculation files, following the Kruskal-Wallis module as the reference pattern. This guarantees maintainability, consistency, and robust error handling across all modules.

---

## Reference Pattern: Kruskal-Wallis Example
- **File upload**: Use the dedicated `*_uploadData_func` from the calculation file.
- **Calculation**: Use the `*_results_func` from the calculation file, passing all required arguments.
- **UI Outputs**: Use `*_summary_html`, `*_plot`, `*_ht_html`, etc., from the calculation file for all outputs.
- **Error Handling**: Check for errors in the calculation result and display them in the UI.
- **No calculation logic in server**: All statistical logic is in the calculation file.

---

## Steps for Each Module
1. **File Upload**
   - Use the calculation file's upload function (e.g., `desc_stats_uploadData_func`).
2. **Calculation**
   - Use the calculation file's results function (e.g., `desc_stats_results_func`).
   - Pass all required arguments from the UI.
3. **UI Outputs**
   - Use the calculation file's output functions for summary, plot, and HTML (e.g., `desc_stats_summary_html`, `desc_stats_plot`, `desc_stats_ht_html`).
4. **Error Handling**
   - Check for errors in the calculation result and display them in the UI.
5. **Remove Calculation Logic from Server**
   - Ensure all statistical logic is in the calculation file, not the server.

---

## Batch Update Process
- Enumerate all modules with a `server.R` file in `modules/analysis/`.
- For each, update as above.
- If a module is already correct, leave it unchanged.
- If a module is not present, skip it.

---

## Status Tracking (Example)
- [ ] desc_stats
- [ ] prob_dist
- [ ] simulation
- [ ] effect_size
- [ ] time_series
- [ ] corr_heatmap
- [ ] roc_analysis
- [ ] power_analysis
- [ ] sample_size_est
- ... (continue for all modules)

---

## Notes
- Use the Kruskal-Wallis module as the gold standard for integration.
- If a module has special requirements, document them here.
- This plan can be resumed at any time by continuing the checklist above. 