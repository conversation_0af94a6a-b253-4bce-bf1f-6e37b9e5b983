## install.packages("remotes")
## remotes::install_github("deepanshu88/shinyDarkmode")
## remotes::install_github("rsquaredacademy/olsrr")

## options(conflicts.policy = TRUE)
## library(conflicted)

# Core Shiny and UI packages
library(aplpack)
library(bslib)
library(car)
library(colourpicker)
library(DescTools)
library(dplyr)
library(DT)
library(generics)
library(ggplot2)
library(plotly)
library(ggpubr)
library(ggsci)
library(e1071)
library(markdown)
library(nortest)
library(readr)
library(readxl)
library(rstatix)
library(shiny)
library(shinyDarkmode)
library(shinythemes)
library(shinyjs)
library(shinyMatrix)
library(shinyvalidate)
library(shinyWidgets)
library(tinytex)
library(writexl)
library(xtable)
library(MASS)
library(latex2exp)
library(thematic)
library(datamods)
library(magrittr)
library(olsrr)
library(ggResidpanel)
library(leaflet)
library(htmlwidgets)
library(sf)
library(spdep)
library(lme4)
library(nlme)
library(randomForest)
library(gbm)
library(xgboost)
library(caret)
library(lavaan)
library(forecast)
library(igraph)
library(visNetwork)
library(GGally)
library(fmsb)
library(treemap)
library(networkD3)
library(scatterplot3d)
library(shinydashboard)

# Additional packages for new modules
library(survival)
# Handle cmprsk package which may not be available
if (requireNamespace("cmprsk", quietly = TRUE)) {
  library(cmprsk)
} else {
  cat("Warning: cmprsk package not available. Competing risks analysis will be disabled.\n")
}
library(psych)
library(metafor)
library(mice)
library(VIM)
library(skimr)
library(poLCA)
library(mirt)
library(mgcv)
library(glmnet)
library(robustbase)
library(pscl)
library(pwr)
library(FSA)
library(polycor)
library(changepoint)
library(boot)
library(brms)
library(loo)
library(BayesFactor)
library(bayestestR)
library(rstanarm)
library(mediation)
library(MatchIt)
library(heplots)
library(ipred)
library(cluster)
library(fpc)
library(dbscan)
library(fields)
library(spgwr)
library(seasonal)
library(Rtsne)
library(umap)
library(pryr)
library(performance)
library(sjPlot)
library(WRS2)
library(ca)

# Text mining and NLP packages
library(tm)
library(quanteda)
library(wordcloud)
library(topicmodels)
library(lda)

# Spatial analysis packages
library(sp)
library(gstat)
library(spatstat)
# library(maptools)  # DEPRECATED - use sf package instead

# Visualization packages
library(plotly)
library(scatterplot3d)
library(networkD3)
library(visNetwork)

# Removed legacy R/ sources (now modularized)

source('modules/shared/utils.R')
source('modules/shared/plot_helpers.R')
source('modules/shared/desc_stats_helpers.R')
source('modules/shared/parse_helpers.R')
source('modules/shared/validation_helpers.R')
source('modules/shared/file_upload.R')
source('modules/shared/plot_options_menu.R')
source('modules/shared/output_helpers.R')
source('modules/shared/error_handling.R')

source('modules/analysis/inference/one_sample/ui.R')
source('modules/analysis/inference/two_sample/ui.R')
source('modules/analysis/inference/anova/ui.R')
source('modules/analysis/inference/kruskal_wallis/ui.R')
source('modules/analysis/inference/chi_square/ui.R')

source('modules/analysis/inference/ui.R')

source('modules/analysis/inference/one_sample/server.R')
source('modules/analysis/inference/two_sample/server.R')
source('modules/analysis/inference/anova/server.R')
source('modules/analysis/inference/kruskal_wallis/server.R')
source('modules/analysis/inference/chi_square/server.R')

source('modules/analysis/inference/server.R')

source('modules/calculations/mann_whitney.R')
source('modules/calculations/wilcoxon.R')
source('modules/analysis/inference/mann_whitney/ui.R')
source('modules/analysis/inference/mann_whitney/server.R')
source('modules/analysis/inference/mann_whitney/mainUI.R')
source('modules/analysis/inference/wilcoxon/ui.R')
source('modules/analysis/inference/wilcoxon/server.R')
source('modules/analysis/inference/wilcoxon/mainUI.R')

source('modules/analysis/authors.R')

source('modules/analysis/inference/paired_t_test/ui.R')
source('modules/analysis/inference/paired_t_test/server.R')
source('modules/analysis/inference/proportion_tests/ui.R')
source('modules/analysis/inference/proportion_tests/server.R')
source('modules/analysis/inference/anova/two_way/ui.R')
source('modules/analysis/inference/anova/two_way/server.R')
source('modules/analysis/inference/anova/repeated_measures/ui.R')
source('modules/analysis/inference/anova/repeated_measures/server.R')
source('modules/analysis/inference/anova/friedman/ui.R')
source('modules/analysis/inference/anova/friedman/server.R')
source('modules/analysis/inference/anova/levene/ui.R')
source('modules/analysis/inference/anova/levene/server.R')
source('modules/analysis/inference/anova/post_hoc/ui.R')
source('modules/analysis/inference/anova/post_hoc/server.R')
source('modules/analysis/categorical_tests/mcnemar/ui.R')
source('modules/analysis/categorical_tests/mcnemar/server.R')
source('modules/analysis/categorical_tests/cochrans_q/ui.R')
source('modules/analysis/categorical_tests/cochrans_q/server.R')
source('modules/analysis/bayesian/ui.R')
source('modules/analysis/bayesian/server.R')
source('modules/analysis/custom_test/ui.R')
source('modules/analysis/custom_test/server.R')
source('modules/analysis/survival_analysis/ui.R')
source('modules/analysis/survival_analysis/server.R')

# Descriptive Statistics module
source('modules/analysis/desc_stats/ui.R')
source('modules/analysis/desc_stats/server.R')

# Probability Distributions module
source('modules/analysis/prob_dist/ui.R')
source('modules/analysis/prob_dist/server.R')

# Sample Size Estimation module
source('modules/analysis/sample_size_est/ui.R')
source('modules/analysis/sample_size_est/server.R')
source('modules/calculations/sample_size_est.R')

# Regression and Correlation modules
source('modules/analysis/simple_linear_regression/ui.R')
source('modules/analysis/simple_linear_regression/server.R')
source('modules/analysis/multiple_linear_regression/ui.R')
source('modules/analysis/multiple_linear_regression/server.R')
source('modules/analysis/logistic_regression/ui.R')
source('modules/analysis/logistic_regression/server.R')
source('modules/analysis/regression_and_correlation/correlation/ui.R')
source('modules/analysis/regression_and_correlation/correlation/server.R')
source('modules/analysis/regression_and_correlation/ui.R')
source('modules/analysis/regression_and_correlation/server.R')

options(scipen = 999) # options(scipen = 0)
## options(shiny.reactlog = TRUE)

## How many digits to round Critical Values
cvDigits <- 3

render <- "
{
  option: function(data, escape){return '<div class=\"option\">'+data.label+'</div>';},
  item: function(data, escape){return '<div class=\"item\">'+data.label+'</div>';}
}"

## NOTE: advanced understanding of R is required to interpret these results.
## It's not for the faint of heart.
## warning("What follows is the base R conflicts() report: all MASK-ed or MASK-ing symbols are given.",
##         immediate. = TRUE)
## print(conflicts(detail = TRUE))

## NOTE: see #41.
## warning("Following this is the conflicted::conflict_scout() report.",
##         immediate. = TRUE)
## print(conflicted::conflict_scout())

## TODO: reenable this line before deployment.
## conflicted::conflicts_prefer(shinyjs::show, dplyr::filter, dplyr::select)

## See the theming issue brought up in #33; use thematic to attempt to make base
## R graphics compliant with ggplot theming, and to anticipate the impact of
## dark mode.
ggplot2::theme_set(ggplot2::theme_minimal())
thematic_shiny()

# Advanced Academic Modules
source('modules/analysis/PCA/ui.R')
source('modules/analysis/PCA/server.R')
source('modules/calculations/pca.R')

source('modules/analysis/cluster_analysis/ui.R')
source('modules/analysis/cluster_analysis/server.R')
source('modules/calculations/cluster_analysis.R')

source('modules/analysis/roc_analysis/ui.R')
source('modules/analysis/roc_analysis/server.R')

source('modules/analysis/power_analysis/ui.R')
source('modules/analysis/power_analysis/server.R')
source('modules/calculations/power_analysis.R')

source('modules/analysis/effect_size/ui.R')
source('modules/analysis/effect_size/server.R')
source('modules/calculations/effect_size.R')

source('modules/analysis/custom_plot/ui.R')
source('modules/analysis/custom_plot/server.R')

source('modules/analysis/meta_analysis/ui.R')
source('modules/analysis/meta_analysis/server.R')
source('modules/calculations/meta_analysis.R')

source('modules/analysis/mixed_effects/ui.R')
source('modules/analysis/mixed_effects/server.R')
source('modules/calculations/mixed_effects.R')

source('modules/analysis/survey_psychometrics/ui.R')
source('modules/analysis/survey_psychometrics/server.R')
source('modules/calculations/survey_psychometrics.R')

source('modules/analysis/time_series/ui.R')
source('modules/analysis/time_series/server.R')
source('modules/calculations/time_series.R')

# --- New Modules: Advanced, Bayesian, Time Series, Network, Data Cleaning, Visualization, Teaching Tools ---

# Permutation/Randomization Tests
source('modules/analysis/permutation_tests/ui.R')
source('modules/analysis/permutation_tests/server.R')
source('modules/calculations/permutation_tests.R')

# Bootstrap Confidence Intervals
source('modules/analysis/bootstrap/ui.R')
source('modules/analysis/bootstrap/server.R')
source('modules/calculations/bootstrap.R')

# Robust Regression
source('modules/analysis/robust_regression/ui.R')
source('modules/analysis/robust_regression/server.R')
source('modules/calculations/robust_regression.R')

# Bayesian Regression/ANOVA
source('modules/analysis/bayesian_regression/ui.R')
source('modules/analysis/bayesian_regression/server.R')
source('modules/calculations/bayesian_regression.R')

# Bayesian Model Comparison
source('modules/analysis/bayesian_model_comparison/ui.R')
source('modules/analysis/bayesian_model_comparison/server.R')
source('modules/calculations/bayesian_model_comparison.R')

# Poisson/Negative Binomial Regression
source('modules/analysis/poisson_regression/ui.R')
source('modules/analysis/poisson_regression/server.R')
source('modules/calculations/poisson_regression.R')

# Quasi-binomial/Quasi-Poisson Regression
source('modules/analysis/quasi_regression/ui.R')
source('modules/analysis/quasi_regression/server.R')
source('modules/calculations/quasi_regression.R')

# Zero-inflated Models
source('modules/analysis/zero_inflated/ui.R')
source('modules/analysis/zero_inflated/server.R')
source('modules/calculations/zero_inflated.R')

# STL Decomposition
source('modules/analysis/stl_decomposition/ui.R')
source('modules/analysis/stl_decomposition/server.R')
source('modules/calculations/stl_decomposition.R')

# State Space Models & Kalman Filtering
source('modules/analysis/state_space/ui.R')
source('modules/analysis/state_space/server.R')
source('modules/calculations/state_space.R')

# Change Point Detection
source('modules/analysis/change_point/ui.R')
source('modules/analysis/change_point/server.R')
source('modules/calculations/change_point.R')

# Spectral Analysis
source('modules/analysis/spectral_analysis/ui.R')
source('modules/analysis/spectral_analysis/server.R')
source('modules/calculations/spectral_analysis.R')

# Network Analysis
source('modules/analysis/network_analysis/ui.R')
source('modules/analysis/network_analysis/server.R')
source('modules/calculations/network_analysis.R')

# Multiple Imputation (MICE)
source('modules/analysis/multiple_imputation/ui.R')
source('modules/analysis/multiple_imputation/server.R')
source('modules/calculations/multiple_imputation.R')

# Missingness Visualization
source('modules/analysis/missingness_viz/ui.R')
source('modules/analysis/missingness_viz/server.R')
source('modules/calculations/missingness_viz.R')

# Outlier Detection
source('modules/analysis/outlier_detection/ui.R')
source('modules/analysis/outlier_detection/server.R')
source('modules/calculations/outlier_detection.R')

# Variable Transformation
source('modules/analysis/variable_transformation/ui.R')
source('modules/analysis/variable_transformation/server.R')
source('modules/calculations/variable_transformation.R')

# Automated Data Summarization
source('modules/analysis/data_summarization/ui.R')
source('modules/analysis/data_summarization/server.R')
source('modules/calculations/data_summarization.R')

# Interactive Dashboards
source('modules/analysis/interactive_dashboards/ui.R')
source('modules/analysis/interactive_dashboards/server.R')

# Correlation Matrix Heatmaps
source('modules/analysis/corr_heatmap/ui.R')
source('modules/analysis/corr_heatmap/server.R')

# Pairwise Plot Matrix
source('modules/analysis/pairwise_plot_matrix/ui.R')
source('modules/analysis/pairwise_plot_matrix/server.R')

# Simulation Modules
source('modules/analysis/simulation/ui.R')
source('modules/analysis/simulation/server.R')

# Structural Equation Modeling (SEM)
source('modules/analysis/sem/ui.R')
source('modules/analysis/sem/server.R')
source('modules/calculations/sem.R')

# Latent Class Analysis
source('modules/analysis/latent_class/ui.R')
source('modules/analysis/latent_class/server.R')
source('modules/calculations/latent_class.R')

# Item Response Theory (IRT)
source('modules/analysis/irt/ui.R')
source('modules/analysis/irt/server.R')
source('modules/calculations/irt.R')

# --- Advanced Stats Modules ---
source('modules/analysis/advanced_stats/bayesian_hierarchical/ui.R')
source('modules/analysis/advanced_stats/bayesian_hierarchical/server.R')
source('modules/calculations/bayesian_hierarchical.R')

source('modules/analysis/advanced_stats/gam/ui.R')
source('modules/analysis/advanced_stats/gam/server.R')
source('modules/calculations/gam.R')

source('modules/analysis/advanced_stats/manova/ui.R')
source('modules/analysis/advanced_stats/manova/server.R')
source('modules/calculations/manova.R')

source('modules/analysis/advanced_stats/nonlinear_regression/ui.R')
source('modules/analysis/advanced_stats/nonlinear_regression/server.R')
source('modules/calculations/nonlinear_regression.R')

source('modules/analysis/advanced_stats/advanced_survival/ui.R')
source('modules/analysis/advanced_stats/advanced_survival/server.R')
source('modules/calculations/advanced_survival.R')

source('modules/analysis/advanced_stats/propensity_score/ui.R')
source('modules/analysis/advanced_stats/propensity_score/server.R')
source('modules/calculations/propensity_score.R')

source('modules/analysis/advanced_stats/mediation_moderation/ui.R')
source('modules/analysis/advanced_stats/mediation_moderation/server.R')
source('modules/calculations/mediation_moderation.R')

# --- Machine Learning Modules ---
source('modules/analysis/machine_learning/supervised/ui.R')
source('modules/analysis/machine_learning/supervised/server.R')
source('modules/calculations/supervised_ml.R')

source('modules/analysis/machine_learning/unsupervised/ui.R')
source('modules/analysis/machine_learning/unsupervised/server.R')
source('modules/calculations/unsupervised_ml.R')

source('modules/analysis/machine_learning/model_comparison/ui.R')
source('modules/analysis/machine_learning/model_comparison/server.R')
source('modules/calculations/model_comparison.R')

source('modules/analysis/machine_learning/feature_selection/ui.R')
source('modules/analysis/machine_learning/feature_selection/server.R')
source('modules/calculations/feature_selection.R')

# New modules
source('modules/analysis/deep_learning/ui.R')
source('modules/analysis/deep_learning/server.R')
source('modules/calculations/deep_learning.R')
source('modules/analysis/natural_language_processing/ui.R')
source('modules/analysis/natural_language_processing/server.R')
source('modules/calculations/natural_language_processing.R')
source('modules/analysis/spatial_analysis/ui.R')
source('modules/analysis/spatial_analysis/server.R')
source('modules/calculations/spatial_analysis.R')
source('modules/analysis/real_time_analytics/ui.R')
source('modules/analysis/real_time_analytics/server.R')
source('modules/calculations/real_time_analytics.R')

source('modules/analysis/multilevel_modeling/ui.R')
source('modules/analysis/multilevel_modeling/server.R')
source('modules/calculations/multilevel_modeling.R')

# Advanced Visualization
source('modules/analysis/advanced_visualization/ui.R')
source('modules/analysis/advanced_visualization/server.R')
source('modules/calculations/advanced_visualization.R')

# Ensemble Methods
source('modules/analysis/ensemble_methods/ui.R')
source('modules/analysis/ensemble_methods/server.R')
source('modules/calculations/ensemble_methods.R')

# Longitudinal Analysis
source('modules/analysis/longitudinal_analysis/ui.R')
source('modules/analysis/longitudinal_analysis/server.R')
source('modules/calculations/longitudinal_analysis.R')

# Discriminant Analysis
source('modules/analysis/discriminant/ui.R')
source('modules/analysis/discriminant/server.R')
source('modules/calculations/discriminant.R')

# Canonical Correlation Analysis
source('modules/analysis/cca/ui.R')
source('modules/analysis/cca/server.R')
source('modules/calculations/cca.R')

# Multidimensional Scaling
source('modules/analysis/mds/ui.R')
source('modules/analysis/mds/server.R')
source('modules/calculations/mds.R')

# TSNE/UMAP
source('modules/analysis/tsne_umap/ui.R')
source('modules/analysis/tsne_umap/server.R')
source('modules/calculations/tsne_umap.R')

# Competing Risks
source('modules/analysis/competing_risks/ui.R')
source('modules/analysis/competing_risks/server.R')
source('modules/calculations/competing_risks.R')

# Stratified Kaplan-Meier
source('modules/analysis/stratified_km/ui.R')
source('modules/analysis/stratified_km/server.R')
source('modules/calculations/stratified_km.R')

# Cox Proportional Hazards
source('modules/analysis/coxph/ui.R')
source('modules/analysis/coxph/server.R')
source('modules/calculations/coxph.R')

# --- Newly Implemented Modules ---

# Sign Test
source('modules/analysis/inference/sign_test/ui.R')
source('modules/analysis/inference/sign_test/server.R')
source('modules/calculations/sign_test.R')

# Jonckheere-Terpstra Test
source('modules/analysis/inference/jonckheere_terpstra/ui.R')
source('modules/analysis/inference/jonckheere_terpstra/server.R')
source('modules/calculations/jonckheere_terpstra.R')

# Three-Way ANOVA
source('modules/analysis/inference/anova/three_way/ui.R')
source('modules/analysis/inference/anova/three_way/server.R')
source('modules/calculations/three_way_anova.R')

# ANCOVA
source('modules/analysis/inference/ancova/ui.R')
source('modules/analysis/inference/ancova/server.R')
source('modules/calculations/ancova.R')

# Polynomial Regression
source('modules/analysis/regression_and_correlation/polynomial/ui.R')
source('modules/analysis/regression_and_correlation/polynomial/server.R')
source('modules/calculations/polynomial_regression.R')

# Stepwise Regression
source('modules/analysis/regression_and_correlation/stepwise/ui.R')
source('modules/analysis/regression_and_correlation/stepwise/server.R')
source('modules/calculations/stepwise_regression.R')

# Ridge Regression
source('modules/analysis/regression_and_correlation/ridge/ui.R')
source('modules/analysis/regression_and_correlation/ridge/server.R')
source('modules/calculations/ridge_regression.R')

# --- Newly Implemented High Priority Modules ---

# Lasso Regression
source('modules/analysis/regression_and_correlation/lasso/ui.R')
source('modules/analysis/regression_and_correlation/lasso/server.R')
source('modules/calculations/lasso_regression.R')

# Elastic Net Regression
source('modules/analysis/regression_and_correlation/elastic_net/ui.R')
source('modules/analysis/regression_and_correlation/elastic_net/server.R')
source('modules/calculations/elastic_net_regression.R')

# Mixed ANOVA
source('modules/analysis/inference/anova/mixed/ui.R')
source('modules/analysis/inference/anova/mixed/server.R')
source('modules/calculations/mixed_anova.R')

# Non-parametric ANCOVA
source('modules/analysis/inference/nonparametric_ancova/ui.R')
source('modules/analysis/inference/nonparametric_ancova/server.R')
source('modules/calculations/nonparametric_ancova.R')

# Cochran-Mantel-Haenszel Test
source('modules/analysis/inference/cochran_mantel_haenszel/ui.R')
source('modules/analysis/inference/cochran_mantel_haenszel/server.R')
source('modules/calculations/cochran_mantel_haenszel.R')

# --- Newly Implemented Medium Priority Modules ---

# Reliability Analysis
source('modules/analysis/reliability_analysis/ui.R')
source('modules/analysis/reliability_analysis/server.R')
source('modules/calculations/reliability_analysis.R')

# Partial and Semi-partial Correlations
source('modules/analysis/regression_and_correlation/partial_correlations/ui.R')
source('modules/analysis/regression_and_correlation/partial_correlations/server.R')
source('modules/calculations/partial_correlations.R')

# Log-linear Models
source('modules/analysis/log_linear_models/ui.R')
source('modules/analysis/log_linear_models/server.R')
source('modules/calculations/log_linear_models.R')

# --- Newly Implemented Low Priority Modules ---

# Quality Control Charts
source('modules/analysis/quality_control/control_charts/ui.R')
source('modules/analysis/quality_control/control_charts/server.R')
source('modules/calculations/quality_control_charts.R')

# --- Phase 1 and Phase 2 Modules ---

# Phase 1 Modules (High Priority)

# Mann-Kendall Trend Test
source('modules/analysis/inference/mann_kendall/ui.R')
source('modules/analysis/inference/mann_kendall/server.R')
source('modules/calculations/mann_kendall.R')

# Anderson-Darling Test
source('modules/analysis/inference/anderson_darling/ui.R')
source('modules/analysis/inference/anderson_darling/server.R')
source('modules/calculations/anderson_darling.R')

# Bartlett's Test
source('modules/analysis/inference/bartletts_test/ui.R')
source('modules/analysis/inference/bartletts_test/server.R')
source('modules/calculations/bartletts_test.R')

# Phase 2 Modules (Medium Priority)

# Runs Test (Wald-Wolfowitz)
source('modules/analysis/inference/runs_test/ui.R')
source('modules/analysis/inference/runs_test/server.R')
source('modules/calculations/runs_test.R')

# Durbin-Watson Test
source('modules/analysis/regression_and_correlation/durbin_watson/ui.R')
source('modules/analysis/regression_and_correlation/durbin_watson/server.R')
source('modules/calculations/durbin_watson.R')

# Breusch-Pagan Test
source('modules/analysis/regression_and_correlation/breusch_pagan/ui.R')
source('modules/analysis/regression_and_correlation/breusch_pagan/server.R')
source('modules/calculations/breusch_pagan.R')

# Shapiro-Wilk Test
source('modules/analysis/inference/shapiro_wilk/ui.R')
source('modules/analysis/inference/shapiro_wilk/server.R')
source('modules/calculations/shapiro_wilk.R')

# Jarque-Bera Test
source('modules/analysis/inference/jarque_bera/ui.R')
source('modules/analysis/inference/jarque_bera/server.R')
source('modules/calculations/jarque_bera.R')

# Ljung-Box Test
source('modules/analysis/time_series/ljung_box/ui.R')
source('modules/analysis/time_series/ljung_box/server.R')
source('modules/calculations/ljung_box.R')

# Augmented Dickey-Fuller Test
source('modules/analysis/time_series/augmented_dickey_fuller/ui.R')
source('modules/analysis/time_series/augmented_dickey_fuller/server.R')
source('modules/calculations/augmented_dickey_fuller.R')

# --- Additional High Priority Modules ---

# Quantile Regression
source('modules/analysis/regression_and_correlation/quantile_regression/ui.R')
source('modules/analysis/regression_and_correlation/quantile_regression/server.R')
source('modules/calculations/quantile_regression.R')

# Robust ANOVA
source('modules/analysis/inference/robust_anova/ui.R')
source('modules/analysis/inference/robust_anova/server.R')
source('modules/calculations/robust_anova.R')

# Factor Analysis
source('modules/analysis/multivariate/factor_analysis/ui.R')
source('modules/analysis/multivariate/factor_analysis/server.R')
source('modules/calculations/factor_analysis.R')

# Discriminant Analysis
source('modules/analysis/multivariate/discriminant_analysis/ui.R')
source('modules/analysis/multivariate/discriminant_analysis/server.R')
source('modules/calculations/discriminant_analysis.R')

# Correspondence Analysis
source('modules/analysis/multivariate/correspondence_analysis/ui.R')
source('modules/analysis/multivariate/correspondence_analysis/server.R')
source('modules/calculations/correspondence_analysis.R')

# --- Additional Medium Priority Modules ---

# Experimental Design Tools
source('modules/analysis/design/experimental_design/ui.R')
source('modules/analysis/design/experimental_design/server.R')
source('modules/calculations/experimental_design.R')

# Social Network Analysis
source('modules/analysis/social_sciences/social_networks/ui.R')
source('modules/analysis/social_sciences/social_networks/server.R')
source('modules/calculations/social_networks.R')

# Data Quality Assessment
source('modules/data_management/data_quality/ui.R')
source('modules/data_management/data_quality/server.R')
source('modules/calculations/data_quality.R')

# Advanced Stats UI modules
source('modules/analysis/advanced_stats/bayesian_networks/ui.R')
source('modules/analysis/advanced_stats/causal_inference/ui.R')
source('modules/analysis/advanced_stats/gee/ui.R')
source('modules/analysis/advanced_stats/glmm/ui.R')

# Decision Tree (ensure correct UI/server is loaded)
source('modules/analysis/machine_learning/supervised/decision_tree/ui.R')
source('modules/analysis/machine_learning/supervised/decision_tree/server.R')
