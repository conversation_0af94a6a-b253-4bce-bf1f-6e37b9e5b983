#!/usr/bin/env Rscript
# Comprehensive Test Runner for CougarStats
# Tests all 81 modules in the application

library(testthat)
library(shiny)
library(shinytest)
library(dplyr)
library(ggplot2)

# Set up test environment
test_dir <- "tests/testthat"
if (!dir.exists(test_dir)) {
  dir.create(test_dir, recursive = TRUE)
}

# Source all calculation modules
source("modules/calculations/desc_stats.R")
source("modules/calculations/anova.R")
source("modules/calculations/bayesian.R")
source("modules/calculations/bayesian_model_comparison.R")
source("modules/calculations/bayesian_regression.R")
source("modules/calculations/bayesian_hierarchical.R")
source("modules/calculations/bootstrap.R")
source("modules/calculations/cca.R")
source("modules/calculations/change_point.R")
source("modules/calculations/chi_square.R")
source("modules/calculations/cluster_analysis.R")
source("modules/calculations/cochrans_q.R")
source("modules/calculations/competing_risks.R")
source("modules/calculations/correlation.R")
source("modules/calculations/coxph.R")
source("modules/calculations/custom_test.R")
source("modules/calculations/discriminant.R")
source("modules/calculations/effect_size.R")
source("modules/calculations/feature_selection.R")
source("modules/calculations/friedman.R")
source("modules/calculations/gam.R")
source("modules/calculations/irt.R")
source("modules/calculations/kruskal_wallis.R")
source("modules/calculations/latent_class.R")
source("modules/calculations/levene.R")
source("modules/calculations/logistic_regression.R")
source("modules/calculations/mann_whitney.R")
source("modules/calculations/manova.R")
source("modules/calculations/mcnemar.R")
source("modules/calculations/mediation_moderation.R")
source("modules/calculations/meta_analysis.R")
source("modules/calculations/missingness_viz.R")
source("modules/calculations/mixed_effects.R")
source("modules/calculations/model_comparison.R")
source("modules/calculations/multiple_imputation.R")
source("modules/calculations/multiple_linear_regression.R")
source("modules/calculations/network_analysis.R")
source("modules/calculations/nonlinear_regression.R")
source("modules/calculations/one_sample.R")
source("modules/calculations/outlier_detection.R")
source("modules/calculations/paired_t_test.R")
source("modules/calculations/pca.R")
source("modules/calculations/permutation_tests.R")
source("modules/calculations/poisson_regression.R")
source("modules/calculations/post_hoc.R")
source("modules/calculations/power_analysis.R")
source("modules/calculations/prob_dist.R")
source("modules/calculations/propensity_score.R")
source("modules/calculations/proportion_tests.R")
source("modules/calculations/quasi_regression.R")
source("modules/calculations/repeated_measures_anova.R")
source("modules/calculations/robust_regression.R")
source("modules/calculations/sample_size_est.R")
source("modules/calculations/sem.R")
source("modules/calculations/simple_linear_regression.R")
source("modules/calculations/spectral_analysis.R")
source("modules/calculations/state_space.R")
source("modules/calculations/stl_decomposition.R")
source("modules/calculations/stratified_km.R")
source("modules/calculations/supervised_ml.R")
source("modules/calculations/survey_psychometrics.R")
source("modules/calculations/survival_analysis.R")
source("modules/calculations/time_series.R")
source("modules/calculations/tsne_umap.R")
source("modules/calculations/two_sample.R")
source("modules/calculations/two_way_anova.R")
source("modules/calculations/unsupervised_ml.R")
source("modules/calculations/variable_transformation.R")
source("modules/calculations/wilcoxon.R")
source("modules/calculations/zero_inflated.R")

# Load sample data
sample_data_dir <- "sample_data"
sample_files <- list.files(sample_data_dir, pattern = "\\.csv$", full.names = TRUE)

# Create test data mapping
test_data_mapping <- list(
  # Basic Statistics
  "desc_stats" = "desc_stats.csv",
  "prob_dist" = "desc_stats.csv",
  "sample_size_est" = "desc_stats.csv",
  
  # Inference Tests
  "one_sample" = "ttest_independent.csv",
  "two_sample" = "ttest_independent.csv",
  "paired_t_test" = "ttest_paired.csv",
  "anova" = "anova_oneway.csv",
  "two_way_anova" = "anova_oneway.csv",
  "repeated_measures_anova" = "anova_oneway.csv",
  "friedman" = "friedman.csv",
  "levene" = "anova_oneway.csv",
  "post_hoc" = "anova_oneway.csv",
  "kruskal_wallis" = "kruskal_wallis.csv",
  "chi_square" = "chi_square.csv",
  "mcnemar" = "chi_square.csv",
  "cochrans_q" = "chi_square.csv",
  "proportion_tests" = "proportion_test.csv",
  "mann_whitney" = "ttest_independent.csv",
  "wilcoxon" = "wilcoxon.csv",
  "bayesian" = "ttest_independent.csv",
  "bayesian_model_comparison" = "regression_multiple.csv",
  "custom_test" = "custom_test.csv",
  
  # Regression & Correlation
  "simple_linear_regression" = "regression_simple.csv",
  "multiple_linear_regression" = "regression_multiple.csv",
  "logistic_regression" = "logistic_regression.csv",
  "correlation" = "correlation.csv",
  "robust_regression" = "regression_multiple.csv",
  "poisson_regression" = "poisson_regression.csv",
  "quasi_regression" = "poisson_regression.csv",
  "zero_inflated" = "zero_inflated.csv",
  "bayesian_regression" = "regression_multiple.csv",
  "survival_analysis" = "survival.csv",
  
  # Advanced Analysis
  "pca" = "pca.csv",
  "cluster_analysis" = "cluster_analysis.csv",
  "roc_analysis" = "roc_analysis.csv",
  "power_analysis" = "desc_stats.csv",
  "effect_size" = "ttest_independent.csv",
  "custom_plot" = "desc_stats.csv",
  "meta_analysis" = "meta_analysis.csv",
  "mixed_effects" = "mixed_effects.csv",
  "survey_psychometrics" = "desc_stats.csv",
  "time_series" = "time_series.csv",
  "stl_decomposition" = "time_series.csv",
  "state_space" = "state_space.csv",
  "change_point" = "time_series.csv",
  "spectral_analysis" = "time_series.csv",
  "network_analysis" = "network_analysis.csv",
  "sem" = "sem.csv",
  "latent_class" = "latent_class.csv",
  "irt" = "irt.csv",
  "data_summarization" = "data_summarization.csv",
  "missingness_viz" = "missingness.csv",
  "multiple_imputation" = "multiple_imputation.csv",
  "outlier_detection" = "desc_stats.csv",
  "pairwise_plot_matrix" = "desc_stats.csv",
  "permutation_tests" = "permutation_tests.csv",
  "bootstrap" = "bootstrapping.csv",
  "simulation" = "simulation.csv",
  "variable_transformation" = "desc_stats.csv",
  "competing_risks" = "survival.csv",
  "correlation_heatmap" = "correlation.csv",
  "coxph" = "coxph.csv",
  "discriminant" = "discriminant.csv",
  "cca" = "cca.csv",
  "mds" = "mds.csv",
  "tsne_umap" = "tsne_umap.csv",
  "stratified_km" = "stratified_km.csv",
  
  # Machine Learning
  "supervised_ml" = "supervised_ml.csv",
  "unsupervised_ml" = "unsupervised_ml.csv",
  "model_comparison" = "model_comparison.csv",
  "feature_selection" = "feature_selection.csv",
  
  # Advanced Stats
  "advanced_survival" = "advanced_survival.csv",
  "gam" = "gam.csv",
  "manova" = "manova.csv",
  "nonlinear_regression" = "nonlinear_regression.csv",
  "propensity_score" = "propensity_score.csv",
  "mediation_moderation" = "mediation_moderation.csv",
  "bayesian_hierarchical" = "bayesian_hierarchical.csv"
)

# Helper function to load test data
load_test_data <- function(module_name) {
  data_file <- test_data_mapping[[module_name]]
  if (is.null(data_file)) {
    # Default to desc_stats.csv if no specific mapping
    data_file <- "desc_stats.csv"
  }
  file_path <- file.path(sample_data_dir, data_file)
  if (file.exists(file_path)) {
    read.csv(file_path, stringsAsFactors = FALSE)
  } else {
    # Create minimal test data if file doesn't exist
    data.frame(
      x = rnorm(20),
      y = rnorm(20),
      group = rep(c("A", "B"), each = 10),
      stringsAsFactors = FALSE
    )
  }
}

# Test results storage
test_results <- list()
test_summary <- list(
  total_modules = 0,
  passed = 0,
  failed = 0,
  errors = 0
)

# Function to run tests for a module
run_module_tests <- function(module_name, test_function) {
  cat(sprintf("Testing module: %s\n", module_name))
  test_summary$total_modules <<- test_summary$total_modules + 1
  
  tryCatch({
    result <- test_function()
    if (result$success) {
      test_summary$passed <<- test_summary$passed + 1
      cat(sprintf("  ✓ %s: PASSED\n", module_name))
    } else {
      test_summary$failed <<- test_summary$failed + 1
      cat(sprintf("  ✗ %s: FAILED - %s\n", module_name, result$error))
    }
    test_results[[module_name]] <<- result
  }, error = function(e) {
    test_summary$errors <<- test_summary$errors + 1
    cat(sprintf("  ✗ %s: ERROR - %s\n", module_name, e$message))
    test_results[[module_name]] <<- list(success = FALSE, error = e$message)
  })
}

# Main test execution
main <- function() {
  cat("Starting Comprehensive CougarStats Module Testing\n")
  cat("================================================\n\n")
  
  # Run all test suites
  source("tests/testthat/test_basic_statistics.R")
  source("tests/testthat/test_inference_tests.R")
  source("tests/testthat/test_regression_correlation.R")
  source("tests/testthat/test_advanced_analysis.R")
  source("tests/testthat/test_machine_learning.R")
  source("tests/testthat/test_advanced_stats.R")
  
  # Print summary
  cat("\n================================================\n")
  cat("TEST SUMMARY\n")
  cat("================================================\n")
  cat(sprintf("Total Modules Tested: %d\n", test_summary$total_modules))
  cat(sprintf("Passed: %d\n", test_summary$passed))
  cat(sprintf("Failed: %d\n", test_summary$failed))
  cat(sprintf("Errors: %d\n", test_summary$errors))
  cat(sprintf("Success Rate: %.1f%%\n", (test_summary$passed / test_summary$total_modules) * 100))
  
  # Save detailed results
  saveRDS(test_results, "tests/test_results.rds")
  saveRDS(test_summary, "tests/test_summary.rds")
  
  cat("\nDetailed results saved to tests/test_results.rds\n")
  cat("Test summary saved to tests/test_summary.rds\n")
  
  # Return exit code
  if (test_summary$failed + test_summary$errors > 0) {
    cat("\nSome tests failed. Check the detailed results.\n")
    quit(status = 1)
  } else {
    cat("\nAll tests passed successfully!\n")
    quit(status = 0)
  }
}

# Run main function if script is executed directly
if (!interactive()) {
  main()
} 