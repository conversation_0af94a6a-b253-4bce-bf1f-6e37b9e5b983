# Script to install cmprsk package for competing risks analysis
# This package is not available on CRAN and needs to be installed from alternative sources

cat("Installing cmprsk package for competing risks analysis...\n")
cat("This package is required for competing risks survival analysis.\n\n")

# Check if already installed
if (requireNamespace("cmprsk", quietly = TRUE)) {
  cat("✓ cmprsk package is already installed!\n")
  library(cmprsk)
  cat("✓ cmprsk package loaded successfully\n")
} else {
  cat("cmprsk package not found. Attempting installation...\n\n")
  
  # Method 1: Try Bioconductor
  cat("Method 1: Installing from Bioconductor...\n")
  tryCatch({
    if (!requireNamespace("BiocManager", quietly = TRUE)) {
      install.packages("BiocManager", repos = "https://cran.rstudio.com/")
    }
    BiocManager::install("cmprsk", update = FALSE, ask = FALSE)
    
    if (requireNamespace("cmprsk", quietly = TRUE)) {
      cat("✓ Successfully installed cmprsk from Bioconductor!\n")
      library(cmprsk)
      cat("✓ cmprsk package loaded successfully\n")
    } else {
      stop("Installation from Bioconductor failed")
    }
  }, error = function(e) {
    cat("✗ Failed to install from Bioconductor:", e$message, "\n\n")
    
    # Method 2: Try GitHub
    cat("Method 2: Installing from GitHub...\n")
    tryCatch({
      if (!requireNamespace("remotes", quietly = TRUE)) {
        install.packages("remotes", repos = "https://cran.rstudio.com/")
      }
      remotes::install_github("cran/cmprsk")
      
      if (requireNamespace("cmprsk", quietly = TRUE)) {
        cat("✓ Successfully installed cmprsk from GitHub!\n")
        library(cmprsk)
        cat("✓ cmprsk package loaded successfully\n")
      } else {
        stop("Installation from GitHub failed")
      }
    }, error = function(e2) {
      cat("✗ Failed to install from GitHub:", e2$message, "\n\n")
      
      # Method 3: Manual installation instructions
      cat("Method 3: Manual installation required\n")
      cat("The cmprsk package could not be installed automatically.\n")
      cat("Please try one of the following manual methods:\n\n")
      
      cat("Option A: Install from Bioconductor manually\n")
      cat("1. Run: install.packages('BiocManager')\n")
      cat("2. Run: BiocManager::install('cmprsk')\n\n")
      
      cat("Option B: Install from GitHub manually\n")
      cat("1. Run: install.packages('remotes')\n")
      cat("2. Run: remotes::install_github('cran/cmprsk')\n\n")
      
      cat("Option C: Download and install manually\n")
      cat("1. Visit: https://cran.r-project.org/src/contrib/Archive/cmprsk/\n")
      cat("2. Download the latest version\n")
      cat("3. Install using: install.packages('path/to/downloaded/file', repos = NULL, type = 'source')\n\n")
      
      cat("Option D: Skip competing risks analysis\n")
      cat("If you cannot install cmprsk, competing risks analysis will not be available,\n")
      cat("but other survival analysis methods (Kaplan-Meier, Cox regression) will still work.\n\n")
      
      cat("After manual installation, restart R and run this script again to verify.\n")
    })
  })
}

# Test the installation
cat("\nTesting cmprsk functionality...\n")
tryCatch({
  # Create some test data
  time <- c(1, 2, 3, 4, 5, 6, 7, 8, 9, 10)
  status <- c(1, 1, 0, 1, 0, 1, 1, 0, 1, 0)
  
  # Try to run a simple competing risks analysis
  fit <- cmprsk::cuminc(time, status)
  cat("✓ cmprsk package is working correctly!\n")
  cat("✓ Competing risks analysis is now available in CougarStats\n")
  
}, error = function(e) {
  cat("✗ cmprsk package test failed:", e$message, "\n")
  cat("The package may not be working correctly.\n")
}) 