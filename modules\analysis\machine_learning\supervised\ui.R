SupervisedMLUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("mlUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("mlResponse"), "Response Variable", choices = NULL),
        selectizeInput(ns("mlPredictors"), "Predictor Variables", choices = NULL, multiple = TRUE),
        selectInput(ns("mlModelType"), "Model Type", choices = c("Random Forest", "SVM", "GBM", "kNN", "Neural Network")),
        numericInput(ns("mlCVFolds"), "Cross-Validation Folds", value = 5, min = 2, max = 20),
        br(),
        actionButton(ns("goML"), label = "Run Model", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("mlError")),
        tableOutput(ns("mlSummary")),
        plotOutput(ns("mlPlot"))
      )
    )
  )
} 