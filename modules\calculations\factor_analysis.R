# Factor Analysis calculation and output helpers

factor_analysis_uploadData_func <- function(faUserData) {
  ext <- tools::file_ext(faUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(faUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(faUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(faUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(faUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

factor_analysis_results_func <- function(data, vars, n_factors, rotation = "varimax", method = "minres") {
  tryCatch({
    if (!requireNamespace("psych", quietly = TRUE)) {
      stop("Package 'psych' is required for factor analysis.")
    }
    
    fa_data <- data[, vars, drop = FALSE]
    fa_data <- na.omit(fa_data)
    
    cor_matrix <- cor(fa_data)
    
    fit <- psych::fa(cor_matrix, nfactors = n_factors, rotate = rotation, fm = method)
    
    list(
      fit = fit,
      data = fa_data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Factor Analysis calculation:", e$message))
  })
}

factor_analysis_ht_html <- function(results) {
  # No formal hypothesis test for factor analysis, summary is more informative
  tagList(
    h4("Factor Analysis"),
    p("See summary table for model details.")
  )
}

factor_analysis_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  out <- list(
    h4("Factor Loadings"),
    renderPrint(results$fit$loadings),
    h4("Variance Explained"),
    renderPrint(results$fit$Vaccounted)
  )
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

factor_analysis_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  psych::fa.diagram(results$fit)
}