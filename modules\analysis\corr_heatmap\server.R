CorrHeatmapServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Source the calculations file
    source("modules/calculations/corr_heatmap.R")
    
    # File upload reactive
    chmData <- eventReactive(input$chmUserData, {
      handle_corr_heatmap_data_upload(input$chmUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(chmData(), {
      data <- chmData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'chmVars', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    chmValidationErrors <- reactive({
      validate_corr_heatmap_inputs(chmData(), input$chmVars)
    })
    
    # Correlation heatmap analysis reactive
    chmResult <- eventReactive(input$goCHM, {
      perform_corr_heatmap_analysis(chmData(), input$chmVars)
    })
    
    # Error handling
    output$chmError <- renderUI({
      errors <- chmValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          chmResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "Correlation Heatmap Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$chmModelSummary <- renderUI({
      req(chmResult())
      render_corr_heatmap_summary_ui(chmResult())
    })
    
    output$chmPlot <- renderPlot({
      req(chmResult())
      render_corr_heatmap_plots(chmResult())
    })
    
    output$chmDiagnostics <- renderUI({
      req(chmResult())
      render_corr_heatmap_diagnostics(chmResult())
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$chmDataSummary <- renderUI({
      req(chmResult())
      render_corr_heatmap_data_summary(chmResult(), chmData())
    })
    
    output$chmAssumptions <- renderUI({
      req(chmResult())
      render_corr_heatmap_assumptions_check(chmResult())
    })
    
    output$chmDiagnosticPlots <- renderPlot({
      req(chmResult())
      render_corr_heatmap_diagnostic_plots(chmResult(), chmData())
    })
    
    # Uploaded Data Tab Outputs
    output$chmDataTable <- DT::renderDT({
      req(chmData())
      render_corr_heatmap_data_table(chmData())
    })
    
    output$chmDataInfo <- renderUI({
      req(chmData())
      render_corr_heatmap_data_info(chmData(), input$chmUserData)
    })
  })
} 