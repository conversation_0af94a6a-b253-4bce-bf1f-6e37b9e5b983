AdvancedSurvivalServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    survData <- eventReactive(input$survUserData, {
      handle_file_upload(input$survUserData)
    })
    
    observeEvent(survData(), {
      data <- survData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'survTime', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'survEvent', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'survCovariates', choices = names(data), server = TRUE)
        updateSelectizeInput(session, 'survGroup', choices = names(data), server = TRUE)
      }
    })
    
    survValidationErrors <- reactive({
      errors <- c()
      data <- survData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$survTime)) {
        errors <- c(errors, "Select a time variable.")
      }
      if (is.null(input$survEvent)) {
        errors <- c(errors, "Select an event variable.")
      }
      if (!is.null(input$survTime) && !is.numeric(data[[input$survTime]])) {
        errors <- c(errors, "Time variable must be numeric.")
      }
      if (!is.null(input$survEvent)) {
        event_values <- unique(data[[input$survEvent]])
        if (length(event_values) < 2) {
          errors <- c(errors, "Event variable must have at least 2 distinct values.")
        }
      }
      for (var in c(input$survCovariates, input$survGroup)) {
        if (!is.null(var) && !is.numeric(data[[var]]) && !is.factor(data[[var]])) {
          errors <- c(errors, sprintf("Variable '%s' must be numeric or factor.", var))
        }
      }
      errors
    })
    
    survResult <- eventReactive(input$goSurv, {
      data <- survData()
      req(data, input$survTime, input$survEvent)
      
      # Get parameters from UI
      analysis_type <- ifelse(is.null(input$survAnalysisType), "cox", input$survAnalysisType)
      competing_risks <- ifelse(is.null(input$survCompetingRisks), FALSE, input$survCompetingRisks)
      covariates <- ifelse(is.null(input$survCovariates), NULL, input$survCovariates)
      group <- ifelse(is.null(input$survGroup), NULL, input$survGroup)
      
      advanced_survival_analysis(data, input$survTime, input$survEvent, 
                               covariates = covariates, group = group,
                               analysis_type = analysis_type, competing_risks = competing_risks)
    })
    
    asurvResult <- eventReactive(input$goASurv, {
      data <- survData()
      req(data, input$asurvTime, input$asurvEvent)
      
      # Get parameters from UI
      model_type <- ifelse(is.null(input$asurvModelType), "Parametric", input$asurvModelType)
      covariates <- ifelse(is.null(input$asurvCovariates), NULL, input$asurvCovariates)
      
      advanced_survival_analysis(data, input$asurvTime, input$asurvEvent, 
                               covariates = covariates, model_type = model_type)
    })
    
    observeEvent(input$goASurv, {
      output$asurvResultsUI <- renderUI({
        errors <- survValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Advanced Survival Analysis", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("asurvTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("asurvAnalysis"),
                title = "Analysis",
                titlePanel("Advanced Survival Analysis Results"),
                br(),
                h4("Analysis Type"),
                textOutput(ns('asurvAnalysisType')),
                h4("Number of Observations"),
                textOutput(ns('asurvObservations')),
                h4("Number of Events"),
                textOutput(ns('asurvEvents')),
                h4("Coefficients Table"),
                tableOutput(ns('asurvCoefficients')),
                h4("Fit Statistics"),
                tableOutput(ns('asurvFitStats')),
                h4("Proportional Hazards Test (Cox)"),
                tableOutput(ns('asurvPHTest')),
                h4("Log-rank Test (Cox)"),
                tableOutput(ns('asurvLogRank')),
                h4("Median Survival (Cox)"),
                textOutput(ns('asurvMedianSurvival')),
                h4("Model Comparison (Parametric)"),
                tableOutput(ns('asurvModelComparison')),
                h4("Best Model Name (Parametric)"),
                textOutput(ns('asurvBestModel')),
                h4("Event Summary (Competing Risks)"),
                tableOutput(ns('asurvEventSummary')),
                h4("CIF Results (Competing Risks)"),
                tableOutput(ns('asurvCIFResults')),
                h4("Number of Event Types (Competing Risks)"),
                textOutput(ns('asurvEventTypes'))
              ),
              tabPanel(
                id = ns("asurvDiagnostics"),
                title = "Diagnostics",
                h4("Survival Curves Plot"),
                plotOutput(ns('asurvPlot'), height = "400px")
              ),
              tabPanel(
                id = ns("asurvUploadedData"),
                title = "Uploaded Data",
                h4("Raw Uploaded Data"),
                DT::DTOutput(ns('asurvRawDataTable'))
              )
            )
          )
        }
      })
    })
    output$asurvRawDataTable <- DT::renderDT({
      req(survData())
      DT::datatable(survData(), options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), rownames = FALSE)
    })
  })
} 