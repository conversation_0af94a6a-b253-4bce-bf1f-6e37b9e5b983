DiscriminantUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("discUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("discPredictors"), "Predictor Variables", choices = NULL, multiple = TRUE),
        selectizeInput(ns("discGroup"), "Grouping Variable", choices = NULL),
        selectInput(ns("discMethod"), "Method", choices = c("LDA", "QDA")),
        br(),
        actionButton(ns("goDisc"), label = "Run Discriminant Analysis", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("discError")),
        tableOutput(ns("discResults")),
        tableOutput(ns("discConfusion")),
        plotOutput(ns("discPlot"))
      )
    )
  )
} 