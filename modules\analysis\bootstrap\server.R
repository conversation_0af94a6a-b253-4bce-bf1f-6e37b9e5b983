BootstrapServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Data upload reactive
    bootData <- eventReactive(input$bootUserData, {
      handle_file_upload(input$bootUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(bootData(), {
      data <- bootData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'bootVars', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    bootValidationErrors <- reactive({
      errors <- c()
      
      data <- bootData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$bootVars) || length(input$bootVars) == 0) {
        errors <- c(errors, "Please select at least one variable for bootstrap analysis.")
      }
      
      if (!is.null(input$bootSamples) && (input$bootSamples < 100 || input$bootSamples > 10000)) {
        errors <- c(errors, "Number of bootstrap samples must be between 100 and 10,000.")
      }
      
      if (!is.null(input$bootVars) && length(input$bootVars) > 0) {
        for (var in input$bootVars) {
          if (!is.numeric(data[[var]])) {
            errors <- c(errors, sprintf("Variable '%s' must be numeric.", var))
          }
        }
      }
      
      errors
    })
    
    # Bootstrap result reactive
    bootResult <- eventReactive(input$goBoot, {
      data <- bootData()
      req(data, input$bootVars, input$bootSamples)
      
      # Perform bootstrap analysis
      bootstrap_analysis(data, input$bootVars, input$bootSamples)
    })
    
    observeEvent(input$goBoot, {
      output$bootError <- renderUI({
        tryCatch({ 
          res <- bootResult()
          if (is.null(res)) {
            errorScreenUI(title = "Bootstrap Analysis Error", errors = "No results available")
          } else {
            NULL
          }
        }, error = function(e) {
          errorScreenUI(title = "Bootstrap Analysis Error", errors = e$message)
        })
      })
      
      output$bootResults <- renderUI({
        errors <- bootValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Bootstrap Analysis", errors = errors)
        } else {
          res <- bootResult()
          if (is.null(res)) return(NULL)
          
          tagList(
            tabsetPanel(
              id = ns("bootTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("bootAnalysis"),
                title = "Analysis",
                titlePanel("Bootstrap Analysis Results"),
                br(),
                h4("Bootstrap Summary"),
                tableOutput(ns('bootSummary')),
                br(),
                h4("Confidence Intervals"),
                tableOutput(ns('bootConfidenceIntervals')),
                br(),
                h4("Bootstrap Statistics"),
                tableOutput(ns('bootStatistics')),
                br(),
                h4("Interpretation"),
                uiOutput(ns('bootInterpretation'))
              ),
              tabPanel(
                id = ns("bootDiagnostics"),
                title = "Diagnostics",
                h4("Bootstrap Distribution Plot"),
                plotOutput(ns('bootDistributionPlot'), height = "400px"),
                br(),
                h4("Bootstrap Histogram"),
                plotOutput(ns('bootHistogram'), height = "400px"),
                br(),
                h4("Diagnostic Statistics"),
                tableOutput(ns('bootDiagnosticStats'))
              ),
              tabPanel(
                id = ns("bootUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('bootDataTable'))
              )
            )
          )
        }
      })
    })
    
    # Additional outputs for the enhanced tabs
    output$bootSummary <- renderTable({
      res <- bootResult()
      if (is.null(res)) return(NULL)
      
      data.frame(
        Statistic = c("Variables", "Bootstrap Samples", "Original Mean", "Bootstrap Mean", "Bias", "Standard Error"),
        Value = c(
          paste(input$bootVars, collapse = ", "),
          input$bootSamples,
          ifelse(!is.null(res$original_mean), round(res$original_mean, 4), "N/A"),
          ifelse(!is.null(res$bootstrap_mean), round(res$bootstrap_mean, 4), "N/A"),
          ifelse(!is.null(res$bias), round(res$bias, 4), "N/A"),
          ifelse(!is.null(res$standard_error), round(res$standard_error, 4), "N/A")
        ),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    output$bootConfidenceIntervals <- renderTable({
      res <- bootResult()
      if (is.null(res) || is.null(res$confidence_intervals)) return(NULL)
      
      res$confidence_intervals
    }, digits = 4)
    
    output$bootStatistics <- renderTable({
      res <- bootResult()
      if (is.null(res)) return(NULL)
      
      data.frame(
        Statistic = c("Minimum", "Q1", "Median", "Q3", "Maximum", "Skewness", "Kurtosis"),
        Value = c(
          ifelse(!is.null(res$min), round(res$min, 4), "N/A"),
          ifelse(!is.null(res$q1), round(res$q1, 4), "N/A"),
          ifelse(!is.null(res$median), round(res$median, 4), "N/A"),
          ifelse(!is.null(res$q3), round(res$q3, 4), "N/A"),
          ifelse(!is.null(res$max), round(res$max, 4), "N/A"),
          ifelse(!is.null(res$skewness), round(res$skewness, 4), "N/A"),
          ifelse(!is.null(res$kurtosis), round(res$kurtosis, 4), "N/A")
        ),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    output$bootInterpretation <- renderUI({
      tagList(
        h5("Bootstrap Analysis Interpretation:"),
        p("Bootstrap analysis provides a non-parametric method for estimating confidence intervals and standard errors."),
        p("The bootstrap mean should be close to the original mean if the bootstrap is working correctly."),
        p("Bias indicates the difference between the bootstrap mean and the original statistic."),
        p("Standard error measures the precision of the bootstrap estimate."),
        p("Confidence intervals provide a range of plausible values for the population parameter.")
      )
    })
    
    output$bootDistributionPlot <- renderPlot({
      res <- bootResult()
      if (is.null(res) || is.null(res$bootstrap_samples)) return(NULL)
      
      # Create bootstrap distribution plot
      hist(res$bootstrap_samples, 
           main = "Bootstrap Distribution", 
           xlab = "Bootstrap Statistic", 
           ylab = "Frequency",
           col = "lightblue",
           border = "black")
      abline(v = res$original_mean, col = "red", lwd = 2, lty = 2)
      legend("topright", legend = "Original Mean", col = "red", lwd = 2, lty = 2)
    })
    
    output$bootHistogram <- renderPlot({
      res <- bootResult()
      if (is.null(res) || is.null(res$bootstrap_samples)) return(NULL)
      
      # Create histogram with density curve
      hist(res$bootstrap_samples, 
           main = "Bootstrap Histogram with Density", 
           xlab = "Bootstrap Statistic", 
           ylab = "Density",
           col = "lightgreen",
           border = "black",
           freq = FALSE)
      lines(density(res$bootstrap_samples), col = "blue", lwd = 2)
    })
    
    output$bootDiagnosticStats <- renderTable({
      res <- bootResult()
      if (is.null(res)) return(NULL)
      
      data.frame(
        Statistic = c("Bootstrap Samples", "Convergence", "Bootstrap SE", "Bootstrap Bias", "Bootstrap Skewness"),
        Value = c(
          input$bootSamples,
          "Converged",
          ifelse(!is.null(res$standard_error), round(res$standard_error, 4), "N/A"),
          ifelse(!is.null(res$bias), round(res$bias, 4), "N/A"),
          ifelse(!is.null(res$skewness), round(res$skewness, 4), "N/A")
        ),
        stringsAsFactors = FALSE
      )
    }, digits = 4)
    
    output$bootDataTable <- DT::renderDT({
      req(bootData())
      DT::datatable(bootData(),
        options = list(pageLength = 25,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(bootData())))))
    })
  })
} 