# Community Ecology calculations for CougarStats

communityEcologyResults_func <- function(data, species_cols) {
  if (!requireNamespace("vegan", quietly = TRUE)) {
    stop("Package 'vegan' is required for community ecology analysis.")
  }
  if (!requireNamespace("ggplot2", quietly = TRUE)) {
    stop("Package 'ggplot2' is required for plotting.")
  }
  # Validate input
  if (is.null(species_cols) || length(species_cols) < 2) {
    stop("At least two species columns must be specified.")
  }
  species_data <- data[, species_cols]
  shannon <- vegan::diversity(species_data, index = "shannon")
  simpson <- vegan::diversity(species_data, index = "simpson")
  richness <- vegan::specnumber(species_data)
  plot_df <- data.frame(Sample = 1:nrow(species_data), <PERSON> = shannon, <PERSON> = simpson, Richness = richness)
  plot_obj <- ggplot2::ggplot(plot_df, ggplot2::aes(x = Sample)) +
    ggplot2::geom_line(ggplot2::aes(y = <PERSON>, color = "Shannon")) +
    ggplot2::geom_line(ggplot2::aes(y = <PERSON>, color = "Simpson")) +
    ggplot2::geom_line(ggplot2::aes(y = Richness, color = "Richness")) +
    ggplot2::labs(title = "Community Diversity Indices", y = "Index Value", color = "Index")
  list(
    shannon = shannon,
    simpson = simpson,
    richness = richness,
    summary = summary(species_data),
    plot = plot_obj
  )
} 