statisticalInferenceServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    OneSampleInferenceServer("one_sample")
    TwoSampleInferenceServer("two_sample")
    PairedTTestServer("paired_t_test")
    ProportionTestServer("proportion_tests")
    AnovaInferenceServer("anova")
    TwoWayAnovaServer("two_way")
    RepeatedMeasuresAnovaServer("repeated_measures")
    FriedmanTestServer("friedman")
    LeveneTestServer("levene")
    PostHocTestServer("post_hoc")
    kruskalWallisServer("kruskal_wallis")
    chiSquareServer("chi_square")
    McNemarTestServer("mcnemar")
    CochransQTestServer("cochrans_q")
    BayesianTestServer("bayesian_tests")
    CustomTestServer("custom_test")
    mannWhitneyServer("mann_whitney")
    wilcoxonServer("wilcoxon")
  })
} 