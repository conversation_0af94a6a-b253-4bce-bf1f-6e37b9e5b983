ModelComparisonServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    mcData <- eventReactive(input$mcUserData, {
      handle_file_upload(input$mcUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(mcData(), {
      data <- mcData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, "mcResponse", choices = names(data), server = TRUE)
        updateSelectizeInput(session, "mcPredictors", choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    mcValidationErrors <- reactive({
      errors <- c()
      data <- mcData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$mcResponse) || input$mcResponse == "") {
        errors <- c(errors, "Please select a response variable.")
      }
      
      if (is.null(input$mcPredictors) || length(input$mcPredictors) == 0) {
        errors <- c(errors, "Please select at least one predictor variable.")
      }
      
      if (!is.null(input$mcResponse) && !is.null(input$mcPredictors) && 
          input$mcResponse != "" && length(input$mcPredictors) > 0) {
        
        # Check if response variable is in predictors
        if (input$mcResponse %in% input$mcPredictors) {
          errors <- c(errors, "Response variable cannot be included in predictors.")
        }
        
        # Check for sufficient observations
        n_obs <- nrow(data)
        n_vars <- length(input$mcPredictors) + 1  # +1 for response
        
        if (n_obs <= n_vars) {
          errors <- c(errors, sprintf("Number of observations (%d) must be greater than number of variables (%d).", n_obs, n_vars))
        }
        
        # Check for variance in response variable
        response_var <- data[[input$mcResponse]]
        if (sd(response_var, na.rm = TRUE) == 0) {
          errors <- c(errors, "Response variable must have variance (not all the same).")
        }
        
        # Check for variance in predictor variables
        for (var in input$mcPredictors) {
          if (sd(data[[var]], na.rm = TRUE) == 0) {
            errors <- c(errors, sprintf("Predictor variable '%s' must have variance (not all the same).", var))
          }
        }
      }
      
      # Check cross-validation folds
      if (!is.null(input$mcCVFolds) && (input$mcCVFolds < 2 || input$mcCVFolds > 20)) {
        errors <- c(errors, "Cross-validation folds must be between 2 and 20.")
      }
      
      errors
    })
    
    # Model comparison analysis reactive
    mcResult <- eventReactive(input$goMC, {
      data <- mcData()
      req(data, input$mcResponse, input$mcPredictors)
      
      # Remove rows with missing values
      complete_data <- data[complete.cases(data[, c(input$mcResponse, input$mcPredictors)]), ]
      
      if (nrow(complete_data) < 10) {
        stop("Insufficient complete cases for model comparison.")
      }
      
      # Prepare data
      x <- complete_data[, input$mcPredictors, drop = FALSE]
      y <- complete_data[[input$mcResponse]]
      
      # Set up cross-validation
      cv_folds <- ifelse(is.null(input$mcCVFolds), 5, input$mcCVFolds)
      
      # Compare multiple models
      tryCatch({
        if (requireNamespace("caret", quietly = TRUE)) {
          ctrl <- caret::trainControl(method = "cv", number = cv_folds)
          
          # Define models to compare
          models <- list()
          model_names <- c()
          
          # Linear Regression
          tryCatch({
            models[["Linear Regression"]] <- caret::train(x, y, method = "lm", trControl = ctrl)
            model_names <- c(model_names, "Linear Regression")
          }, error = function(e) {
            # Skip if model fails
          })
          
          # Random Forest
          if (requireNamespace("randomForest", quietly = TRUE)) {
            tryCatch({
              models[["Random Forest"]] <- caret::train(x, y, method = "rf", trControl = ctrl)
              model_names <- c(model_names, "Random Forest")
            }, error = function(e) {
              # Skip if model fails
            })
          }
          
          # SVM
          if (requireNamespace("e1071", quietly = TRUE)) {
            tryCatch({
              models[["SVM"]] <- caret::train(x, y, method = "svmRadial", trControl = ctrl)
              model_names <- c(model_names, "SVM")
            }, error = function(e) {
              # Skip if model fails
            })
          }
          
          # Gradient Boosting
          if (requireNamespace("gbm", quietly = TRUE)) {
            tryCatch({
              models[["Gradient Boosting"]] <- caret::train(x, y, method = "gbm", trControl = ctrl)
              model_names <- c(model_names, "Gradient Boosting")
            }, error = function(e) {
              # Skip if model fails
            })
          }
          
          # k-NN
          tryCatch({
            models[["k-NN"]] <- caret::train(x, y, method = "knn", trControl = ctrl)
            model_names <- c(model_names, "k-NN")
          }, error = function(e) {
            # Skip if model fails
          })
          
          if (length(models) == 0) {
            stop("No models could be fitted successfully.")
          }
          
          # Compare models
          comparison_results <- data.frame(
            Model = model_names,
            RMSE = sapply(models, function(m) min(m$results$RMSE, na.rm = TRUE)),
            MAE = sapply(models, function(m) min(m$results$MAE, na.rm = TRUE)),
            R_squared = sapply(models, function(m) max(m$results$Rsquared, na.rm = TRUE)),
            stringsAsFactors = FALSE
          )
          
          # Find best model for each metric
          best_rmse <- comparison_results$Model[which.min(comparison_results$RMSE)]
          best_mae <- comparison_results$Model[which.min(comparison_results$MAE)]
          best_r2 <- comparison_results$Model[which.max(comparison_results$R_squared)]
          
          # Get predictions from all models
          predictions <- list()
          for (name in model_names) {
            predictions[[name]] <- predict(models[[name]], x)
          }
          
          list(
            models = models,
            comparison_results = comparison_results,
            predictions = predictions,
            best_rmse = best_rmse,
            best_mae = best_mae,
            best_r2 = best_r2,
            cv_folds = cv_folds,
            n_observations = nrow(complete_data),
            n_predictors = length(input$mcPredictors),
            response_variable = input$mcResponse,
            predictor_variables = input$mcPredictors,
            model_names = model_names
          )
          
        } else {
          stop("Package 'caret' is required for model comparison.")
        }
        
      }, error = function(e) {
        stop(e$message)
      })
    })
    
    # Error handling
    output$mcError <- renderUI({
      errors <- mcValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          mcResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "Model Comparison Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$mcModelSummary <- renderUI({
      req(mcResult())
      res <- mcResult()
      
      tagList(
        h4("Model Comparison Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Metric = c("Number of Models", "Response Variable", "Number of Predictors", "Number of Observations", "Cross-Validation Folds"),
            Value = c(
              length(res$models),
              res$response_variable,
              res$n_predictors,
              res$n_observations,
              res$cv_folds
            )
          )
        }),
        h4("Model Performance Comparison"),
        renderTable({
          res$comparison_results
        }),
        h4("Best Models by Metric"),
        renderTable({
          data.frame(
            Metric = c("Best RMSE", "Best MAE", "Best R-squared"),
            Model = c(res$best_rmse, res$best_mae, res$best_r2)
          )
        })
      )
    })
    
    output$mcPlot <- renderPlot({
      req(mcResult())
      res <- mcResult()
      
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # Performance comparison bar plot
      performance_data <- res$comparison_results
      barplot(performance_data$RMSE, names.arg = performance_data$Model,
              main = "RMSE Comparison", ylab = "RMSE", col = "lightblue", las = 2)
      
      # R-squared comparison
      barplot(performance_data$R_squared, names.arg = performance_data$Model,
              main = "R-squared Comparison", ylab = "R-squared", col = "lightgreen", las = 2)
      
      # Actual vs Predicted for best model
      best_model_name <- res$best_r2
      best_predictions <- res$predictions[[best_model_name]]
      actual <- res$models[[best_model_name]]$trainingData$.outcome
      
      plot(actual, best_predictions, main = paste("Actual vs Predicted -", best_model_name),
           xlab = "Actual Values", ylab = "Predicted Values", pch = 19, col = "blue")
      abline(a = 0, b = 1, col = "red", lty = 2)
      
      # Residuals for best model
      residuals <- actual - best_predictions
      plot(best_predictions, residuals, main = paste("Residuals -", best_model_name),
           xlab = "Predicted Values", ylab = "Residuals", pch = 19, col = "green")
      abline(h = 0, col = "red", lty = 2)
      
      par(mfrow = c(1, 1))
    })
    
    output$mcDiagnostics <- renderUI({
      req(mcResult())
      res <- mcResult()
      
      tagList(
        h4("Model Comparison Diagnostics"),
        renderTable({
          data.frame(
            Metric = c("Number of Models Compared", "Cross-Validation Method", "Best Overall Model", "Performance Range"),
            Value = c(
              length(res$models),
              paste(res$cv_folds, "-fold CV"),
              res$best_r2,
              paste("RMSE:", round(min(res$comparison_results$RMSE), 4), "to", round(max(res$comparison_results$RMSE), 4))
            )
          )
        }),
        h4("Model Rankings"),
        renderTable({
          # Rank models by each metric
          rmse_rank <- res$comparison_results[order(res$comparison_results$RMSE), ]
          r2_rank <- res$comparison_results[order(res$comparison_results$R_squared, decreasing = TRUE), ]
          
          data.frame(
            Rank = 1:length(res$models),
            By_RMSE = rmse_rank$Model,
            By_R_squared = r2_rank$Model
          )
        })
      )
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$mcDataSummary <- renderUI({
      req(mcData(), input$mcResponse, input$mcPredictors)
      data <- mcData()
      response <- input$mcResponse
      predictors <- input$mcPredictors
      
      tagList(
        h4("Dataset Summary"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "Response Variable", "Number of Predictors", "Complete Cases"),
            Value = c(
              nrow(data),
              ncol(data),
              response,
              length(predictors),
              sum(complete.cases(data[, c(response, predictors)]))
            )
          )
        }),
        h4("Response Variable Summary"),
        renderTable({
          if (!is.null(response) && response != "") {
            response_data <- data[[response]]
            data.frame(
              Metric = c("Mean", "Median", "SD", "Min", "Max", "Missing"),
              Value = c(
                round(mean(response_data, na.rm = TRUE), 4),
                round(median(response_data, na.rm = TRUE), 4),
                round(sd(response_data, na.rm = TRUE), 4),
                round(min(response_data, na.rm = TRUE), 4),
                round(max(response_data, na.rm = TRUE), 4),
                sum(is.na(response_data))
              )
            )
          } else {
            data.frame(Metric = "N/A", Value = "N/A")
          }
        })
      )
    })
    
    output$mcAssumptions <- renderUI({
      req(mcResult())
      res <- mcResult()
      
      tagList(
        h4("Model Comparison Assumptions Check"),
        renderTable({
          data.frame(
            Assumption = c("Adequate Sample Size", "Variable Selection", "Cross-Validation", "Model Diversity"),
            Status = c(
              ifelse(res$n_observations >= 30, "Pass", "Fail"),
              "Pass",
              "Pass",
              ifelse(length(res$models) >= 3, "Pass", "Fail")
            ),
            Description = c(
              "Sufficient observations for reliable model training",
              "Appropriate variables selected for prediction",
              "Cross-validation used to prevent overfitting",
              "Multiple diverse models for meaningful comparison"
            )
          )
        }),
        h4("Model Selection Guidelines"),
        renderTable({
          data.frame(
            Criterion = c("RMSE", "MAE", "R-squared", "Interpretability"),
            Use_When = c(
              "Equal penalty for all errors",
              "Linear penalty for errors",
              "Explained variance is important",
              "Model interpretability is crucial"
            ),
            Best_For = c(
              "General prediction accuracy",
              "Robust to outliers",
              "Model performance assessment",
              "Business applications"
            )
          )
        })
      )
    })
    
    output$mcDiagnosticPlots <- renderPlot({
      req(mcResult())
      res <- mcResult()
      
      par(mfrow = c(2, 2))
      
      # Performance comparison
      performance_data <- res$comparison_results
      barplot(performance_data$RMSE, names.arg = performance_data$Model,
              main = "RMSE Comparison", ylab = "RMSE", col = "lightblue", las = 2)
      
      # R-squared comparison
      barplot(performance_data$R_squared, names.arg = performance_data$Model,
              main = "R-squared Comparison", ylab = "R-squared", col = "lightgreen", las = 2)
      
      # Actual vs Predicted for best model
      best_model_name <- res$best_r2
      best_predictions <- res$predictions[[best_model_name]]
      actual <- res$models[[best_model_name]]$trainingData$.outcome
      
      plot(actual, best_predictions, main = paste("Actual vs Predicted -", best_model_name),
           xlab = "Actual Values", ylab = "Predicted Values", pch = 19, col = "blue")
      abline(a = 0, b = 1, col = "red", lty = 2)
      
      # Residuals for best model
      residuals <- actual - best_predictions
      plot(best_predictions, residuals, main = paste("Residuals -", best_model_name),
           xlab = "Predicted Values", ylab = "Residuals", pch = 19, col = "green")
      abline(h = 0, col = "red", lty = 2)
      
      par(mfrow = c(1, 1))
    })
    
    # Uploaded Data Tab Outputs
    output$mcDataTable <- renderDT({
      req(mcData())
      data <- mcData()
      
      DT::datatable(
        data,
        options = list(
          pageLength = 10,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
        ),
        extensions = 'Buttons',
        filter = 'top',
        rownames = FALSE
      )
    })
    
    output$mcDataInfo <- renderUI({
      req(mcData())
      data <- mcData()
      
      tagList(
        h4("Dataset Information"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "File Name"),
            Value = c(nrow(data), ncol(data), 
                     ifelse(!is.null(input$mcUserData), input$mcUserData$name, "Unknown"))
          )
        }),
        h4("Variable Information"),
        renderTable({
          data.frame(
            Variable = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Unique = sapply(data, function(x) length(unique(x)))
          )
        })
      )
    })
    
    # Legacy outputs for backward compatibility
    output$mcSummary <- renderTable({
      req(mcResult())
      res <- mcResult()
      
      data.frame(
        Metric = c("Number of Models", "Best Model (R²)", "Best Model (RMSE)", "CV Folds"),
        Value = c(
          length(res$models),
          res$best_r2,
          res$best_rmse,
          res$cv_folds
        )
      )
    })
  })
} 