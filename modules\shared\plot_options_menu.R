# Shared plot options menu UI

# (Copy all relevant code from R/plotOptionsMenu.R)

plotOptionsMenuUI <- function(id, plotType = NULL, title = "Plot", xlab = "", ylab = "", colour = "#7293AD",
                              dim = "auto", includeGridlines = TRUE, includeFlip = TRUE) {
  ns <- NS(id)
  
  flip <- addFlipCheckbox(includeFlip, ns)
  grid <- addGridlines(includeGridlines, ns)
  extraOptions <- tagList()
  
  if(!is.null(plotType)) {
    extraOptions <- switch(
      plotType, 
      "Boxplot" = BoxplotOptions(ns),
      "Scatterplot" = ScatterplotOptions(ns) 
    )
  }
  
  menu <- tagList(
    dropdown(
      tags$h3("Plot Options"),
      textInput(ns("Title"), strong("Main title and axes labels:"), value = title, placeholder = "main title"),
      textInput(ns("Xlab"), NULL, value = xlab, placeholder = "x-axis label"),
      textInput(ns("Ylab"), NULL, value = ylab, placeholder = "y-axis label"),
      colourpicker::colourInput(ns("Colour"), strong("Plot Colour"), value = colour),
      radioButtons(ns("Height"), strong("Plot Height"), choices = c("auto", "in px"), selected = dim, inline = TRUE),
      conditionalPanel(ns = ns, condition = "input.Height == 'in px'",
        numericInput(ns("HeightPx"), NULL, value = 400, min = 100, max = 1500, step = 1)),
      radioButtons(ns("Width"), strong("Plot Width"), choices = c("auto", "in px"), selected = dim, inline = TRUE),
      conditionalPanel(ns = ns, condition = "input.Width == 'in px'",
        numericInput(ns("WidthPx"), NULL, value = 750, min = 100, max = 1500, step = 1)),
      grid,      
      flip,
      extraOptions,
      style = "jelly", 
      icon = icon("gear"),
      status = "primary", 
      width = "300px",
      animate = animateOptions(
        enter = animations$fading_entrances$fadeInDown,
        exit = animations$fading_exits$fadeOutUp)
    )
  )
}

addFlipCheckbox <- function(includeFlip, ns) {
  flip <- tagList()
  if(includeFlip){
    flip <- tagList(
      p(strong("Orientation")),
      checkboxInput(ns("Flip"), "Plot Vertically", value = FALSE)
    )
  }
}

addGridlines <- function(includeGridlines, ns) {
  grid <- tagList()
  if(includeGridlines){
    grid <- tagList(
      checkboxGroupInput(ns("Gridlines"), strong("Gridlines"), choices = c("Major", "Minor"), selected = "Major")
    )
  }
}

# Add BoxplotOptions, ScatterplotOptions, and any other helpers as needed 