AdvancedSurvivalUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("asurvUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("asurvTime"), "Time Variable", choices = NULL),
        selectizeInput(ns("asurvEvent"), "Event Variable", choices = NULL),
        selectizeInput(ns("asurvCovariates"), "Covariates", choices = NULL, multiple = TRUE),
        selectInput(ns("asurvModelType"), "Model Type", choices = c("Parametric", "AFT", "Competing Risks")),
        br(),
        actionButton(ns("goASurv"), label = "Run Survival Model", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("asurvError")),
        tableOutput(ns("asurvSummary")),
        plotOutput(ns("asurvPlot"))
      )
    )
  )
} 