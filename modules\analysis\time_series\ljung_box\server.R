ljungBoxServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # Reactives
    lbUploadData <- eventReactive(input$lbUserData, {
      handle_file_upload(input$lbUserData)
    })
    
    lbResults <- reactive({
      data <- lbUploadData()
      if (is.null(data) || is.null(input$lbVariable) || input$lbVariable == "") {
        return(NULL)
      }
      
      values <- data[[input$lbVariable]]
      values <- values[!is.na(values)]
      
      if (length(values) < 3) return(NULL)
      
      list(values = values, n = length(values), lag = input$lbLag)
    })
    
    # Validation errors
    lbValidationErrors <- reactive({
      errors <- c()
      data <- lbUploadData()
      
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      if (is.null(input$lbVariable) || input$lbVariable == "") {
        errors <- c(errors, "Please select a variable.")
        return(errors)
      }
      
      values <- data[[input$lbVariable]]
      if (length(na.omit(values)) < 3) {
        errors <- c(errors, "At least 3 non-missing values are required.")
      }
      
      if (input$lbLag >= length(na.omit(values))) {
        errors <- c(errors, "Number of lags must be less than the number of observations.")
      }
      
      errors
    })
    
    # Outputs
    output$lbHT <- renderUI({
      results <- lbResults()
      if (is.null(results)) return(NULL)
      ljungBoxHT(lbResults, reactive({input$lbSigLvl}))
    })
    
    output$ljungBoxPlot <- renderPlot({
      results <- lbResults()
      if (is.null(results)) return(NULL)
      ljungBoxPlot(lbResults)
    })
    
    output$lbConclusionOutput <- renderUI({
      results <- lbResults()
      if (is.null(results)) return(NULL)
      lbConclusion(lbResults, reactive({input$lbSigLvl}))
    })
    
    output$renderLBData <- renderUI({
      req(lbUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("lbInitialUploadTable")), style = "width: 75%"),
        br(),
        br()
      )
    })
    
    output$lbInitialUploadTable <- DT::renderDT({
      req(lbUploadData())
      DT::datatable(lbUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(lbUploadData())))))
    })
    
    # Update selectizeInput choices after file upload
    observeEvent(lbUploadData(), {
      data <- lbUploadData()
      updateSelectizeInput(session, 'lbVariable', choices = character(0), selected = NULL, server = TRUE)
      output$ljungBoxResults <- renderUI({ NULL })
      
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'lbVariable', choices = names(data), server = TRUE)
        output$ljungBoxResults <- renderUI({
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('lbPreviewTable'))
          )
        })
        output$lbPreviewTable <- DT::renderDT({
          head(data, 20)
        })
      }
    })
    
    # Show main results or error screen when Calculate is pressed
    observeEvent(input$goInference, {
      output$ljungBoxResults <- renderUI({
        errors <- lbValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Ljung-Box Test", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("lbTabset"),
              selected = "Analysis",
              tabPanel(
                id    = ns("lb"),
                title = "Analysis",
                titlePanel("Ljung-Box Test for Autocorrelation"),
                br(),
                uiOutput(ns('lbHT')),
                br(),
                plotOutput(ns('ljungBoxPlot'), width = "50%", height = "400px"),
                br(),
                uiOutput(ns('lbConclusionOutput'))
              ),
              tabPanel(
                id    = ns("lbData"),
                title = "Uploaded Data",
                uiOutput(ns("renderLBData"))
              )
            )
          )
        }
      })
    })
  })
} 