{"R": {"Version": "4.5.1", "Repositories": [{"Name": "CRAN", "URL": "https://muug.ca/mirror/cran"}]}, "Packages": {"DEoptimR": {"Package": "DEoptimR", "Version": "1.1-3-1", "Source": "Repository", "Date": "2024-11-23", "Title": "Differential Evolution Optimization in Pure R", "Authors@R": "c( person(c(\"<PERSON>\", \"<PERSON><PERSON> <PERSON><PERSON>\"), \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-8685-9910\")) )", "URL": "svn://svn.r-forge.r-project.org/svnroot/robustbase/pkg/DEoptimR", "Description": "Differential Evolution (DE) stochastic heuristic algorithms for global optimization of problems with and without general constraints. The aim is to curate a collection of its variants that (1) do not sacrifice simplicity of design, (2) are essentially tuning-free, and (3) can be efficiently implemented directly in the R language. Currently, it provides implementations of the algorithms 'jDE' by Brest et al. (2006) <doi:10.1109/TEVC.2006.872133> for single-objective optimization and 'NCDE' by <PERSON><PERSON> et al. (2012) <doi:10.1109/TEVC.2011.2161873> for multimodal optimization (single-objective problems with multiple solutions).", "Imports": ["stats"], "Enhances": ["robustbase"], "License": "GPL (>= 2)", "Author": "<PERSON> [aut, cre], <PERSON> [ctb] (<https://orcid.org/0000-0002-8685-9910>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest", "Repository/R-Forge/Project": "robustbase", "Repository/R-Forge/Revision": "1008", "Repository/R-Forge/DateTimeStamp": "2024-11-23 19:13:45", "NeedsCompilation": "no", "Encoding": "UTF-8"}, "DT": {"Package": "DT", "Version": "0.33", "Source": "Repository", "Type": "Package", "Title": "A Wrapper of the JavaScript Library 'DataTables'", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"SpryMedia Limited\", role = c(\"ctb\", \"cph\"), comment = \"DataTables in htmlwidgets/lib\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"selectize.js in htmlwidgets/lib\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"noUiSlider in htmlwidgets/lib\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"jquery.highlight.js in htmlwidgets/lib\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = c(\"ctb\")), person(\"<PERSON>\", \"<PERSON>\", role = c(\"ctb\")), person(\"<PERSON>k<PERSON>\", \"<PERSON>tila\", role = c(\"ctb\")), person(\"<PERSON>s\", \"Quintero\", role = c(\"ctb\")), person(\"<PERSON><PERSON>phane\", \"<PERSON>\", role = c(\"ctb\")), person(given = \"Posit <PERSON>, P<PERSON>\", role = c(\"cph\", \"fnd\")) )", "Description": "Data objects in R can be rendered as HTML tables using the JavaScript library 'DataTables' (typically via R Markdown or Shiny). The 'DataTables' library has been included in this R package. The package name 'DT' is an abbreviation of 'DataTables'.", "URL": "https://github.com/rstudio/DT", "BugReports": "https://github.com/rstudio/DT/issues", "License": "GPL-3 | file LICENSE", "Imports": ["htmltools (>= 0.3.6)", "htmlwidgets (>= 1.3)", "httpuv", "jsonlite (>= 0.9.16)", "magrit<PERSON>", "crosstalk", "j<PERSON><PERSON><PERSON>", "promises"], "Suggests": ["knitr (>= 1.8)", "rmarkdown", "shiny (>= 1.6)", "bslib", "future", "testit", "tibble"], "VignetteBuilder": "knitr", "RoxygenNote": "7.3.1", "Encoding": "UTF-8", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut], <PERSON> [aut, cre], <PERSON><PERSON><PERSON> [aut], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], SpryMedia Limited [ctb, cph] (DataTables in htmlwidgets/lib), <PERSON> [ctb, cph] (selectize.js in htmlwidgets/lib), <PERSON> [ctb, cph] (noUiSlider in htmlwidgets/lib), <PERSON><PERSON> [ctb, cph] (jquery.highlight.js in htmlwidgets/lib), <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON><PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "Deriv": {"Package": "Deriv", "Version": "4.2.0", "Source": "Repository", "Type": "Package", "Title": "Symbolic Differentiation", "Date": "2025-06-20", "Authors@R": "c(person(given=\"<PERSON>\", family=\"<PERSON><PERSON>\", role=\"aut\"), person(given=\"<PERSON><PERSON><PERSON>\", family=\"Sokol\", role=c(\"aut\", \"cre\"), email=\"<EMAIL>\", comment = c(ORCID = \"0000-0002-5674-3327\")), person(given=\"<PERSON>\", family=\"Rappold\", role=\"ctb\", email=\"<EMAIL>\"))", "Description": "R-based solution for symbolic differentiation. It admits user-defined function as well as function substitution in arguments of functions to be differentiated. Some symbolic simplification is part of the work.", "License": "GPL (>= 3)", "Suggests": ["testthat (>= 0.11.0)"], "BugReports": "https://github.com/sgsokol/Deriv/issues", "RoxygenNote": "7.3.1", "Imports": ["methods"], "Encoding": "UTF-8", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON><PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-5674-3327>), <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "DescTools": {"Package": "DescTools", "Version": "0.99.60", "Source": "Repository", "Type": "Package", "Title": "Tools for Descriptive Statistics", "Date": "2025-03-28", "Authors@R": "c( person(given=\"<PERSON><PERSON>\", family=\"<PERSON>orell\",  email = \"<EMAIL>\",  role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-4311-1969\")), person(\"<PERSON>\"           , \"<PERSON><PERSON>\",                 role = c(\"ctb\")), person(\"<PERSON>\"       , \"<PERSON><PERSON><PERSON>\",              role = c(\"ctb\")), person(\"<PERSON><PERSON>\"        , \"<PERSON><PERSON><PERSON>\",            role = c(\"ctb\")), person(\"<PERSON>\"         , \"<PERSON>\",              role = c(\"ctb\")), person(\"<PERSON><PERSON><PERSON>\"      , \"Arach<PERSON><PERSON>\",          role = c(\"ctb\")), person(\"<PERSON><PERSON>\"         , \"<PERSON>rp<PERSON>\",               role = c(\"ctb\")), person(\"<PERSON>\"        , \"<PERSON><PERSON><PERSON>\",            role = c(\"ctb\")), person(\"<PERSON><PERSON><PERSON>\"         , \"<PERSON>\",              role = c(\"ctb\")), person(\"<PERSON>\"           , \"<PERSON><PERSON><PERSON>\",              role = c(\"ctb\")), person(\"<PERSON>\"       , \"<PERSON><PERSON><PERSON>\",            role = c(\"ctb\")), person(\"<PERSON><PERSON>\"     , \"<PERSON><PERSON><PERSON>\",              role = c(\"ctb\")), person(\"<PERSON><PERSON>\"      , \"<PERSON><PERSON><PERSON>\",            role = c(\"ctb\")), person(\"<PERSON>\"        , \"<PERSON><PERSON>\",             role = c(\"ctb\")), person(\"<PERSON><PERSON>\"        , \"Chhay\",               role = c(\"ctb\")), person(\"<PERSON>\"      , \"<PERSON>\",              role = c(\"ctb\")), person(\"<PERSON>\"         , \"Cummins\",             role = c(\"ctb\")), person(\"<PERSON>\"       , \"<PERSON>\",               role = c(\"ctb\")), person(\"<PERSON> C.\"     , \"<PERSON>n\",               role = c(\"ctb\")), person(\"<PERSON>e\"      , \"Dray\",                role = c(\"ctb\")), person(\"Charles\"       , \"Dupont\",              role = c(\"ctb\")), person(\"Dirk\"          , \"Eddelbuettel\",        role = c(\"ctb\")), person(\"Claus\"         , \"Ekstrom\",             role = c(\"ctb\")), person(\"Martin\"        , \"Elff\",                role = c(\"ctb\")), person(\"Jeff\"          , \"Enos\",                role = c(\"ctb\")), person(\"Richard W.\"    , \"Farebrother\",         role = c(\"ctb\")), person(\"John\"          , \"Fox\",                 role = c(\"ctb\")), person(\"Romain\"        , \"Francois\",            role = c(\"ctb\")), person(\"Michael\"       , \"Friendly\",            role = c(\"ctb\")), person(\"Tal\"           , \"Galili\",              role = c(\"ctb\")), person(\"Matthias\"      , \"Gamer\",               role = c(\"ctb\")), person(\"Joseph L.\"     , \"Gastwirth\",           role = c(\"ctb\")), person(\"Vilmantas\"     , \"Gegzna\",              role = c(\"ctb\")), person(\"Yulia R.\"      , \"Gel\",                 role = c(\"ctb\")), person(\"Sereina\"       , \"Graber\",              role = c(\"ctb\")), person(\"Juergen\"       , \"Gross\",               role = c(\"ctb\")), person(\"Gabor\"         , \"Grothendieck\",        role = c(\"ctb\")), person(\"Frank E.\"      , \"Harrell Jr\",          role = c(\"ctb\")), person(\"Richard\"       , \"Heiberger\",           role = c(\"ctb\")), person(\"Michael\"       , \"Hoehle\",              role = c(\"ctb\")), person(\"Christian W.\"  , \"Hoffmann\",            role = c(\"ctb\")), person(\"Soeren\"        , \"Hojsgaard\",           role = c(\"ctb\")), person(\"Torsten\"       , \"Hothorn\",             role = c(\"ctb\")), person(\"Markus\"        , \"Huerzeler\",           role = c(\"ctb\")), person(\"Wallace W.\"    , \"Hui\",                 role = c(\"ctb\")), person(\"Pete\"          , \"Hurd\",                role = c(\"ctb\")), person(\"Rob J.\"        , \"Hyndman\",             role = c(\"ctb\")), person(\"Christopher\"   , \"Jackson\",             role = c(\"ctb\")), person(\"Matthias\"      , \"Kohl\",                role = c(\"ctb\")), person(\"Mikko\"         , \"Korpela\",             role = c(\"ctb\")), person(\"Max\"           , \"Kuhn\",                role = c(\"ctb\")), person(\"Detlew\"        , \"Labes\",               role = c(\"ctb\")), person(\"Friederich\"    , \"Leisch\",              role = c(\"ctb\")), person(\"Jim\"           , \"Lemon\",               role = c(\"ctb\")), person(\"Dong\"          , \"Li\",                  role = c(\"ctb\")), person(\"Martin\"        , \"Maechler\",            role = c(\"ctb\")), person(\"Arni\"          , \"Magnusson\",           role = c(\"ctb\")), person(\"Ben\"           , \"Mainwaring\",          role = c(\"ctb\")), person(\"Daniel\"        , \"Malter\",              role = c(\"ctb\")), person(\"George\"        , \"Marsaglia\",           role = c(\"ctb\")), person(\"John\"          , \"Marsaglia\",           role = c(\"ctb\")), person(\"Alina\"         , \"Matei\",               role = c(\"ctb\")), person(\"David\"         , \"Meyer\",               role = c(\"ctb\")), person(\"Weiwen\"        , \"Miao\",                role = c(\"ctb\")), person(\"Giovanni\"      , \"Millo\",               role = c(\"ctb\")), person(\"Yongyi\"        , \"Min\",                 role = c(\"ctb\")), person(\"David\"         , \"Mitchell\",            role = c(\"ctb\")), person(\"Cyril Flurin\"  , \"Moser\",               role = c(\"ctb\")), person(\"Franziska\"     , \"Mueller\",             role = c(\"ctb\")), person(\"Markus\"        , \"Naepflin\",            role = c(\"ctb\")), person(\"Danielle\"      , \"Navarro\",             role = c(\"ctb\")), person(\"Henric\"        , \"Nilsson\",             role = c(\"ctb\")), person(\"Klaus\"         , \"Nordhausen\",          role = c(\"ctb\")), person(\"Derek\"         , \"Ogle\",                role = c(\"ctb\")), person(\"Hong\"          , \"Ooi\",                 role = c(\"ctb\")), person(\"Nick\"          , \"Parsons\",             role = c(\"ctb\")), person(\"Sandrine\"      , \"Pavoine\",             role = c(\"ctb\")), person(\"Tony\"          , \"Plate\",               role = c(\"ctb\")), person(\"Luke\"          , \"Prendergast\",         role = c(\"ctb\")), person(\"Roland\"        , \"Rapold\",              role = c(\"ctb\")), person(\"William\"       , \"Revelle\",             role = c(\"ctb\")), person(\"Tyler\"         , \"Rinker\",              role = c(\"ctb\")), person(\"Brian D.\"      , \"Ripley\",              role = c(\"ctb\")), person(\"Caroline\"      , \"Rodriguez\",           role = c(\"ctb\")), person(\"Nathan\"        , \"Russell\",             role = c(\"ctb\")), person(\"Nick\"          , \"Sabbe\",               role = c(\"ctb\")), person(\"Ralph\"         , \"Scherer\",             role = c(\"ctb\")), person(\"Venkatraman E.\", \"Seshan\",              role = c(\"ctb\")), person(\"Michael\"       , \"Smithson\",            role = c(\"ctb\")), person(\"Greg\"          , \"Snow\",                role = c(\"ctb\")), person(\"Karline\"       , \"Soetaert\",            role = c(\"ctb\")), person(\"Werner A.\"     , \"Stahel\",              role = c(\"ctb\")), person(\"Alec\"          , \"Stephenson\",          role = c(\"ctb\")), person(\"Mark\"          , \"Stevenson\",           role = c(\"ctb\")), person(\"Ralf\"          , \"Stubner\",             role = c(\"ctb\")), person(\"Matthias\"      , \"Templ\",               role = c(\"ctb\")), person(\"Duncan\"        , \"Temple Lang\",         role = c(\"ctb\")), person(\"Terry\"         , \"Therneau\",            role = c(\"ctb\")), person(\"Yves\"          , \"Tille\",               role = c(\"ctb\")), person(\"Luis\"          , \"Torgo\",               role = c(\"ctb\")), person(\"Adrian\"        , \"Trapletti\",           role = c(\"ctb\")), person(\"Joshua\"        , \"Ulrich\",              role = c(\"ctb\")), person(\"Kevin\"         , \"Ushey\",               role = c(\"ctb\")), person(\"Jeremy\"        , \"VanDerWal\",           role = c(\"ctb\")), person(\"Bill\"          , \"Venables\",            role = c(\"ctb\")), person(\"John\"          , \"Verzani\",             role = c(\"ctb\")), person(\"Pablo J.\"      , \"Villacorta Iglesias\", role = c(\"ctb\")), person(\"Gregory R.\"    , \"Warnes\",              role = c(\"ctb\")), person(\"Stefan\"        , \"Wellek\",              role = c(\"ctb\")), person(\"Hadley\"        , \"Wickham\",             role = c(\"ctb\")), person(\"Rand R.\"       , \"Wilcox\",              role = c(\"ctb\")), person(\"Peter\"         , \"Wolf\",                role = c(\"ctb\")), person(\"Daniel\"        , \"Wollschlaeger\",       role = c(\"ctb\")), person(\"Joseph\"        , \"Wood\",                role = c(\"ctb\")), person(\"Ying\"          , \"Wu\",                  role = c(\"ctb\")), person(\"Thomas\"        , \"Yee\",                 role = c(\"ctb\")), person(\"Achim\"         , \"Zeileis\",             role = c(\"ctb\")) )", "Description": "A collection of miscellaneous basic statistic functions and convenience wrappers for efficiently describing data. The author's intention was to create a toolbox, which facilitates the (notoriously time consuming) first descriptive tasks in data analysis, consisting of calculating descriptive statistics, drawing graphical summaries and reporting the results. The package contains furthermore functions to produce documents using MS Word (or PowerPoint) and functions to import data from Excel. Many of the included functions can be found scattered in other packages and other sources written partly by Titans of R. The reason for collecting them here, was primarily to have them consolidated in ONE instead of dozens of packages (which themselves might depend on other packages which are not needed at all), and to provide a common and consistent interface as far as function and arguments naming, NA handling, recycling rules etc. are concerned. Google style guides were used as naming rules (in absence of convincing alternatives). The 'BigCamelCase' style was consequently applied to functions borrowed from contributed R packages as well.", "Suggests": ["RDCOMClient", "tcltk", "VGAM", "R.rsp", "testthat (>= 3.0.0)"], "Depends": ["base", "stats", "R (>= 4.2.0)"], "Imports": ["graphics", "grDevices", "methods", "MASS", "utils", "boot", "mvtnorm", "expm", "Rcpp (>= 0.12.10)", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Exact", "gld", "data.table", "readxl", "readr", "haven", "httr", "withr", "cli", "fs"], "LinkingTo": ["Rcpp"], "License": "GPL (>= 2)", "LazyLoad": "yes", "LazyData": "yes", "Additional_repositories": "http://www.omegahat.net/R", "URL": "https://andrisignorell.github.io/DescTools/, https://github.com/AndriSignorell/DescTools/", "BugReports": "https://github.com/AndriSignorell/DescTools/issues", "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "yes", "SystemRequirements": "C++17", "VignetteBuilder": "R.rsp", "Config/testthat/edition": "3", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-4311-1969>), <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [c<PERSON><PERSON>], <PERSON> [c<PERSON>b], <PERSON><PERSON><PERSON><PERSON> [c<PERSON>], <PERSON> <PERSON>. <PERSON><PERSON> [c<PERSON><PERSON>], <PERSON> <PERSON><PERSON> [c<PERSON><PERSON>], <PERSON><PERSON> [c<PERSON><PERSON>], <PERSON> <PERSON>. <PERSON> [c<PERSON>b], <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> [c<PERSON><PERSON>], <PERSON> [c<PERSON><PERSON>], <PERSON> <PERSON><PERSON><PERSON> [c<PERSON><PERSON>], <PERSON> <PERSON>. <PERSON> [c<PERSON><PERSON>], <PERSON> <PERSON><PERSON> [c<PERSON><PERSON>], <PERSON>. <PERSON>yndman [ctb], Christopher Jackson [ctb], Matthias Kohl [ctb], Mikko Korpela [ctb], Max Kuhn [ctb], Detlew Labes [ctb], Friederich Leisch [ctb], Jim Lemon [ctb], Dong Li [ctb], Martin Maechler [ctb], Arni Magnusson [ctb], Ben Mainwaring [ctb], Daniel Malter [ctb], George Marsaglia [ctb], John Marsaglia [ctb], Alina Matei [ctb], David Meyer [ctb], Weiwen Miao [ctb], Giovanni Millo [ctb], Yongyi Min [ctb], David Mitchell [ctb], Cyril Flurin Moser [ctb], Franziska Mueller [ctb], Markus Naepflin [ctb], Danielle Navarro [ctb], Henric Nilsson [ctb], Klaus Nordhausen [ctb], Derek Ogle [ctb], Hong Ooi [ctb], Nick Parsons [ctb], Sandrine Pavoine [ctb], Tony Plate [ctb], Luke Prendergast [ctb], Roland Rapold [ctb], William Revelle [ctb], Tyler Rinker [ctb], Brian D. Ripley [ctb], Caroline Rodriguez [ctb], Nathan Russell [ctb], Nick Sabbe [ctb], Ralph Scherer [ctb], Venkatraman E. Seshan [ctb], Michael Smithson [ctb], Greg Snow [ctb], Karline Soetaert [ctb], Werner A. Stahel [ctb], Alec Stephenson [ctb], Mark Stevenson [ctb], Ralf Stubner [ctb], Matthias Templ [ctb], Duncan Temple Lang [ctb], Terry Therneau [ctb], Yves Tille [ctb], Luis Torgo [ctb], Adrian Trapletti [ctb], Joshua Ulrich [ctb], Kevin Ushey [ctb], Jeremy VanDerWal [ctb], Bill Venables [ctb], John Verzani [ctb], Pablo J. Villacorta Iglesias [ctb], Gregory R. Warnes [ctb], Stefan Wellek [ctb], Hadley Wickham [ctb], Rand R. Wilcox [ctb], Peter Wolf [ctb], Daniel Wollschlaeger [ctb], Joseph Wood [ctb], Ying Wu [ctb], Thomas Yee [ctb], Achim Zeileis [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "Exact": {"Package": "Exact", "Version": "3.3", "Source": "Repository", "Type": "Package", "Title": "Unconditional Exact Test", "Authors@R": "person(\"<PERSON>\", \"<PERSON>\", email=\"<EMAIL>\", role=c(\"aut\", \"cre\"))", "Author": "<PERSON> [aut, cre]", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "Performs unconditional exact tests and power calculations for 2x2 contingency tables. For comparing two independent proportions, performs <PERSON>'s test (1945) <doi:10.1038/156177a0> using the original CSM test (<PERSON>, 1947 <doi:10.1093/biomet/34.1-2.123>), using <PERSON>'s p-value referred to as <PERSON><PERSON><PERSON><PERSON>'s test (1970) <doi:10.1111/j.1467-9574.1970.tb00104.x>, or using a Z-statistic (<PERSON><PERSON> and <PERSON>, 1985, <doi:10.2307/2981892>). For comparing two binary proportions, performs unconditional exact test using <PERSON><PERSON><PERSON><PERSON><PERSON>'s Z-statistic (<PERSON> and <PERSON>, 2003, <doi:10.1191/0962280203sm312ra>), using <PERSON><PERSON><PERSON><PERSON><PERSON>'s conditional p-value, using <PERSON><PERSON><PERSON><PERSON><PERSON>'s Z-statistic with continuity correction, or using CSM test.  Calculates confidence intervals for the difference in proportion. This package interacts with pre-computed data available through the ExactData R package, which is available in a 'drat' repository. Install the ExactData R package from GitHub at <https://pcalhoun1.github.io/drat/>. The ExactData R package is approximately 85 MB.", "License": "GPL-2", "Depends": ["R (>= 3.5.0)"], "Imports": ["graphics", "stats", "utils", "rootSolve"], "Suggests": ["ExactData"], "Additional_repositories": "https://pcalhoun1.github.io/drat", "NeedsCompilation": "no", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "Formula": {"Package": "Formula", "Version": "1.2-5", "Source": "Repository", "Date": "2023-02-23", "Title": "Extended Model Formulas", "Description": "Infrastructure for extended formulas with multiple parts on the right-hand side and/or multiple responses on the left-hand side (see <doi:10.18637/jss.v034.i01>).", "Authors@R": "c(person(given = \"Achim\", family = \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0918-3766\")), person(given = \"<PERSON>\", family = \"<PERSON>roissan<PERSON>\", role = \"aut\", email = \"<EMAIL>\"))", "Depends": ["R (>= 2.0.0)", "stats"], "License": "GPL-2 | GPL-3", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0918-3766>), <PERSON> [aut]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "GGally": {"Package": "GGally", "Version": "2.2.1", "Source": "Repository", "License": "GPL (>= 2.0)", "Title": "Extension to 'ggplot2'", "Type": "Package", "LazyLoad": "yes", "LazyData": "true", "URL": "https://ggobi.github.io/ggally/, https://github.com/ggobi/ggally", "BugReports": "https://github.com/ggobi/ggally/issues", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role = c(\"aut\", \"ths\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON><PERSON>\", \"Mar<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"Ott\", \"Too<PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ths\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>ickham\", role = \"ths\", email = \"<EMAIL>\") )", "Description": "The R package 'ggplot2' is a plotting system based on the grammar of graphics. 'GGally' extends 'ggplot2' by adding several functions to reduce the complexity of combining geometric objects with transformed data. Some of these functions include a pairwise plot matrix, a two group pairwise plot matrix, a parallel coordinates plot, a survival plot, and several functions to plot networks.", "Depends": ["R (>= 3.1)", "ggplot2 (>= 3.4.4)"], "Imports": ["dplyr (>= 1.0.0)", "tidyr (>= 1.3.0)", "grDevices", "grid", "ggstats", "gtable (>= 0.2.0)", "lifecycle", "plyr (>= 1.8.3)", "progress", "RColorBrewer", "rlang", "scales (>= 1.1.0)", "utils", "magrit<PERSON>"], "Suggests": ["broom (>= 0.7.0)", "broom.helpers (>= 1.3.0)", "chemometrics", "geosphere (>= 1.5-1)", "ggforce", "Hmisc", "igraph (>= 1.0.1)", "intergraph (>= 2.0-2)", "labelled", "maps (>= 3.1.0)", "map<PERSON><PERSON>j", "nnet", "network (>= 1.17.1)", "scagnostics", "sna (>= 2.3-2)", "survival", "rmarkdown", "roxygen2", "testthat", "crosstalk", "knitr", "spelling", "emmeans", "vdiffr"], "RoxygenNote": "7.3.1", "SystemRequirements": "openssl", "Encoding": "UTF-8", "Language": "en-US", "RdMacros": "lifecycle", "Config/testthat/edition": "3", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre], <PERSON> [aut, ths], <PERSON> [aut], <PERSON><PERSON> [aut], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON> [ctb], <PERSON> [aut], <PERSON><PERSON> [ths], <PERSON> [ths]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "MASS": {"Package": "MASS", "Version": "7.3-65", "Source": "Repository", "Priority": "recommended", "Date": "2025-02-19", "Revision": "$Rev: 3681 $", "Depends": ["R (>= 4.4.0)", "grDevices", "graphics", "stats", "utils"], "Imports": ["methods"], "Suggests": ["lattice", "nlme", "nnet", "survival"], "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\", \"cph\"), email = \"<PERSON>.<PERSON>@R-project.org\"), person(\"<PERSON>\", \"Venables\", role = c(\"aut\", \"cph\")), person(c(\"<PERSON>\", \"<PERSON><PERSON>\"), \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"trl\", comment = \"partial port ca 1998\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"trl\", comment = \"partial port ca 1998\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", comment = \"support functions for polr\"))", "Description": "Functions and datasets to support <PERSON><PERSON><PERSON> and <PERSON>ipley, \"Modern Applied Statistics with S\" (4th edition, 2002).", "Title": "Support Functions and Datasets for Venables and <PERSON><PERSON><PERSON>'s MASS", "LazyData": "yes", "ByteCompile": "yes", "License": "GPL-2 | GPL-3", "URL": "http://www.stats.ox.ac.uk/pub/MASS4/", "Contact": "<<EMAIL>>", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre, cph], <PERSON> [aut, cph], <PERSON> [ctb], <PERSON> [trl] (partial port ca 1998), <PERSON><PERSON> [trl] (partial port ca 1998), <PERSON> [ctb] (support functions for polr)", "Maintainer": "<PERSON> <<PERSON><PERSON>@R-project.org>", "Repository": "CRAN"}, "Matrix": {"Package": "Matrix", "Version": "1.7-3", "Source": "Repository", "VersionNote": "do also bump src/version.h, inst/include/Matrix/version.h", "Date": "2025-03-05", "Priority": "recommended", "Title": "Sparse and Dense Matrix Classes and Methods", "Description": "A rich hierarchy of sparse and dense matrix classes, including general, symmetric, triangular, and diagonal matrices with numeric, logical, or pattern entries.  Efficient methods for operating on such matrices, often wrapping the 'BLAS', 'LAPACK', and 'SuiteSparse' libraries.", "License": "GPL (>= 2) | file LICENCE", "URL": "https://Matrix.R-forge.R-project.org", "BugReports": "https://R-forge.R-project.org/tracker/?atid=294&group_id=61", "Contact": "<EMAIL>", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0001-8316-9503\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"mm<PERSON><PERSON><PERSON>+<PERSON>@gmail.com\", comment = c(ORCID = \"0000-0002-8685-9910\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-3542-2938\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0001-7614-6899\", \"SuiteSparse libraries\", \"collaborators listed in dir(system.file(\\\"doc\\\", \\\"SuiteSparse\\\", package=\\\"Matrix\\\"), pattern=\\\"License\\\", full.names=TRUE, recursive=TRUE)\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0003-2753-1437\", \"METIS library\", \"Copyright: Regents of the University of Minnesota\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-4345-4200\", \"GNU Octave's condest() and onenormest()\", \"Copyright: Regents of the University of California\")), person(\"Jens\", \"Oehlschlägel\", role = \"ctb\", comment = \"initial nearPD()\"), person(\"R Core Team\", role = \"ctb\", comment = c(ROR = \"02zz1nj61\", \"base R's matrix implementation\")))", "Depends": ["R (>= 4.4)", "methods"], "Imports": ["grDevices", "graphics", "grid", "lattice", "stats", "utils"], "Suggests": ["MASS", "datasets", "sfsmisc", "tools"], "Enhances": ["SparseM", "graph"], "LazyData": "no", "LazyDataNote": "not possible, since we use data/*.R and our S4 classes", "BuildResaveData": "no", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (<https://orcid.org/0000-0001-8316-9503>), <PERSON> [aut, cre] (<https://orcid.org/0000-0002-8685-9910>), <PERSON><PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-3542-2938>), <PERSON> [ctb] (<https://orcid.org/0000-0001-7614-6899>, SuiteSparse libraries, collaborators listed in dir(system.file(\"doc\", \"SuiteSparse\", package=\"Matrix\"), pattern=\"License\", full.names=TRUE, recursive=TRUE)), <PERSON> [ctb] (<https://orcid.org/0000-0003-2753-1437>, METIS library, Copyright: Regents of the University of Minnesota), <PERSON> [ctb] (<https://orcid.org/0000-0002-4345-4200>, GNU Octave's condest() and onenormest(), Copyright: Regents of the University of California), <PERSON><PERSON> [ctb] (initial nearPD()), R Core Team [ctb] (02zz1nj61, base R's matrix implementation)", "Maintainer": "<PERSON> <mmae<PERSON>ler+<PERSON>@gmail.com>", "Repository": "CRAN"}, "MatrixModels": {"Package": "MatrixModels", "Version": "0.5-4", "Source": "Repository", "VersionNote": "Released 0.5-3 on 2023-11-06", "Date": "2025-03-25", "Title": "Modelling with <PERSON><PERSON> and <PERSON><PERSON>s", "Contact": "<EMAIL>", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-8316-9503\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"mmae<PERSON><PERSON>+<PERSON>@gmail.com\", comment = c(ORCID = \"0000-0002-8685-9910\")))", "Description": "Generalized Linear Modelling with sparse and dense 'Matrix' matrices, using modular prediction and response module classes.", "Depends": ["R (>= 3.6.0)"], "Imports": ["stats", "methods", "Matrix (>= 1.6-0)", "Matrix(< 1.8-0)"], "ImportsNote": "_not_yet_stats4", "Encoding": "UTF-8", "LazyLoad": "yes", "License": "GPL (>= 2)", "URL": "https://Matrix.R-forge.R-project.org/, https://r-forge.r-project.org/R/?group_id=61", "BugReports": "https://R-forge.R-project.org/tracker/?func=add&atid=294&group_id=61", "NeedsCompilation": "no", "Author": "<PERSON> [aut] (<https://orcid.org/0000-0001-8316-9503>), <PERSON> [aut, cre] (<https://orcid.org/0000-0002-8685-9910>)", "Maintainer": "<PERSON> <mmae<PERSON>ler+<PERSON>@gmail.com>", "Repository": "CRAN"}, "PKI": {"Package": "PKI", "Version": "0.1-14", "Source": "Repository", "Title": "Public Key Infrastucture for R Based on the X.509 Standard", "Author": "<PERSON> <<PERSON>.<PERSON>@r-project.org>", "Maintainer": "<PERSON> <<PERSON>.<PERSON>@r-project.org>", "Depends": ["R (>= 2.9.0)", "base64enc"], "Enhances": ["gmp"], "Description": "Public Key Infrastucture functions such as verifying certificates, RSA encription and signing which can be used to build PKI infrastructure and perform cryptographic tasks.", "License": "GPL-2 | GPL-3 | file LICENSE", "URL": "http://www.rforge.net/PKI", "SystemRequirements": "OpenSSL library and headers (openssl-dev or similar)", "NeedsCompilation": "yes", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "R.methodsS3": {"Package": "<PERSON>.<PERSON>S3", "Version": "1.8.2", "Source": "Repository", "Depends": ["R (>= 2.13.0)"], "Imports": ["utils"], "Suggests": ["codetools"], "Title": "S3 Methods Simplified", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\", \"cph\"), email = \"<EMAIL>\"))", "Author": "<PERSON> [aut, cre, cph]", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "Methods that simplify the setup of S3 generic functions and S3 methods.  Major effort has been made in making definition of methods as simple as possible with a minimum of maintenance for package developers.  For example, generic functions are created automatically, if missing, and naming conflict are automatically solved, if possible.  The method setMethodS3() is a good start for those who in the future may want to migrate to S4.  This is a cross-platform package implemented in pure R that generates standard S3 methods.", "License": "LGPL (>= 2.1)", "LazyLoad": "TRUE", "URL": "https://github.com/HenrikBengtsson/R.methodsS3", "BugReports": "https://github.com/HenrikBengtsson/R.methodsS3/issues", "NeedsCompilation": "no", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "R.oo": {"Package": "<PERSON>.oo", "Version": "1.27.1", "Source": "Repository", "Depends": ["R (>= 2.13.0)", "<PERSON>.<PERSON>S3 (>= 1.8.2)"], "Imports": ["methods", "utils"], "Suggests": ["tools"], "Title": "R Object-Oriented Programming with or without References", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\", \"cph\"), email = \"<EMAIL>\"))", "Author": "<PERSON> [aut, cre, cph]", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "Methods and classes for object-oriented programming in R with or without references.  Large effort has been made on making definition of methods as simple as possible with a minimum of maintenance for package developers.  The package has been developed since 2001 and is now considered very stable.  This is a cross-platform package implemented in pure R that defines standard S3 classes without any tricks.", "License": "LGPL (>= 2.1)", "LazyLoad": "TRUE", "URL": "https://henrikbengtsson.github.io/R.oo/, https://github.com/HenrikB<PERSON>tsson/R.oo", "BugReports": "https://github.com/HenrikBengtsson/R.oo/issues", "NeedsCompilation": "no", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "R.utils": {"Package": "<PERSON><PERSON>utils", "Version": "2.13.0", "Source": "Repository", "Depends": ["R (>= 2.14.0)", "<PERSON>.oo"], "Imports": ["methods", "utils", "tools", "<PERSON>.<PERSON>S3"], "Suggests": ["datasets", "digest (>= 0.6.10)"], "Title": "Various Programming Utilities", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\", \"cph\"), email = \"<EMAIL>\"))", "Author": "<PERSON> [aut, cre, cph]", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "Utility functions useful when programming and developing R packages.", "License": "LGPL (>= 2.1)", "LazyLoad": "TRUE", "URL": "https://henrikbengtsson.github.io/R.utils/, https://github.com/Henrik<PERSON>son/R.utils", "BugReports": "https://github.com/HenrikBengtsson/R.utils/issues", "NeedsCompilation": "no", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "R6": {"Package": "R6", "Version": "2.6.1", "Source": "Repository", "Title": "Encapsulated Classes with Reference Semantics", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Creates classes with reference semantics, similar to R's built-in reference classes. Compared to reference classes, R6 classes are simpler and lighter-weight, and they are not built on S4 classes so they do not require the methods package. These classes allow public and private members, and they support inheritance, even when the classes are defined in different packages.", "License": "MIT + file LICENSE", "URL": "https://r6.r-lib.org, https://github.com/r-lib/R6", "BugReports": "https://github.com/r-lib/R6/issues", "Depends": ["R (>= 3.6)"], "Suggests": ["lobstr", "testthat (>= 3.0.0)"], "Config/Needs/website": "tidyverse/tidytemplate, ggplot2, microbenchmark, scales", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "RColorBrewer": {"Package": "RColorBrewer", "Version": "1.1-3", "Source": "Repository", "Date": "2022-04-03", "Title": "ColorBrewer Palettes", "Authors@R": "c(person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"))", "Author": "<PERSON> [aut, cre]", "Maintainer": "<PERSON> <<EMAIL>>", "Depends": ["R (>= 2.0.0)"], "Description": "Provides color schemes for maps (and other graphics) designed by <PERSON> as described at http://colorbrewer2.org.", "License": "Apache License 2.0", "NeedsCompilation": "no", "Repository": "CRAN"}, "Rcpp": {"Package": "Rcpp", "Version": "1.0.14", "Source": "Repository", "Title": "Seamless R and C++ Integration", "Date": "2025-01-11", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-6419-907X\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2444-4226\")), person(\"<PERSON><PERSON>\", \"<PERSON>aire\", role = \"aut\", comment = c(ORCID = \"0000-0003-0174-9868\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0003-2880-7407\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0001-6786-5453\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0001-6403-5550\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0001-8316-9503\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"))", "Description": "The 'Rcpp' package provides R functions as well as C++ classes which offer a seamless integration of R and C++. Many R data types and objects can be mapped back and forth to C++ equivalents which facilitates both writing of new code as well as easier integration of third-party libraries. Documentation about 'Rcpp' is provided by several vignettes included in this package, via the 'Rcpp Gallery' site at <https://gallery.rcpp.org>, the paper by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (2011, <doi:10.18637/jss.v040.i08>), the book by <PERSON><PERSON><PERSON><PERSON><PERSON> (2013, <doi:10.1007/978-1-4614-6868-4>) and the paper by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2018, <doi:10.1080/00031305.2017.1375990>); see 'citation(\"Rcpp\")' for details.", "Imports": ["methods", "utils"], "Suggests": ["tinytest", "inline", "rbenchmark", "pkgKitten (>= 0.1.2)"], "URL": "https://www.rcpp.org, https://dirk.eddelbuettel.com/code/rcpp.html, https://github.com/RcppCore/Rcpp", "License": "GPL (>= 2)", "BugReports": "https://github.com/RcppCore/Rcpp/issues", "MailingList": "<EMAIL>", "RoxygenNote": "6.1.1", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0001-6419-907X>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-2444-4226>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0003-0174-9868>), <PERSON> [aut] (<https://orcid.org/0000-0003-2880-7407>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0001-6786-5453>), <PERSON> [aut], <PERSON><PERSON><PERSON> [aut] (<https://orcid.org/0000-0001-6403-5550>), <PERSON> [aut] (<https://orcid.org/0000-0001-8316-9503>), <PERSON> [aut]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "RcppEigen": {"Package": "RcppEigen", "Version": "0.3.4.0.2", "Source": "Repository", "Type": "Package", "Title": "'Rcpp' Integration for the 'Eigen' Templated Linear Algebra Library", "Date": "2024-08-23", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0001-8316-9503\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-6419-907X\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2444-4226\")), person(\"Yix<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0003-0109-6692\")), person(\"Authors of\", \"Eigen\", role = \"cph\", comment = \"Authorship and copyright in included Eigen library as detailed in inst/COPYRIGHTS\"))", "Copyright": "See the file COPYRIGHTS for various Eigen copyright details", "Description": "R and 'Eigen' integration using 'Rcpp'. 'Eigen' is a C++ template library for linear algebra: matrices, vectors, numerical solvers and related algorithms.  It supports dense and sparse matrices on integer, floating point and complex numbers, decompositions of such matrices, and solutions of linear systems. Its performance on many algorithms is comparable with some of the best implementations based on 'Lapack' and level-3 'BLAS'. The 'RcppEigen' package includes the header files from the 'Eigen' C++ template library. Thus users do not need to install 'Eigen' itself in order to use 'RcppEigen'. Since version 3.1.1, 'Eigen' is licensed under the Mozilla Public License (version 2); earlier version were licensed under the GNU LGPL version 3 or later. 'RcppEigen' (the 'Rcpp' bindings/bridge to 'Eigen') is licensed under the GNU GPL version 2 or later, as is the rest of 'Rcpp'.", "License": "GPL (>= 2) | file LICENSE", "LazyLoad": "yes", "Depends": ["R (>= 3.6.0)"], "LinkingTo": ["Rcpp"], "Imports": ["Rcpp (>= 0.11.0)", "stats", "utils"], "Suggests": ["Matrix", "inline", "tinytest", "pkgKitten", "microbenchmark"], "URL": "https://github.com/RcppCore/RcppEigen, https://dirk.eddelbuettel.com/code/rcpp.eigen.html", "BugReports": "https://github.com/RcppCore/RcppEigen/issues", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (<https://orcid.org/0000-0001-8316-9503>), <PERSON> [aut, cre] (<https://orcid.org/0000-0001-6419-907X>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-2444-4226>), <PERSON><PERSON><PERSON> [aut] (<https://orcid.org/0000-0003-0109-6692>), Authors of Eigen [cph] (Authorship and copyright in included Eigen library as detailed in inst/COPYRIGHTS)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "RcppTOML": {"Package": "RcppTOML", "Version": "0.2.3", "Source": "Repository", "Type": "Package", "Title": "'<PERSON><PERSON><PERSON>' Bindings to <PERSON><PERSON><PERSON> for \"Tom's Obvious Mark<PERSON>\"", "Date": "2025-03-08", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-6419-907X\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = \"Author of 'toml++' header library\"))", "Description": "The configuration format defined by 'TOML' (which expands to \"Tom's Obvious Markup Language\") specifies an excellent format (described at <https://toml.io/en/>) suitable for both human editing as well as the common uses of a machine-readable format. This package uses 'Rcpp' to connect to the 'toml++' parser written by <PERSON> to R.", "SystemRequirements": "A C++17 compiler", "BugReports": "https://github.com/eddelbuettel/rcpptoml/issues", "URL": "http://dirk.eddelbuettel.com/code/rcpp.toml.html", "Imports": ["Rcpp (>= 1.0.8)"], "Depends": ["R (>= 3.3.0)"], "LinkingTo": ["Rcpp"], "Suggests": ["tinytest"], "License": "GPL (>= 2)", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0001-6419-907X>), <PERSON> [aut] (Author of 'toml++' header library)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "Rdpack": {"Package": "Rdpack", "Version": "2.6.4", "Source": "Repository", "Type": "Package", "Title": "Update and Manipulate Rd Documentation Objects", "Authors@R": "c( person(given = c(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\"), family = \"<PERSON><PERSON><PERSON><PERSON>\",  role = c(\"aut\", \"cre\"),  email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-2839-346X\")), person(given = \"<PERSON>\",  family = \"<PERSON>\",  role = \"ctb\", email = \"<EMAIL>\") )", "Description": "Functions for manipulation of R documentation objects, including functions reprompt() and ereprompt() for updating 'Rd' documentation for functions, methods and classes; 'Rd' macros for citations and import of references from 'bibtex' files for use in 'Rd' files and 'roxygen2' comments; 'Rd' macros for evaluating and inserting snippets of 'R' code and the results of its evaluation or creating graphics on the fly; and many functions for manipulation of references and Rd files.", "URL": "https://geobosh.github.io/Rdpack/ (doc), https://github.com/GeoBosh/Rdpack (devel)", "BugReports": "https://github.com/GeoBosh/Rdpack/issues", "Depends": ["R (>= 2.15.0)", "methods"], "Imports": ["tools", "utils", "rbibutils (>= 1.3)"], "Suggests": ["grDevices", "testthat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rprojroot", "gbRd"], "License": "GPL (>= 2)", "LazyLoad": "yes", "RoxygenNote": "7.1.1", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-2839-346X>), <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "ResourceSelection": {"Package": "ResourceSelection", "Version": "0.3-6", "Source": "Repository", "Type": "Package", "Title": "Resource Selection (Probability) Functions for Use-Availability Data", "Date": "2023-06-27", "Authors@R": "c( person(given = c(\"<PERSON><PERSON><PERSON>\", \"<PERSON>.\"), family = \"Le<PERSON>\", role = c(\"aut\")), person(given = c(\"<PERSON>\", \"<PERSON><PERSON>\"), family = \"Keim\", role = c(\"aut\")), person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-7337-1740\"), role = c(\"aut\", \"cre\")))", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "Resource Selection (Probability) Functions for use-availability wildlife data based on weighted distributions as described in <PERSON><PERSON> and <PERSON><PERSON> (2006) <doi:10.1890/0012-9658(2006)87%5B3021:WDAEOR%5D2.0.CO;2>, <PERSON><PERSON> (2009) <doi:10.2193/2007-535>, and <PERSON><PERSON><PERSON> & <PERSON> (2016) <doi:10.1111/2041-210X.12432>.", "Depends": ["R (>= 2.13.0)"], "Imports": ["MASS", "pbapply", "Matrix"], "URL": "https://github.com/psolymos/ResourceSelection", "BugReports": "https://github.com/psolymos/ResourceSelection/issues", "License": "GPL-2", "LazyLoad": "yes", "LazyData": "true", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> <PERSON> [aut], <PERSON> [aut], <PERSON> [aut, cre] (<https://orcid.org/0000-0001-7337-1740>)", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "SparseM": {"Package": "SparseM", "Version": "1.84-2", "Source": "Repository", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\",  role = c(\"cre\",\"aut\"), email =  \"<EMAIL>\"), person(c(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\"), \"<PERSON>\",  role = c(\"ctb\"), comment = \"Contributions to Sparse QR code\", email =  \"<EMAIL>\") , person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\",  role = c(\"ctb\"), comment = \"author of sparskit2\") , person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\"), comment = \"author of chol2csr\") , person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = c(\"chol() tweaks; S4\", ORCID = \"0000-0002-8685-9910\")) )", "Maintainer": "<PERSON> <<EMAIL>>", "Depends": ["R (>= 2.15)", "methods"], "Imports": ["graphics", "stats", "utils"], "VignetteBuilder": "knitr", "Suggests": ["knitr"], "Description": "Some basic linear algebra functionality for sparse matrices is provided:  including Cholesky decomposition and backsolving as well as standard R subsetting and <PERSON><PERSON><PERSON> products.", "License": "GPL (>= 2)", "Title": "Sparse Linear Algebra", "URL": "http://www.econ.uiuc.edu/~roger/research/sparse/sparse.html", "NeedsCompilation": "yes", "Author": "<PERSON> [cre, aut], <PERSON><PERSON> [ctb] (Contributions to Sparse QR code), <PERSON><PERSON> [ctb] (author of sparskit2), <PERSON> [ctb] (author of chol2csr), <PERSON> [ctb] (chol() tweaks; S4, <https://orcid.org/0000-0002-8685-9910>)", "Repository": "CRAN"}, "abind": {"Package": "abind", "Version": "1.4-8", "Source": "Repository", "Date": "2024-09-08", "Title": "Combine Multidimensional Arrays", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\")))", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "Combine multidimensional arrays into a single array. This is a generalization of 'cbind' and 'rbind'.  Works with vectors, matrices, and higher-dimensional arrays (aka tensors). Also provides functions 'adrop', 'asub', and 'afill' for manipulating, extracting and replacing data in arrays.", "Depends": ["R (>= 1.5.0)"], "Imports": ["methods", "utils"], "License": "MIT + file LICENSE", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON> [aut]", "Repository": "CRAN"}, "aplpack": {"Package": "aplpack", "Version": "1.3.5", "Source": "Repository", "Title": "Another Plot Package: 'Bagplots', 'Iconplots', 'Summaryplots', Slider Functions and Others", "Date": "2021-09-30", "Author": "<PERSON> [aut, cre]", "Maintainer": "<PERSON> <<EMAIL>>", "Depends": ["R (>= 3.0.0)"], "Suggests": ["tkrplot", "jpeg", "png", "splines", "utils", "tcltk"], "Description": "Some functions for drawing some special plots: The function 'bagplot' plots a bagplot, 'faces' plots chernoff faces, 'iconplot' plots a representation of a frequency table or a data matrix, 'plothulls' plots hulls of a bivariate data set, 'plotsummary' plots a graphical summary of a data set, 'puticon' adds icons to a plot, 'skyline.hist' combines several histograms of a one dimensional data set in one plot, 'slider' functions supports some interactive graphics, 'spin3R' helps an inspection of a 3-dim point cloud, 'stem.leaf' plots a stem and leaf plot, 'stem.leaf.backback' plots back-to-back versions of stem and leaf plot.", "License": "GPL (>= 2)", "URL": "https://www.uni-bielefeld.de/fakultaeten/wirtschaftswissenschaften/fakultaet/lehrende-ehemalige/pwolf/wolf_aplpack/index.xml", "NeedsCompilation": "no", "Repository": "CRAN"}, "askpass": {"Package": "askpass", "Version": "1.2.1", "Source": "Repository", "Type": "Package", "Title": "Password Entry Utilities for R, Git, and SSH", "Authors@R": "person(\"<PERSON><PERSON><PERSON>\", \"O<PERSON>\", role = c(\"aut\", \"cre\"),  email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4035-0289\"))", "Description": "Cross-platform utilities for prompting the user for credentials or a  passphrase, for example to authenticate with a server or read a protected key. Includes native programs for MacOS and Windows, hence no 'tcltk' is required.  Password entry can be invoked in two different ways: directly from R via the  askpass() function, or indirectly as password-entry back-end for 'ssh-agent'  or 'git-credential' via the SSH_ASKPASS and GIT_ASKPASS environment variables. Thereby the user can be prompted for credentials or a passphrase if needed  when R calls out to git or ssh.", "License": "MIT + file LICENSE", "URL": "https://r-lib.r-universe.dev/askpass", "BugReports": "https://github.com/r-lib/askpass/issues", "Encoding": "UTF-8", "Imports": ["sys (>= 2.1)"], "RoxygenNote": "7.2.3", "Suggests": ["testthat"], "Language": "en-US", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-4035-0289>)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "backports": {"Package": "backports", "Version": "1.5.0", "Source": "Repository", "Type": "Package", "Title": "Reimplementations of Functions Introduced Since R-3.0.0", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", <PERSON><PERSON><PERSON>, \"mi<PERSON><PERSON>@gmail.com\", role = c(\"cre\", \"aut\"), comment = c(ORCID = \"0000-0001-9754-0393\")), person(\"<PERSON>\", \"<PERSON>\", NULL, \"<EMAIL>\", role = c(\"aut\")), person(\"R Core Team\", role = \"aut\"))", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "Functions introduced or changed since R v3.0.0 are re-implemented in this package. The backports are conditionally exported in order to let R resolve the function name to either the implemented backport, or the respective base version, if available. Package developers can make use of new functions or arguments by selectively importing specific backports to support older installations.", "URL": "https://github.com/r-lib/backports", "BugReports": "https://github.com/r-lib/backports/issues", "License": "GPL-2 | GPL-3", "NeedsCompilation": "yes", "ByteCompile": "yes", "Depends": ["R (>= 3.0.0)"], "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "Author": "<PERSON> [cre, aut] (<https://orcid.org/0000-0001-9754-0393>), <PERSON> [aut], R <PERSON> [aut]", "Repository": "CRAN"}, "base64enc": {"Package": "base64enc", "Version": "0.1-3", "Source": "Repository", "Title": "Tools for base64 encoding", "Author": "<PERSON> <<PERSON>.<PERSON>@r-project.org>", "Maintainer": "<PERSON> <<PERSON>.<PERSON>@r-project.org>", "Depends": ["R (>= 2.9.0)"], "Enhances": ["png"], "Description": "This package provides tools for handling base64 encoding. It is more flexible than the orphaned base64 package.", "License": "GPL-2 | GPL-3", "URL": "http://www.rforge.net/base64enc", "NeedsCompilation": "yes", "Repository": "CRAN"}, "bit": {"Package": "bit", "Version": "4.6.0", "Source": "Repository", "Title": "Classes and Methods for Fast Memory-Efficient Boolean Selections", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\") )", "Depends": ["R (>= 3.4.0)"], "Suggests": ["testthat (>= 3.0.0)", "roxygen2", "knitr", "markdown", "rmarkdown", "microbenchmark", "bit64 (>= 4.0.0)", "ff (>= 4.0.0)"], "Description": "Provided are classes for boolean and skewed boolean vectors, fast boolean methods, fast unique and non-unique integer sorting, fast set operations on sorted and unsorted sets of integers, and foundations for ff (range index, compression, chunked processing).", "License": "GPL-2 | GPL-3", "LazyLoad": "yes", "ByteCompile": "yes", "Encoding": "UTF-8", "URL": "https://github.com/r-lib/bit", "VignetteBuilder": "knitr, rmarkdown", "RoxygenNote": "7.3.2", "Config/testthat/edition": "3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "bit64": {"Package": "bit64", "Version": "4.6.0-1", "Source": "Repository", "Title": "A S3 Class for Vectors of 64bit Integers", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"micha<PERSON><PERSON><PERSON><EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\") )", "Depends": ["R (>= 3.4.0)", "bit (>= 4.0.0)"], "Description": "Package 'bit64' provides serializable S3 atomic 64bit (signed) integers. These are useful for handling database keys and exact counting in +-2^63. WARNING: do not use them as replacement for 32bit integers, integer64 are not supported for subscripting by R-core and they have different semantics when combined with double, e.g. integer64 + double => integer64. Class integer64 can be used in vectors, matrices, arrays and data.frames. Methods are available for coercion from and to logicals, integers, doubles, characters and factors as well as many elementwise and summary functions. Many fast algorithmic operations such as 'match' and 'order' support inter- active data exploration and manipulation and optionally leverage caching.", "License": "GPL-2 | GPL-3", "LazyLoad": "yes", "ByteCompile": "yes", "URL": "https://github.com/r-lib/bit64", "Encoding": "UTF-8", "Imports": ["graphics", "methods", "stats", "utils"], "Suggests": ["testthat (>= 3.0.3)", "withr"], "Config/testthat/edition": "3", "Config/needs/development": "testthat", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [ctb], <PERSON><PERSON> [ctb]", "Maintainer": "<PERSON> <michael<PERSON><EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "bitops": {"Package": "bitops", "Version": "1.0-9", "Source": "Repository", "Date": "2024-10-03", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\", comment = \"S original; then (after MM's port) revised and modified\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"cre\", \"aut\"), email = \"<EMAIL>\", comment = c(\"Initial R port; tweaks\", ORCID = \"0000-0002-8685-9910\")))", "Title": "Bitwise Operations", "Description": "Functions for bitwise operations on integer vectors.", "License": "GPL (>= 2)", "URL": "https://github.com/mmaechler/R-bitops", "BugReports": "https://github.com/mmaechler/R-bitops/issues", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (S original; then (after MM's port) revised and modified), <PERSON> [cre, aut] (Initial R port; tweaks, <https://orcid.org/0000-0002-8685-9910>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "boot": {"Package": "boot", "Version": "1.3-31", "Source": "Repository", "Priority": "recommended", "Date": "2024-08-28", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\",  comment = \"author of original code for S\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"trl\"), email = \"<EMAIL>\", comment = \"conversion to R, maintainer 1999--2022, author of parallel support\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cre\"), email = \"<EMAIL>\", comment = \"minor bug fixes\"))", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Note": "Maintainers are not available to give advice on using a package they did not author.", "Description": "Functions and datasets for bootstrapping from the book \"Bootstrap Methods and Their Application\" by <PERSON><PERSON> <PERSON><PERSON> and  <PERSON><PERSON> <PERSON><PERSON> (1997, CUP), originally written by <PERSON> for S.", "Title": "Bootstrap Functions (Originally by <PERSON> for S)", "Depends": ["R (>= 3.0.0)", "graphics", "stats"], "Suggests": ["MASS", "survival"], "LazyData": "yes", "ByteCompile": "yes", "License": "Unlimited", "NeedsCompilation": "no", "Author": "<PERSON> [aut] (author of original code for S), <PERSON> [aut, trl] (conversion to R, maintainer 1999--2022, author of parallel support), <PERSON><PERSON><PERSON> [ctb, cre] (minor bug fixes)", "Repository": "CRAN"}, "brio": {"Package": "brio", "Version": "1.1.5", "Source": "Repository", "Title": "Basic R Input Output", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2739-7082\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(given = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Functions to handle basic input output, these functions always read and write UTF-8 (8-bit Unicode Transformation Format) files and provide more explicit control over line endings.", "License": "MIT + file LICENSE", "URL": "https://brio.r-lib.org, https://github.com/r-lib/brio", "BugReports": "https://github.com/r-lib/brio/issues", "Depends": ["R (>= 3.6)"], "Suggests": ["covr", "testthat (>= 3.0.0)"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (<https://orcid.org/0000-0002-2739-7082>), <PERSON><PERSON><PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "broom": {"Package": "broom", "Version": "1.0.8", "Source": "Repository", "Type": "Package", "Title": "Convert Statistical Objects into Tidy Tibbles", "Authors@R": "c(person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4985-5160\")), person(given = \"<PERSON>\", family = \"Couch\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-5676-5107\")), person(given = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(given = \"Indrajeet\", family = \"Patil\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-1995-6531\")), person(given = \"<PERSON>\", family = \"Chiu\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"<PERSON>hi<PERSON>\", family = \"<PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"Demeshev\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"<PERSON>er\", family = \"Menne\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"Nutter\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Ben\", family = \"Bolker\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Francois\", family = \"Briatte\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Jeffrey\", family = \"Arnold\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Jonah\", family = \"Gabry\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Luciano\", family = \"Selzer\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Gavin\", family = \"Simpson\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Jens\", family = \"Preussner\", role = \"ctb\", email = \" <EMAIL>\"), person(given = \"Jay\", family = \"Hesselberth\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Hadley\", family = \"Wickham\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Matthew\", family = \"Lincoln\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Alessandro\", family = \"Gasparini\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Lukasz\", family = \"Komsta\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Frederick\", family = \"Novometsky\", role = \"ctb\"), person(given = \"Wilson\", family = \"Freitas\", role = \"ctb\"), person(given = \"Michelle\", family = \"Evans\", role = \"ctb\"), person(given = \"Jason Cory\", family = \"Brunson\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Simon\", family = \"Jackson\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Ben\", family = \"Whalley\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Karissa\", family = \"Whiting\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Yves\", family = \"Rosseel\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Michael\", family = \"Kuehn\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Jorge\", family = \"Cimentada\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Erle\", family = \"Holgersen\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Karl\", family = \"Dunkle Werner\", role = \"ctb\", comment = c(ORCID = \"0000-0003-0523-7309\")), person(given = \"Ethan\", family = \"Christensen\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Steven\", family = \"Pav\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Paul\", family = \"PJ\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Ben\", family = \"Schneider\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Patrick\", family = \"Kennedy\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Lily\", family = \"Medina\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Brian\", family = \"Fannin\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Jason\", family = \"Muhlenkamp\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Matt\", family = \"Lehman\", role = \"ctb\"), person(given = \"Bill\", family = \"Denney\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-5759-428X\")), person(given = \"Nic\", family = \"Crane\", role = \"ctb\"), person(given = \"Andrew\", family = \"Bates\", role = \"ctb\"), person(given = \"Vincent\", family = \"Arel-Bundock\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-2042-7063\")), person(given = \"Hideaki\", family = \"Hayashi\", role = \"ctb\"), person(given = \"Luis\", family = \"Tobalina\", role = \"ctb\"), person(given = \"Annie\", family = \"Wang\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Wei Yang\", family = \"Tham\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Clara\", family = \"Wang\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Abby\", family = \"Smith\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-3207-0375\")), person(given = \"Jasper\", family = \"Cooper\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-8639-3188\")), person(given = \"E Auden\", family = \"Krauska\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1466-5850\")), person(given = \"Alex\", family = \"Wang\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Malcolm\", family = \"Barrett\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0299-5825\")), person(given = \"Charles\", family = \"Gray\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-9978-011X\")), person(given = \"Jared\", family = \"Wilber\", role = \"ctb\"), person(given = \"Vilmantas\", family = \"Gegzna\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-9500-5167\")), person(given = \"Eduard\", family = \"Szoecs\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Frederik\", family = \"Aust\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-4900-788X\")), person(given = \"Angus\", family = \"Moore\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Nick\", family = \"Williams\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Marius\", family = \"Barth\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-3421-6665\")), person(given = \"Bruna\", family = \"Wundervald\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-8163-220X\")), person(given = \"Joyce\", family = \"Cahoon\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-7217-4702\")), person(given = \"Grant\", family = \"McDermott\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-7883-8573\")), person(given = \"Kevin\", family = \"Zarca\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Shiro\", family = \"Kuriwaki\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-5687-2647\")), person(given = \"Lukas\", family = \"Wallrich\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-2121-5177\")), person(given = \"James\", family = \"Martherus\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-8285-3300\")), person(given = \"Chuliang\", family = \"Xiao\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-8466-9398\")), person(given = \"Joseph\", family = \"Larmarange\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Max\", family = \"Kuhn\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Michal\", family = \"Bojanowski\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Hakon\", family = \"Malmedal\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Clara\", family = \"Wang\", role = \"ctb\"), person(given = \"Sergio\", family = \"Oller\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Luke\", family = \"Sonnet\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Jim\", family = \"Hester\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Ben\", family = \"Schneider\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Bernie\", family = \"Gray\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-9190-6032\")), person(given = \"Mara\", family = \"Averick\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Aaron\", family = \"Jacobs\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Andreas\", family = \"Bender\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Sven\", family = \"Templer\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Paul-Christian\", family = \"Buerkner\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Matthew\", family = \"Kay\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Erwan\", family = \"Le Pennec\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Johan\", family = \"Junkka\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Hao\", family = \"Zhu\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Benjamin\", family = \"Soltoff\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Zoe\", family = \"Wilkinson Saldana\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Tyler\", family = \"Littlefield\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Charles T.\", family = \"Gray\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Shabbh E.\", family = \"Banks\", role = \"ctb\"), person(given = \"Serina\", family = \"Robinson\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Roger\", family = \"Bivand\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Riinu\", family = \"Ots\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Nicholas\", family = \"Williams\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Nina\", family = \"Jakobsen\", role = \"ctb\"), person(given = \"Michael\", family = \"Weylandt\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Lisa\", family = \"Lendway\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Karl\", family = \"Hailperin\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Josue\", family = \"Rodriguez\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Jenny\", family = \"Bryan\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Chris\", family = \"Jarvis\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Greg\", family = \"Macfarlane\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Brian\", family = \"Mannakee\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Drew\", family = \"Tyre\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Shreyas\", family = \"Singh\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Laurens\", family = \"Geffert\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Hong\", family = \"Ooi\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Henrik\", family = \"Bengtsson\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Eduard\", family = \"Szocs\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"David\", family = \"Hugh-Jones\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Matthieu\", family = \"Stigler\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Hugo\", family = \"Tavares\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-9373-2726\")), person(given = \"R. Willem\", family = \"Vervoort\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Brenton M.\", family = \"Wiernik\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Josh\", family = \"Yamamoto\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"Jasme\", family = \"Lee\", role = \"ctb\"), person(given = \"Taren\", family = \"Sanders\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4504-6008\")), person(given = \"Ilaria\", family = \"Prosdocimi\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-8565-094X\")), person(given = \"Daniel D.\", family = \"Sjoberg\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0862-2018\")), person(given = \"Alex\", family = \"Reinhart\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-6658-514X\")))", "Description": "Summarizes key information about statistical objects in tidy tibbles. This makes it easy to report results, create plots and consistently work with large numbers of models at once. Broom provides three verbs that each provide different types of information about a model. tidy() summarizes information about model components such as coefficients of a regression. glance() reports information about an entire model, such as goodness of fit measures like AIC and BIC. augment() adds information about individual observations to a dataset, such as fitted values or influence measures.", "License": "MIT + file LICENSE", "URL": "https://broom.tidymodels.org/, https://github.com/tidymodels/broom", "BugReports": "https://github.com/tidymodels/broom/issues", "Depends": ["R (>= 3.5)"], "Imports": ["backports", "cli", "dplyr (>= 1.0.0)", "generics (>= 0.0.2)", "glue", "lifecycle", "purrr", "rlang (>= 1.1.0)", "stringr", "tibble (>= 3.0.0)", "tidyr (>= 1.0.0)"], "Suggests": ["AER", "AUC", "bbmle", "betareg (>= 3.2-1)", "biglm", "binGroup", "boot", "btergm (>= 1.10.6)", "car (>= 3.1-2)", "carData", "caret", "cluster", "cmprsk", "coda", "covr", "drc", "e1071", "emmeans", "epiR", "ergm (>= 3.10.4)", "fixest (>= 0.9.0)", "gam (>= 1.15)", "gee", "geepack", "ggplot2", "glmnet", "glmnetUtils", "gmm", "Hmisc", "irlba", "interp", "joineRML", "<PERSON>", "knitr", "ks", "<PERSON><PERSON>", "lavaan (>= 0.6.18)", "leaps", "lfe", "lm.beta", "lme4", "lmodel2", "lmtest (>= 0.9.38)", "lsmeans", "maps", "margins", "MASS", "mclust", "mediation", "metafor", "mfx", "mgcv", "mlogit", "modeldata", "modeltests (>= 0.1.6)", "muhaz", "multcomp", "network", "nnet", "ordinal", "plm", "poLCA", "psych", "quantreg", "rmarkdown", "robust", "robustbase", "rsample", "sandwich", "spdep (>= 1.1)", "spatialreg", "speedglm", "spelling", "survey", "survival (>= 3.6-4)", "systemfit", "testthat (>= 3.0.0)", "tseries", "vars", "zoo"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "Language": "en-US", "Collate": "'aaa-documentation-helper.R' 'null-and-default.R' 'aer.R' 'auc.R' 'base.R' 'bbmle.R' 'betareg.R' 'biglm.R' 'bingroup.R' 'boot.R' 'broom-package.R' 'broom.R' 'btergm.R' 'car.R' 'caret.R' 'cluster.R' 'cmprsk.R' 'data-frame.R' 'deprecated-0-7-0.R' 'drc.R' 'emmeans.R' 'epiR.R' 'ergm.R' 'fixest.R' 'gam.R' 'geepack.R' 'glmnet-cv-glmnet.R' 'glmnet-glmnet.R' 'gmm.R' 'hmisc.R' 'import-standalone-obj-type.R' 'import-standalone-types-check.R' 'joinerml.R' 'kendall.R' 'ks.R' 'lavaan.R' 'leaps.R' 'lfe.R' 'list-irlba.R' 'list-optim.R' 'list-svd.R' 'list-xyz.R' 'list.R' 'lm-beta.R' 'lmodel2.R' 'lmtest.R' 'maps.R' 'margins.R' 'mass-fitdistr.R' 'mass-negbin.R' 'mass-polr.R' 'mass-ridgelm.R' 'stats-lm.R' 'mass-rlm.R' 'mclust.R' 'mediation.R' 'metafor.R' 'mfx.R' 'mgcv.R' 'mlogit.R' 'muhaz.R' 'multcomp.R' 'nnet.R' 'nobs.R' 'ordinal-clm.R' 'ordinal-clmm.R' 'plm.R' 'polca.R' 'psych.R' 'stats-nls.R' 'quantreg-nlrq.R' 'quantreg-rq.R' 'quantreg-rqs.R' 'robust-glmrob.R' 'robust-lmrob.R' 'robustbase-glmrob.R' 'robustbase-lmrob.R' 'sp.R' 'spdep.R' 'speedglm-speedglm.R' 'speedglm-speedlm.R' 'stats-anova.R' 'stats-arima.R' 'stats-decompose.R' 'stats-factanal.R' 'stats-glm.R' 'stats-htest.R' 'stats-kmeans.R' 'stats-loess.R' 'stats-mlm.R' 'stats-prcomp.R' 'stats-smooth.spline.R' 'stats-summary-lm.R' 'stats-time-series.R' 'survey.R' 'survival-aareg.R' 'survival-cch.R' 'survival-coxph.R' 'survival-pyears.R' 'survival-survdiff.R' 'survival-survexp.R' 'survival-survfit.R' 'survival-survreg.R' 'systemfit.R' 'tseries.R' 'utilities.R' 'vars.R' 'zoo.R' 'zzz.R'", "Config/testthat/edition": "3", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut] (<https://orcid.org/0000-0002-4985-5160>), <PERSON> [aut, cre] (<https://orcid.org/0000-0001-5676-5107>), <PERSON><PERSON><PERSON> Software, PBC [cph, fnd], <PERSON><PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0003-1995-6531>), <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> <PERSON><PERSON>ada [ctb], <PERSON><PERSON><PERSON><PERSON> [ctb], <PERSON>nkle <PERSON> [ctb] (<https://or<PERSON>.org/0000-0003-0523-7309>), <PERSON> <PERSON> [ctb], <PERSON> <PERSON>v [ctb], <PERSON> P<PERSON> [ctb], <PERSON> <PERSON> [ctb], <PERSON> <PERSON> [ctb], <PERSON> <PERSON> [ctb], <PERSON> <PERSON><PERSON> [ctb], <PERSON> <PERSON>hlenkamp [ctb], Matt Lehman [ctb], Bill Denney [ctb] (<https://orcid.org/0000-0002-5759-428X>), Nic Crane [ctb], Andrew Bates [ctb], Vincent Arel-Bundock [ctb] (<https://orcid.org/0000-0003-2042-7063>), Hideaki Hayashi [ctb], Luis Tobalina [ctb], Annie Wang [ctb], Wei Yang Tham [ctb], Clara Wang [ctb], Abby Smith [ctb] (<https://orcid.org/0000-0002-3207-0375>), Jasper Cooper [ctb] (<https://orcid.org/0000-0002-8639-3188>), E Auden Krauska [ctb] (<https://orcid.org/0000-0002-1466-5850>), Alex Wang [ctb], Malcolm Barrett [ctb] (<https://orcid.org/0000-0003-0299-5825>), Charles Gray [ctb] (<https://orcid.org/0000-0002-9978-011X>), Jared Wilber [ctb], Vilmantas Gegzna [ctb] (<https://orcid.org/0000-0002-9500-5167>), Eduard Szoecs [ctb], Frederik Aust [ctb] (<https://orcid.org/0000-0003-4900-788X>), Angus Moore [ctb], Nick Williams [ctb], Marius Barth [ctb] (<https://orcid.org/0000-0002-3421-6665>), Bruna Wundervald [ctb] (<https://orcid.org/0000-0001-8163-220X>), Joyce Cahoon [ctb] (<https://orcid.org/0000-0001-7217-4702>), Grant McDermott [ctb] (<https://orcid.org/0000-0001-7883-8573>), Kevin Zarca [ctb], Shiro Kuriwaki [ctb] (<https://orcid.org/0000-0002-5687-2647>), Lukas Wallrich [ctb] (<https://orcid.org/0000-0003-2121-5177>), James Martherus [ctb] (<https://orcid.org/0000-0002-8285-3300>), Chuliang Xiao [ctb] (<https://orcid.org/0000-0002-8466-9398>), Joseph Larmarange [ctb], Max Kuhn [ctb], Michal Bojanowski [ctb], Hakon Malmedal [ctb], Clara Wang [ctb], Sergio Oller [ctb], Luke Sonnet [ctb], Jim Hester [ctb], Ben Schneider [ctb], Bernie Gray [ctb] (<https://orcid.org/0000-0001-9190-6032>), Mara Averick [ctb], Aaron Jacobs [ctb], Andreas Bender [ctb], Sven Templer [ctb], Paul-Christian Buerkner [ctb], Matthew Kay [ctb], Erwan Le Pennec [ctb], Johan Junkka [ctb], Hao Zhu [ctb], Benjamin Soltoff [ctb], Zoe Wilkinson Saldana [ctb], Tyler Littlefield [ctb], Charles T. Gray [ctb], Shabbh E. Banks [ctb], Serina Robinson [ctb], Roger Bivand [ctb], Riinu Ots [ctb], Nicholas Williams [ctb], Nina Jakobsen [ctb], Michael Weylandt [ctb], Lisa Lendway [ctb], Karl Hailperin [ctb], Josue Rodriguez [ctb], Jenny Bryan [ctb], Chris Jarvis [ctb], Greg Macfarlane [ctb], Brian Mannakee [ctb], Drew Tyre [ctb], Shreyas Singh [ctb], Laurens Geffert [ctb], Hong Ooi [ctb], Henrik Bengtsson [ctb], Eduard Szocs [ctb], David Hugh-Jones [ctb], Matthieu Stigler [ctb], Hugo Tavares [ctb] (<https://orcid.org/0000-0001-9373-2726>), R. Willem Vervoort [ctb], Brenton M. Wiernik [ctb], Josh Yamamoto [ctb], Jasme Lee [ctb], Taren Sanders [ctb] (<https://orcid.org/0000-0002-4504-6008>), Ilaria Prosdocimi [ctb] (<https://orcid.org/0000-0001-8565-094X>), Daniel D. Sjoberg [ctb] (<https://orcid.org/0000-0003-0862-2018>), Alex Reinhart [ctb] (<https://orcid.org/0000-0002-6658-514X>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "broom.helpers": {"Package": "broom.helpers", "Version": "1.21.0", "Source": "Repository", "Title": "Helpers for Model Coefficients Tibbles", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"jose<PERSON>@larmarange.net\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0001-7097-700X\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0003-0862-2018\")) )", "Description": "Provides suite of functions to work with regression model 'broom::tidy()' tibbles.  The suite includes functions to group regression model terms by variable, insert reference and header rows for categorical variables, add variable labels, and more.", "License": "GPL (>= 3)", "URL": "https://larmarange.github.io/broom.helpers/, https://github.com/larmarange/broom.helpers", "BugReports": "https://github.com/larmarange/broom.helpers/issues", "Depends": ["R (>= 4.1)"], "Imports": ["broom (>= 0.8)", "cards", "cli", "dplyr (>= 1.1.0)", "labelled", "lifecycle", "purrr", "rlang (>= 1.0.1)", "stats", "stringr", "tibble", "tidyr", "tidyselect"], "Suggests": ["betareg", "biglm", "brms (>= 2.13.0)", "broom.mixed", "cmprsk", "covr", "datasets", "effects", "emmeans", "fixest (>= 0.10.0)", "forcats", "gam", "gee", "geepack", "ggplot2", "ggeffects (>= 1.3.2)", "ggstats (>= 0.2.1)", "glmmTMB", "glmtoolbox", "glue", "gt", "gtsummary (>= 2.0.0)", "knitr", "<PERSON>an", "lfe", "lme4 (>= 1.1.28)", "logitr (>= 0.8.0)", "marginaleffects (>= 0.21.0)", "margins", "MASS", "mgcv", "mice", "mmrm (>= 0.3.6)", "multgee", "nnet", "ordinal", "parameters", "parsnip", "patchwork", "plm", "pscl", "rmarkdown", "rstanarm", "scales", "spelling", "survey", "survival", "testthat (>= 3.0.0)", "tidycmprsk", "VGAM", "svyVGAM"], "VignetteBuilder": "knitr", "RdMacros": "lifecycle", "Encoding": "UTF-8", "Language": "en-US", "LazyData": "true", "RoxygenNote": "7.3.2", "Config/testthat/edition": "3", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0001-7097-700X>), <PERSON> [aut] (<https://orcid.org/0000-0003-0862-2018>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "bslib": {"Package": "bslib", "Version": "0.9.0", "Source": "Repository", "Title": "Custom 'Bootstrap' 'Sass' Themes for 'shiny' and 'rmarkdown'", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0002-7111-0077\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(, \"Bootstrap contributors\", role = \"ctb\", comment = \"Bootstrap library\"), person(, \"Twitter, Inc\", role = \"cph\", comment = \"Bootstrap library\"), person(\"<PERSON><PERSON>\", \"<PERSON>gu<PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Bootstrap colorpicker library\"), person(\"<PERSON>\", \"Park\", role = c(\"ctb\", \"cph\"), comment = \"Bootswatch library\"), person(, \"PayP<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Bootstrap accessibility plugin\") )", "Description": "Simplifies custom 'CSS' styling of both 'shiny' and 'rmarkdown' via 'Bootstrap' 'Sass'. Supports 'Bootstrap' 3, 4 and 5 as well as their various 'Bootswatch' themes. An interactive widget is also provided for previewing themes in real time.", "License": "MIT + file LICENSE", "URL": "https://rstudio.github.io/bslib/, https://github.com/rstudio/bslib", "BugReports": "https://github.com/rstudio/bslib/issues", "Depends": ["R (>= 2.10)"], "Imports": ["base64enc", "cachem", "fastmap (>= 1.1.1)", "grDevices", "htmltools (>= 0.5.8)", "jquerylib (>= 0.1.3)", "jsonlite", "lifecycle", "memoise (>= 2.0.1)", "mime", "rlang", "sass (>= 0.4.9)"], "Suggests": ["bsicons", "curl", "fontawesome", "future", "ggplot2", "knitr", "magrit<PERSON>", "rapp<PERSON>s", "rmarkdown (>= 2.7)", "shiny (> 1.8.1)", "testthat", "thematic", "tools", "utils", "withr", "yaml"], "Config/Needs/deploy": "BH, chiflights22, colourpicker, commonmark, cpp11, cpsievert/chiflights22, cpsievert/histoslider, dplyr, DT, ggplot2, ggridges, gt, hexbin, histoslider, htmlwidgets, lattice, leaflet, lubridate, markdown, modelr, plotly, reactable, reshape2, rprojroot, rsconnect, rstudio/shiny, scales, styler, tibble", "Config/Needs/routine": "chromote, desc, renv", "Config/Needs/website": "brio, crosstalk, dplyr, DT, ggplot2, glue, htmlwidgets, leaflet, lorem, palmerpenguins, plotly, purrr, rprojroot, rstudio/htmltools, scales, stringr, tidyr, webshot2", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "zzzz-bs-sass, fonts, zzz-precompile, theme-*, rmd-*", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "Collate": "'accordion.R' 'breakpoints.R' 'bs-current-theme.R' 'bs-dependencies.R' 'bs-global.R' 'bs-remove.R' 'bs-theme-layers.R' 'bs-theme-preset-bootswatch.R' 'bs-theme-preset-brand.R' 'bs-theme-preset-builtin.R' 'bs-theme-preset.R' 'utils.R' 'bs-theme-preview.R' 'bs-theme-update.R' 'bs-theme.R' 'bslib-package.R' 'buttons.R' 'card.R' 'deprecated.R' 'files.R' 'fill.R' 'imports.R' 'input-dark-mode.R' 'input-switch.R' 'layout.R' 'nav-items.R' 'nav-update.R' 'navbar_options.R' 'navs-legacy.R' 'navs.R' 'onLoad.R' 'page.R' 'popover.R' 'precompiled.R' 'print.R' 'shiny-devmode.R' 'sidebar.R' 'staticimports.R' 'tooltip.R' 'utils-deps.R' 'utils-shiny.R' 'utils-tags.R' 'value-box.R' 'version-default.R' 'versions.R'", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-4958-2844>), <PERSON> [aut], <PERSON><PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-7111-0077>), Posit Software, PBC [cph, fnd], Bootstrap contributors [ctb] (Bootstrap library), Twitter, Inc [cph] (Bootstrap library), <PERSON><PERSON> [ctb, cph] (Bootstrap colorpicker library), <PERSON> [ctb, cph] (Bootswatch library), PayPal [ctb, cph] (Bootstrap accessibility plugin)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "caTools": {"Package": "caTools", "Version": "1.18.3", "Source": "Repository", "Type": "Package", "Title": "Tools: Moving Window Statistics, GIF, Base64, ROC AUC, etc", "Date": "2024-09-04", "Authors@R": "c(person(given = \"<PERSON><PERSON><PERSON>\", family = \"<PERSON><PERSON><PERSON><PERSON>\", role = \"aut\", email = \"jaroslaw.w.t<PERSON>@saic.com\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON>\", role = \"cre\", email = \"<EMAIL>\"))", "Maintainer": "<PERSON> <<EMAIL>>", "Depends": ["R (>= 3.6.0)"], "Imports": ["bitops"], "Suggests": ["MASS", "rpart"], "Description": "Contains several basic utility functions including: moving (rolling, running) window statistic functions, read/write for GIF and ENVI binary files, fast calculation of AUC, LogitBoost classifier, base64 encoder/decoder, round-off-error-free sum and cumsum, etc.", "License": "GPL-3", "NeedsCompilation": "yes", "Repository": "https://packagemanager.posit.co/cran/latest", "Author": "<PERSON><PERSON><PERSON> [aut], <PERSON> [cre]", "Encoding": "UTF-8"}, "cachem": {"Package": "cachem", "Version": "1.1.0", "Source": "Repository", "Title": "<PERSON><PERSON> R Objects with Automatic Pruning", "Description": "Key-value stores with automatic pruning. Caches can limit either their total size or the age of the oldest object (or both), automatically pruning objects to maintain the constraints.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", c(\"aut\", \"cre\")), person(family = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")))", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "ByteCompile": "true", "URL": "https://cachem.r-lib.org/, https://github.com/r-lib/cachem", "Imports": ["rlang", "fastmap (>= 1.2.0)"], "Suggests": ["testthat"], "RoxygenNote": "7.2.3", "Config/Needs/routine": "lobstr", "Config/Needs/website": "pkgdown", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "callr": {"Package": "callr", "Version": "3.7.6", "Source": "Repository", "Title": "Call R from R", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\", \"cph\"), comment = c(ORCID = \"0000-0001-7098-9676\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(\"Ascent Digital Services\", role = c(\"cph\", \"fnd\")) )", "Description": "It is sometimes useful to perform a computation in a separate R process, without affecting the current R process at all.  This packages does exactly that.", "License": "MIT + file LICENSE", "URL": "https://callr.r-lib.org, https://github.com/r-lib/callr", "BugReports": "https://github.com/r-lib/callr/issues", "Depends": ["R (>= 3.4)"], "Imports": ["processx (>= 3.6.1)", "R6", "utils"], "Suggests": ["asciicast (>= 2.3.1)", "cli (>= 1.1.0)", "mockery", "ps", "rprojroot", "spelling", "testthat (>= 3.2.0)", "withr (>= 2.3.0)"], "Config/Needs/website": "r-lib/asciicast, glue, htmlwidgets, igraph, tibble, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.3.1.9000", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre, cph] (<https://orcid.org/0000-0001-7098-9676>), <PERSON> [aut], Posit Software, PBC [cph, fnd], Ascent Digital Services [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "car": {"Package": "car", "Version": "3.1-3", "Source": "Repository", "Date": "2024-09-23", "Title": "Companion to Applied Regression", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>jan<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>ette\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>c\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>ip<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"V<PERSON>bles\", role = \"ctb\"), person(\"Steve\", \"Walker\", role=\"ctb\"), person(\"David\", \"Winsemius\", role=\"ctb\"), person(\"Achim\", \"Zeileis\", role = \"ctb\"), person(\"R-Core\", role=\"ctb\"))", "Depends": ["R (>= 3.5.0)", "carData (>= 3.0-0)"], "Imports": ["abind", "Formula", "MASS", "mgcv", "nnet", "pbkrtest (>= 0.4-4)", "quantreg", "grDevices", "utils", "stats", "graphics", "lme4 (>= 1.1-27.1)", "nlme", "scales"], "Suggests": ["alr4", "boot", "coxme", "effects", "knitr", "leaps", "lmtest", "Matrix", "MatrixModels", "ordinal", "<PERSON>rix", "mvtnorm", "rgl (>= 0.111.3)", "rio", "sandwich", "SparseM", "survival", "survey"], "ByteCompile": "yes", "LazyLoad": "yes", "Description": "Functions to <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON>,  An R Companion to Applied Regression, Third Edition, Sage, 2019.", "License": "GPL (>= 2)", "URL": "https://r-forge.r-project.org/projects/car/, https://CRAN.R-project.org/package=car, https://www.john-fox.ca/Companion/index.html", "VignetteBuilder": "knitr", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON> [aut], <PERSON> [aut], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "carData": {"Package": "carData", "Version": "3.0-5", "Source": "Repository", "Date": "2022-01-05", "Title": "Companion to Applied Regression Data Sets", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"))", "Depends": ["R (>= 3.5.0)"], "Suggests": ["car (>= 3.0-0)"], "LazyLoad": "yes", "LazyData": "yes", "Description": "Datasets to <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON>,  An R Companion to Applied Regression, Third Edition, Sage (2019).", "License": "GPL (>= 2)", "URL": "https://r-forge.r-project.org/projects/car/, https://CRAN.R-project.org/package=carData, https://socialsciences.mcmaster.ca/jfox/Books/Companion/index.html", "Author": "<PERSON> [aut, cre], <PERSON> [aut], <PERSON> [aut]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN", "Repository/R-Forge/Project": "car", "Repository/R-Forge/Revision": "694", "Repository/R-Forge/DateTimeStamp": "2022-01-05 19:40:37", "NeedsCompilation": "no"}, "cards": {"Package": "cards", "Version": "0.6.0", "Source": "Repository", "Title": "Analysis Results Data", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-0862-2018\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"de la <PERSON>ua\", , \"<EMAIL>\", role = \"aut\"), person(\"F. Hoffmann-La Roche AG\", role = c(\"cph\", \"fnd\")), person(\"GlaxoSmithKline Research & Development Limited\", role = \"cph\") )", "Description": "Construct CDISC (Clinical Data Interchange Standards Consortium) compliant Analysis Results Data objects. These objects are used and re-used to construct summary tables, visualizations, and written reports. The package also exports utilities for working with these objects and creating new Analysis Results Data objects.", "License": "Apache License 2.0", "URL": "https://github.com/insightsengineering/cards, https://insightsengineering.github.io/cards/", "BugReports": "https://github.com/insightsengineering/cards/issues", "Depends": ["R (>= 4.1)"], "Imports": ["cli (>= 3.6.1)", "dplyr (>= 1.1.2)", "glue (>= 1.6.2)", "lifecycle (>= 1.0.3)", "rlang (>= 1.1.1)", "tidyr (>= 1.3.0)", "tidyselect (>= 1.2.0)"], "Suggests": ["testthat (>= 3.2.0)", "withr (>= 3.0.0)"], "Config/Needs/coverage": "hms", "Config/Needs/website": "rmarkdown, jsonlite, yaml, gtsummary, tfrmt, cardx, gt, fontawesome, insightsengineering/nesttemplate", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Encoding": "UTF-8", "Language": "en-US", "LazyData": "true", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0003-0862-2018>), <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON> AG [cph, fnd], GlaxoSmithKline Research & Development Limited [cph]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "cellranger": {"Package": "cellranger", "Version": "1.1.0", "Source": "Repository", "Title": "Translate Spreadsheet Cell Ranges to Rows and Columns", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", c(\"cre\", \"aut\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", \"ctb\") )", "Description": "Helper functions to work with spreadsheets and the \"A1:D10\" style of cell range specification.", "Depends": ["R (>= 3.0.0)"], "License": "MIT + file LICENSE", "LazyData": "true", "URL": "https://github.com/rsheets/cellranger", "BugReports": "https://github.com/rsheets/cellranger/issues", "Suggests": ["covr", "testthat (>= 1.0.0)", "knitr", "rmarkdown"], "RoxygenNote": "5.0.1.9000", "VignetteBuilder": "knitr", "Imports": ["rematch", "tibble"], "NeedsCompilation": "no", "Author": "<PERSON> [cre, aut], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "classInt": {"Package": "classInt", "Version": "0.4-11", "Source": "Repository", "Date": "2025-01-06", "Title": "Choose Univariate Class Intervals", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\", \"cre\"), email=\"<PERSON>.<EMAIL>\", comment=c(ORCID=\"0000-0003-2392-6140\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\", comment=c(ORCID=\"0000-0002-5759-428X\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"Hernangómez\", role=\"ctb\", comment=c(ORCID=\"0000-0001-8457-4658\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0001-9910-865X\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\", comment =c(ORCID=\"0000-0002-6802-4290\")))", "Depends": ["R (>= 2.2)"], "Imports": ["grDevices", "stats", "graphics", "e1071", "class", "KernSmooth"], "Suggests": ["spData (>= *******)", "units", "knitr", "rmarkdown", "tinytest"], "NeedsCompilation": "yes", "Description": "Selected commonly used methods for choosing univariate class intervals for mapping or other graphics purposes.", "License": "GPL (>= 2)", "URL": "https://r-spatial.github.io/classInt/, https://github.com/r-spatial/classInt/", "BugReports": "https://github.com/r-spatial/classInt/issues/", "RoxygenNote": "6.1.1", "Encoding": "UTF-8", "VignetteBuilder": "knitr", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0003-2392-6140>), <PERSON> [ctb] (<https://orcid.org/0000-0002-5759-428X>), <PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0001-8457-4658>), <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0001-9910-865X>), <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-6802-4290>)", "Maintainer": "<PERSON> <<PERSON>.B<PERSON><EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "cli": {"Package": "cli", "Version": "3.6.5", "Source": "Repository", "Title": "Helpers for Developing Command Line Interfaces", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"g<PERSON><PERSON>@posit.co\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-5329-5987\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A suite of tools to build attractive command line interfaces ('CLIs'), from semantic elements: headings, lists, alerts, paragraphs, etc. Supports custom themes via a 'CSS'-like language. It also contains a number of lower level 'CLI' elements: rules, boxes, trees, and 'Unicode' symbols with 'ASCII' alternatives. It support ANSI colors and text styles as well.", "License": "MIT + file LICENSE", "URL": "https://cli.r-lib.org, https://github.com/r-lib/cli", "BugReports": "https://github.com/r-lib/cli/issues", "Depends": ["R (>= 3.4)"], "Imports": ["utils"], "Suggests": ["callr", "covr", "crayon", "digest", "glue (>= 1.6.0)", "grDevices", "htmltools", "htmlwidgets", "knitr", "methods", "processx", "ps (>= 1.3.4.9000)", "rlang (>= 1.0.2.9003)", "rmarkdown", "rprojroot", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "testthat (>= 3.2.0)", "tibble", "whoami", "withr"], "Config/Needs/website": "r-lib/asciicast, bench, brio, cpp11, decor, desc, fansi, prettyunits, sessioninfo, tidyverse/tidytemplate, usethis, vctrs", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-5329-5987>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "clipr": {"Package": "clipr", "Version": "0.8.0", "Source": "Repository", "Type": "Package", "Title": "Read and Write from the System Clipboard", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-4387-3384\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\") )", "Description": "Simple utility functions to read from and write to the Windows, OS X, and X11 clipboards.", "License": "GPL-3", "URL": "https://github.com/mdlincoln/clipr, http://matthewlincoln.net/clipr/", "BugReports": "https://github.com/mdlincoln/clipr/issues", "Imports": ["utils"], "Suggests": ["covr", "knitr", "rmarkdown", "rstudioa<PERSON> (>= 0.5)", "testthat (>= 2.0.0)"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.1.2", "SystemRequirements": "xclip (https://github.com/astrand/xclip) or xsel (http://www.vergenet.net/~conrad/software/xsel/) for accessing the X11 clipboard, or wl-clipboard (https://github.com/bugaevc/wl-clipboard) for systems using Wayland.", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-4387-3384>), <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "colourpicker": {"Package": "colourpicker", "Version": "1.3.0", "Source": "Repository", "Title": "A Colour Picker Tool for Shiny and for Selecting Colours in Plots", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\",  email = \"da<PERSON><PERSON>@gmail.com\", role = c(\"aut\", \"cre\"), comment= c(ORCID=\"0000-0002-5645-3493\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email=\"<EMAIL>\", role = \"ctb\") )", "Description": "A colour picker that can be used as an input in 'Shiny' apps or Rmarkdown documents. The colour picker supports alpha opacity, custom colour palettes, and many more options. A Plot Colour Helper tool is available as an 'RStudio' Addin, which helps you pick colours to use in your plots. A more generic Colour Picker 'RStudio' Addin is also provided to let  you select colours to use in your R code.", "URL": "https://github.com/daattali/colourpicker, https://daattali.com/shiny/colourInput/", "BugReports": "https://github.com/daattali/colourpicker/issues", "Depends": ["R (>= 3.1.0)"], "Imports": ["ggplot2", "htmltools", "htmlwidgets (>= 0.7)", "jsonlite", "miniUI (>= 0.1.1)", "shiny (>= 0.11.1)", "shinyjs (>= 2.0.0)", "utils"], "Suggests": ["knitr (>= 1.7)", "rmarkdown", "rstudioa<PERSON> (>= 0.5)", "shinydisconnect"], "License": "MIT + file LICENSE", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-5645-3493>), <PERSON> [ctb]", "Maintainer": "<PERSON> <da<PERSON><PERSON>@gmail.com>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "commonmark": {"Package": "commonmark", "Version": "1.9.5", "Source": "Repository", "Type": "Package", "Title": "High Performance CommonMark and Github Markdown Rendering in R", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", ,\"jero<PERSON><PERSON>@gmail.com\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON>\", role = \"cph\", comment = \"Author of cmark\"))", "Description": "The CommonMark specification <https://github.github.com/gfm/> defines a rationalized version of markdown syntax. This package uses the 'cmark'  reference implementation for converting markdown text into various formats including html, latex and groff man. In addition it exposes the markdown parse tree in xml format. Also includes opt-in support for GFM extensions including tables, autolinks, and strikethrough text.", "License": "BSD_2_clause + file LICENSE", "URL": "https://docs.ropensci.org/commonmark/ https://ropensci.r-universe.dev/commonmark", "BugReports": "https://github.com/r-lib/commonmark/issues", "Suggests": ["curl", "testthat", "xml2"], "RoxygenNote": "7.3.2", "Language": "en-US", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-4035-0289>), <PERSON> [cph] (Author of cmark)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "corrplot": {"Package": "corrplot", "Version": "0.95", "Source": "Repository", "Type": "Package", "Title": "Visualization of a Correlation Matrix", "Date": "2024-10-14", "Authors@R": "c( person('<PERSON><PERSON>', '<PERSON>', email = '<PERSON><PERSON><PERSON><PERSON>@gmail.com', role = c('cre', 'aut')), person('Viliam', '<PERSON><PERSON><PERSON>', email = '<EMAIL>', role = 'aut'), person('<PERSON>', '<PERSON>', email = '<EMAIL>', role = 'ctb'), person('<PERSON><PERSON>', '<PERSON><PERSON>', email = '<EMAIL>', role = 'ctb'), person('<PERSON>', '<PERSON>', email = '<EMAIL>', role = 'ctb'), person('<PERSON>', '<PERSON>em<PERSON>', email = '<EMAIL>', role = 'ctb'), person('<PERSON><PERSON>', '<PERSON>eidank', email = '<EMAIL>', role = 'ctb'), person('<PERSON>', '<PERSON><PERSON>', email = '<EMAIL>', role = 'ctb'), person('<PERSON>', '<PERSON>ti<PERSON><PERSON>', email = 'to<PERSON>.<EMAIL>', role = 'ctb') )", "Maintainer": "<PERSON><PERSON> <weita<PERSON><PERSON>@gmail.com>", "Suggests": ["seriation", "knitr", "RColorBrewer", "rmarkdown", "magrit<PERSON>", "prettydoc", "testthat"], "Description": "Provides a visual exploratory tool on correlation matrix that supports automatic variable reordering to help detect hidden patterns among variables.", "License": "MIT + file LICENSE", "URL": "https://github.com/taiyun/corrplot", "BugReports": "https://github.com/taiyun/corrplot/issues", "VignetteBuilder": "knitr", "RoxygenNote": "7.2.1", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [cre, aut], <PERSON><PERSON><PERSON> [aut], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb]", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "cowplot": {"Package": "cowplot", "Version": "1.1.3", "Source": "Repository", "Title": "Streamlined Plot Theme and Plot Annotations for 'ggplot2'", "Authors@R": "person( given = \"<PERSON>\", family = \"Wilke\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-7470-9261\") )", "Description": "Provides various features that help with creating publication-quality figures with 'ggplot2', such as a set of themes, functions to align plots and arrange them into complex compound figures, and functions that make it easy to annotate plots and or mix plots with images. The package was originally written for internal use in the <PERSON>ilke lab, hence the name (<PERSON>'s plot package). It has also been used extensively in the book Fundamentals of Data Visualization.", "URL": "https://wilkelab.org/cowplot/", "BugReports": "https://github.com/wilkelab/cowplot/issues", "Depends": ["R (>= 3.5.0)"], "Imports": ["ggplot2 (>= 3.4.0)", "grid", "gtable", "grDevices", "methods", "rlang", "scales"], "License": "GPL-2", "Suggests": ["Cairo", "covr", "dplyr", "forcats", "gridGraphics (>= 0.4-0)", "knitr", "lattice", "magick", "maps", "PASWR", "patchwork", "rmarkdown", "ragg", "testthat (>= 1.0.0)", "tidyr", "vdiffr (>= 0.3.0)", "VennDiagram"], "VignetteBuilder": "knitr", "Collate": "'add_sub.R' 'align_plots.R' 'as_grob.R' 'as_gtable.R' 'axis_canvas.R' 'cowplot.R' 'draw.R' 'get_plot_component.R' 'get_axes.R' 'get_titles.R' 'get_legend.R' 'get_panel.R' 'gtable.R' 'key_glyph.R' 'plot_grid.R' 'save.R' 'set_null_device.R' 'setup.R' 'stamp.R' 'themes.R' 'utils_ggplot2.R'", "RoxygenNote": "7.2.3", "Encoding": "UTF-8", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-7470-9261>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "cpp11": {"Package": "cpp11", "Version": "0.5.2", "Source": "Repository", "Title": "A C++11 Interface for R's C Interface", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-4777-038X\")), person(\"<PERSON>\",\"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2739-7082\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2444-4226\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Provides a header only, C++11 interface to R's C interface.  Compared to other approaches 'cpp11' strives to be safe against long jumps from the C API as well as C++ exceptions, conform to normal R function semantics and supports interaction with 'ALTREP' vectors.", "License": "MIT + file LICENSE", "URL": "https://cpp11.r-lib.org, https://github.com/r-lib/cpp11", "BugReports": "https://github.com/r-lib/cpp11/issues", "Depends": ["R (>= 4.0.0)"], "Suggests": ["bench", "brio", "callr", "cli", "covr", "decor", "desc", "ggplot2", "glue", "knitr", "lobstr", "mockery", "progress", "rmarkdown", "scales", "Rcpp", "testthat (>= 3.2.0)", "tibble", "utils", "vctrs", "withr"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/Needs/cpp11/cpp_register": "brio, cli, decor, desc, glue, tibble, vctrs", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0003-4777-038X>), <PERSON> [aut] (<https://orcid.org/0000-0002-2739-7082>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-2444-4226>), <PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "crayon": {"Package": "crayon", "Version": "1.5.3", "Source": "Repository", "Title": "Colored Terminal Output", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON><PERSON>\", \"Gaslam\", , \"<EMAIL>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "The crayon package is now superseded. Please use the 'cli' package for new projects.  Colored terminal output on terminals that support 'ANSI' color and highlight codes. It also works in 'Emacs' 'ESS'. 'ANSI' color support is automatically detected. Colors and highlighting can be combined and nested. New styles can also be created easily.  This package was inspired by the 'chalk' 'JavaScript' project.", "License": "MIT + file LICENSE", "URL": "https://r-lib.github.io/crayon/, https://github.com/r-lib/crayon", "BugReports": "https://github.com/r-lib/crayon/issues", "Imports": ["grDevices", "methods", "utils"], "Suggests": ["mockery", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "testthat", "withr"], "Config/Needs/website": "tidyverse/tidytemplate", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "Collate": "'aaa-rstudio-detect.R' 'aaaa-rematch2.R' 'aab-num-ansi-colors.R' 'aac-num-ansi-colors.R' 'ansi-256.R' 'ansi-palette.R' 'combine.R' 'string.R' 'utils.R' 'crayon-package.R' 'disposable.R' 'enc-utils.R' 'has_ansi.R' 'has_color.R' 'link.R' 'styles.R' 'machinery.R' 'parts.R' 'print.R' 'style-var.R' 'show.R' 'string_operations.R'", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON><PERSON><PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "crosstalk": {"Package": "crosstalk", "Version": "1.2.1", "Source": "Repository", "Type": "Package", "Title": "Inter-Widget Interactivity for HTML Widgets", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(family = \"jQuery Foundation\", role = \"cph\", comment = \"jQuery library and jQuery UI library\"), person(family = \"jQuery contributors\", role = c(\"ctb\", \"cph\"), comment = \"jQuery library; authors listed in inst/www/shared/jquery-AUTHORS.txt\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"Bootstrap library\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"Bootstrap library\"), person(family = \"Bootstrap contributors\", role = \"ctb\", comment = \"Bootstrap library\"), person(family = \"Twitter, Inc\", role = \"cph\", comment = \"<PERSON><PERSON><PERSON> library\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"selectize.js library\"), person(\"<PERSON><PERSON>her <PERSON>\", \"<PERSON>wal\", role = c(\"ctb\", \"cph\"), comment = \"es5-shim library\"), person(family = \"es5-shim contributors\", role = c(\"ctb\", \"cph\"), comment = \"es5-shim library\"), person(\"Denis\", \"Ineshin\", role = c(\"ctb\", \"cph\"), comment = \"ion.rangeSlider library\"), person(\"Sami\", \"Samhuri\", role = c(\"ctb\", \"cph\"), comment = \"Javascript strftime library\") )", "Description": "Provides building blocks for allowing HTML widgets to communicate with each other, with <PERSON>y or without (i.e. static .html files). Currently supports linked brushing and filtering.", "License": "MIT + file LICENSE", "Imports": ["htmltools (>= 0.3.6)", "jsonlite", "lazyeval", "R6"], "Suggests": ["shiny", "ggplot2", "testthat (>= 2.1.0)", "sass", "bslib"], "URL": "https://rstudio.github.io/crosstalk/, https://github.com/rstudio/crosstalk", "BugReports": "https://github.com/rstudio/crosstalk/issues", "RoxygenNote": "7.2.3", "Encoding": "UTF-8", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut, cre] (<https://orcid.org/0000-0002-4958-2844>), Posit Software, PBC [cph, fnd], jQuery Foundation [cph] (jQuery library and jQuery UI library), jQuery contributors [ctb, cph] (jQuery library; authors listed in inst/www/shared/jquery-AUTHORS.txt), <PERSON> [ctb] (Bootstrap library), <PERSON> [ctb] (Bootstrap library), Bootstrap contributors [ctb] (Bootstrap library), Twitter, Inc [cph] (Bootstrap library), <PERSON> [ctb, cph] (selectize.js library), <PERSON><PERSON><PERSON> [ctb, cph] (es5-shim library), es5-shim contributors [ctb, cph] (es5-shim library), <PERSON> [ctb, cph] (ion.rangeSlider library), <PERSON> [ctb, cph] (Javascript strftime library)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "curl": {"Package": "curl", "Version": "6.4.0", "Source": "Repository", "Type": "Package", "Title": "A Modern and Flexible Web Client for R", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"O<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = \"cph\"))", "Description": "Bindings to 'libcurl' <https://curl.se/libcurl/> for performing fully configurable HTTP/FTP requests where responses can be processed in memory, on disk, or streaming via the callback or connection interfaces. Some knowledge of 'libcurl' is recommended; for a more-user-friendly web client see the  'httr2' package which builds on this package with http specific tools and logic.", "License": "MIT + file LICENSE", "SystemRequirements": "libcurl (>= 7.73): libcurl-devel (rpm) or libcurl4-openssl-dev (deb)", "URL": "https://jeroen.r-universe.dev/curl", "BugReports": "https://github.com/jeroen/curl/issues", "Suggests": ["spelling", "testthat (>= 1.0.0)", "knitr", "jsonlite", "later", "rmarkdown", "httpuv (>= 1.4.4)", "webutils"], "VignetteBuilder": "knitr", "Depends": ["R (>= 3.0.0)"], "RoxygenNote": "7.3.2.9000", "Encoding": "UTF-8", "Language": "en-US", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-4035-0289>), <PERSON> [ctb], Posit Software, PBC [cph]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "data.table": {"Package": "data.table", "Version": "1.17.6", "Source": "Repository", "Title": "Extension of `data.frame`", "Depends": ["R (>= 3.3.0)"], "Imports": ["methods"], "Suggests": ["bit64 (>= 4.0.0)", "bit (>= 4.0.4)", "<PERSON><PERSON>utils", "xts", "zoo (>= 1.8-1)", "yaml", "knitr", "markdown"], "Description": "Fast aggregation of large data (e.g. 100GB in RAM), fast ordered joins, fast add/modify/delete of columns by group using no copies at all, list columns, friendly and fast character-separated-value read/write. Offers a natural and flexible syntax, for faster development.", "License": "MPL-2.0 | file LICENSE", "URL": "https://r-datatable.com, https://Rdatatable.gitlab.io/data.table, https://github.com/Rdatatable/data.table", "BugReports": "https://github.com/Rdatatable/data.table/issues", "VignetteBuilder": "knitr", "Encoding": "UTF-8", "ByteCompile": "TRUE", "Authors@R": "c( person(\"<PERSON>\",\"<PERSON>\",        role=c(\"aut\",\"cre\"), email=\"<EMAIL>\", comment = c(ORCID=\"0000-0002-2137-1391\")), person(\"<PERSON>\",\"<PERSON><PERSON>\",           role=\"aut\",          email=\"mattj<PERSON><PERSON><EMAIL>\"), person(\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",      role=\"aut\",          email=\"as<PERSON>@pm.me\"), person(\"<PERSON>\",\"<PERSON><PERSON>\",          role=\"aut\"), person(\"<PERSON>\",\"<PERSON><PERSON><PERSON>\",      role=\"aut\", comment = c(ORCID=\"0000-0003-0787-087X\")), person(\"<PERSON>\",\"Hocking\",         role=\"aut\", comment = c(ORCID=\"0000-0002-3146-0865\")), person(\"<PERSON>\",\"<PERSON><PERSON><PERSON><PERSON><PERSON>\",role=\"aut\", comment = c(ORCID=\"0000-0003-3315-8114\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\",         role=\"aut\",          email=\"i<PERSON><PERSON><PERSON>@disroot.org\",   comment = c(ORCID=\"0000-0002-0172-3812\")), person(\"<PERSON>\",\"<PERSON><PERSON><PERSON>\",      role=\"ctb\"), person(\"<PERSON>\",\"<PERSON>\",            role=\"ctb\"), person(\"<PERSON>\",\"<PERSON>noglou\",      role=\"ctb\"), person(\"<PERSON>\",\"<PERSON><PERSON>\",      role=\"ctb\"), person(\"<PERSON>\",\"<PERSON><PERSON>\",        role=\"ctb\"), person(\"<PERSON>\",\"<PERSON>rsonage\",       role=\"ctb\"), person(\"<PERSON>\",\"<PERSON>\",        role=\"ctb\"), person(\"<PERSON>n\",\"<PERSON>\",              role=\"ctb\"), person(\"Xianying\",\"Tan\",         role=\"ctb\"), person(\"Rick\",\"Saporta\",         role=\"ctb\"), person(\"Otto\",\"Seiskari\",        role=\"ctb\"), person(\"Xianghui\",\"Dong\",        role=\"ctb\"), person(\"Michel\",\"Lang\",          role=\"ctb\"), person(\"Watal\",\"Iwasaki\",        role=\"ctb\"), person(\"Seth\",\"Wenchel\",         role=\"ctb\"), person(\"Karl\",\"Broman\",          role=\"ctb\"), person(\"Tobias\",\"Schmidt\",       role=\"ctb\"), person(\"David\",\"Arenburg\",       role=\"ctb\"), person(\"Ethan\",\"Smith\",          role=\"ctb\"), person(\"Francois\",\"Cocquemas\",   role=\"ctb\"), person(\"Matthieu\",\"Gomez\",       role=\"ctb\"), person(\"Philippe\",\"Chataignon\",  role=\"ctb\"), person(\"Nello\",\"Blaser\",         role=\"ctb\"), person(\"Dmitry\",\"Selivanov\",     role=\"ctb\"), person(\"Andrey\",\"Riabushenko\",   role=\"ctb\"), person(\"Cheng\",\"Lee\",            role=\"ctb\"), person(\"Declan\",\"Groves\",        role=\"ctb\"), person(\"Daniel\",\"Possenriede\",   role=\"ctb\"), person(\"Felipe\",\"Parages\",       role=\"ctb\"), person(\"Denes\",\"Toth\",           role=\"ctb\"), person(\"Mus\",\"Yaramaz-David\",    role=\"ctb\"), person(\"Ayappan\",\"Perumal\",      role=\"ctb\"), person(\"James\",\"Sams\",           role=\"ctb\"), person(\"Martin\",\"Morgan\",        role=\"ctb\"), person(\"Michael\",\"Quinn\",        role=\"ctb\"), person(\"@javrucebo\",\"\",          role=\"ctb\"), person(\"@marc-outins\",\"\",        role=\"ctb\"), person(\"Roy\",\"Storey\",           role=\"ctb\"), person(\"Manish\",\"Saraswat\",      role=\"ctb\"), person(\"Morgan\",\"Jacob\",         role=\"ctb\"), person(\"Michael\",\"Schubmehl\",    role=\"ctb\"), person(\"Davis\",\"Vaughan\",        role=\"ctb\"), person(\"Leonardo\",\"Silvestri\",   role=\"ctb\"), person(\"Jim\",\"Hester\",           role=\"ctb\"), person(\"Anthony\",\"Damico\",       role=\"ctb\"), person(\"Sebastian\",\"Freundt\",    role=\"ctb\"), person(\"David\",\"Simons\",         role=\"ctb\"), person(\"Elliott\",\"Sales de Andrade\", role=\"ctb\"), person(\"Cole\",\"Miller\",          role=\"ctb\"), person(\"Jens Peder\",\"Meldgaard\", role=\"ctb\"), person(\"Vaclav\",\"Tlapak\",        role=\"ctb\"), person(\"Kevin\",\"Ushey\",          role=\"ctb\"), person(\"Dirk\",\"Eddelbuettel\",    role=\"ctb\"), person(\"Tony\",\"Fischetti\",       role=\"ctb\"), person(\"Ofek\",\"Shilon\",          role=\"ctb\"), person(\"Vadim\",\"Khotilovich\",    role=\"ctb\"), person(\"Hadley\",\"Wickham\",       role=\"ctb\"), person(\"Bennet\",\"Becker\",        role=\"ctb\"), person(\"Kyle\",\"Haynes\",          role=\"ctb\"), person(\"Boniface Christian\",\"Kamgang\", role=\"ctb\"), person(\"Olivier\",\"Delmarcell\",   role=\"ctb\"), person(\"Josh\",\"O'Brien\",         role=\"ctb\"), person(\"Dereck\",\"de Mezquita\",   role=\"ctb\"), person(\"Michael\",\"Czekanski\",    role=\"ctb\"), person(\"Dmitry\", \"Shemetov\",     role=\"ctb\"), person(\"Nitish\", \"Jha\",          role=\"ctb\"), person(\"Joshua\", \"Wu\",           role=\"ctb\"), person(\"Iago\", \"Giné-Vázquez\",   role=\"ctb\"), person(\"Anirban\", \"Chetia\",      role=\"ctb\"), person(\"Doris\", \"Amoakohene\",    role=\"ctb\"), person(\"Angel\", \"Feliz\",         role=\"ctb\"), person(\"Michael\",\"Young\",        role=\"ctb\"), person(\"Mark\", \"Seeto\",          role=\"ctb\"), person(\"Philippe\", \"Grosjean\",   role=\"ctb\"), person(\"Vincent\", \"Runge\",       role=\"ctb\"), person(\"Christian\", \"Wia\",       role=\"ctb\"), person(\"Elise\", \"Maigné\",        role=\"ctb\"), person(\"Vincent\", \"Rocher\",      role=\"ctb\"), person(\"Vijay\", \"Lulla\",         role=\"ctb\"), person(\"Aljaž\", \"Sluga\",         role=\"ctb\"), person(\"Bill\", \"Evans\",          role=\"ctb\") )", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-2137-1391>), <PERSON> [aut], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut] (ORCID: <https://orcid.org/0000-0003-0787-087X>), <PERSON> [aut] (ORCID: <https://orcid.org/0000-0002-3146-0865>), <PERSON> [aut] (ORCID: <https://orcid.org/0000-0003-3315-8114>), <PERSON> [aut] (ORCID: <https://orcid.org/0000-0002-0172-3812>), <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [c<PERSON>b], <PERSON><PERSON><PERSON><PERSON><PERSON> [c<PERSON><PERSON>], <PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON> [c<PERSON><PERSON>], <PERSON><PERSON> <PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON><PERSON> [c<PERSON><PERSON>], <PERSON><PERSON> <PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON> [c<PERSON>b], <PERSON> <PERSON> [c<PERSON><PERSON>], <PERSON> <PERSON><PERSON><PERSON> [c<PERSON><PERSON>], <PERSON><PERSON> [c<PERSON><PERSON>], <PERSON><PERSON> <PERSON><PERSON> [c<PERSON><PERSON>], <PERSON><PERSON> <PERSON>ramaz-David [ctb], Ayappan Perumal [ctb], James Sams [ctb], Martin Morgan [ctb], Michael Quinn [ctb], @javrucebo [ctb], @marc-outins [ctb], Roy Storey [ctb], Manish Saraswat [ctb], Morgan Jacob [ctb], Michael Schubmehl [ctb], Davis Vaughan [ctb], Leonardo Silvestri [ctb], Jim Hester [ctb], Anthony Damico [ctb], Sebastian Freundt [ctb], David Simons [ctb], Elliott Sales de Andrade [ctb], Cole Miller [ctb], Jens Peder Meldgaard [ctb], Vaclav Tlapak [ctb], Kevin Ushey [ctb], Dirk Eddelbuettel [ctb], Tony Fischetti [ctb], Ofek Shilon [ctb], Vadim Khotilovich [ctb], Hadley Wickham [ctb], Bennet Becker [ctb], Kyle Haynes [ctb], Boniface Christian Kamgang [ctb], Olivier Delmarcell [ctb], Josh O'Brien [ctb], Dereck de Mezquita [ctb], Michael Czekanski [ctb], Dmitry Shemetov [ctb], Nitish Jha [ctb], Joshua Wu [ctb], Iago Giné-Vázquez [ctb], Anirban Chetia [ctb], Doris Amoakohene [ctb], Angel Feliz [ctb], Michael Young [ctb], Mark Seeto [ctb], Philippe Grosjean [ctb], Vincent Runge [ctb], Christian Wia [ctb], Elise Maigné [ctb], Vincent Rocher [ctb], Vijay Lulla [ctb], Aljaž Sluga [ctb], Bill Evans [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "datamods": {"Package": "datamods", "Version": "1.5.3", "Source": "Repository", "Title": "<PERSON><PERSON><PERSON> to Import and Manipulate Data in 'Shiny'", "Authors@R": "c(person(given = \"<PERSON>\", family = \"Perrier\", role = c(\"aut\", \"cre\", \"cph\"), email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"aut\"), person(given = \"<PERSON><PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(given = \"<PERSON><PERSON><PERSON>\", family = \"Abeer\", role = \"aut\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\") )", "Description": "'Shiny' modules to import data into an application or 'addin' from various sources, and to manipulate them after that.", "License": "GPL-3", "URL": "https://github.com/dreamRs/datamods, https://dreamrs.github.io/datamods/", "BugReports": "https://github.com/dreamRs/datamods/issues", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "Imports": ["bslib", "classInt", "data.table", "htmltools", "phosphoricons", "reactable", "readxl", "rio", "rlang", "shiny (>= 1.5.0)", "shinyWidgets (>= 0.8.4)", "tibble", "<PERSON>ui (>= 0.3.3)", "tools", "shinybusy", "writexl"], "Suggests": ["ggplot2", "jsonlite", "knitr", "MASS", "rmarkdown", "testthat", "validate"], "VignetteBuilder": "knitr", "Depends": ["R (>= 2.10)"], "LazyData": "true", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre, cph], <PERSON> [aut], <PERSON><PERSON> [aut], <PERSON><PERSON><PERSON> [aut], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "desc": {"Package": "desc", "Version": "1.4.3", "Source": "Repository", "Title": "Manipulate DESCRIPTION Files", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"csar<PERSON>.<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-2815-0399\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Description": "Tools to read, write, create, and manipulate DESCRIPTION files.  It is intended for packages that create or manipulate other packages.", "License": "MIT + file LICENSE", "URL": "https://desc.r-lib.org/, https://github.com/r-lib/desc", "BugReports": "https://github.com/r-lib/desc/issues", "Depends": ["R (>= 3.4)"], "Imports": ["cli", "R6", "utils"], "Suggests": ["callr", "covr", "gh", "spelling", "testthat", "whoami", "withr"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.2.3", "Collate": "'assertions.R' 'authors-at-r.R' 'built.R' 'classes.R' 'collate.R' 'constants.R' 'deps.R' 'desc-package.R' 'description.R' 'encoding.R' 'find-package-root.R' 'latex.R' 'non-oo-api.R' 'package-archives.R' 'read.R' 'remotes.R' 'str.R' 'syntax_checks.R' 'urls.R' 'utils.R' 'validate.R' 'version.R'", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-2815-0399>), Posit Software, PBC [cph, fnd]", "Repository": "https://packagemanager.posit.co/cran/latest"}, "diffobj": {"Package": "diffob<PERSON>", "Version": "0.3.6", "Source": "Repository", "Type": "Package", "Title": "Diffs for R Objects", "Description": "Generate a colorized diff of two R objects for an intuitive visualization of their differences.", "Authors@R": "c( person( \"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", email=\"<EMAIL>\", role=c(\"aut\", \"cre\")), person( \"<PERSON>\", \"<PERSON>\", email=\"<EMAIL>\", role=c(\"ctb\", \"cph\"), comment=\"Original C implementation of Myers Diff Algorithm\"))", "Depends": ["R (>= 3.1.0)"], "License": "GPL-2 | GPL-3", "URL": "https://github.com/brodieG/diffobj", "BugReports": "https://github.com/brodieG/diffobj/issues", "RoxygenNote": "7.2.3", "VignetteBuilder": "knitr", "Encoding": "UTF-8", "Suggests": ["knitr", "rmarkdown"], "Collate": "'capt.R' 'options.R' 'pager.R' 'check.R' 'finalizer.R' 'misc.R' 'html.R' 'styles.R' 's4.R' 'core.R' 'diff.R' 'get.R' 'guides.R' 'hunks.R' 'layout.R' 'myerssimple.R' 'rdiff.R' 'rds.R' 'set.R' 'subset.R' 'summmary.R' 'system.R' 'text.R' 'tochar.R' 'trim.R' 'word.R'", "Imports": ["crayon (>= 1.3.2)", "tools", "methods", "utils", "stats"], "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON> [ctb, cph] (Original C implementation of Myers Diff Algorithm)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "digest": {"Package": "digest", "Version": "0.6.37", "Source": "Repository", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-6419-907X\")), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\", comment = c(ORCID = \"0000-0002-7579-5165\")), person(\"<PERSON>\", \"<PERSON>ek\", role=\"ctb\", comment = c(ORCID = \"0000-0003-2297-1732\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"ctb\", comment = c(ORCID = \"0000-0001-5180-0567\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\", comment = c(ORCID = \"0000-0001-6786-5453\")), person(\"Thierry\", \"Onkelinx\", role=\"ctb\", comment = c(ORCID = \"0000-0001-8804-4216\")), person(\"Michel\", \"Lang\", role=\"ctb\", comment = c(ORCID = \"0000-0001-9754-0393\")), person(\"Viliam\", \"Simko\", role=\"ctb\"), person(\"Kurt\", \"Hornik\", role=\"ctb\", comment = c(ORCID = \"0000-0003-4198-9911\")), person(\"Radford\", \"Neal\", role=\"ctb\", comment = c(ORCID = \"0000-0002-2473-3407\")), person(\"Kendon\", \"Bell\", role=\"ctb\", comment = c(ORCID = \"0000-0002-9093-8312\")), person(\"Matthew\", \"de Queljoe\", role=\"ctb\"), person(\"Dmitry\", \"Selivanov\", role=\"ctb\"), person(\"Ion\", \"Suruceanu\", role=\"ctb\"), person(\"Bill\", \"Denney\", role=\"ctb\"), person(\"Dirk\", \"Schumacher\", role=\"ctb\"), person(\"András\", \"Svraka\", role=\"ctb\"), person(\"Sergey\", \"Fedorov\", role=\"ctb\"), person(\"Will\", \"Landau\", role=\"ctb\", comment = c(ORCID = \"0000-0003-1878-3253\")), person(\"Floris\", \"Vanderhaeghe\", role=\"ctb\", comment = c(ORCID = \"0000-0002-6378-6229\")), person(\"Kevin\", \"Tappe\", role=\"ctb\"), person(\"Harris\", \"McGehee\", role=\"ctb\"), person(\"Tim\", \"Mastny\", role=\"ctb\"), person(\"Aaron\", \"Peikert\", role=\"ctb\", comment = c(ORCID = \"0000-0001-7813-818X\")), person(\"Mark\", \"van der Loo\", role=\"ctb\", comment = c(ORCID = \"0000-0002-9807-4686\")), person(\"Chris\", \"Muir\", role=\"ctb\", comment = c(ORCID = \"0000-0003-2555-3878\")), person(\"Moritz\", \"Beller\", role=\"ctb\", comment = c(ORCID = \"0000-0003-4852-0526\")), person(\"Sebastian\", \"Campbell\", role=\"ctb\"), person(\"Winston\", \"Chang\", role=\"ctb\", comment = c(ORCID = \"0000-0002-1576-2126\")), person(\"Dean\", \"Attali\", role=\"ctb\", comment = c(ORCID = \"0000-0002-5645-3493\")), person(\"Michael\", \"Chirico\", role=\"ctb\", comment = c(ORCID = \"0000-0003-0787-087X\")), person(\"Kevin\", \"Ushey\", role=\"ctb\"))", "Date": "2024-08-19", "Title": "Create Compact Hash Digests of R Objects", "Description": "Implementation of a function 'digest()' for the creation of hash digests of arbitrary R objects (using the 'md5', 'sha-1', 'sha-256', 'crc32', 'xxhash', 'murmurhash', 'spookyhash', 'blake3', 'crc32c', 'xxh3_64', and 'xxh3_128' algorithms) permitting easy comparison of R language objects, as well as functions such as'hmac()' to create hash-based message authentication code. Please note that this package is not meant to be deployed for cryptographic purposes for which more comprehensive (and widely tested) libraries such as 'OpenSSL' should be used.", "URL": "https://github.com/eddelbuettel/digest, https://dirk.eddelbuettel.com/code/digest.html", "BugReports": "https://github.com/eddelbuettel/digest/issues", "Depends": ["R (>= 3.3.0)"], "Imports": ["utils"], "License": "GPL (>= 2)", "Suggests": ["tinytest", "simplermarkdown"], "VignetteBuilder": "simplermarkdown", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0001-6419-907X>), <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0002-7579-5165>), <PERSON> [ctb] (<https://orcid.org/0000-0003-2297-1732>), <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0001-5180-0567>), <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0001-6786-5453>), <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0001-8804-4216>), <PERSON> [ctb] (<https://orcid.org/0000-0001-9754-0393>), <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0003-4198-9911>), <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-2473-3407>), <PERSON>don <PERSON> [ctb] (<https://or<PERSON>.org/0000-0002-9093-8312>), <PERSON> de <PERSON>l<PERSON>e [ctb], <PERSON> <PERSON><PERSON><PERSON> [ctb], <PERSON> <PERSON><PERSON><PERSON> [ctb], <PERSON> <PERSON><PERSON> [ctb], <PERSON> <PERSON> [ctb], Andr<PERSON> <PERSON>v<PERSON><PERSON> [ctb], <PERSON> <PERSON><PERSON>v [ctb], Will Landau [ctb] (<https://orcid.org/0000-0003-1878-3253>), Floris Vanderhaeghe [ctb] (<https://orcid.org/0000-0002-6378-6229>), Kevin Tappe [ctb], Harris McGehee [ctb], Tim Mastny [ctb], Aaron Peikert [ctb] (<https://orcid.org/0000-0001-7813-818X>), Mark van der Loo [ctb] (<https://orcid.org/0000-0002-9807-4686>), Chris Muir [ctb] (<https://orcid.org/0000-0003-2555-3878>), Moritz Beller [ctb] (<https://orcid.org/0000-0003-4852-0526>), Sebastian Campbell [ctb], Winston Chang [ctb] (<https://orcid.org/0000-0002-1576-2126>), Dean Attali [ctb] (<https://orcid.org/0000-0002-5645-3493>), Michael Chirico [ctb] (<https://orcid.org/0000-0003-0787-087X>), Kevin Ushey [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "doBy": {"Package": "doBy", "Version": "4.6.27", "Source": "Repository", "Title": "Groupwise Statistics, LSmeans, Linear Estimates, Utilities", "Authors@R": "c( person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cph\")), person(given = \"<PERSON><PERSON><PERSON>\", family = \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\", \"cph\")) )", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Description": "Utility package containing: 1) Facilities for working with grouped data: 'do' something to data stratified 'by' some variables. 2) LSmeans (least-squares means), general linear estimates. 3) Restrict functions to a smaller domain. 4) Miscellaneous other utilities.", "Encoding": "UTF-8", "VignetteBuilder": "knitr", "LazyData": "true", "LazyDataCompression": "xz", "URL": "https://github.com/hojsgaard/doBy", "License": "GPL (>= 2)", "Depends": ["R (>= 4.2.0)", "methods"], "Imports": ["boot", "broom", "cowplot", "Deriv", "dplyr", "ggplot2", "MASS", "Matrix", "modelr", "microbenchmark", "rlang", "tibble", "tidyr"], "Suggests": ["geepack", "knitr", "lme4", "markdown", "multcomp", "pbkrtest (>= 0.5.2)", "survival", "testthat (>= 2.1.0)"], "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cph], <PERSON><PERSON><PERSON> [aut, cre, cph]", "Repository": "CRAN"}, "doParallel": {"Package": "doP<PERSON>llel", "Version": "1.0.17", "Source": "Repository", "Type": "Package", "Title": "For<PERSON> Para<PERSON>l Adaptor for the 'parallel' Package", "Authors@R": "c(person(\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON>\", role=\"cre\", email=\"<EMAIL>\"), person(\"Microsoft\", \"Corporation\", role=c(\"aut\", \"cph\")), person(\"<PERSON>\", \"<PERSON>\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"))", "Description": "Provides a parallel backend for the %dopar% function using the parallel package.", "Depends": ["R (>= 2.14.0)", "foreach (>= 1.2.0)", "iterators (>= 1.0.0)", "parallel", "utils"], "Suggests": ["caret", "mlbench", "rpart", "RUnit"], "Enhances": ["compiler"], "License": "GPL-2", "URL": "https://github.com/RevolutionAnalytics/doparallel", "BugReports": "https://github.com/RevolutionAnalytics/doparallel/issues", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON><PERSON> [cre], Microsoft Corporation [aut, cph], <PERSON> [aut], <PERSON> [ctb]", "Maintainer": "Folashade Daniel <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "dplyr": {"Package": "dplyr", "Version": "1.1.4", "Source": "Repository", "Type": "Package", "Title": "A Grammar of Data Manipulation", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2444-4226\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0003-4777-038X\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A fast, consistent tool for working with data frame like objects, both in memory and out of memory.", "License": "MIT + file LICENSE", "URL": "https://dplyr.tidyverse.org, https://github.com/tidyverse/dplyr", "BugReports": "https://github.com/tidyverse/dplyr/issues", "Depends": ["R (>= 3.5.0)"], "Imports": ["cli (>= 3.4.0)", "generics", "glue (>= 1.3.2)", "lifecycle (>= 1.0.3)", "magrittr (>= 1.5)", "methods", "pillar (>= 1.9.0)", "R6", "rlang (>= 1.1.0)", "tibble (>= 3.2.0)", "tidyselect (>= 1.2.0)", "utils", "vctrs (>= 0.6.4)"], "Suggests": ["bench", "broom", "callr", "covr", "DBI", "dbplyr (>= 2.2.1)", "ggplot2", "knitr", "<PERSON><PERSON>", "lobstr", "microbenchmark", "nycflights13", "purrr", "rmarkdown", "RMySQL", "RPostgreSQL", "RSQLite", "stringi (>= 1.7.6)", "testthat (>= 3.1.5)", "tidyr (>= 1.3.0)", "withr"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse, shiny, pkgdown, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.2.3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0003-4757-117X>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-2444-4226>), <PERSON> [aut], <PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-1416-3412>), <PERSON> [aut] (<https://orcid.org/0000-0003-4777-038X>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "e1071": {"Package": "e1071", "Version": "1.7-16", "Source": "Repository", "Title": "Misc Functions of the Department of Statistics, Probability Theory Group (Formerly: E1071), TU Wien", "Imports": ["graphics", "grDevices", "class", "stats", "methods", "utils", "proxy"], "Suggests": ["cluster", "mlbench", "nnet", "randomForest", "rpart", "SparseM", "xtable", "Matrix", "MASS", "slam"], "Authors@R": "c(person(given = \"<PERSON>\", family = \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<PERSON><PERSON>@R-project.org\", comment = c(ORCID = \"0000-0002-5196-3048\")),     person(given = \"Evgenia\", family = \"<PERSON><PERSON><PERSON>\", role = c(\"aut\",\"cph\")), person(given = \"<PERSON>\", family = \"<PERSON><PERSON>\", role = \"aut\", email = \"<PERSON>.Horn<PERSON>@R-project.org\", comment = c(ORCID = \"0000-0003-4198-9911\")), person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON>\", role = \"aut\"), person(given = \"<PERSON><PERSON>-<PERSON>\", family = \"<PERSON>\", role = c(\"ctb\",\"cph\"), comment = \"libsvm C++-code\"), person(given = \"<PERSON><PERSON>-<PERSON>\", family = \"<PERSON>\", role = c(\"ctb\",\"cph\"), comment = \"libsvm C++-code\"))", "Description": "Functions for latent class analysis, short time Fourier transform, fuzzy clustering, support vector machines, shortest path computation, bagged clustering, naive <PERSON><PERSON> classifier, generalized k-nearest neighbour ...", "License": "GPL-2 | GPL-3", "LazyLoad": "yes", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-5196-3048>), <PERSON><PERSON><PERSON><PERSON> [aut, cph], <PERSON> [aut] (<https://orcid.org/0000-0003-4198-9911>), <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON><PERSON> [ctb, cph] (libsvm C++-code), <PERSON><PERSON><PERSON><PERSON> [ctb, cph] (libsvm C++-code)", "Maintainer": "<PERSON> <<PERSON><PERSON>@R-project.org>", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "evaluate": {"Package": "evaluate", "Version": "1.0.4", "Source": "Repository", "Type": "Package", "Title": "Parsing and Evaluation Tools that Provide More Details than the Default", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0003-0645-5666\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Parsing and evaluation tools that make it easy to recreate the command line behaviour of R.", "License": "MIT + file LICENSE", "URL": "https://evaluate.r-lib.org/, https://github.com/r-lib/evaluate", "BugReports": "https://github.com/r-lib/evaluate/issues", "Depends": ["R (>= 3.6.0)"], "Suggests": ["callr", "covr", "ggplot2 (>= 3.3.6)", "lattice", "methods", "pkgload", "ragg (>= 1.4.0)", "rlang (>= 1.1.5)", "knitr", "testthat (>= 3.0.0)", "withr"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0003-0645-5666>), <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "expm": {"Package": "expm", "Version": "1.0-0", "Source": "Repository", "Type": "Package", "Title": "Matrix Exponential, Log, 'etc'", "Date": "2024-08-19", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\",\"cre\"), email=\"<EMAIL>\", comment = c(ORCID = \"0000-0002-8685-9910\")) , person(\"<PERSON>\",\"Dutang\", role = \"aut\", comment = c(ORCID = \"0000-0001-6732-1501\")) , person(\"<PERSON>\",   \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-9315-5719\")) , person(\"<PERSON>\",   \"<PERSON>\",      role = \"ctb\", comment = \"cosmetic clean up, in svn r42\") , person(\"<PERSON>\",     \"<PERSON><PERSON>\",      role = \"ctb\", comment = \"expm(method= \\\"PadeO\\\" and \\\"TaylorO\\\")\") , person(\"<PERSON>\",    \"<PERSON><PERSON><PERSON><PERSON>\",    role = \"ctb\", comment = \"expm(method= \\\"PadeO\\\" and \\\"TaylorO\\\")\") , person(\"<PERSON>\",   \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = \"\\\"Higham08*\\\" methods, see ?expm.Higham08...\") )", "Contact": "<EMAIL>", "Description": "Computation of the matrix exponential, logarithm, sqrt, and related quantities, using traditional and modern methods.", "Depends": ["Matrix"], "Imports": ["methods"], "Suggests": ["RColorBrewer", "sfsmisc", "Rmpfr"], "BuildResaveData": "no", "License": "GPL (>= 2)", "URL": "https://R-Forge.R-project.org/projects/expm/", "BugReports": "https://R-forge.R-project.org/tracker/?atid=472&group_id=107", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-8685-9910>), <PERSON> [aut] (<https://orcid.org/0000-0001-6732-1501>), <PERSON> [aut] (<https://orcid.org/0000-0002-9315-5719>), <PERSON> [ctb] (cosmetic clean up, in svn r42), <PERSON> [ctb] (expm(method= \"PadeO\" and \"TaylorO\")), <PERSON> [ctb] (expm(method= \"PadeO\" and \"TaylorO\")), <PERSON> [ctb] (\"Higham08*\" methods, see ?expm.Higham08...)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "farver": {"Package": "farver", "Version": "2.1.2", "Source": "Repository", "Type": "Package", "Title": "High Performance Colour Space Manipulation", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"cre\", \"aut\"), comment = c(ORCID = \"0000-0002-5147-4711\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = \"Author of the ColorSpace C++ library\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2444-4226\")), person(\"Posit, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "The encoding of colour can be handled in many different ways, using different colour spaces. As different colour spaces have different uses, efficient conversion between these representations are important. The 'farver' package provides a set of functions that gives access to very fast colour space conversion and comparisons implemented in C++, and offers speed improvements over the 'convertColor' function in the 'grDevices' package.", "License": "MIT + file LICENSE", "URL": "https://farver.data-imaginist.com, https://github.com/thomasp85/farver", "BugReports": "https://github.com/thomasp85/farver/issues", "Suggests": ["covr", "testthat (>= 3.0.0)"], "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "NeedsCompilation": "yes", "Author": "<PERSON> [cre, aut] (<https://orcid.org/0000-0002-5147-4711>), <PERSON><PERSON><PERSON> [aut] (Author of the ColorSpace C++ library), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-2444-4226>), <PERSON><PERSON>t, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "fastmap": {"Package": "fastmap", "Version": "1.2.0", "Source": "Repository", "Title": "Fast Data Structures", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(given = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(given = \"Tessil\", role = \"cph\", comment = \"hopscotch_map library\") )", "Description": "Fast implementation of data structures, including a key-value store, stack, and queue. Environments are commonly used as key-value stores in R, but every time a new key is used, it is added to R's global symbol table, causing a small amount of memory leakage. This can be problematic in cases where many different keys are used. Fastmap avoids this memory leak issue by implementing the map using data structures in C++.", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "Suggests": ["testthat (>= 2.1.1)"], "URL": "https://r-lib.github.io/fastmap/, https://github.com/r-lib/fastmap", "BugReports": "https://github.com/r-lib/fastmap/issues", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd], Tessil [cph] (hopscotch_map library)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "fontawesome": {"Package": "fontawesome", "Version": "0.5.3", "Source": "Repository", "Type": "Package", "Title": "Easily Work with 'Font Awesome' Icons", "Description": "Easily and flexibly insert 'Font Awesome' icons into 'R Markdown' documents and 'Shiny' apps. These icons can be inserted into HTML content through inline 'SVG' tags or 'i' tags. There is also a utility function for exporting 'Font Awesome' icons as 'PNG' images for those situations where raster graphics are needed.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-3925-190X\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"ctb\", comment = c(ORCID = \"0000-0003-4474-2498\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Font-Awesome font\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "License": "MIT + file LICENSE", "URL": "https://github.com/rstudio/fontawesome, https://rstudio.github.io/fontawesome/", "BugReports": "https://github.com/rstudio/fontawesome/issues", "Encoding": "UTF-8", "ByteCompile": "true", "RoxygenNote": "7.3.2", "Depends": ["R (>= 3.3.0)"], "Imports": ["rlang (>= 1.0.6)", "htmltools (>= *******)"], "Suggests": ["covr", "dplyr (>= 1.0.8)", "gt (>= 0.9.0)", "knitr (>= 1.31)", "testthat (>= 3.0.0)", "rsvg"], "Config/testthat/edition": "3", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0003-3925-190X>), <PERSON> [ctb] (<https://orcid.org/0000-0003-4474-2498>), <PERSON> [ctb], <PERSON> [ctb, cph] (Font-Awesome font), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "forcats": {"Package": "forcats", "Version": "1.0.0", "Source": "Repository", "Title": "Tools for Working with Categorical Variables (Factors)", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"RStudio\", role = c(\"cph\", \"fnd\")) )", "Description": "Helpers for reordering factor levels (including moving specified levels to front, ordering by first appearance, reversing, and randomly shuffling), and tools for modifying factor levels (including collapsing rare levels into other, 'anonymising', and manually 'recoding').", "License": "MIT + file LICENSE", "URL": "https://forcats.tidyverse.org/, https://github.com/tidyverse/forcats", "BugReports": "https://github.com/tidyverse/forcats/issues", "Depends": ["R (>= 3.4)"], "Imports": ["cli (>= 3.4.0)", "glue", "lifecycle", "magrit<PERSON>", "rlang (>= 1.0.0)", "tibble"], "Suggests": ["covr", "dplyr", "ggplot2", "knitr", "readr", "rmarkdown", "testthat (>= 3.0.0)", "withr"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON>tu<PERSON> [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "foreach": {"Package": "foreach", "Version": "1.5.2", "Source": "Repository", "Type": "Package", "Title": "Provides Foreach Looping Construct", "Authors@R": "c(person(\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON>\", role=\"cre\", email=\"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"Microsoft\", role=c(\"aut\", \"cph\")), person(\"<PERSON>\", \"<PERSON>\", role=\"aut\"))", "Description": "Support for the foreach looping construct.  Foreach is an idiom that allows for iterating over elements in a collection, without the use of an explicit loop counter.  This package in particular is intended to be used for its return value, rather than for its side effects.  In that sense, it is similar to the standard lapply function, but doesn't require the evaluation of a function.  Using foreach without side effects also facilitates executing the loop in parallel.", "License": "Apache License (== 2.0)", "URL": "https://github.com/RevolutionAnalytics/foreach", "BugReports": "https://github.com/RevolutionAnalytics/foreach/issues", "Depends": ["R (>= 2.5.0)"], "Imports": ["codetools", "utils", "iterators"], "Suggests": ["randomForest", "doMC", "doP<PERSON>llel", "testthat", "knitr", "rmarkdown"], "VignetteBuilder": "knitr", "RoxygenNote": "7.1.1", "Collate": "'callCombine.R' 'foreach.R' 'do.R' 'foreach-ext.R' 'foreach-pkg.R' 'getDoPar.R' 'getDoSeq.R' 'getsyms.R' 'iter.R' 'nextElem.R' 'onLoad.R' 'setDoPar.R' 'setDoSeq.R' 'times.R' 'utils.R'", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON><PERSON> [cre], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [aut, cph], <PERSON> [aut]", "Maintainer": "Folashade Daniel <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "fs": {"Package": "fs", "Version": "1.6.6", "Source": "Repository", "Title": "Cross-Platform File System Operations Based on 'libuv'", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"libuv project contributors\", role = \"cph\", comment = \"libuv library\"), person(\"Joyent, Inc. and other Node contributors\", role = \"cph\", comment = \"libuv library\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A cross-platform interface to file system operations, built on top of the 'libuv' C library.", "License": "MIT + file LICENSE", "URL": "https://fs.r-lib.org, https://github.com/r-lib/fs", "BugReports": "https://github.com/r-lib/fs/issues", "Depends": ["R (>= 3.6)"], "Imports": ["methods"], "Suggests": ["covr", "crayon", "knitr", "pillar (>= 1.0.0)", "rmarkdown", "spelling", "testthat (>= 3.0.0)", "tibble (>= 1.1.0)", "vctrs (>= 0.3.0)", "withr"], "VignetteBuilder": "knitr", "ByteCompile": "true", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Copyright": "file COPYRIGHTS", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.2.3", "SystemRequirements": "GNU make", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [aut, cre], libuv project contributors [cph] (libuv library), Joyent, Inc. and other Node contributors [cph] (libuv library), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "generics": {"Package": "generics", "Version": "0.1.4", "Source": "Repository", "Title": "Common S3 Generics not Provided by Base R Methods Related to Model Fitting", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"https://ror.org/03wc8by49\")) )", "Description": "In order to reduce potential package dependencies and conflicts, generics provides a number of commonly used S3 generics.", "License": "MIT + file LICENSE", "URL": "https://generics.r-lib.org, https://github.com/r-lib/generics", "BugReports": "https://github.com/r-lib/generics/issues", "Depends": ["R (>= 3.6)"], "Imports": ["methods"], "Suggests": ["covr", "pkgload", "testthat (>= 3.0.0)", "tibble", "withr"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0003-4757-117X>), <PERSON> [aut], <PERSON> [aut], Posit Software, PBC [cph, fnd] (ROR: <https://ror.org/03wc8by49>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "ggResidpanel": {"Package": "ggResidpanel", "Version": "0.3.0", "Source": "Repository", "Type": "Package", "Title": "Panels and Interactive Versions of Diagnostic Plots using 'ggplot2'", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = c(\"aut\")))", "Description": "An R package for creating panels of diagnostic plots for residuals from a model  using ggplot2 and plotly to analyze residuals and model assumptions from a variety of  viewpoints. It also allows for the creation of interactive diagnostic plots.", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "LazyData": "true", "URL": "https://goodekat.github.io/ggResidpanel/", "Imports": ["cowplot", "ggplot2", "grDevices", "grid", "MASS", "plotly", "qqplotr", "stats", "stringr"], "RoxygenNote": "6.1.1", "Suggests": ["dplyr", "forcats", "knitr", "lme4", "lmerTest", "nlme", "randomForest", "rmarkdown", "rpart", "testthat", "vdiffr", "<PERSON><PERSON><PERSON><PERSON>"], "VignetteBuilder": "knitr", "Depends": ["R (>= 3.0.0)"], "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON> [aut]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "ggplot2": {"Package": "ggplot2", "Version": "3.5.2", "Source": "Repository", "Title": "Create Elegant Data Visualisations Using the Grammar of Graphics", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\", comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-1576-2126\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-5147-4711\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-7470-9261\")), person(\"<PERSON>\", \"Woo\", role = \"aut\", comment = c(ORCID = \"0000-0002-5125-4188\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-3385-7233\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-9415-4582\")), person(\"Teun\", \"van den <PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-9335-7468\")), person(\"Posit, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A system for 'declaratively' creating graphics, based on \"The Grammar of Graphics\". You provide the data, tell 'ggplot2' how to map variables to aesthetics, what graphical primitives to use, and it takes care of the details.", "License": "MIT + file LICENSE", "URL": "https://ggplot2.tidyverse.org, https://github.com/tidyverse/ggplot2", "BugReports": "https://github.com/tidyverse/ggplot2/issues", "Depends": ["R (>= 3.5)"], "Imports": ["cli", "glue", "grDevices", "grid", "gtable (>= 0.1.1)", "isoband", "lifecycle (> 1.0.1)", "MASS", "mgcv", "rlang (>= 1.1.0)", "scales (>= 1.3.0)", "stats", "tibble", "vctrs (>= 0.6.0)", "withr (>= 2.5.0)"], "Suggests": ["covr", "dplyr", "ggplot2movies", "hex<PERSON>", "Hmisc", "knitr", "map<PERSON><PERSON>j", "maps", "multcomp", "munsell", "nlme", "profvis", "quantreg", "ragg (>= 1.2.6)", "RColorBrewer", "rmarkdown", "rpart", "sf (>= 0.7-3)", "svglite (>= 2.1.2)", "testthat (>= 3.1.2)", "vdiffr (>= 1.0.6)", "xml2"], "Enhances": ["sp"], "VignetteBuilder": "knitr", "Config/Needs/website": "ggtext, tidyr, forcats, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.3.2", "Collate": "'ggproto.R' 'ggplot-global.R' 'aaa-.R' 'aes-colour-fill-alpha.R' 'aes-evaluation.R' 'aes-group-order.R' 'aes-linetype-size-shape.R' 'aes-position.R' 'compat-plyr.R' 'utilities.R' 'aes.R' 'utilities-checks.R' 'legend-draw.R' 'geom-.R' 'annotation-custom.R' 'annotation-logticks.R' 'geom-polygon.R' 'geom-map.R' 'annotation-map.R' 'geom-raster.R' 'annotation-raster.R' 'annotation.R' 'autolayer.R' 'autoplot.R' 'axis-secondary.R' 'backports.R' 'bench.R' 'bin.R' 'coord-.R' 'coord-cartesian-.R' 'coord-fixed.R' 'coord-flip.R' 'coord-map.R' 'coord-munch.R' 'coord-polar.R' 'coord-quickmap.R' 'coord-radial.R' 'coord-sf.R' 'coord-transform.R' 'data.R' 'docs_layer.R' 'facet-.R' 'facet-grid-.R' 'facet-null.R' 'facet-wrap.R' 'fortify-lm.R' 'fortify-map.R' 'fortify-multcomp.R' 'fortify-spatial.R' 'fortify.R' 'stat-.R' 'geom-abline.R' 'geom-rect.R' 'geom-bar.R' 'geom-bin2d.R' 'geom-blank.R' 'geom-boxplot.R' 'geom-col.R' 'geom-path.R' 'geom-contour.R' 'geom-count.R' 'geom-crossbar.R' 'geom-segment.R' 'geom-curve.R' 'geom-defaults.R' 'geom-ribbon.R' 'geom-density.R' 'geom-density2d.R' 'geom-dotplot.R' 'geom-errorbar.R' 'geom-errorbarh.R' 'geom-freqpoly.R' 'geom-function.R' 'geom-hex.R' 'geom-histogram.R' 'geom-hline.R' 'geom-jitter.R' 'geom-label.R' 'geom-linerange.R' 'geom-point.R' 'geom-pointrange.R' 'geom-quantile.R' 'geom-rug.R' 'geom-sf.R' 'geom-smooth.R' 'geom-spoke.R' 'geom-text.R' 'geom-tile.R' 'geom-violin.R' 'geom-vline.R' 'ggplot2-package.R' 'grob-absolute.R' 'grob-dotstack.R' 'grob-null.R' 'grouping.R' 'theme-elements.R' 'guide-.R' 'guide-axis.R' 'guide-axis-logticks.R' 'guide-axis-stack.R' 'guide-axis-theta.R' 'guide-legend.R' 'guide-bins.R' 'guide-colorbar.R' 'guide-colorsteps.R' 'guide-custom.R' 'layer.R' 'guide-none.R' 'guide-old.R' 'guides-.R' 'guides-grid.R' 'hexbin.R' 'import-standalone-obj-type.R' 'import-standalone-types-check.R' 'labeller.R' 'labels.R' 'layer-sf.R' 'layout.R' 'limits.R' 'margins.R' 'performance.R' 'plot-build.R' 'plot-construction.R' 'plot-last.R' 'plot.R' 'position-.R' 'position-collide.R' 'position-dodge.R' 'position-dodge2.R' 'position-identity.R' 'position-jitter.R' 'position-jitterdodge.R' 'position-nudge.R' 'position-stack.R' 'quick-plot.R' 'reshape-add-margins.R' 'save.R' 'scale-.R' 'scale-alpha.R' 'scale-binned.R' 'scale-brewer.R' 'scale-colour.R' 'scale-continuous.R' 'scale-date.R' 'scale-discrete-.R' 'scale-expansion.R' 'scale-gradient.R' 'scale-grey.R' 'scale-hue.R' 'scale-identity.R' 'scale-linetype.R' 'scale-linewidth.R' 'scale-manual.R' 'scale-shape.R' 'scale-size.R' 'scale-steps.R' 'scale-type.R' 'scale-view.R' 'scale-viridis.R' 'scales-.R' 'stat-align.R' 'stat-bin.R' 'stat-bin2d.R' 'stat-bindot.R' 'stat-binhex.R' 'stat-boxplot.R' 'stat-contour.R' 'stat-count.R' 'stat-density-2d.R' 'stat-density.R' 'stat-ecdf.R' 'stat-ellipse.R' 'stat-function.R' 'stat-identity.R' 'stat-qq-line.R' 'stat-qq.R' 'stat-quantilemethods.R' 'stat-sf-coordinates.R' 'stat-sf.R' 'stat-smooth-methods.R' 'stat-smooth.R' 'stat-sum.R' 'stat-summary-2d.R' 'stat-summary-bin.R' 'stat-summary-hex.R' 'stat-summary.R' 'stat-unique.R' 'stat-ydensity.R' 'summarise-plot.R' 'summary.R' 'theme.R' 'theme-defaults.R' 'theme-current.R' 'utilities-break.R' 'utilities-grid.R' 'utilities-help.R' 'utilities-matrix.R' 'utilities-patterns.R' 'utilities-resolution.R' 'utilities-tidy-eval.R' 'zxx.R' 'zzz.R'", "NeedsCompilation": "no", "Author": "<PERSON> [aut] (<https://orcid.org/0000-0003-4757-117X>), <PERSON> [aut] (<https://orcid.org/0000-0002-1576-2126>), <PERSON> [aut], <PERSON> [aut, cre] (<https://orcid.org/0000-0002-5147-4711>), <PERSON><PERSON><PERSON> [aut], <PERSON> [aut] (<https://orcid.org/0000-0002-7470-9261>), <PERSON> [aut] (<https://orcid.org/0000-0002-5125-4188>), <PERSON><PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-3385-7233>), <PERSON> [aut] (<https://orcid.org/0000-0002-9415-4582>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-9335-7468>), <PERSON><PERSON><PERSON>, <PERSON><PERSON> [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "ggpubr": {"Package": "ggpubr", "Version": "0.6.0", "Source": "Repository", "Type": "Package", "Title": "'ggplot2' Based Publication Ready Plots", "Date": "2023-02-05", "Authors@R": "c( person(\"<PERSON><PERSON>uka<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"))", "Description": "The 'ggplot2' package is excellent and flexible for elegant data visualization in R. However the default generated plots requires some formatting before we can send them for publication. Furthermore, to customize a 'ggplot', the syntax is opaque and this raises the level of difficulty for researchers with no advanced R programming skills. 'ggpubr' provides some easy-to-use functions for creating and customizing 'ggplot2'- based publication ready plots.", "License": "GPL (>= 2)", "LazyData": "TRUE", "Encoding": "UTF-8", "Depends": ["R (>= 3.1.0)", "ggplot2 (>= 3.4.0)"], "Imports": ["ggrepel (>= 0.9.2)", "grid", "ggsci", "stats", "utils", "tidyr (>= 1.3.0)", "purrr", "dplyr (>= 0.7.1)", "cowplot (>= 1.1.1)", "ggsignif", "scales", "gridExtra", "glue", "polynom", "rlang (>= 0.4.6)", "rstatix (>= 0.7.2)", "tibble", "magrit<PERSON>"], "Suggests": ["grDevices", "knitr", "RColorBrewer", "gtable", "testthat"], "URL": "https://rpkgs.datanovia.com/ggpubr/", "BugReports": "https://github.com/kassambara/ggpubr/issues", "RoxygenNote": "7.2.3", "Collate": "'utilities_color.R' 'utilities_base.R' 'desc_statby.R' 'utilities.R' 'add_summary.R' 'annotate_figure.R' 'as_ggplot.R' 'as_npc.R' 'axis_scale.R' 'background_image.R' 'bgcolor.R' 'border.R' 'compare_means.R' 'create_aes.R' 'diff_express.R' 'facet.R' 'font.R' 'gene_citation.R' 'gene_expression.R' 'geom_bracket.R' 'geom_exec.R' 'utils-aes.R' 'utils_stat_test_label.R' 'geom_pwc.R' 'get_breaks.R' 'get_coord.R' 'get_legend.R' 'get_palette.R' 'ggadd.R' 'ggadjust_pvalue.R' 'ggarrange.R' 'ggballoonplot.R' 'ggpar.R' 'ggbarplot.R' 'ggboxplot.R' 'ggdensity.R' 'ggpie.R' 'ggdonutchart.R' 'stat_conf_ellipse.R' 'stat_chull.R' 'ggdotchart.R' 'ggdotplot.R' 'ggecdf.R' 'ggerrorplot.R' 'ggexport.R' 'gghistogram.R' 'ggline.R' 'ggmaplot.R' 'ggpaired.R' 'ggparagraph.R' 'ggpubr-package.R' 'ggpubr_args.R' 'ggpubr_options.R' 'ggqqplot.R' 'utilities_label.R' 'stat_cor.R' 'stat_stars.R' 'ggscatter.R' 'ggscatterhist.R' 'ggstripchart.R' 'ggsummarystats.R' 'ggtext.R' 'ggtexttable.R' 'ggviolin.R' 'gradient_color.R' 'grids.R' 'npc_to_data_coord.R' 'reexports.R' 'rotate.R' 'rotate_axis_text.R' 'rremove.R' 'set_palette.R' 'show_line_types.R' 'show_point_shapes.R' 'stat_anova_test.R' 'stat_central_tendency.R' 'stat_compare_means.R' 'stat_friedman_test.R' 'stat_kruskal_test.R' 'stat_mean.R' 'stat_overlay_normal_density.R' 'stat_pvalue_manual.R' 'stat_regline_equation.R' 'stat_welch_anova_test.R' 'text_grob.R' 'theme_pubr.R' 'theme_transparent.R' 'utils-geom-signif.R' 'utils-pipe.R' 'utils-tidyr.R'", "NeedsCompilation": "no", "Author": "Albouka<PERSON> [aut, cre]", "Maintainer": "Alboukadel Kassambara <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "ggrepel": {"Package": "ggrepel", "Version": "0.9.6", "Source": "Repository", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<PERSON><PERSON><PERSON><PERSON>@gmail.com\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-2843-6370\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-3915-0618\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-9409-9405\")), person(\"Trung Kien\", \"Dang\", role = \"ctb\", comment = c(ORCID = \"0000-0001-7562-6495\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0003-4920-3880\")), person(\"<PERSON><PERSON> N\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0003-1458-7108\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-0450-8181\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0003-4474-2498\")), person(\"Yutani\", \"Hiroaki\", role = \"ctb\"), person(\"<PERSON>\", \"Gramme\", role = \"ctb\"), person(\"Amir Masoud\", \"Abdol\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0003-0299-5825\")), person(\"Robrecht\", \"Cannoodt\", role = \"ctb\", comment = c(ORCID = \"0000-0003-3641-729X\")), person(\"Michał\", \"Krassowski\", role = \"ctb\", comment = c(ORCID = \"0000-0002-9638-7785\")), person(\"Michael\", \"Chirico\", role = \"ctb\", comment = c(ORCID = \"0000-0003-0787-087X\")), person(\"Pedro\", \"Aphalo\", role = \"ctb\", comment = c(ORCID = \"0000-0003-3385-972X\")), person(\"Francis\", \"Barton\", role = \"ctb\") )", "Title": "Automatically Position Non-Overlapping Text Labels with 'ggplot2'", "Description": "Provides text and label geoms for 'ggplot2' that help to avoid overlapping text labels. Labels repel away from each other and away from the data points.", "Depends": ["R (>= 3.0.0)", "ggplot2 (>= 2.2.0)"], "Imports": ["grid", "Rcpp", "rlang (>= 0.3.0)", "scales (>= 0.5.0)", "withr (>= 2.5.0)"], "Suggests": ["knitr", "rmarkdown", "testthat", "svglite", "vdiffr", "gridExtra", "ggpp", "patchwork", "devtools", "prettydoc", "ggbeeswarm", "dplyr", "magrit<PERSON>", "readr", "stringr"], "VignetteBuilder": "knitr", "License": "GPL-3 | file LICENSE", "URL": "https://ggrepel.slowkow.com/, https://github.com/slowkow/ggrepel", "BugReports": "https://github.com/slowkow/ggrepel/issues", "RoxygenNote": "7.3.1", "LinkingTo": ["Rcpp"], "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-2843-6370>), <PERSON> [ctb] (<https://orcid.org/0000-0002-3915-0618>), <PERSON> [ctb] (<https://orcid.org/0000-0002-9409-9405>), <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0001-7562-6495>), <PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0003-4920-3880>), <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0003-1458-7108>), <PERSON> [ctb] (<https://orcid.org/0000-0002-0450-8181>), <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0003-4474-2498>), <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0003-0299-5825>), <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0003-3641-729X>), Michał Krassowski [ctb] (<https://orcid.org/0000-0002-9638-7785>), Michael Chirico [ctb] (<https://orcid.org/0000-0003-0787-087X>), <PERSON> Aphalo [ctb] (<https://orcid.org/0000-0003-3385-972X>), Francis Barton [ctb]", "Maintainer": "<PERSON><PERSON><PERSON> <k<PERSON><PERSON><PERSON>@gmail.com>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "ggsci": {"Package": "ggsci", "Version": "3.2.0", "Source": "Repository", "Type": "Package", "Title": "Scientific Journal and Sci-Fi Themed Color Palettes for 'ggplot2'", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-0250-5673\")), person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = \"ctb\") )", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "A collection of 'ggplot2' color palettes inspired by plots in scientific journals, data visualization libraries, science fiction movies, and TV shows.", "License": "GPL (>= 3)", "URL": "https://nanx.me/ggsci/, https://github.com/nanxstats/ggsci", "BugReports": "https://github.com/nanxstats/ggsci/issues", "Depends": ["R (>= 3.5.0)"], "Imports": ["ggplot2 (>= 2.0.0)", "grDevices", "scales"], "Suggests": ["gridExtra", "knitr", "ragg", "rmarkdown"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-0250-5673>), <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb]", "Repository": "https://packagemanager.posit.co/cran/latest"}, "ggsignif": {"Package": "ggsignif", "Version": "0.6.4", "Source": "Repository", "Type": "Package", "Title": "Significance Brackets for 'ggplot2'", "Authors@R": "c( person(given = \"Constantin\", family = \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\", \"ctb\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-3762-068X\", Twitter = \"@const_ae\")), person(given = \"Indrajeet\", family = \"Patil\", role = c(\"aut\", \"ctb\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-1995-6531\", Twitter = \"@patilindrajeets\")) )", "Description": "Enrich your 'ggplots' with group-wise comparisons. This package provides an easy way to indicate if two groups are significantly different. Commonly this is shown by a bracket on top connecting the groups of interest which itself is annotated with the level of significance (NS, *, **, ***).  The package provides a single layer (geom_signif()) that takes the groups for comparison and the test (t.test(), wilcox.text() etc.) as arguments and adds the annotation to the plot.", "License": "GPL-3 | file LICENSE", "URL": "https://const-ae.github.io/ggsignif/, https://github.com/const-ae/ggsignif", "VignetteBuilder": "knitr", "Encoding": "UTF-8", "Language": "en-US", "Imports": ["ggplot2 (>= 3.3.5)"], "Suggests": ["knitr", "rmarkdown", "testthat", "vdiffr (>= 1.0.2)"], "RoxygenNote": "7.2.1", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "NeedsCompilation": "no", "Author": "Constantin <PERSON> [aut, cre, ctb] (<https://orcid.org/0000-0002-3762-068X>, @const_ae), <PERSON><PERSON><PERSON><PERSON> [aut, ctb] (<https://orcid.org/0000-0003-1995-6531>, @patilindrajeets)", "Maintainer": "Constantin <PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "ggstats": {"Package": "ggstats", "Version": "0.9.0", "Source": "Repository", "Type": "Package", "Title": "Extension to 'ggplot2' for Plotting Stats", "Authors@R": "c( person( \"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0001-7097-700X\") ) )", "Description": "Provides new statistics, new geometries and new positions for  'ggplot2' and a suite of functions to facilitate the creation of  statistical plots.", "License": "GPL (>= 3)", "URL": "https://larmarange.github.io/ggstats/, https://github.com/larmarange/ggstats", "BugReports": "https://github.com/larmarange/ggstats/issues", "Depends": ["R (>= 4.2)"], "Imports": ["cli", "dplyr", "forcats", "ggplot2 (>= 3.4.0)", "lifecycle", "patchwork", "purrr", "rlang", "scales", "stats", "stringr", "utils", "tidyr"], "Suggests": ["betareg", "broom", "broom.helpers (>= 1.20.0)", "emmeans", "glue", "gtsummary", "knitr", "labelled (>= 2.11.0)", "reshape", "rmarkdown", "nnet", "parameters", "pscl", "testthat (>= 3.0.0)", "spelling", "survey", "survival", "vdiffr"], "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "Config/testthat/edition": "3", "Language": "en-US", "VignetteBuilder": "knitr", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0001-7097-700X>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "gld": {"Package": "gld", "Version": "2.6.7", "Source": "Repository", "Date": "2025-01-17", "Title": "Estimation and Use of the Generalised (Tukey) Lambda Distribution", "Suggests": [], "Imports": ["stats", "graphics", "e1071", "lmom"], "Authors@R": "c(person(given=\"<PERSON>\",family=\"King\", role=c(\"aut\",\"cre\"), email=\"<PERSON>.<PERSON>.<EMAIL>\", comment=c(ORCID=\"0000-0001-7495-6599\")), person(given=\"<PERSON>\",family=\"<PERSON>\", role=\"aut\", email=\"<PERSON>.<PERSON>@uon.edu.au\"), person(given=\"<PERSON><PERSON><PERSON>\",family=\"Klinke\", role=\"aut\"), person(given=\"<PERSON>\",family=\"<PERSON>\", role=\"aut\",email=\"paul.van<PERSON>@up.ac.za\", comment=c(ORCID=\"0000-0002-5710-5984\")) )", "Description": "The generalised lambda distribution, or Tukey lambda distribution,  provides a wide variety of shapes with one functional form.    This package provides random numbers, quantiles, probabilities,  densities and density quantiles for four different types of the distribution, the FKML (<PERSON><PERSON> et al 1988), RS (<PERSON> and <PERSON> 1974), GPD (<PERSON> and <PERSON> 2009) and FM5 - see documentation for details. It provides the density function, distribution function, and Quantile-Quantile  plots.   It implements a variety of estimation methods for the distribution,  including diagnostic plots.  Estimation methods include the starship (all 4 types),  method of L-Moments for the GPD and FKML types, and a  number of methods for only the FKML type.   These include maximum likelihood, maximum product of spacings,  <PERSON><PERSON><PERSON>'s method, Moments, Trimmed L-Moments and  Distributional Least Absolutes.", "License": "GPL (>= 2)", "URL": "https://github.com/newystats/gld/", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0001-7495-6599>), <PERSON> [aut], <PERSON><PERSON><PERSON> [aut], <PERSON> [aut] (<https://orcid.org/0000-0002-5710-5984>)", "Maintainer": "<PERSON> <<PERSON>.<PERSON>.<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "glue": {"Package": "glue", "Version": "1.8.0", "Source": "Repository", "Title": "Interpreted String Literals", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2739-7082\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-6983-2759\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "An implementation of interpreted string literals, inspired by Python's Literal String Interpolation <https://www.python.org/dev/peps/pep-0498/> and Docstrings <https://www.python.org/dev/peps/pep-0257/> and <PERSON>'s Triple-Quoted String Literals <https://docs.julialang.org/en/v1.3/manual/strings/#Triple-Quoted-String-Literals-1>.", "License": "MIT + file LICENSE", "URL": "https://glue.tidyverse.org/, https://github.com/tidyverse/glue", "BugReports": "https://github.com/tidyverse/glue/issues", "Depends": ["R (>= 3.6)"], "Imports": ["methods"], "Suggests": ["crayon", "DBI (>= 1.2.0)", "dplyr", "knitr", "magrit<PERSON>", "rlang", "rmarkdown", "RSQLite", "testthat (>= 3.2.0)", "vctrs (>= 0.3.0)", "waldo (>= 0.5.3)", "withr"], "VignetteBuilder": "knitr", "ByteCompile": "true", "Config/Needs/website": "bench, forcats, ggbeeswarm, ggplot2, <PERSON><PERSON>utils, rprintf, tidyr, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (<https://orcid.org/0000-0002-2739-7082>), <PERSON> [aut, cre] (<https://orcid.org/0000-0002-6983-2759>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "goftest": {"Package": "goftest", "Version": "1.2-3", "Source": "Repository", "Type": "Package", "Title": "Classical Goodness-of-Fit Tests for Univariate Distributions", "Date": "2021-10-07", "Authors@R": "c(person(\"<PERSON>\", \"Far<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>ag<PERSON>\", role = \"aut\"), person(\"<PERSON>\",   \"<PERSON>ag<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<PERSON>.<EMAIL>\"))", "Depends": ["R (>= 3.3)"], "Imports": ["stats"], "Description": "<PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> tests of goodness-of-fit for continuous univariate distributions, using efficient algorithms.", "URL": "https://github.com/baddstats/goftest", "BugReports": "https://github.com/baddstats/goftest/issues", "License": "GPL (>= 2)", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut, cre]", "Maintainer": "<PERSON> <<PERSON><PERSON>@curtin.edu.au>", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "gridExtra": {"Package": "gridExtra", "Version": "2.3", "Source": "Repository", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", email = \"ton<PERSON><PERSON>@gmail.com\", role = c(\"ctb\")))", "License": "GPL (>= 2)", "Title": "Miscellaneous Functions for \"Grid\" Graphics", "Type": "Package", "Description": "Provides a number of user-level functions to work with \"grid\" graphics, notably to arrange multiple grid-based plots on a page, and draw tables.", "VignetteBuilder": "knitr", "Imports": ["gtable", "grid", "grDevices", "graphics", "utils"], "Suggests": ["ggplot2", "egg", "lattice", "knitr", "testthat"], "RoxygenNote": "6.0.1", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "gtable": {"Package": "gtable", "Version": "0.3.6", "Source": "Repository", "Title": "Arrange 'Grobs' in Tables", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Tools to make it easier to work with \"tables\" of 'grobs'. The 'gtable' package defines a 'gtable' grob class that specifies a grid along with a list of grobs and their placement in the grid. Further the package makes it easy to manipulate and combine 'gtable' objects so that complex compositions can be built up sequentially.", "License": "MIT + file LICENSE", "URL": "https://gtable.r-lib.org, https://github.com/r-lib/gtable", "BugReports": "https://github.com/r-lib/gtable/issues", "Depends": ["R (>= 4.0)"], "Imports": ["cli", "glue", "grid", "lifecycle", "rlang (>= 1.1.0)", "stats"], "Suggests": ["covr", "ggplot2", "knitr", "profvis", "rmarkdown", "testthat (>= 3.0.0)"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/usethis/last-upkeep": "2024-10-25", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "haven": {"Package": "haven", "Version": "2.5.5", "Source": "Repository", "Title": "Import and Export 'SPSS', 'Stata' and 'SAS' Files", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON>\", role = c(\"aut\", \"cph\"), comment = \"Author of included ReadStat code\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Import foreign statistical formats into R via the embedded 'ReadStat' C library, <https://github.com/WizardMac/ReadStat>.", "License": "MIT + file LICENSE", "URL": "https://haven.tidyverse.org, https://github.com/tidyverse/haven, https://github.com/WizardMac/ReadStat", "BugReports": "https://github.com/tidyverse/haven/issues", "Depends": ["R (>= 3.6)"], "Imports": ["cli (>= 3.0.0)", "forcats (>= 0.2.0)", "hms", "lifecycle", "methods", "readr (>= 0.1.0)", "rlang (>= 0.4.0)", "tibble", "tidyselect", "vctrs (>= 0.3.0)"], "Suggests": ["covr", "crayon", "fs", "knitr", "pillar (>= 1.4.0)", "rmarkdown", "testthat (>= 3.0.0)", "utf8"], "LinkingTo": ["cpp11"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "SystemRequirements": "GNU make, zlib: zlib1g-dev (deb), zlib-devel (rpm)", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON> [aut, cph] (Author of included ReadStat code), <PERSON> [aut], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "highr": {"Package": "highr", "Version": "0.11", "Source": "Repository", "Type": "Package", "Title": "Syntax Highlighting for R Source Code", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\") )", "Description": "Provides syntax highlighting for R source code. Currently it supports LaTeX and HTML output. Source code of other languages is supported via <PERSON>'s highlight package (<https://gitlab.com/saalen/highlight>).", "Depends": ["R (>= 3.3.0)"], "Imports": ["xfun (>= 0.18)"], "Suggests": ["knitr", "markdown", "testit"], "License": "GPL", "URL": "https://github.com/yihui/highr", "BugReports": "https://github.com/yihui/highr/issues", "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0645-5666>), <PERSON><PERSON><PERSON> [aut], <PERSON> [ctb], <PERSON><PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "hms": {"Package": "hms", "Version": "1.1.3", "Source": "Repository", "Title": "Pretty Time of Day", "Date": "2023-03-21", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(\"R Consortium\", role = \"fnd\"), person(\"RStudio\", role = \"fnd\") )", "Description": "Implements an S3 class for storing and formatting time-of-day values, based on the 'difftime' class.", "Imports": ["lifecycle", "methods", "pkgconfig", "rlang (>= 1.0.2)", "vctrs (>= 0.3.8)"], "Suggests": ["crayon", "lubridate", "pillar (>= 1.1.0)", "testthat (>= 3.0.0)"], "License": "MIT + file LICENSE", "Encoding": "UTF-8", "URL": "https://hms.tidyverse.org/, https://github.com/tidyverse/hms", "BugReports": "https://github.com/tidyverse/hms/issues", "RoxygenNote": "7.2.3", "Config/testthat/edition": "3", "Config/autostyle/scope": "line_breaks", "Config/autostyle/strict": "false", "Config/Needs/website": "tidyverse/tidytemplate", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-1416-3412>), R Consortium [fnd], RStudio [fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "htmltools": {"Package": "htmltools", "Version": "*******", "Source": "Repository", "Type": "Package", "Title": "Tools for HTML", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0001-9986-114X\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0002-1576-2126\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Tools for HTML generation and output.", "License": "GPL (>= 2)", "URL": "https://github.com/rstudio/htmltools, https://rstudio.github.io/htmltools/", "BugReports": "https://github.com/rstudio/htmltools/issues", "Depends": ["R (>= 2.14.1)"], "Imports": ["base64enc", "digest", "fastmap (>= 1.1.0)", "grDevices", "rlang (>= 1.0.0)", "utils"], "Suggests": ["Cairo", "markdown", "ragg", "shiny", "testthat", "withr"], "Enhances": ["knitr"], "Config/Needs/check": "knitr", "Config/Needs/website": "rstudio/quillt, bench", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "Collate": "'colors.R' 'fill.R' 'html_dependency.R' 'html_escape.R' 'html_print.R' 'htmltools-package.R' 'images.R' 'known_tags.R' 'selector.R' 'staticimports.R' 'tag_query.R' 'utils.R' 'tags.R' 'template.R'", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut, cre] (<https://orcid.org/0000-0002-4958-2844>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0001-9986-114X>), <PERSON> [aut] (<https://orcid.org/0000-0002-1576-2126>), <PERSON><PERSON> [aut], <PERSON> [aut], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "htmlwidgets": {"Package": "htmlwidgets", "Version": "1.6.4", "Source": "Repository", "Type": "Package", "Title": "HTML Widgets for R", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cph\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = c(\"aut\", \"cph\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A framework for creating HTML widgets that render in various contexts including the R console, 'R Markdown' documents, and 'Shiny' web applications.", "License": "MIT + file LICENSE", "URL": "https://github.com/ramnathv/htmlwidgets", "BugReports": "https://github.com/ramnathv/htmlwidgets/issues", "Imports": ["grDevices", "htmltools (>= 0.5.7)", "jsonlite (>= 0.9.16)", "knitr (>= 1.8)", "rmarkdown", "yaml"], "Suggests": ["testthat"], "Enhances": ["shiny (>= 1.1)"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cph], <PERSON><PERSON> [aut], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut, cre] (<https://orcid.org/0000-0002-4958-2844>), <PERSON><PERSON> [aut, cph], <PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "httpuv": {"Package": "httpuv", "Version": "1.6.16", "Source": "Repository", "Type": "Package", "Title": "HTTP and WebSocket Server Library", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>si<PERSON>, P<PERSON>\", \"fnd\", role = \"cph\"), person(\"<PERSON>\", \"<PERSON><PERSON>da Bravo\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"cph\", comment = \"optional.hpp\"), person(\"libuv project contributors\", role = \"cph\", comment = \"libuv library, see src/libuv/AUTHORS file\"), person(\"Joyent, Inc. and other Node contributors\", role = \"cph\", comment = \"libuv library, see src/libuv/AUTHORS file; and http-parser library, see src/http-parser/AUTHORS file\"), person(\"<PERSON><PERSON>\", \"Provos\", role = \"cph\", comment = \"libuv subcomponent: tree.h\"), person(\"Internet Systems Consortium, Inc.\", role = \"cph\", comment = \"libuv subcomponent: inet_pton and inet_ntop, contained in src/libuv/src/inet.c\"), person(\"<PERSON>\", \"Chemeris\", role = \"cph\", comment = \"libuv subcomponent: stdint-msvc2008.h (from msinttypes)\"), person(\"Google, Inc.\", role = \"cph\", comment = \"libuv subcomponent: pthread-fixes.c\"), person(\"Sony Mobile Communcations AB\", role = \"cph\", comment = \"libuv subcomponent: pthread-fixes.c\"), person(\"Berkeley Software Design Inc.\", role = \"cph\", comment = \"libuv subcomponent: android-ifaddrs.h, android-ifaddrs.c\"), person(\"Kenneth\", \"MacKay\", role = \"cph\", comment = \"libuv subcomponent: android-ifaddrs.h, android-ifaddrs.c\"), person(\"Emergya (Cloud4all, FP7/2007-2013, grant agreement no 289016)\", role = \"cph\", comment = \"libuv subcomponent: android-ifaddrs.h, android-ifaddrs.c\"), person(\"Steve\", \"Reid\", role = \"aut\", comment = \"SHA-1 implementation\"), person(\"James\", \"Brown\", role = \"aut\", comment = \"SHA-1 implementation\"), person(\"Bob\", \"Trower\", role = \"aut\", comment = \"base64 implementation\"), person(\"Alexander\", \"Peslyak\", role = \"aut\", comment = \"MD5 implementation\"), person(\"Trantor Standard Systems\", role = \"cph\", comment = \"base64 implementation\"), person(\"Igor\", \"Sysoev\", role = \"cph\", comment = \"http-parser\") )", "Description": "Provides low-level socket and protocol support for handling HTTP and WebSocket requests directly from within R. It is primarily intended as a building block for other packages, rather than making it particularly easy to create complete web applications using httpuv alone.  httpuv is built on top of the libuv and http-parser C libraries, both of which were developed by Joyent, Inc. (See LICENSE file for libuv and http-parser license information.)", "License": "GPL (>= 2) | file LICENSE", "URL": "https://github.com/rstudio/httpuv", "BugReports": "https://github.com/rstudio/httpuv/issues", "Depends": ["R (>= 2.15.1)"], "Imports": ["later (>= 0.8.0)", "promises", "R6", "Rcpp (>= 1.0.7)", "utils"], "Suggests": ["callr", "curl", "jsonlite", "testthat", "websocket"], "LinkingTo": ["later", "Rcpp"], "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "SystemRequirements": "GNU make, zlib", "Collate": "'RcppExports.R' 'httpuv.R' 'random_port.R' 'server.R' 'staticServer.R' 'static_paths.R' 'utils.R'", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut, cre], <PERSON><PERSON><PERSON>, PBC fnd [cph], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [cph] (optional.hpp), libuv project contributors [cph] (libuv library, see src/libuv/AUTHORS file), Joyent, Inc. and other Node contributors [cph] (libuv library, see src/libuv/AUTHORS file; and http-parser library, see src/http-parser/AUTHORS file), <PERSON>els <PERSON> [cph] (libuv subcomponent: tree.h), Internet Systems Consortium, Inc. [cph] (libuv subcomponent: inet_pton and inet_ntop, contained in src/libuv/src/inet.c), <PERSON> [cph] (libuv subcomponent: stdint-msvc2008.h (from msinttypes)), Google, Inc. [cph] (libuv subcomponent: pthread-fixes.c), Sony Mobile Communcations AB [cph] (libuv subcomponent: pthread-fixes.c), Berkeley Software Design Inc. [cph] (libuv subcomponent: android-ifaddrs.h, android-ifaddrs.c), <PERSON> [cph] (libuv subcomponent: android-ifaddrs.h, android-ifaddrs.c), Emergya (Cloud4all, FP7/2007-2013, grant agreement no 289016) [cph] (libuv subcomponent: android-ifaddrs.h, android-ifaddrs.c), Steve Reid [aut] (SHA-1 implementation), James Brown [aut] (SHA-1 implementation), Bob Trower [aut] (base64 implementation), Alexander Peslyak [aut] (MD5 implementation), Trantor Standard Systems [cph] (base64 implementation), Igor Sysoev [cph] (http-parser)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "httr": {"Package": "httr", "Version": "1.4.7", "Source": "Repository", "Title": "Tools for Working with URLs and HTTP", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Useful tools for working with HTTP organised by HTTP verbs (GET(), POST(), etc). Configuration functions make it easy to control additional request components (authenticate(), add_headers() and so on).", "License": "MIT + file LICENSE", "URL": "https://httr.r-lib.org/, https://github.com/r-lib/httr", "BugReports": "https://github.com/r-lib/httr/issues", "Depends": ["R (>= 3.5)"], "Imports": ["curl (>= 5.0.2)", "jsonlite", "mime", "openssl (>= 0.8)", "R6"], "Suggests": ["covr", "httpuv", "jpeg", "knitr", "png", "readr", "rmarkdown", "testthat (>= 0.8.0)", "xml2"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON><PERSON><PERSON>, <PERSON><PERSON> [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "isoband": {"Package": "isoband", "Version": "0.2.7", "Source": "Repository", "Title": "Generate Isolines and Isobands from Regularly Spaced Elevation Grids", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON>\", \"W<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(\"Original author\", ORCID = \"0000-0002-7470-9261\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0002-5147-4711\")) )", "Description": "A fast C++ implementation to generate contour lines (isolines) and contour polygons (isobands) from regularly spaced grids containing elevation data.", "License": "MIT + file LICENSE", "URL": "https://isoband.r-lib.org", "BugReports": "https://github.com/r-lib/isoband/issues", "Imports": ["grid", "utils"], "Suggests": ["covr", "ggplot2", "knitr", "magick", "microbenchmark", "rmarkdown", "sf", "testthat", "xml2"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "SystemRequirements": "C++11", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0003-4757-117X>), <PERSON> [aut] (Original author, <https://orcid.org/0000-0002-7470-9261>), <PERSON> [aut] (<https://orcid.org/0000-0002-5147-4711>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "iterators": {"Package": "iterators", "Version": "1.0.14", "Source": "Repository", "Type": "Package", "Title": "Provides Iterator Construct", "Authors@R": "c(person(\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON>\", role=\"cre\", email=\"<EMAIL>\"), person(\"Revolution\", \"Analytics\", role=c(\"aut\", \"cph\")), person(\"<PERSON>\", \"<PERSON>\", role=\"aut\"))", "Description": "Support for iterators, which allow a programmer to traverse through all the elements of a vector, list, or other collection of data.", "URL": "https://github.com/RevolutionAnalytics/iterators", "Depends": ["R (>= 2.5.0)", "utils"], "Suggests": ["RUnit", "foreach"], "License": "Apache License (== 2.0)", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON><PERSON> [cre], Revolution Analytics [aut, cph], <PERSON> [aut]", "Maintainer": "Folashade Daniel <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "jose": {"Package": "jose", "Version": "1.2.1", "Source": "Repository", "Type": "Package", "Title": "JavaScript Object Signing and Encryption", "Authors@R": "person(\"<PERSON><PERSON><PERSON>\", \"O<PERSON>\", role = c(\"aut\", \"cre\"),  email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4035-0289\"))", "Description": "Read and write JSON Web Keys (JWK, rfc7517), generate and verify JSON Web Signatures (JWS, rfc7515) and encode/decode JSON Web Tokens (JWT, rfc7519) <https://datatracker.ietf.org/wg/jose/documents/>. These standards provide  modern signing and encryption formats that are natively supported by browsers via the JavaScript WebCryptoAPI <https://www.w3.org/TR/WebCryptoAPI/#jose>,  and used by services like OAuth 2.0, LetsEncrypt, and Github Apps.", "License": "MIT + file LICENSE", "URL": "https://r-lib.r-universe.dev/jose", "BugReports": "https://github.com/r-lib/jose/issues", "Depends": ["openssl (>= 1.2.1)"], "Imports": ["jsonlite"], "RoxygenNote": "7.1.2", "VignetteBuilder": "knitr", "Suggests": ["spelling", "testthat", "knitr", "rmarkdown"], "Encoding": "UTF-8", "Language": "en-US", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-4035-0289>)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "jquerylib": {"Package": "j<PERSON><PERSON><PERSON>", "Version": "0.1.4", "Source": "Repository", "Title": "Obtain 'jQuery' as an HTML Dependency Object", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(family = \"RStudio\", role = \"cph\"), person(family = \"jQuery Foundation\", role = \"cph\", comment = \"jQuery library and jQuery UI library\"), person(family = \"jQuery contributors\", role = c(\"ctb\", \"cph\"), comment = \"jQuery library; authors listed in inst/lib/jquery-AUTHORS.txt\") )", "Description": "Obtain any major version of 'jQuery' (<https://code.jquery.com/>) and use it in any webpage generated by 'htmltools' (e.g. 'shiny', 'htmlwidgets', and 'rmarkdown'). Most R users don't need to use this package directly, but other R packages (e.g. 'shiny', 'rmarkdown', etc.) depend on this package to avoid bundling redundant copies of 'jQuery'.", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "Config/testthat/edition": "3", "RoxygenNote": "7.0.2", "Imports": ["htmltools"], "Suggests": ["testthat"], "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-4958-2844>), <PERSON> [aut], <PERSON>tudio [cph], jQuery Foundation [cph] (jQuery library and jQuery UI library), jQuery contributors [ctb, cph] (jQuery library; authors listed in inst/lib/jquery-AUTHORS.txt)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "jsonlite": {"Package": "jsonlite", "Version": "2.0.0", "Source": "Repository", "Title": "A Simple and Robust JSON Parser and Generator for R", "License": "MIT + file LICENSE", "Depends": ["methods"], "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"cph\", comment=\"author of bundled libyajl\"))", "URL": "https://jeroen.r-universe.dev/jsonlite https://arxiv.org/abs/1403.2805", "BugReports": "https://github.com/jeroen/jsonlite/issues", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "VignetteBuilder": "knitr, R.rsp", "Description": "A reasonably fast JSON parser and generator, optimized for statistical  data and the web. Offers simple, flexible tools for working with JSON in R, and is particularly powerful for building pipelines and interacting with a web API.  The implementation is based on the mapping described in the vignette (Ooms, 2014). In addition to converting JSON data from/to R objects, 'jsonlite' contains  functions to stream, validate, and prettify JSON data. The unit tests included  with the package verify that all edge cases are encoded and decoded consistently  for use with dynamic data in systems and applications.", "Suggests": ["httr", "vctrs", "testthat", "knitr", "rmarkdown", "R.rsp", "sf"], "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-4035-0289>), <PERSON> [ctb], <PERSON> [cph] (author of bundled libyajl)", "Repository": "CRAN"}, "knitr": {"Package": "knitr", "Version": "1.50", "Source": "Repository", "Type": "Package", "Title": "A General-Purpose Package for Dynamic Report Generation in R", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\", URL = \"https://yihui.org\")), person(\"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"the CSS files under inst/themes/ were derived from the Highlight package http://www.andre-simon.de\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-8335-495X\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>t\", \"<PERSON><PERSON>rov\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>vie<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>-<PERSON>\", role = \"ctb\"), person(\"David\", \"Robinson\", role = \"ctb\"), person(\"Doug\", \"Hemken\", role = \"ctb\"), person(\"Duncan\", \"Murdoch\", role = \"ctb\"), person(\"Elio\", \"Campitelli\", role = \"ctb\"), person(\"Ellis\", \"Hughes\", role = \"ctb\"), person(\"Emily\", \"Riederer\", role = \"ctb\"), person(\"Fabian\", \"Hirschmann\", role = \"ctb\"), person(\"Fitch\", \"Simeon\", role = \"ctb\"), person(\"Forest\", \"Fang\", role = \"ctb\"), person(c(\"Frank\", \"E\", \"Harrell\", \"Jr\"), role = \"ctb\", comment = \"the Sweavel package at inst/misc/Sweavel.sty\"), person(\"Garrick\", \"Aden-Buie\", role = \"ctb\"), person(\"Gregoire\", \"Detrez\", role = \"ctb\"), person(\"Hadley\", \"Wickham\", role = \"ctb\"), person(\"Hao\", \"Zhu\", role = \"ctb\"), person(\"Heewon\", \"Jeon\", role = \"ctb\"), person(\"Henrik\", \"Bengtsson\", role = \"ctb\"), person(\"Hiroaki\", \"Yutani\", role = \"ctb\"), person(\"Ian\", \"Lyttle\", role = \"ctb\"), person(\"Hodges\", \"Daniel\", role = \"ctb\"), person(\"Jacob\", \"Bien\", role = \"ctb\"), person(\"Jake\", \"Burkhead\", role = \"ctb\"), person(\"James\", \"Manton\", role = \"ctb\"), person(\"Jared\", \"Lander\", role = \"ctb\"), person(\"Jason\", \"Punyon\", role = \"ctb\"), person(\"Javier\", \"Luraschi\", role = \"ctb\"), person(\"Jeff\", \"Arnold\", role = \"ctb\"), person(\"Jenny\", \"Bryan\", role = \"ctb\"), person(\"Jeremy\", \"Ashkenas\", role = c(\"ctb\", \"cph\"), comment = \"the CSS file at inst/misc/docco-classic.css\"), person(\"Jeremy\", \"Stephens\", role = \"ctb\"), person(\"Jim\", \"Hester\", role = \"ctb\"), person(\"Joe\", \"Cheng\", role = \"ctb\"), person(\"Johannes\", \"Ranke\", role = \"ctb\"), person(\"John\", \"Honaker\", role = \"ctb\"), person(\"John\", \"Muschelli\", role = \"ctb\"), person(\"Jonathan\", \"Keane\", role = \"ctb\"), person(\"JJ\", \"Allaire\", role = \"ctb\"), person(\"Johan\", \"Toloe\", role = \"ctb\"), person(\"Jonathan\", \"Sidi\", role = \"ctb\"), person(\"Joseph\", \"Larmarange\", role = \"ctb\"), person(\"Julien\", \"Barnier\", role = \"ctb\"), person(\"Kaiyin\", \"Zhong\", role = \"ctb\"), person(\"Kamil\", \"Slowikowski\", role = \"ctb\"), person(\"Karl\", \"Forner\", role = \"ctb\"), person(c(\"Kevin\", \"K.\"), \"Smith\", role = \"ctb\"), person(\"Kirill\", \"Mueller\", role = \"ctb\"), person(\"Kohske\", \"Takahashi\", role = \"ctb\"), person(\"Lorenz\", \"Walthert\", role = \"ctb\"), person(\"Lucas\", \"Gallindo\", role = \"ctb\"), person(\"Marius\", \"Hofert\", role = \"ctb\"), person(\"Martin\", \"Modrák\", role = \"ctb\"), person(\"Michael\", \"Chirico\", role = \"ctb\"), person(\"Michael\", \"Friendly\", role = \"ctb\"), person(\"Michal\", \"Bojanowski\", role = \"ctb\"), person(\"Michel\", \"Kuhlmann\", role = \"ctb\"), person(\"Miller\", \"Patrick\", role = \"ctb\"), person(\"Nacho\", \"Caballero\", role = \"ctb\"), person(\"Nick\", \"Salkowski\", role = \"ctb\"), person(\"Niels Richard\", \"Hansen\", role = \"ctb\"), person(\"Noam\", \"Ross\", role = \"ctb\"), person(\"Obada\", \"Mahdi\", role = \"ctb\"), person(\"Pavel N.\", \"Krivitsky\", role = \"ctb\", comment=c(ORCID = \"0000-0002-9101-3362\")), person(\"Pedro\", \"Faria\", role = \"ctb\"), person(\"Qiang\", \"Li\", role = \"ctb\"), person(\"Ramnath\", \"Vaidyanathan\", role = \"ctb\"), person(\"Richard\", \"Cotton\", role = \"ctb\"), person(\"Robert\", \"Krzyzanowski\", role = \"ctb\"), person(\"Rodrigo\", \"Copetti\", role = \"ctb\"), person(\"Romain\", \"Francois\", role = \"ctb\"), person(\"Ruaridh\", \"Williamson\", role = \"ctb\"), person(\"Sagiru\", \"Mati\", role = \"ctb\", comment = c(ORCID = \"0000-0003-1413-3974\")), person(\"Scott\", \"Kostyshak\", role = \"ctb\"), person(\"Sebastian\", \"Meyer\", role = \"ctb\"), person(\"Sietse\", \"Brouwer\", role = \"ctb\"), person(c(\"Simon\", \"de\"), \"Bernard\", role = \"ctb\"), person(\"Sylvain\", \"Rousseau\", role = \"ctb\"), person(\"Taiyun\", \"Wei\", role = \"ctb\"), person(\"Thibaut\", \"Assus\", role = \"ctb\"), person(\"Thibaut\", \"Lamadon\", role = \"ctb\"), person(\"Thomas\", \"Leeper\", role = \"ctb\"), person(\"Tim\", \"Mastny\", role = \"ctb\"), person(\"Tom\", \"Torsney-Weir\", role = \"ctb\"), person(\"Trevor\", \"Davis\", role = \"ctb\"), person(\"Viktoras\", \"Veitas\", role = \"ctb\"), person(\"Weicheng\", \"Zhu\", role = \"ctb\"), person(\"Wush\", \"Wu\", role = \"ctb\"), person(\"Zachary\", \"Foster\", role = \"ctb\"), person(\"Zhian N.\", \"Kamvar\", role = \"ctb\", comment = c(ORCID = \"0000-0003-1458-7108\")), person(given = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Provides a general-purpose tool for dynamic report generation in R using Literate Programming techniques.", "Depends": ["R (>= 3.6.0)"], "Imports": ["evaluate (>= 0.15)", "highr (>= 0.11)", "methods", "tools", "xfun (>= 0.51)", "yaml (>= 2.1.19)"], "Suggests": ["bslib", "codetools", "DBI (>= 0.4-1)", "digest", "formatR", "gifski", "gridSVG", "htmlwidgets (>= 0.7)", "jpeg", "JuliaCall (>= 0.11.1)", "magick", "litedown", "markdown (>= 1.3)", "png", "ragg", "reticulate (>= 1.4)", "rgl (>= 0.95.1201)", "rlang", "rmarkdown", "sass", "showtext", "styler (>= 1.2.0)", "targets (>= 0.6.0)", "testit", "tibble", "tikzDevice (>= 0.10)", "tinytex (>= 0.56)", "webshot", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "svglite"], "License": "GPL", "URL": "https://yihui.org/knitr/", "BugReports": "https://github.com/yihui/knitr/issues", "Encoding": "UTF-8", "VignetteBuilder": "litedown, knitr", "SystemRequirements": "Package vignettes based on R Markdown v2 or reStructuredText require Pandoc (http://pandoc.org). The function rst2pdf() requires rst2pdf (https://github.com/rst2pdf/rst2pdf).", "Collate": "'block.R' 'cache.R' 'citation.R' 'hooks-html.R' 'plot.R' 'utils.R' 'defaults.R' 'concordance.R' 'engine.R' 'highlight.R' 'themes.R' 'header.R' 'hooks-asciidoc.R' 'hooks-chunk.R' 'hooks-extra.R' 'hooks-latex.R' 'hooks-md.R' 'hooks-rst.R' 'hooks-textile.R' 'hooks.R' 'output.R' 'package.R' 'pandoc.R' 'params.R' 'parser.R' 'pattern.R' 'rocco.R' 'spin.R' 'table.R' 'template.R' 'utils-conversion.R' 'utils-rd2html.R' 'utils-string.R' 'utils-sweave.R' 'utils-upload.R' 'utils-vignettes.R' 'zzz.R'", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0645-5666>, https://yihui.org), <PERSON><PERSON><PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb] (the CSS files under inst/themes/ were derived from the Highlight package http://www.andre-simon.de), <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-8335-495X>), <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb] (the Sweavel package at inst/mi<PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON>.s<PERSON>), <PERSON><PERSON><PERSON> <PERSON>-<PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON> [c<PERSON><PERSON>], <PERSON> <PERSON> [c<PERSON>b], <PERSON><PERSON> <PERSON> [c<PERSON>b], <PERSON><PERSON><PERSON> <PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON> [c<PERSON>b], <PERSON><PERSON><PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON><PERSON><PERSON> [ctb], James Manton [ctb], Jared Lander [ctb], Jason Punyon [ctb], Javier Luraschi [ctb], Jeff Arnold [ctb], Jenny Bryan [ctb], Jeremy Ashkenas [ctb, cph] (the CSS file at inst/misc/docco-classic.css), Jeremy Stephens [ctb], Jim Hester [ctb], Joe Cheng [ctb], Johannes Ranke [ctb], John Honaker [ctb], John Muschelli [ctb], Jonathan Keane [ctb], JJ Allaire [ctb], Johan Toloe [ctb], Jonathan Sidi [ctb], Joseph Larmarange [ctb], Julien Barnier [ctb], Kaiyin Zhong [ctb], Kamil Slowikowski [ctb], Karl Forner [ctb], Kevin K. Smith [ctb], Kirill Mueller [ctb], Kohske Takahashi [ctb], Lorenz Walthert [ctb], Lucas Gallindo [ctb], Marius Hofert [ctb], Martin Modrák [ctb], Michael Chirico [ctb], Michael Friendly [ctb], Michal Bojanowski [ctb], Michel Kuhlmann [ctb], Miller Patrick [ctb], Nacho Caballero [ctb], Nick Salkowski [ctb], Niels Richard Hansen [ctb], Noam Ross [ctb], Obada Mahdi [ctb], Pavel N. Krivitsky [ctb] (<https://orcid.org/0000-0002-9101-3362>), Pedro Faria [ctb], Qiang Li [ctb], Ramnath Vaidyanathan [ctb], Richard Cotton [ctb], Robert Krzyzanowski [ctb], Rodrigo Copetti [ctb], Romain Francois [ctb], Ruaridh Williamson [ctb], Sagiru Mati [ctb] (<https://orcid.org/0000-0003-1413-3974>), Scott Kostyshak [ctb], Sebastian Meyer [ctb], Sietse Brouwer [ctb], Simon de Bernard [ctb], Sylvain Rousseau [ctb], Taiyun Wei [ctb], Thibaut Assus [ctb], Thibaut Lamadon [ctb], Thomas Leeper [ctb], Tim Mastny [ctb], Tom Torsney-Weir [ctb], Trevor Davis [ctb], Viktoras Veitas [ctb], Weicheng Zhu [ctb], Wush Wu [ctb], Zachary Foster [ctb], Zhian N. Kamvar [ctb] (<https://orcid.org/0000-0003-1458-7108>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "labeling": {"Package": "labeling", "Version": "0.4.3", "Source": "Repository", "Type": "Package", "Title": "Axis Labeling", "Date": "2023-08-29", "Author": "<PERSON>,", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Description": "Functions which provide a range of axis labeling algorithms.", "License": "MIT + file LICENSE | Unlimited", "Collate": "'labeling.R'", "NeedsCompilation": "no", "Imports": ["stats", "graphics"], "Repository": "CRAN"}, "labelled": {"Package": "labelled", "Version": "2.14.1", "Source": "Repository", "Type": "Package", "Title": "Manipulating Labelled Data", "Maintainer": "<PERSON> <<EMAIL>>", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0001-7097-700X\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\") )", "Description": "Work with labelled data imported from 'SPSS' or 'Stata' with 'haven' or 'foreign'. This package provides useful functions to deal with \"haven_labelled\" and \"haven_labelled_spss\" classes introduced by 'haven' package.", "License": "GPL (>= 3)", "Encoding": "UTF-8", "Depends": ["R (>= 3.2)"], "Imports": ["haven (>= 2.4.1)", "cli", "dplyr (>= 1.1.0)", "lifecycle", "rlang (>= 1.1.0)", "vctrs", "stringr", "tidyr", "tidyselect"], "Suggests": ["testthat (>= 3.2.0)", "knitr", "rmarkdown", "questionr", "snakecase", "spelling"], "Enhances": ["memisc"], "URL": "https://larmarange.github.io/labelled/, https://github.com/larmarange/labelled", "BugReports": "https://github.com/larmarange/labelled/issues", "VignetteBuilder": "knitr", "LazyData": "true", "RoxygenNote": "7.3.2", "Language": "en-US", "Config/testthat/edition": "3", "Config/Needs/check": "memisc", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0001-7097-700X>), <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb]", "Repository": "https://packagemanager.posit.co/cran/latest"}, "later": {"Package": "later", "Version": "1.4.2", "Source": "Repository", "Type": "Package", "Title": "Utilities for Scheduling Functions to Execute Later with Event Loops", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-0750-061X\")), person(family = \"Posit Software, PBC\", role = \"cph\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"TinyCThread library, https://tinycthread.github.io/\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"TinyCThread library, https://tinycthread.github.io/\") )", "Description": "Executes arbitrary R or C functions some time after the current time, after the R execution stack has emptied. The functions are scheduled in an event loop.", "URL": "https://r-lib.github.io/later/, https://github.com/r-lib/later", "BugReports": "https://github.com/r-lib/later/issues", "License": "MIT + file LICENSE", "Imports": ["Rcpp (>= 0.12.9)", "rlang"], "LinkingTo": ["Rcpp"], "RoxygenNote": "7.3.2", "Suggests": ["knitr", "nanonext", "R6", "rmarkdown", "testthat (>= 2.1.0)"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON> [aut], <PERSON> [aut] (<https://orcid.org/0000-0002-0750-061X>), Posit Software, PBC [cph], <PERSON> [ctb, cph] (TinyCThread library, https://tinycthread.github.io/), <PERSON> [ctb, cph] (TinyCThread library, https://tinycthread.github.io/)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "latex2exp": {"Package": "latex2exp", "Version": "0.9.6", "Source": "Repository", "Type": "Package", "Title": "Use LaTeX Expressions in Plots", "Date": "2022-11-27", "Authors@R": "person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", email=\"<EMAIL>\", role=c(\"aut\", \"cre\"))", "Description": "Parses and converts LaTeX math formulas to R's plotmath expressions, used to enter mathematical formulas and symbols to be rendered as text, axis labels, etc. throughout R's plotting system.", "License": "MIT + file LICENSE", "URL": "https://www.stefanom.io/latex2exp/, https://github.com/stefano-meschiari/latex2exp", "BugReports": "https://github.com/stefano-meschiari/latex2exp/issues", "Imports": ["stringr", "magrit<PERSON>"], "Encoding": "UTF-8", "Suggests": ["testthat", "waldo", "knitr", "ggplot2", "rmarkdown", "purrr", "tibble", "reactable", "htmltools", "<PERSON><PERSON><PERSON>", "rlang", "dplyr"], "VignetteBuilder": "knitr", "RoxygenNote": "7.1.2", "Language": "en-US", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "lattice": {"Package": "lattice", "Version": "0.22-7", "Source": "Repository", "Date": "2025-03-31", "Priority": "recommended", "Title": "Trellis Graphics for R", "Authors@R": "c(person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>rka<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-4107-1553\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"documentation\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", comment = \"miscellaneous improvements\"), person(\"<PERSON><PERSON><PERSON><PERSON> (<PERSON>)\", \"<PERSON>\", role = \"cph\", comment = \"filled contour code\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"Eng\", role = \"ctb\", comment = \"violin plot improvements\"), person(\"<PERSON>chi<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\", comment = \"modern colors\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", comment = \"generics for larrows, lpolygon, lrect and lsegments\") )", "Description": "A powerful and elegant high-level data visualization system inspired by Trellis graphics, with an emphasis on multivariate data. Lattice is sufficient for typical graphics needs, and is also flexible enough to handle most nonstandard requirements. See ?Lattice for an introduction.", "Depends": ["R (>= 4.0.0)"], "Suggests": ["KernSmooth", "MASS", "latticeExtra", "colorspace"], "Imports": ["grid", "grDevices", "graphics", "stats", "utils"], "Enhances": ["chron", "zoo"], "LazyLoad": "yes", "LazyData": "yes", "License": "GPL (>= 2)", "URL": "https://lattice.r-forge.r-project.org/", "BugReports": "https://github.com/deepayan/lattice/issues", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-4107-1553>), <PERSON> [ctb], <PERSON> [ctb] (documentation), <PERSON> [ctb], <PERSON> [ctb] (miscellaneous improvements), <PERSON><PERSON><PERSON><PERSON> (<PERSON>) <PERSON> [cph] (filled contour code), <PERSON> [ctb], <PERSON> [ctb] (violin plot improvements), <PERSON><PERSON><PERSON> [ctb] (modern colors), <PERSON> [ctb] (generics for larrows, lpolygon, lrect and lsegments)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "lazyeval": {"Package": "lazyeval", "Version": "0.2.2", "Source": "Repository", "Title": "Lazy (Non-Standard) Evaluation", "Description": "An alternative approach to non-standard evaluation using formulas. Provides a full implementation of LISP style 'quasiquotation', making it easier to generate code with other code.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", ,\"<EMAIL>\", c(\"aut\", \"cre\")), person(\"<PERSON>tu<PERSON>\", role = \"cph\") )", "License": "GPL-3", "LazyData": "true", "Depends": ["R (>= 3.1.0)"], "Suggests": ["knitr", "rmarkdown (>= 0.2.65)", "testthat", "covr"], "VignetteBuilder": "knitr", "RoxygenNote": "6.1.1", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON><PERSON><PERSON> [cph]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "lifecycle": {"Package": "lifecycle", "Version": "1.0.4", "Source": "Repository", "Title": "Manage the Life Cycle of your Package Functions", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Manage the life cycle of your exported functions with shared conventions, documentation badges, and user-friendly deprecation warnings.", "License": "MIT + file LICENSE", "URL": "https://lifecycle.r-lib.org/, https://github.com/r-lib/lifecycle", "BugReports": "https://github.com/r-lib/lifecycle/issues", "Depends": ["R (>= 3.6)"], "Imports": ["cli (>= 3.4.0)", "glue", "rlang (>= 1.1.0)"], "Suggests": ["covr", "crayon", "knitr", "lintr", "rmarkdown", "testthat (>= 3.0.1)", "tibble", "tidyverse", "tools", "vctrs", "withr"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate, usethis", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.2.1", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON> [aut] (<https://orcid.org/0000-0003-4757-117X>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "litedown": {"Package": "litedown", "Version": "0.7", "Source": "Repository", "Type": "Package", "Title": "A Lightweight Version of R Markdown", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\", URL = \"https://yihui.org\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-8587-7113\")), person() )", "Description": "Render R Markdown to Markdown (without using 'knitr'), and Markdown to lightweight HTML or 'LaTeX' documents with the 'commonmark' package (instead of 'Pandoc'). Some missing Markdown features in 'commonmark' are also supported, such as raw HTML or 'LaTeX' blocks, 'LaTeX' math, superscripts, subscripts, footnotes, element attributes, and appendices, but not all 'Pandoc' Markdown features are (or will be) supported. With additional JavaScript and CSS, you can also create HTML slides and articles. This package can be viewed as a trimmed-down version of R Markdown and 'knitr'. It does not aim at rich Markdown features or a large variety of output formats (the primary formats are HTML and 'LaTeX'). Book and website projects of multiple input documents are also supported.", "Depends": ["R (>= 3.2.0)"], "Imports": ["utils", "commonmark (>= 1.9.5)", "xfun (>= 0.52)"], "Suggests": ["rbibutils", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tinytex"], "License": "MIT + file LICENSE", "URL": "https://github.com/yihui/litedown", "BugReports": "https://github.com/yihui/litedown/issues", "VignetteBuilder": "litedown", "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0645-5666>, https://yihui.org), <PERSON> [ctb] (<https://orcid.org/0000-0002-8587-7113>)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "lme4": {"Package": "lme4", "Version": "1.1-37", "Source": "Repository", "Title": "Linear Mixed-Effects Models using 'Eigen' and S4", "Authors@R": "c( person(\"<PERSON>\",\"<PERSON>\", role=\"aut\", comment=c(ORCID=\"0000-0001-8316-9503\")), person(\"<PERSON>\",\"<PERSON><PERSON><PERSON>\", role=\"aut\", comment=c(ORCID=\"0000-0002-8685-9910\")), person(\"<PERSON>\",\"<PERSON><PERSON><PERSON>\",email=\"b<PERSON><PERSON>+<EMAIL>\", role=c(\"aut\",\"cre\"), comment=c(ORCID=\"0000-0002-2127-0443\")), person(\"<PERSON>\",\"<PERSON>\",role=\"aut\", comment=c(ORCID=\"0000-0002-4394-9078\")), person(\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON>\", role=\"ctb\", comment=c(ORCID=\"0000-0002-4494-3399\")), person(\"<PERSON>\",\"<PERSON><PERSON>\", role=\"ctb\", comment=c(ORCID=\"0000-0002-4842-3657\")), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"ctb\", comment=c(ORCID=\"0000-0001-8172-3603\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\", comment=c(ORCID=\"0000-0002-0238-9852\")), person(\"<PERSON>\", \"Fox\", role=\"ctb\"), person(\"<PERSON>\", \"Bauer\", role=\"ctb\"), person(\"<PERSON> N.\", \"Krivitsky\", role=c(\"ctb\",\"cph\"), comment=c(OR<PERSON>D=\"0000-0002-9101-3362\", \"shared copyright on simulate.formula\")), person(\"Emi\", \"Tanaka\", role = \"ctb\", comment = c(ORCID=\"0000-0002-1455-259X\")), person(\"Mikael\", \"Jagan\", role = \"ctb\", comment = c(ORCID=\"0000-0002-3542-2938\")), person(\"Ross D.\", \"Boylan\", email=\"<EMAIL>\", role=(\"ctb\"), comment = c(ORCID=\"0009-0003-4123-8090\")) )", "Description": "Fit linear and generalized linear mixed-effects models. The models and their components are represented using S4 classes and methods.  The core computational algorithms are implemented using the 'Eigen' C++ library for numerical linear algebra and 'RcppEigen' \"glue\".", "Depends": ["R (>= 3.6.0)", "Matrix", "methods", "stats"], "LinkingTo": ["Rcpp (>= 0.10.5)", "RcppEigen (>= *******.4)", "Matrix (>= 1.2-3)"], "Imports": ["graphics", "grid", "splines", "utils", "parallel", "MASS", "lattice", "boot", "nlme (>= 3.1-123)", "minqa (>= 1.1.15)", "nloptr (>= 1.0.4)", "reformulas (>= 0.3.0)"], "Suggests": ["knitr", "rmarkdown", "MEMSS", "testthat (>= 0.8.1)", "ggplot2", "<PERSON><PERSON><PERSON><PERSON>", "optimx (>= 2013.8.6)", "gamm4", "pbkrtest", "HSAUR3", "numDeriv", "car", "dfoptim", "mgcv", "statmod", "rr2", "sem<PERSON>ff", "tibble", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "VignetteBuilder": "knitr", "LazyData": "yes", "License": "GPL (>= 2)", "URL": "https://github.com/lme4/lme4/", "BugReports": "https://github.com/lme4/lme4/issues", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (<https://orcid.org/0000-0001-8316-9503>), <PERSON> [aut] (<https://orcid.org/0000-0002-8685-9910>), <PERSON> [aut, cre] (<https://orcid.org/0000-0002-2127-0443>), <PERSON> [aut] (<https://orcid.org/0000-0002-4394-9078>), <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-4494-3399>), <PERSON> [ctb] (<https://orcid.org/0000-0002-4842-3657>), <PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0001-8172-3603>), <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0002-0238-9852>), <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb, cph] (<https://orcid.org/0000-0002-9101-3362>, shared copyright on simulate.formula), <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-1455-259X>), <PERSON><PERSON>el Jagan [ctb] (<https://orcid.org/0000-0002-3542-2938>), Ross D. Boylan [ctb] (<https://orcid.org/0009-0003-4123-8090>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "lmom": {"Package": "lmom", "Version": "3.2", "Source": "Repository", "Date": "2024-09-29", "Title": "L-Moments", "Author": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> [aut, cre]", "Maintainer": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>", "Authors@R": "person(given = c(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\"), family = \"Hosking\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\")", "Description": "Functions related to L-moments: computation of L-moments and trimmed L-moments of distributions and data samples; parameter estimation; L-moment ratio diagram; plot vs. quantiles of an extreme-value distribution.", "Depends": ["R (>= 3.0.0)"], "Imports": ["stats", "graphics"], "License": "Common Public License Version 1.0", "NeedsCompilation": "yes", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "magrittr": {"Package": "magrit<PERSON>", "Version": "2.0.3", "Source": "Repository", "Type": "Package", "Title": "A Forward-Pipe Operator for R", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cph\"), comment = \"Original author and creator of magrittr\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@rstudio.com\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"cre\"), person(\"RStudio\", role = c(\"cph\", \"fnd\")) )", "Description": "Provides a mechanism for chaining commands with a new forward-pipe operator, %>%. This operator will forward a value, or the result of an expression, into the next function call/expression. There is flexible support for the type of right-hand side expressions. For more information, see package vignette.  To quote <PERSON>, \"Ceci n'est pas un pipe.\"", "License": "MIT + file LICENSE", "URL": "https://magrittr.tidyverse.org, https://github.com/tidyverse/magrittr", "BugReports": "https://github.com/tidyverse/magrittr/issues", "Depends": ["R (>= 3.4.0)"], "Suggests": ["covr", "knitr", "rlang", "rmarkdown", "testthat"], "VignetteBuilder": "knitr", "ByteCompile": "Yes", "Config/Needs/website": "tidyverse/tidytemplate", "Encoding": "UTF-8", "RoxygenNote": "7.1.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cph] (Original author and creator of magrittr), <PERSON> [aut], <PERSON> [cre], <PERSON><PERSON><PERSON> [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "markdown": {"Package": "markdown", "Version": "2.0", "Source": "Repository", "Type": "Package", "Title": "Render Markdown with 'commonmark'", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\")), person(\"<PERSON><PERSON>\", \"<PERSON>aire\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"November\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"Caballer<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(given = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Render Markdown to full and lightweight HTML/LaTeX documents with the 'commonmark' package. This package has been superseded by 'litedown'.", "Depends": ["R (>= 2.11.1)"], "Imports": ["utils", "xfun", "litedown (>= 0.6)"], "Suggests": ["knitr", "rmarkdown (>= 2.18)", "yaml", "<PERSON><PERSON><PERSON>"], "License": "MIT + file LICENSE", "URL": "https://github.com/rstudio/markdown", "BugReports": "https://github.com/rstudio/markdown/issues", "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0645-5666>), <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "memoise": {"Package": "memoise", "Version": "2.0.1", "Source": "Repository", "Title": "'Memoisation' of Functions", "Authors@R": "c(person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON>\", role = \"aut\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(given = \"Kirill\", family = \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\"))", "Description": "Cache the results of a function so that when you call it again with the same arguments it returns the previously computed value.", "License": "MIT + file LICENSE", "URL": "https://memoise.r-lib.org, https://github.com/r-lib/memoise", "BugReports": "https://github.com/r-lib/memoise/issues", "Imports": ["rlang (>= 0.4.10)", "cachem"], "Suggests": ["digest", "aws.s3", "covr", "googleAuthR", "googleCloudStorageR", "httr", "testthat"], "Encoding": "UTF-8", "RoxygenNote": "7.1.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "mgcv": {"Package": "mgcv", "Version": "1.9-3", "Source": "Repository", "Authors@R": "person(given = \"<PERSON>\", family = \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\")", "Title": "Mixed GAM Computation Vehicle with Automatic Smoothness Estimation", "Description": "Generalized additive (mixed) models, some of their extensions and  other generalized ridge regression with multiple smoothing  parameter estimation by (Restricted) Marginal Likelihood,  Generalized Cross Validation and similar, or using iterated  nested Laplace approximation for fully Bayesian inference. See  <PERSON> (2017) <doi:10.1201/9781315370279> for an overview.  Includes a gam() function, a wide variety of smoothers, 'JAGS'  support and distributions beyond the exponential family.", "Priority": "recommended", "Depends": ["R (>= 3.6.0)", "nlme (>= 3.1-64)"], "Imports": ["methods", "stats", "graphics", "Matrix", "splines", "utils"], "Suggests": ["parallel", "survival", "MASS"], "LazyLoad": "yes", "ByteCompile": "yes", "License": "GPL (>= 2)", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "microbenchmark": {"Package": "microbenchmark", "Version": "1.5.0", "Source": "Repository", "Title": "Accurate Timing Functions", "Description": "Provides infrastructure to accurately measure and compare the execution time of R expressions.", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"aut\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"ctb\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=c(\"ctb\")), person(\"<PERSON>\", \"<PERSON>\", role=c(\"ctb\")), person(given=c(\"<PERSON>\",\"<PERSON><PERSON>\"), family=\"<PERSON>\", role=\"cre\", email=\"<EMAIL>\"))", "URL": "https://github.com/joshuaulrich/microbenchmark/", "BugReports": "https://github.com/joshuaulrich/microbenchmark/issues/", "License": "BSD_2_clause + file LICENSE", "Depends": ["R (>= 3.2.0)"], "Imports": ["graphics", "stats"], "Suggests": ["ggplot2", "multcomp", "RUnit"], "SystemRequirements": "On a Unix-alike, one of the C functions mach_absolute_time (macOS), clock_gettime or gethrtime. If none of these is found, the obsolescent POSIX function gettimeofday will be tried.", "ByteCompile": "yes", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [cre]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "mime": {"Package": "mime", "Version": "0.13", "Source": "Repository", "Type": "Package", "Title": "Map Filenames to MIME Types", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\", URL = \"https://yihui.org\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\") )", "Description": "Guesses the MIME type from a filename extension using the data derived from /etc/mime.types in UNIX-type systems.", "Imports": ["tools"], "License": "GPL", "URL": "https://github.com/yihui/mime", "BugReports": "https://github.com/yihui/mime/issues", "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0645-5666>, https://yihui.org), <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "miniUI": {"Package": "miniUI", "Version": "0.1.2", "Source": "Repository", "Type": "Package", "Title": "Shiny UI Widgets for Small Screens", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", role = c(\"cre\", \"aut\"), email = \"<EMAIL>\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"03wc8by49\")) )", "Description": "Provides UI widget and layout functions for writing Shiny apps that work well on small screens.", "License": "GPL-3", "URL": "https://github.com/rstudio/miniUI", "BugReports": "https://github.com/rstudio/miniUI/issues", "Imports": ["shiny (>= 0.13)", "htmltools (>= 0.3)", "utils"], "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "no", "Author": "<PERSON> [cre, aut], Posit Software, PBC [cph, fnd] (03wc8by49)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "minqa": {"Package": "minqa", "Version": "1.2.8", "Source": "Repository", "Type": "Package", "Title": "Derivative-Free Optimization Algorithms by Quadratic Approximation", "Authors@R": "c(person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"aut\"), person(given = c(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\"), family = \"Mullen\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(given = c(\"<PERSON>\", \"<PERSON><PERSON>\"), family = \"<PERSON>\", role = \"aut\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON><PERSON>\", role = \"aut\"))", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Description": "Derivative-free optimization by quadratic approximation based on an interface to Fortran implementations by <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>.", "License": "GPL-2", "URL": "http://optimizer.r-forge.r-project.org", "Imports": ["Rcpp (>= 0.9.10)"], "LinkingTo": ["Rcpp"], "SystemRequirements": "GNU make", "NeedsCompilation": "yes", "Repository": "CRAN", "Author": "<PERSON> [aut], <PERSON><PERSON> [aut, cre], <PERSON> [aut], <PERSON> [aut]"}, "modelr": {"Package": "modelr", "Version": "0.1.11", "Source": "Repository", "Title": "Modelling Functions that Work with the Pipe", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Functions for modelling that help you seamlessly integrate modelling into a pipeline of data manipulation and visualisation.", "License": "GPL-3", "URL": "https://modelr.tidyverse.org, https://github.com/tidyverse/modelr", "BugReports": "https://github.com/tidyverse/modelr/issues", "Depends": ["R (>= 3.2)"], "Imports": ["broom", "magrit<PERSON>", "purrr (>= 0.2.2)", "rlang (>= 1.0.6)", "tibble", "tidyr (>= 0.8.0)", "tidyselect", "vctrs"], "Suggests": ["compiler", "covr", "ggplot2", "testthat (>= 3.0.0)"], "Config/Needs/website": "tidyverse/tidytemplate", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.2.3", "Config/testthat/edition": "3", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "mvtnorm": {"Package": "mvtnorm", "Version": "1.3-3", "Source": "Repository", "Title": "Multivariate Normal and t Distributions", "Date": "2025-01-09", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-6294-8185\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-8685-9910\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-8301-0471\")))", "Description": "Computes multivariate normal and t probabilities, quantiles, random deviates,  and densities. Log-likelihoods for multivariate Gaussian models and Gaussian copulae  parameterised by Cholesky factors of covariance or precision matrices are implemented  for interval-censored and exact data, or a mix thereof. Score functions for these  log-likelihoods are available. A class representing multiple lower triangular matrices  and corresponding methods are part of this package.", "Imports": ["stats"], "Depends": ["R(>= 3.5.0)"], "Suggests": ["qrng", "numDeriv"], "License": "GPL-2", "URL": "http://mvtnorm.R-forge.R-project.org", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON><PERSON> [aut], <PERSON><PERSON><PERSON> [aut], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-6294-8185>), <PERSON> [ctb] (<https://orcid.org/0000-0002-8685-9910>), <PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0001-8301-0471>)", "Maintainer": "<PERSON><PERSON> Hothorn <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "nlme": {"Package": "nlme", "Version": "3.1-168", "Source": "Repository", "Date": "2025-03-31", "Priority": "recommended", "Title": "Linear and Nonlinear Mixed Effects Models", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", comment = \"S version\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", comment = \"up to 2007\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\", comment = \"up to 2002\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = \"up to 2005\"), person(\"EISPACK authors\", role = \"ctb\", comment = \"src/rs.f\"), person(\"<PERSON><PERSON>\", \"Heisterka<PERSON>\", role = \"ctb\", comment = \"Author fixed sigma\"), person(\"<PERSON>\", \"<PERSON>\",role = \"ctb\", comment = \"Programmer fixed sigma\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", comment = \"varConstProp()\"), person(\"R Core Team\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ROR = \"02zz1nj61\")))", "Contact": "see 'MailingList'", "Description": "Fit and compare Gaussian linear and nonlinear mixed-effects models.", "Depends": ["R (>= 3.6.0)"], "Imports": ["graphics", "stats", "utils", "lattice"], "Suggests": ["MASS", "SASmixed"], "LazyData": "yes", "Encoding": "UTF-8", "License": "GPL (>= 2)", "BugReports": "https://bugs.r-project.org", "MailingList": "<EMAIL>", "URL": "https://svn.r-project.org/R-packages/trunk/nlme/", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (S version), <PERSON> [aut] (up to 2007), <PERSON><PERSON> [ctb] (up to 2002), <PERSON><PERSON><PERSON> [ctb] (up to 2005), EISPACK authors [ctb] (src/rs.f), <PERSON><PERSON> [ctb] (Author fixed sigma), <PERSON> [ctb] (Programmer fixed sigma), <PERSON> [ctb] (varConstProp()), R Core Team [aut, cre] (02zz1nj61)", "Maintainer": "R Core Team <<EMAIL>>", "Repository": "CRAN"}, "nloptr": {"Package": "nloptr", "Version": "2.2.1", "Source": "Repository", "Type": "Package", "Title": "R Interface to NLopt", "Authors@R": "c(person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(c(\"<PERSON>\", \"<PERSON><PERSON>\"), \"<PERSON>\", role = \"aut\", comment = \"author of the NLopt C library\"), person(\"Aymeric\", \"Stamm\", role = c(\"ctb\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-8725-3654\")), person(c(\"<PERSON>\", \"<PERSON><PERSON>\"), \"<PERSON><PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON>buette<PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = \"build process on multiple OS\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", comment = \"build process on multiple OS\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\",  email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-3039-0703\")), person(\"Xiongtao\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>en\", \"Ooms\", role = \"ctb\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>bera\", role = \"ctb\"), person(\"<PERSON>kael\", \"<PERSON><PERSON>\", role = \"ctb\"))", "Description": "Solve optimization problems using an R interface to NLopt. NLopt is a  free/open-source library for nonlinear optimization, providing a common interface for a number of different free optimization routines available online as well as original implementations of various other algorithms. See <https://nlopt.readthedocs.io/en/latest/NLopt_Algorithms/> for more information on the available algorithms. Building from included sources  requires 'CMake'. On Linux and 'macOS', if a suitable system build of  NLopt (2.7.0 or later) is found, it is used; otherwise, it is built  from included sources via 'CMake'. On Windows, NLopt is obtained through  'rwinlib' for 'R <= 4.1.x' or grabbed from the appropriate toolchain for 'R >= 4.2.0'.", "License": "LGPL (>= 3)", "SystemRequirements": "cmake (>= 3.2.0) which is used only on Linux or macOS systems when no system build of nlopt (>= 2.7.0) can be found.", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "Suggests": ["knitr", "rmarkdown", "covr", "tinytest"], "VignetteBuilder": "knitr", "URL": "https://github.com/astamm/nloptr, https://astamm.github.io/nloptr/", "BugReports": "https://github.com/astamm/nloptr/issues", "NeedsCompilation": "yes", "UseLTO": "yes", "Author": "<PERSON><PERSON> [aut], <PERSON> [aut] (author of the NLopt C library), <PERSON><PERSON><PERSON> [ctb, cre] (<https://orcid.org/0000-0002-8725-3654>), <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb] (build process on multiple OS), <PERSON> [ctb] (build process on multiple OS), <PERSON> [ctb], <PERSON><PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-3039-0703>), <PERSON><PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb]", "Maintainer": "Aymeric Stamm <<EMAIL>>", "Repository": "CRAN"}, "nnet": {"Package": "nnet", "Version": "7.3-20", "Source": "Repository", "Priority": "recommended", "Date": "2025-01-01", "Depends": ["R (>= 3.0.0)", "stats", "utils"], "Suggests": ["MASS"], "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\", \"cph\"), email = \"<PERSON>.<PERSON>@R-project.org\"), person(\"<PERSON>\", \"Vena<PERSON>\", role = \"cph\"))", "Description": "Software for feed-forward neural networks with a single hidden layer, and for multinomial log-linear models.", "Title": "Feed-Forward Neural Networks and Multinomial Log-Linear Models", "ByteCompile": "yes", "License": "GPL-2 | GPL-3", "URL": "http://www.stats.ox.ac.uk/pub/MASS4/", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre, cph], <PERSON> [cph]", "Maintainer": "<PERSON> <<PERSON><PERSON>@R-project.org>", "Repository": "CRAN"}, "nortest": {"Package": "nortest", "Version": "1.0-4", "Source": "Repository", "Title": "Tests for Normality", "Date": "2015-07-29", "Description": "Five omnibus tests for testing the composite hypothesis of normality.", "License": "GPL (>= 2)", "Authors@R": "c(person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"Uwe\", \"Ligges\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"))", "Imports": ["stats"], "Author": "<PERSON><PERSON><PERSON> [aut], <PERSON><PERSON> [aut, cre]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "NeedsCompilation": "no", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "numDeriv": {"Package": "numDeriv", "Version": "2016.8-1.1", "Source": "Repository", "Title": "Accurate Numerical Derivatives", "Description": "Methods for calculating (usually) accurate numerical first and second order derivatives. Accurate calculations  are done using 'Richardson''s' extrapolation or, when applicable, a complex step derivative is available. A simple difference  method is also provided. Simple difference is (usually) less accurate but is much quicker than '<PERSON>''s' extrapolation and provides a  useful cross-check.  Methods are provided for real scalar and vector valued functions.", "Depends": ["R (>= 2.11.1)"], "LazyLoad": "yes", "ByteCompile": "yes", "License": "GPL-2", "Copyright": "2006-2011, Bank of Canada. 2012-2016, <PERSON>", "Author": "<PERSON> and <PERSON>", "Maintainer": "<PERSON> <<EMAIL>>", "URL": "http://optimizer.r-forge.r-project.org/", "NeedsCompilation": "no", "Repository": "CRAN"}, "olsrr": {"Package": "olsrr", "Version": "0.6.1", "Source": "Repository", "Type": "Package", "Title": "Tools for Building OLS Regression Models", "Authors@R": "person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\"))", "Description": "Tools designed to make it easier for users, particularly beginner/intermediate R users  to build ordinary least squares regression models. Includes comprehensive regression output,  heteroskedasticity tests, collinearity diagnostics, residual diagnostics, measures of influence,  model fit assessment and variable selection procedures.", "Depends": ["R(>= 3.3)"], "Imports": ["car", "ggplot2", "goftest", "graphics", "gridExtra", "nortest", "stats", "utils", "xplorerr"], "Suggests": ["covr", "descriptr", "knitr", "rmarkdown", "testthat", "vdiffr"], "License": "MIT + file LICENSE", "URL": "https://olsrr.rsquaredacademy.com/, https://github.com/rsquaredacademy/olsrr", "BugReports": "https://github.com/rsquaredacademy/olsrr/issues", "Encoding": "UTF-8", "LazyData": "true", "VignetteBuilder": "knitr", "RoxygenNote": "7.3.2", "Config/testthat/edition": "3", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "opdisDownsampling": {"Package": "opdisDownsampling", "Version": "1.0.1", "Source": "Repository", "Type": "Package", "Title": "Optimal Distribution Preserving Down-Sampling of Bio-Medical Data", "Description": "An optimized method for distribution-preserving class-proportional down-sampling of bio-medical data.", "Depends": ["R (>= 3.5.0)"], "Imports": ["parallel", "graphics", "methods", "stats", "caTools", "pracma", "twosamples", "doP<PERSON>llel", "pbmcapply", "foreach"], "LazyData": "true", "Suggests": ["testthat"], "License": "GPL-3", "URL": "https://cran.r-project.org/package=opdisDownsampling", "Encoding": "UTF-8", "Author": "<PERSON><PERSON> [aut,cre] (<https://orcid.org/0000-0002-5818-6958>), <PERSON> [aut] (<https://orcid.org/0000-0001-6766-140X>), <PERSON> [aut] (<https://orcid.org/0000-0002-7845-3283>)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "NeedsCompilation": "no", "Repository": "https://packagemanager.posit.co/cran/latest"}, "openssl": {"Package": "openssl", "Version": "2.3.3", "Source": "Repository", "Type": "Package", "Title": "Toolkit for Encryption, Signatures and Certificates Based on OpenSSL", "Authors@R": "c(person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"))", "Description": "Bindings to OpenSSL libssl and libcrypto, plus custom SSH key parsers. Supports RSA, DSA and EC curves P-256, P-384, P-521, and curve25519. Cryptographic signatures can either be created and verified manually or via x509 certificates.  AES can be used in cbc, ctr or gcm mode for symmetric encryption; RSA for asymmetric (public key) encryption or EC for <PERSON><PERSON><PERSON>. High-level envelope functions  combine RSA and AES for encrypting arbitrary sized data. Other utilities include key generators, hash functions (md5, sha1, sha256, etc), base64 encoder, a secure random number generator, and 'bignum' math methods for manually performing crypto  calculations on large multibyte integers.", "License": "MIT + file LICENSE", "URL": "https://jeroen.r-universe.dev/openssl", "BugReports": "https://github.com/jeroen/openssl/issues", "SystemRequirements": "OpenSSL >= 1.0.2", "VignetteBuilder": "knitr", "Imports": ["askpass"], "Suggests": ["curl", "testthat (>= 2.1.0)", "digest", "knitr", "rmarkdown", "jsonlite", "jose", "sodium"], "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-4035-0289>), <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "packrat": {"Package": "packrat", "Version": "0.9.3", "Source": "Repository", "Type": "Package", "Title": "A Dependency Management System for Projects and their R Package Dependencies", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Manage the R packages your project depends on in an isolated, portable, and reproducible way.", "License": "GPL-2", "URL": "https://github.com/rstudio/packrat", "BugReports": "https://github.com/rstudio/packrat/issues", "Depends": ["R (>= 3.0.0)"], "Imports": ["tools", "utils"], "Suggests": ["devtools", "httr", "knitr", "mockery", "rmarkdown", "testthat (>= 3.0.0)", "webfakes", "withr"], "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON> [aut], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "patchwork": {"Package": "patchwork", "Version": "1.3.1", "Source": "Repository", "Type": "Package", "Title": "The Composer of Plots", "Authors@R": "person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = c(\"cre\", \"aut\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-5147-4711\"))", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "The 'ggplot2' package provides a strong API for sequentially  building up a plot, but does not concern itself with composition of multiple plots. 'patchwork' is a package that expands the API to allow for  arbitrarily complex composition of plots by, among others, providing  mathematical operators for combining multiple plots. Other packages that try  to address this need (but with a different approach) are 'gridExtra' and  'cowplot'.", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "Imports": ["ggplot2 (>= 3.0.0)", "gtable (>= 0.3.6)", "grid", "stats", "grDevices", "utils", "graphics", "rlang (>= 1.0.0)", "cli", "farver"], "RoxygenNote": "7.3.2", "URL": "https://patchwork.data-imaginist.com, https://github.com/thomasp85/patchwork", "BugReports": "https://github.com/thomasp85/patchwork/issues", "Suggests": ["knitr", "rmarkdown", "gridGraphics", "gridExtra", "ragg", "testthat (>= 2.1.0)", "vdiffr", "covr", "png", "gt (>= 0.11.0)"], "VignetteBuilder": "knitr", "Config/Needs/website": "gifski", "NeedsCompilation": "no", "Author": "<PERSON> [cre, aut] (ORCID: <https://orcid.org/0000-0002-5147-4711>)", "Repository": "https://packagemanager.posit.co/cran/latest"}, "pbapply": {"Package": "pbapply", "Version": "1.7-2", "Source": "Repository", "Type": "Package", "Title": "Adding Progress Bar to '*apply' Functions", "Date": "2023-06-27", "Authors@R": "c(person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON>\", comment = c(ORCID = \"0000-0001-7337-1740\"), role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(given = \"Zygmunt\", family = \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(given = \"<PERSON>\",  family = \"<PERSON><PERSON><PERSON>\",  role = \"ctb\", email = \"<EMAIL>\"), person(\"R Core Team\", role = c(\"cph\", \"ctb\")))", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "A lightweight package that adds progress bar to vectorized R functions ('*apply'). The implementation can easily be added to functions where showing the progress is useful (e.g. bootstrap). The type and style of the progress bar (with percentages or remaining time) can be set through options. Supports several parallel processing backends including future.", "Depends": ["R (>= 3.2.0)"], "Imports": ["parallel"], "Suggests": ["shiny", "future", "future.apply"], "License": "GPL (>= 2)", "URL": "https://github.com/psolymos/pbapply", "BugReports": "https://github.com/psolymos/pbapply/issues", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0001-7337-1740>), <PERSON><PERSON><PERSON><PERSON><PERSON> [aut], <PERSON> [ctb], R Core <PERSON> [cph, ctb]", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "pbkrtest": {"Package": "pbkrtest", "Version": "0.5.4", "Source": "Repository", "Title": "Parametric Bootstrap, <PERSON><PERSON><PERSON><PERSON> and <PERSON> Based Methods for Test in Mixed Models", "Authors@R": "c( person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cph\")), person(given = \"<PERSON><PERSON><PERSON>\", family = \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\", \"cph\")) )", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Description": "Computes p-values based on (a) <PERSON><PERSON><PERSON><PERSON><PERSON> or <PERSON><PERSON><PERSON> degree of freedom methods and (b) parametric bootstrap for mixed effects models as implemented in the 'lme4' package. Implements parametric bootstrap test for generalized linear mixed models as implemented in 'lme4' and generalized linear models. The package is documented in the paper by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>, (2012, <doi:10.18637/jss.v059.i09>).  Please see 'citation(\"pbkrtest\")' for citation details.", "URL": "https://people.math.aau.dk/~sorenh/software/pbkrtest/", "Depends": ["R (>= 4.2.0)", "lme4 (>= 1.1.31)"], "Imports": ["broom", "dplyr", "MASS", "methods", "numDeriv", "Matrix (>= 1.2.3)", "doBy (>= 4.6.22)"], "Suggests": ["markdown", "knitr"], "Encoding": "UTF-8", "VignetteBuilder": "knitr", "License": "GPL (>= 2)", "ByteCompile": "Yes", "RoxygenNote": "7.3.2", "LazyData": "true", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cph], <PERSON><PERSON><PERSON> [aut, cre, cph]", "Repository": "CRAN"}, "pbmcapply": {"Package": "pbmcapply", "Version": "1.5.1", "Source": "Repository", "Type": "Package", "Title": "Tracking the Progress of Mc*pply with Progress Bar", "Author": "<PERSON> (aut), <PERSON><PERSON><PERSON> (ctb), <PERSON> (ctb)", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "A light-weight package helps you track and visualize the progress of parallel version of vectorized R functions (mc*apply). Parallelization (mc.core > 1) works only on *nix (Linux, Unix such as macOS) system due to the lack of fork() functionality, which is essential for mc*apply, on Windows.", "Depends": ["utils", "parallel"], "BugReports": "https://github.com/kvnkuang/pbmcapply/issues", "URL": "https://github.com/kvnkuang/pbmcapply", "License": "MIT + file LICENSE", "RoxygenNote": "6.1.1", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Repository": "https://packagemanager.posit.co/cran/latest"}, "phosphoricons": {"Package": "phosphoricons", "Version": "0.2.1", "Source": "Repository", "Title": "'Phosphor' Icons for R", "Authors@R": "c(person(given = \"<PERSON>\", family = \"Perrier\", role = c(\"aut\", \"cre\", \"cph\"), email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"aut\"), person(given = \"Phosphor Icons\", role = \"cph\", comment = \"Phosphor Icons <https://github.com/phosphor-icons>\"))", "Description": "Use 'Phosphor' icons in 'shiny' applications or 'rmarkdown' documents. Icons are available in 5 different weights and can be customized by setting color, size, orientation and more.", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "Depends": ["R (>= 2.10)"], "Imports": ["htmltools (>= 0.3)"], "Suggests": ["covr", "shiny", "tinytest"], "URL": "https://dreamrs.github.io/phosphoricons/, https://github.com/dreamRs/phosphoricons", "BugReports": "https://github.com/dreamRs/phosphoricons/issues", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre, cph], <PERSON> [aut], <PERSON><PERSON><PERSON><PERSON> [cph] (Phosphor Icons <https://github.com/phosphor-icons>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "pillar": {"Package": "pillar", "Version": "1.10.2", "Source": "Repository", "Title": "Coloured Formatting for Columns", "Authors@R": "c(person(given = \"Kirill\", family = \"M\\u00fcller\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(given = \"<PERSON>\", family = \"Wickham\", role = \"aut\"), person(given = \"RStudio\", role = \"cph\"))", "Description": "Provides 'pillar' and 'colonnade' generics designed for formatting columns of data using the full range of colours provided by modern terminals.", "License": "MIT + file LICENSE", "URL": "https://pillar.r-lib.org/, https://github.com/r-lib/pillar", "BugReports": "https://github.com/r-lib/pillar/issues", "Imports": ["cli (>= 2.3.0)", "glue", "lifecycle", "rlang (>= 1.0.2)", "utf8 (>= 1.1.0)", "utils", "vctrs (>= 0.5.0)"], "Suggests": ["bit64", "DBI", "debugme", "DiagrammeR", "dplyr", "formattable", "ggplot2", "knitr", "lubridate", "nanotime", "nycflights13", "palmerpenguins", "rmarkdown", "scales", "stringi", "survival", "testthat (>= 3.1.1)", "tibble", "units (>= 0.7.2)", "vdiffr", "withr"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.3.2.9000", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "format_multi_fuzz, format_multi_fuzz_2, format_multi, ctl_colonnade, ctl_colonnade_1, ctl_colonnade_2", "Config/autostyle/scope": "line_breaks", "Config/autostyle/strict": "true", "Config/gha/extra-packages": "units=?ignore-before-r=4.3.0", "Config/Needs/website": "tidyverse/tidytemplate", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-1416-3412>), <PERSON> [aut], <PERSON>tudio [cph]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "pkgbuild": {"Package": "pkgbuild", "Version": "1.4.8", "Source": "Repository", "Title": "Find Tools Needed to Build R Packages", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"03wc8by49\")) )", "Description": "Provides functions used to build R packages. Locates compilers needed to build R packages on various platforms and ensures the PATH is configured appropriately so R can use them.", "License": "MIT + file LICENSE", "URL": "https://github.com/r-lib/pkgbuild, https://pkgbuild.r-lib.org", "BugReports": "https://github.com/r-lib/pkgbuild/issues", "Depends": ["R (>= 3.5)"], "Imports": ["callr (>= 3.2.0)", "cli (>= 3.4.0)", "desc", "processx", "R6"], "Suggests": ["covr", "cpp11", "knitr", "Rcpp", "rmarkdown", "testthat (>= 3.2.0)", "withr (>= 2.3.0)"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/usethis/last-upkeep": "2025-04-30", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [aut, cre], Posit Software, PBC [cph, fnd] (ROR: <https://ror.org/03wc8by49>)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "pkgconfig": {"Package": "pkgconfig", "Version": "2.0.3", "Source": "Repository", "Title": "Private Configuration for 'R' Packages", "Author": "<PERSON><PERSON><PERSON>", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Description": "Set configuration options on a per-package basis. Options set by a given package only apply to that package, other packages are unaffected.", "License": "MIT + file LICENSE", "LazyData": "true", "Imports": ["utils"], "Suggests": ["covr", "testthat", "disposables (>= 1.0.3)"], "URL": "https://github.com/r-lib/pkgconfig#readme", "BugReports": "https://github.com/r-lib/pkgconfig/issues", "Encoding": "UTF-8", "NeedsCompilation": "no", "Repository": "CRAN"}, "pkgload": {"Package": "pkgload", "Version": "1.4.0", "Source": "Repository", "Title": "Simulate Package Installation and Attach", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(\"R Core team\", role = \"ctb\", comment = \"Some namespace and vignette code extracted from base R\") )", "Description": "Simulates the process of installing a package and then attaching it. This is a key part of the 'devtools' package as it allows you to rapidly iterate while developing a package.", "License": "GPL-3", "URL": "https://github.com/r-lib/pkgload, https://pkgload.r-lib.org", "BugReports": "https://github.com/r-lib/pkgload/issues", "Depends": ["R (>= 3.4.0)"], "Imports": ["cli (>= 3.3.0)", "desc", "fs", "glue", "lifecycle", "methods", "pkgbuild", "processx", "rlang (>= 1.1.1)", "rprojroot", "utils", "withr (>= 2.4.3)"], "Suggests": ["bitops", "jsonlite", "mathjaxr", "pak", "Rcpp", "remotes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "testthat (>= *******)", "usethis"], "Config/Needs/website": "tidyverse/tidytemplate, ggplot2", "Config/testthat/edition": "3", "Config/testthat/parallel": "TRUE", "Config/testthat/start-first": "dll", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut, cre], Posit Software, PBC [cph, fnd], R Core team [ctb] (Some namespace and vignette code extracted from base R)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "plotly": {"Package": "plotly", "Version": "4.11.0", "Source": "Repository", "Title": "Create Interactive Web Graphics via 'plotly.js'", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"Hocking\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1994-3581\")), person(\"<PERSON>\", \"Despouy\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-5329-5987\")), person(\"Plotly Technologies Inc.\", role = \"cph\"))", "License": "MIT + file LICENSE", "Description": "Create interactive web graphics from 'ggplot2' graphs and/or a custom interface to the (MIT-licensed) JavaScript library 'plotly.js' inspired by the grammar of graphics.", "URL": "https://plotly-r.com, https://github.com/plotly/plotly.R, https://plotly.com/r/", "BugReports": "https://github.com/plotly/plotly.R/issues", "Depends": ["R (>= 3.2.0)", "ggplot2 (>= 3.0.0)"], "Imports": ["tools", "scales", "httr (>= 1.3.0)", "jsonlite (>= 1.6)", "magrit<PERSON>", "digest", "viridisLite", "base64enc", "htmltools (>= 0.3.6)", "htmlwidgets (>= 1.5.2.9001)", "tidyr (>= 1.0.0)", "RColorBrewer", "dplyr", "vctrs", "tibble", "lazyeval (>= 0.2.0)", "rlang (>= 1.0.0)", "crosstalk", "purrr", "data.table", "promises"], "Suggests": ["MASS", "maps", "hex<PERSON>", "ggthemes", "GGally", "ggalluvial", "testthat", "knitr", "shiny (>= 1.1.0)", "shinytest2", "curl", "rmarkdown", "Cairo", "broom", "webshot", "listviewer", "dendextend", "sf", "png", "IRdisplay", "processx", "plotlyGeoAssets", "forcats", "withr", "palmerpenguins", "rversions", "reticulate", "rsvg", "ggridges"], "LazyData": "true", "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "Config/Needs/check": "tidyverse/ggplot2, ggobi/GGally, rcmdcheck, devtools, reshape2, s2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-4958-2844>), <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [aut], <PERSON> [aut] (ORCID: <https://orcid.org/0000-0002-1994-3581>), <PERSON> [aut], <PERSON><PERSON> [ctb] (ORCID: <https://orcid.org/0000-0002-5329-5987>), Plotly Technologies Inc. [cph]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "plyr": {"Package": "plyr", "Version": "1.8.9", "Source": "Repository", "Title": "Tools for Splitting, Applying and Combining Data", "Authors@R": "person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"))", "Description": "A set of tools that solves a common set of problems: you need to break a big problem down into manageable pieces, operate on each piece and then put all the pieces back together.  For example, you might want to fit a model to each spatial location or time point in your study, summarise data by panels or collapse high-dimensional arrays to simpler summary statistics. The development of 'plyr' has been generously supported by 'Becton Dickinson'.", "License": "MIT + file LICENSE", "URL": "http://had.co.nz/plyr, https://github.com/hadley/plyr", "BugReports": "https://github.com/hadley/plyr/issues", "Depends": ["R (>= 3.1.0)"], "Imports": ["Rcpp (>= 0.11.0)"], "Suggests": ["abind", "covr", "doP<PERSON>llel", "foreach", "iterators", "itertools", "tcltk", "testthat"], "LinkingTo": ["Rcpp"], "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.2.3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "polynom": {"Package": "polynom", "Version": "1.4-1", "Source": "Repository", "Title": "A Collection of Functions to Implement a Class for Univariate Polynomial Manipulations", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = \"S original\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", email = \"<PERSON><PERSON>@R-project.org\", comment = \"R port\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", email = \"maech<PERSON>@stat.math.ethz.ch\", comment = \"R port\"))", "Description": "A collection of functions to implement a class for univariate polynomial manipulations.", "Imports": ["stats", "graphics"], "License": "GPL-2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (S original), <PERSON> [aut] (R port), <PERSON> [aut] (R port)", "Maintainer": "<PERSON> <<PERSON>.<EMAIL>>", "Suggests": ["knitr", "rmarkdown"], "VignetteBuilder": "knitr", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "pracma": {"Package": "pracma", "Version": "2.4.4", "Source": "Repository", "Type": "Package", "Date": "2023-11-08", "Title": "Practical Numerical Math Functions", "Authors@R": "person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\",  email=\"<EMAIL>\",  role=c(\"aut\", \"cre\"))", "Depends": ["R (>= 3.1.0)"], "Imports": ["graphics", "grDevices", "stats", "utils"], "Suggests": ["NlcOptim", "quadprog"], "Description": "Provides a large number of functions from numerical analysis and linear algebra, numerical optimization, differential equations, time series, plus some well-known special mathematical functions. Uses 'MATLAB' function names where appropriate to simplify porting.", "License": "GPL (>= 3)", "ByteCompile": "true", "LazyData": "yes", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "praise": {"Package": "praise", "Version": "1.0.0", "Source": "Repository", "Title": "Praise Users", "Author": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Description": "Build friendly R packages that praise their users if they have done something good, or they just need it to feel better.", "License": "MIT + file LICENSE", "LazyData": "true", "URL": "https://github.com/gaborcsardi/praise", "BugReports": "https://github.com/gaborcsardi/praise/issues", "Suggests": ["testthat"], "Collate": "'adjective.R' 'adverb.R' 'exclamation.R' 'verb.R' 'rpackage.R' 'package.R'", "NeedsCompilation": "no", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "prettyunits": {"Package": "prettyunits", "Version": "1.2.0", "Source": "Repository", "Title": "Pretty, Human Readable Formatting of Quantities", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", email=\"<EMAIL>\", role=c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", email=\"<EMAIL>\", role=c(\"ctb\"), comment=c(ORCID=\"0000-0002-5759-428X\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email=\"<EMAIL>\", role=c(\"ctb\")) )", "Description": "Pretty, human readable formatting of quantities. Time intervals: '1337000' -> '15d 11h 23m 20s'. Vague time intervals: '2674000' -> 'about a month ago'. Bytes: '1337' -> '1.34 kB'. Rounding: '99' with 3 significant digits -> '99.0' p-values: '0.00001' -> '<0.0001'. Colors: '#FF0000' -> 'red'. Quantities: '1239437' -> '1.24 M'.", "License": "MIT + file LICENSE", "URL": "https://github.com/r-lib/prettyunits", "BugReports": "https://github.com/r-lib/prettyunits/issues", "Depends": ["R(>= 2.10)"], "Suggests": ["codetools", "covr", "testthat"], "RoxygenNote": "7.2.3", "Encoding": "UTF-8", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON> [ctb] (<https://orcid.org/0000-0002-5759-428X>), <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "processx": {"Package": "processx", "Version": "3.8.6", "Source": "Repository", "Title": "Execute and Control System Processes", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\", \"cph\"), comment = c(ORCID = \"0000-0001-7098-9676\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(\"Ascent Digital Services\", role = c(\"cph\", \"fnd\")) )", "Description": "Tools to run system processes in the background.  It can check if a background process is running; wait on a background process to finish; get the exit status of finished processes; kill background processes. It can read the standard output and error of the processes, using non-blocking connections. 'processx' can poll a process for standard output or error, with a timeout. It can also poll several processes at once.", "License": "MIT + file LICENSE", "URL": "https://processx.r-lib.org, https://github.com/r-lib/processx", "BugReports": "https://github.com/r-lib/processx/issues", "Depends": ["R (>= 3.4.0)"], "Imports": ["ps (>= 1.2.0)", "R6", "utils"], "Suggests": ["callr (>= 3.7.3)", "cli (>= 3.3.0)", "codetools", "covr", "curl", "debugme", "parallel", "rlang (>= 1.0.2)", "testthat (>= 3.0.0)", "webfakes", "withr"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.1.9000", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre, cph] (<https://orcid.org/0000-0001-7098-9676>), <PERSON> [aut], Posit Software, PBC [cph, fnd], Ascent Digital Services [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "progress": {"Package": "progress", "Version": "1.2.3", "Source": "Repository", "Title": "Terminal Progress Bars", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Configurable Progress bars, they may include percentage, elapsed time, and/or the estimated completion time. They work in terminals, in 'Emacs' 'ESS', 'RStudio', 'Windows' 'Rgui' and the 'macOS' 'R.app'. The package also provides a 'C++' 'API', that works with or without 'Rcpp'.", "License": "MIT + file LICENSE", "URL": "https://github.com/r-lib/progress#readme, http://r-lib.github.io/progress/", "BugReports": "https://github.com/r-lib/progress/issues", "Depends": ["R (>= 3.6)"], "Imports": ["crayon", "hms", "prettyunits", "R6"], "Suggests": ["Rcpp", "testthat (>= 3.0.0)", "withr"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON> [aut], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "promises": {"Package": "promises", "Version": "1.3.3", "Source": "Repository", "Type": "Package", "Title": "Abstractions for Promise-Based Asynchronous Programming", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"03wc8by49\")) )", "Description": "Provides fundamental abstractions for doing asynchronous programming in R using promises. Asynchronous programming is useful for allowing a single R process to orchestrate multiple tasks in the background while also attending to something else. Semantics are similar to 'JavaScript' promises, but with a syntax that is idiomatic R.", "License": "MIT + file LICENSE", "URL": "https://rstudio.github.io/promises/, https://github.com/rstudio/promises", "BugReports": "https://github.com/rstudio/promises/issues", "Imports": ["fastmap (>= 1.1.0)", "later", "magrittr (>= 1.5)", "R6", "Rcpp", "rlang", "stats"], "Suggests": ["future (>= 1.21.0)", "knitr", "purrr", "rmarkdown", "spelling", "testthat (>= 3.0.0)", "ve<PERSON><PERSON>"], "LinkingTo": ["later", "Rcpp"], "VignetteBuilder": "knitr", "Config/Needs/website": "rsconnect, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/usethis/last-upkeep": "2025-05-27", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd] (ROR: <https://ror.org/03wc8by49>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "proxy": {"Package": "proxy", "Version": "0.4-27", "Source": "Repository", "Type": "Package", "Title": "Distance and Similarity Measures", "Authors@R": "c(person(given = \"<PERSON>\", family = \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<PERSON><PERSON>@R-project.org\"), person(given = \"Christian\", family = \"<PERSON><PERSON><PERSON>\", role = \"aut\"))", "Description": "Provides an extensible framework for the efficient calculation of auto- and cross-proximities, along with implementations of the most popular ones.", "Depends": ["R (>= 3.4.0)"], "Imports": ["stats", "utils"], "Suggests": ["cba"], "Collate": "registry.R database.R dist.R similarities.R dissimilarities.R util.R seal.R", "License": "GPL-2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON> [aut]", "Maintainer": "<PERSON> <<PERSON><PERSON>@R-project.org>", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "ps": {"Package": "ps", "Version": "1.9.1", "Source": "Repository", "Title": "List, Query, Manipulate System Processes", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>'\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "List, query and manipulate all system processes, on 'Windows', 'Linux' and 'macOS'.", "License": "MIT + file LICENSE", "URL": "https://github.com/r-lib/ps, https://ps.r-lib.org/", "BugReports": "https://github.com/r-lib/ps/issues", "Depends": ["R (>= 3.4)"], "Imports": ["utils"], "Suggests": ["callr", "covr", "curl", "pillar", "pingr", "processx (>= 3.1.0)", "R6", "rlang", "testthat (>= 3.0.0)", "webfakes", "withr"], "Biarch": "true", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON><PERSON> [aut], <PERSON><PERSON><PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "purrr": {"Package": "purrr", "Version": "1.0.4", "Source": "Repository", "Title": "Functional Programming Tools", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"03wc8by49\")) )", "Description": "A complete and consistent functional programming toolkit for R.", "License": "MIT + file LICENSE", "URL": "https://purrr.tidyverse.org/, https://github.com/tidyverse/purrr", "BugReports": "https://github.com/tidyverse/purrr/issues", "Depends": ["R (>= 4.0)"], "Imports": ["cli (>= 3.6.1)", "lifecycle (>= 1.0.3)", "magrittr (>= 1.5.0)", "rlang (>= 1.1.1)", "vctrs (>= 0.6.3)"], "Suggests": ["covr", "dplyr (>= 0.7.8)", "httr", "knitr", "lubridate", "rmarkdown", "testthat (>= 3.0.0)", "tibble", "tidyselect"], "LinkingTo": ["cli"], "VignetteBuilder": "knitr", "Biarch": "true", "Config/build/compilation-database": "true", "Config/Needs/website": "tidyverse/tidytemplate, tidyr", "Config/testthat/edition": "3", "Config/testthat/parallel": "TRUE", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0003-4757-117X>), <PERSON> [aut], Posit Software, PBC [cph, fnd] (03wc8by49)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "qqconf": {"Package": "qqconf", "Version": "1.3.2", "Source": "Repository", "Type": "Package", "Title": "Creates Simultaneous Testing Bands for QQ-Plots", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON>cPeek\", email = \"<EMAIL>\", role = \"aut\"), person(\"Ab<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = \"aut\"))", "Description": "Provides functionality for creating Quantile-Quantile (QQ) and Probability-Probability (PP) plots with simultaneous  testing bands to asses significance of sample deviation from a reference distribution <doi:10.18637/jss.v106.i10>.", "License": "GPL-3", "Depends": ["R (>= 4.0.0)"], "SystemRequirements": "fftw3 (>= 3.1.2)", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "Imports": ["MASS (>= 7.3-50)", "robustbase (>= 0.93-4)", "Rcpp"], "Suggests": ["knitr", "rmarkdown", "distr (>= 2.8.0)"], "Collate": "'one_sided.R' 'ppplot.R' 'qqconf-package.R' 'qqplot.R' 'RcppExports.R' 'two_sided.R' 'utils.R'", "VignetteBuilder": "knitr", "LinkingTo": ["Rcpp"], "URL": "https://github.com/eweine/qqconf", "BugReports": "https://github.com/eweine/qqconf/issues", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON> [aut], <PERSON><PERSON><PERSON> [aut]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "qqplotr": {"Package": "qqplotr", "Version": "0.0.6", "Source": "Repository", "Type": "Package", "Title": "Quantile-Quantile Plot Extensions for 'ggplot2'", "Description": "Extensions of 'ggplot2' Q-Q plot functionalities.", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"))", "URL": "https://github.com/aloy/qqplotr", "BugReports": "https://github.com/aloy/qqplotr/issues", "License": "GPL-3 | file LICENSE", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.2.2", "Collate": "'data.R' 'geom_qq_band.R' 'qqplotr.R' 'runShinyExample.R' 'stat_pp_band.R' 'stat_pp_line.R' 'stat_pp_point.R' 'stat_qq_line.R' 'stat_qq_band.R' 'stat_qq_point.R'", "VignetteBuilder": "knitr", "Depends": ["R (>= 3.1)", "ggplot2 (>= 2.2)"], "Imports": ["dplyr", "robustbase", "MASS", "opdisDownsampling", "qqconf (>= 1.3.1)"], "Suggests": ["shiny", "devtools", "lattice", "shinyBS", "knitr", "rmarkdown"], "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut, cre], <PERSON><PERSON> [aut]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "quantreg": {"Package": "quantreg", "Version": "6.1", "Source": "Repository", "Title": "Quantile Regression", "Description": "Estimation and inference methods for models for conditional quantile functions:  Linear and nonlinear parametric and non-parametric (total variation penalized) models  for conditional quantiles of a univariate response and several methods for handling censored survival data.  Portfolio selection methods based on expected shortfall risk are also now included. See <PERSON><PERSON>, <PERSON><PERSON> (2005) Quantile Regression, Cambridge U. Press, <doi:10.1017/CBO9780511754098> and <PERSON><PERSON><PERSON>, <PERSON> et al. (2017) Handbook of Quantile Regression,  CRC Press, <doi:10.1201/9781315120256>.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\",  role = c(\"cre\",\"aut\"), email =  \"r<PERSON><PERSON><EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON>\",  role = c(\"ctb\"),  comment = \"Contributions to Censored QR code\", email =  \"<EMAIL>\"), person(c(\"Pin\", \"<PERSON><PERSON>\"), \"<PERSON>\",  role = c(\"ctb\"),  comment = \"Contributions to Sparse QR code\", email =  \"<EMAIL>\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\",  role = c(\"ctb\"),  comment = \"Contributions to preprocessing code\", email =  \"<EMAIL>\"), person(\"<PERSON>chi<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\",  role = c(\"ctb\"),  comment = \"Contributions to dynrq code essentially identical to his dynlm code\",  email =  \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\",  role = c(\"ctb\"),  comment = \"Contributions to nlrq code\", email =  \"phgro<PERSON><PERSON><PERSON>@sciviews.org\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\",  role = c(\"ctb\"),  comment = \"author of several linpack routines\"), person(\"<PERSON><PERSON>\", \"<PERSON>ad\",  role = c(\"ctb\"),  comment = \"author of sparskit2\"), person(\"<PERSON>\", \"Chernozhukov\",  role = c(\"ctb\"),  comment = \"contributions to extreme value inference code\"), person(\"Ivan\", \"<PERSON>-Val\",  role = c(\"ctb\"),  comment = \"contributions to extreme value inference code\"), person(\"<PERSON>\", \"Maechler\", role = \"ctb\", comment = c(\"tweaks (src/chlfct.f, 'tiny','Large')\", ORCID = \"0000-0002-8685-9910\")), person(c(\"Brian\", \"D\"), \"Ripley\",  role = c(\"trl\",\"ctb\"),  comment = \"Initial (2001) R port from S (to my everlasting shame --  how could I have been so slow to adopt R!) and for numerous other  suggestions and useful advice\", email =  \"<EMAIL>\"))", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN", "Depends": ["R (>= 3.5)", "stats", "SparseM"], "Imports": ["methods", "graphics", "Matrix", "MatrixModels", "survival", "MASS"], "Suggests": ["interp", "rgl", "logspline", "nor1mix", "Formula", "zoo", "R.rsp", "conquer"], "License": "GPL (>= 2)", "URL": "https://www.r-project.org", "NeedsCompilation": "yes", "VignetteBuilder": "R.rsp", "Author": "<PERSON> [cre, aut], <PERSON> [ctb] (Contributions to Censored QR code), <PERSON><PERSON> [ctb] (Contributions to Sparse QR code), <PERSON><PERSON><PERSON> [ctb] (Contributions to preprocessing code), <PERSON><PERSON><PERSON> [ctb] (Contributions to dynrq code essentially identical to his dynlm code), <PERSON> [ctb] (Contributions to nlrq code), <PERSON><PERSON><PERSON> [ctb] (author of several linpack routines), <PERSON><PERSON> [ctb] (author of sparskit2), <PERSON> [ctb] (contributions to extreme value inference code), <PERSON> [ctb] (contributions to extreme value inference code), <PERSON> [ctb] (tweaks (src/chlfct.f, 'tiny','Large'), <https://orcid.org/0000-0002-8685-9910>), <PERSON> [trl, ctb] (Initial (2001) R port from S (to my everlasting shame -- how could I have been so slow to adopt R!) and for numerous other suggestions and useful advice)"}, "rappdirs": {"Package": "rapp<PERSON>s", "Version": "0.3.3", "Source": "Repository", "Type": "Package", "Title": "Application Directories: Determine Where to Save Data, Caches, and Logs", "Authors@R": "c(person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = c(\"trl\", \"cre\", \"cph\"), email = \"<EMAIL>\"), person(given = \"RStudio\", role = \"cph\"), person(given = \"<PERSON><PERSON><PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"aut\"), person(given = \"ActiveState\", role = \"cph\", comment = \"R/appdir.r, R/cache.r, R/data.r, R/log.r translated from appdirs\"), person(given = \"<PERSON>\", family = \"Petrisor\", role = \"ctb\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = c(\"trl\", \"aut\")), person(given = \"<PERSON><PERSON><PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = \"ctb\"))", "Description": "An easy way to determine which directories on the users computer you should use to save data, caches and logs. A port of Python's 'Appdirs' (<https://github.com/ActiveState/appdirs>) to R.", "License": "MIT + file LICENSE", "URL": "https://rappdirs.r-lib.org, https://github.com/r-lib/rappdirs", "BugReports": "https://github.com/r-lib/rappdirs/issues", "Depends": ["R (>= 3.2)"], "Suggests": ["roxygen2", "testthat (>= 3.0.0)", "covr", "withr"], "Copyright": "Original python appdirs module copyright (c) 2010 ActiveState Software Inc. R port copyright Hadley <PERSON>, RStudio. See file LICENSE for details.", "Encoding": "UTF-8", "RoxygenNote": "7.1.1", "Config/testthat/edition": "3", "NeedsCompilation": "yes", "Author": "<PERSON> [trl, cre, cph], <PERSON><PERSON><PERSON> [cph], <PERSON><PERSON><PERSON> [aut], <PERSON> [aut], ActiveState [cph] (R/appdir.r, R/cache.r, R/data.r, R/log.r translated from appdirs), <PERSON> [ctb], <PERSON> [trl, aut], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "rbibutils": {"Package": "rbibutils", "Version": "2.3", "Source": "Repository", "Type": "Package", "Title": "Read 'Bibtex' Files and Convert Between Bibliography Formats", "Authors@R": "c( person(given = c(\"<PERSON><PERSON>\", \"<PERSON>.\"), family = \"<PERSON><PERSON><PERSON><PERSON>\",  role = c(\"aut\", \"cre\"), \t email = \"<EMAIL>\", comment = \"R port, R code, new C code and modifications to bibutils' C code, conversion to Bibentry (R and C code)\" ), person(given = \"<PERSON>\", family = \"Putman\", role = \"aut\", comment = \"src/*, author of the bibutils libraries, https://sourceforge.net/projects/bibutils/\"), person(given = \"<PERSON>\", family = \"Mathar\", role = \"ctb\", comment = \"src/addsout.c\"), person(given = \"<PERSON>\", family = \"Wilm\", role = \"ctb\", comment = \"src/biblatexin.c, src/bltypes.c\"), person(\"R Core Team\", role = \"ctb\", comment = \"base R's bibentry and bibstyle implementation\") )", "Description": "Read and write 'Bibtex' files. Convert between bibliography formats, including 'Bibtex', 'Biblatex', 'PubMed', 'Endnote', and 'Bibentry'.  Includes a port of the 'bibutils' utilities by <PERSON> <https://sourceforge.net/projects/bibutils/>. Supports all bibliography formats and character encodings implemented in 'bibutils'.", "License": "GPL-2", "URL": "https://geobosh.github.io/rbibutils/ (doc), https://github.com/GeoBosh/rbibutils (devel)", "BugReports": "https://github.com/GeoBosh/rbibutils/issues", "Depends": ["R (>= 2.10)"], "Imports": ["utils", "tools"], "Suggests": ["testthat"], "Encoding": "UTF-8", "NeedsCompilation": "yes", "Config/Needs/memcheck": "dev<PERSON><PERSON>, rcmdcheck", "Author": "<PERSON><PERSON> [aut, cre] (R port, R code, new C code and modifications to bibutils' C code, conversion to Bibentry (R and C code)), <PERSON> [aut] (src/*, author of the bibutils libraries, https://sourceforge.net/projects/bibutils/), <PERSON> [ctb] (src/addsout.c), <PERSON> [ctb] (src/biblatexin.c, src/bltypes.c), R Core Team [ctb] (base R's bibentry and bibstyle implementation)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "reactR": {"Package": "reactR", "Version": "0.6.1", "Source": "Repository", "Type": "Package", "Title": "React Helpers", "Date": "2024-09-14", "Authors@R": "c( person( \"<PERSON>\", \"Inc\" , role = c(\"aut\", \"cph\") , comment = \"React library in lib, https://reactjs.org/; see AUTHORS for full list of contributors\" ), person( \"<PERSON>\",\"Weststrate\", , role = c(\"aut\", \"cph\") , comment = \"mobx library in lib, https://github.com/mobxjs\" ), person( \"<PERSON>\", \"<PERSON>\" , role = c(\"aut\", \"cre\") , comment = \"R interface\" , email = \"<EMAIL>\" ), person( \"<PERSON>\", \"<PERSON><PERSON>\" , role = c(\"aut\") , comment = \"R interface\" , email = \"<EMAIL>\" ), person( \"<PERSON>\", \"<PERSON>\" , role = c(\"aut\") , comment = \"R interface\" , email = \"<EMAIL>\" ) )", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "Make it easy to use 'React' in R with 'htmlwidget' scaffolds, helper dependency functions, an embedded 'Babel' 'transpiler', and examples.", "URL": "https://github.com/react-R/reactR", "BugReports": "https://github.com/react-R/reactR/issues", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "Imports": ["htmltools"], "Suggests": ["htmlwidgets (>= 1.5.3)", "rmarkdown", "shiny", "V8", "knitr", "usethis", "jsonlite"], "RoxygenNote": "7.3.2", "VignetteBuilder": "knitr", "NeedsCompilation": "no", "Author": "Facebook Inc [aut, cph] (React library in lib, https://reactjs.org/; see AUTHORS for full list of contributors), <PERSON> [aut, cph] (mobx library in lib, https://github.com/mobxjs), <PERSON> [aut, cre] (R interface), <PERSON> [aut] (R interface), <PERSON> [aut] (R interface)", "Repository": "https://packagemanager.posit.co/cran/latest"}, "reactable": {"Package": "reactable", "Version": "0.4.4", "Source": "Repository", "Type": "Package", "Title": "Interactive Data Tables for R", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"React Table library\"), person(family = \"Emotion team and other contributors\", role = c(\"ctb\", \"cph\"), comment = \"Emotion library\"), person(\"<PERSON>\", \"<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"reactR package\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"htmlwidgets package\"), person(\"<PERSON>\", \"<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"htmlwidgets package\"), person(\"J<PERSON>\", \"Allaire\", role = c(\"ctb\", \"cph\"), comment = \"htmlwidgets package\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"htmlwidgets package\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"htmlwidgets package\"), person(family = \"Facebook, Inc. and its affiliates\", role = c(\"ctb\", \"cph\"), comment = \"React library\"), person(family = \"FormatJS\", role = c(\"ctb\", \"cph\"), comment = \"FormatJS libraries\"), person(family = \"Feross Aboukhadijeh, and other contributors\", role = c(\"ctb\", \"cph\"), comment = \"buffer library\"), person(\"Roman\", \"Shtylman\", role = c(\"ctb\", \"cph\"), comment = \"process library\"), person(\"James\", \"Halliday\", role = c(\"ctb\", \"cph\"), comment = \"stream-browserify library\"), person(family = \"Posit Software, PBC\", role = c(\"fnd\", \"cph\")) )", "Description": "Interactive data tables for R, based on the 'React Table' JavaScript library. Provides an HTML widget that can be used in 'R Markdown' or 'Quarto' documents, 'Shiny' applications, or viewed from an R console.", "License": "MIT + file LICENSE", "URL": "https://glin.github.io/reactable/, https://github.com/glin/reactable", "BugReports": "https://github.com/glin/reactable/issues", "Depends": ["R (>= 3.1)"], "Imports": ["digest", "htmltools (>= 0.5.2)", "htmlwidgets (>= 1.5.3)", "jsonlite", "reactR"], "Suggests": ["covr", "crosstalk", "dplyr", "fontawesome", "knitr", "leaflet", "MASS", "rmarkdown", "shiny", "sparkline", "testthat", "tippy", "V8"], "Encoding": "UTF-8", "RoxygenNote": "7.2.1", "Config/testthat/edition": "3", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON> [ctb, cph] (React Table library), Emotion team and other contributors [ctb, cph] (Emotion library), <PERSON> [ctb, cph] (reactR package), <PERSON><PERSON> [ctb, cph] (htmlwidgets package), <PERSON> [ctb, cph] (htmlwidgets package), <PERSON><PERSON> [ctb, cph] (htmlwidgets package), <PERSON><PERSON> [ctb, cph] (htmlwidgets package), <PERSON><PERSON> [ctb, cph] (htmlwidgets package), Facebook, Inc. and its affiliates [ctb, cph] (React library), FormatJS [ctb, cph] (FormatJS libraries), <PERSON><PERSON>, and other contributors [ctb, cph] (buffer library), <PERSON> [ctb, cph] (process library), <PERSON> [ctb, cph] (stream-browserify library), Posit Software, PBC [fnd, cph]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "readr": {"Package": "readr", "Version": "2.1.5", "Source": "Repository", "Title": "Read Rectangular Text Data", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-6983-2759\")), person(\"<PERSON>\", \"<PERSON>rows\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(\"https://github.com/mandreyel/\", role = \"cph\", comment = \"mio library\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"grisu3 implementation\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"grisu3 implementation\") )", "Description": "The goal of 'readr' is to provide a fast and friendly way to read rectangular data (like 'csv', 'tsv', and 'fwf').  It is designed to flexibly parse many types of data found in the wild, while still cleanly failing when data unexpectedly changes.", "License": "MIT + file LICENSE", "URL": "https://readr.tidyverse.org, https://github.com/tidyverse/readr", "BugReports": "https://github.com/tidyverse/readr/issues", "Depends": ["R (>= 3.6)"], "Imports": ["cli (>= 3.2.0)", "clipr", "crayon", "hms (>= 0.4.1)", "lifecycle (>= 0.2.0)", "methods", "R6", "rlang", "tibble", "utils", "vroom (>= 1.6.0)"], "Suggests": ["covr", "curl", "datasets", "knitr", "rmarkdown", "spelling", "stringi", "testthat (>= 3.2.0)", "tzdb (>= 0.1.1)", "waldo", "withr", "xml2"], "LinkingTo": ["cpp11", "tzdb (>= 0.1.1)"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/testthat/parallel": "false", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.2.3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON><PERSON> [ctb], <PERSON> [aut, cre] (<https://orcid.org/0000-0002-6983-2759>), <PERSON> [ctb], Posit Software, PBC [cph, fnd], https://github.com/mandreyel/ [cph] (mio library), <PERSON><PERSON> [ctb, cph] (grisu3 implementation), <PERSON><PERSON><PERSON> [ctb, cph] (grisu3 implementation)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "readxl": {"Package": "readxl", "Version": "1.4.5", "Source": "Repository", "Title": "Read Excel Files", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\", comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-6983-2759\")), person(\"Posit, PBC\", role = c(\"cph\", \"fnd\"), comment = \"Copyright holder of all R code and all C/C++ code without explicit copyright attribution\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Author of included RapidXML code\"), person(\"<PERSON><PERSON><PERSON> Valery\", role = c(\"ctb\", \"cph\"), comment = \"Author of included libxls code\"), person(\"<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Author of included libxls code\"), person(\"<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Author of included libxls code\"), person(\"<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Author of included libxls code\"), person(\"<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Author of included libxls code\") )", "Description": "Import excel files into R. Supports '.xls' via the embedded 'libxls' C library <https://github.com/libxls/libxls> and '.xlsx' via the embedded 'RapidXML' C++ library <https://rapidxml.sourceforge.net/>. Works on Windows, Mac and Linux without external dependencies.", "License": "MIT + file LICENSE", "URL": "https://readxl.tidyverse.org, https://github.com/tidyverse/readxl", "BugReports": "https://github.com/tidyverse/readxl/issues", "Depends": ["R (>= 3.6)"], "Imports": ["cellranger", "tibble (>= 2.0.1)", "utils"], "Suggests": ["covr", "knitr", "rmarkdown", "testthat (>= 3.1.6)", "withr"], "LinkingTo": ["cpp11 (>= 0.4.0)", "progress"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate, tidyverse", "Config/testthat/edition": "3", "Encoding": "UTF-8", "Note": "libxls v1.6.3 c199d13", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (<https://orcid.org/0000-0003-4757-117X>), <PERSON> [aut, cre] (<https://orcid.org/0000-0002-6983-2759>), <PERSON><PERSON><PERSON>, <PERSON><PERSON> [cph, fnd] (Copyright holder of all R code and all C/C++ code without explicit copyright attribution), <PERSON><PERSON> [ctb, cph] (Author of included RapidXML code), <PERSON><PERSON><PERSON> [ctb, cph] (Author of included libxls code), <PERSON> [ctb, cph] (Author of included libxls code), <PERSON> [ctb, cph] (Author of included libxls code), <PERSON> [ctb, cph] (Author of included libxls code), <PERSON> [ctb, cph] (Author of included libxls code)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "reformulas": {"Package": "reformulas", "Version": "0.4.1", "Source": "Repository", "Title": "Machinery for Processing Random Effect Formulas", "Authors@R": "person(given = \"<PERSON>\", family = \"Bolker\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment=c(ORCID=\"0000-0002-2127-0443\"))", "Description": "Takes formulas including random-effects components (formatted as in 'lme4', 'glmmTMB', etc.) and processes them. Includes various helper functions.", "URL": "https://github.com/bbolker/reformulas", "License": "GPL-3", "Encoding": "UTF-8", "Imports": ["stats", "methods", "Matrix", "Rdpack"], "RdMacros": "Rdpack", "Suggests": ["lme4", "tinytest", "glmmTMB"], "RoxygenNote": "7.3.2.9000", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-2127-0443>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "rematch": {"Package": "rematch", "Version": "2.0.0", "Source": "Repository", "Title": "Match Regular Expressions with a Nicer 'API'", "Author": "<PERSON><PERSON><PERSON>", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Description": "A small wrapper on 'regexpr' to extract the matches and captured groups from the match of a regular expression to a character vector.", "License": "MIT + file LICENSE", "URL": "https://github.com/gaborcsardi/rematch", "BugReports": "https://github.com/gaborcsardi/rematch/issues", "RoxygenNote": "5.0.1.9000", "Suggests": ["covr", "testthat"], "Encoding": "UTF-8", "NeedsCompilation": "no", "Repository": "https://packagemanager.posit.co/cran/latest"}, "renv": {"Package": "renv", "Version": "1.1.4", "Source": "Repository", "Type": "Package", "Title": "Project Environments", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-2880-7407\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A dependency management toolkit for R. Using 'renv', you can create and manage project-local R libraries, save the state of these libraries to a 'lockfile', and later restore your library as required. Together, these tools can help make your projects more isolated, portable, and reproducible.", "License": "MIT + file LICENSE", "URL": "https://rstudio.github.io/renv/, https://github.com/rstudio/renv", "BugReports": "https://github.com/rstudio/renv/issues", "Imports": ["utils"], "Suggests": ["BiocManager", "cli", "compiler", "covr", "cpp11", "devtools", "gitcreds", "jsonlite", "jsonvalidate", "knitr", "miniUI", "modules", "packrat", "pak", "R6", "remotes", "reticulate", "rmarkdown", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shiny", "testthat", "uuid", "waldo", "yaml", "webfakes"], "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "bioconductor,python,install,restore,snapshot,retrieve,remotes", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0003-2880-7407>), <PERSON> [aut] (<https://orcid.org/0000-0003-4757-117X>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "rio": {"Package": "rio", "Version": "1.2.3", "Source": "Repository", "Type": "Package", "Title": "A Swiss-Army Knife for Data I/O", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-6232-7530\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\"), comment = c(ORCID = \"0000-0003-2952-4812\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\",  email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-4097-6326\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\", email = \"ruaridh.<PERSON><PERSON><PERSON>@gmail.com\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = \"ctb\"), person(\"<PERSON> L\", \"<PERSON>\", email = \"<EMAIL>\", role = \"ctb\"), person(\"<PERSON>\", \"Day\", email = \"<EMAIL>\", role = \"ctb\"), person(\"<PERSON>\", \"Denney\", email=\"<EMAIL>\", role=\"ctb\", comment=c(ORCID=\"0000-0002-5759-428X\")), person(\"Alex\", \"Bokov\", email = \"<EMAIL>\", role = \"ctb\", comment=c(ORCID=\"0000-0002-0511-9815\")), person(\"Hugo\", \"Gruson\", role = \"ctb\", comment = c(ORCID = \"0000-0002-4094-1476\")) )", "Description": "Streamlined data import and export by making assumptions that the user is probably willing to make: 'import()' and 'export()' determine the data format from the file extension, reasonable defaults are used for data import and export, web-based import is natively supported (including from SSL/HTTPS), compressed files can be read directly, and fast import packages are used where appropriate. An additional convenience function, 'convert()', provides a simple method for converting between file types.", "URL": "https://gesistsa.github.io/rio/, https://github.com/gesistsa/rio", "BugReports": "https://github.com/gesistsa/rio/issues", "Depends": ["R (>= 4.0)"], "Imports": ["tools", "stats", "utils", "foreign", "haven (>= 1.1.2)", "curl (>= 0.6)", "data.table (>= 1.11.2)", "readxl (>= 0.1.1)", "tibble", "writexl", "lifecycle", "<PERSON><PERSON>utils", "readr"], "Suggests": ["datasets", "bit64", "testthat", "knitr", "magrit<PERSON>", "clipr", "fst", "hex<PERSON>iew", "jsonlite", "pzfx", "readODS (>= 2.1.0)", "rmarkdown", "rmatio", "xml2 (>= 1.2.0)", "yaml", "qs", "arrow (>= 0.17.0)", "stringi", "withr", "nanoparquet"], "License": "GPL-2", "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "Config/Needs/website": "gesistsa/tsatemplate", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON><PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-6232-7530>), <PERSON> [aut] (<https://orcid.org/0000-0003-2952-4812>), <PERSON> [ctb], <PERSON> [aut] (<https://orcid.org/0000-0003-4097-6326>), <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0002-5759-428X>), <PERSON> [ctb] (<https://orcid.org/0000-0002-0511-9815>), <PERSON> [ctb] (<https://orcid.org/0000-0002-4094-1476>)", "Maintainer": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "rlang": {"Package": "rlang", "Version": "1.1.6", "Source": "Repository", "Title": "Functions for Base Types and Core R and 'Tidyverse' Features", "Description": "A toolbox for working with base types, core R features like the condition system, and core 'Tidyverse' features like tidy evaluation.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", ,\"<EMAIL>\", c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", ,\"<EMAIL>\", \"aut\"), person(given = \"mikefc\", email = \"<EMAIL>\", role = \"cph\", comment = \"Hash implementation based on <PERSON>'s xxhashlite\"), person(given = \"Yann\", family = \"Collet\", role = \"cph\", comment = \"Author of the embedded xxHash library\"), person(given = \"Posit, PBC\", role = c(\"cph\", \"fnd\")) )", "License": "MIT + file LICENSE", "ByteCompile": "true", "Biarch": "true", "Depends": ["R (>= 3.5.0)"], "Imports": ["utils"], "Suggests": ["cli (>= 3.1.0)", "covr", "crayon", "desc", "fs", "glue", "knitr", "magrit<PERSON>", "methods", "pillar", "pkgload", "rmarkdown", "stats", "testthat (>= 3.2.0)", "tibble", "usethis", "vctrs (>= 0.2.3)", "withr"], "Enhances": ["winch"], "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "URL": "https://rlang.r-lib.org, https://github.com/r-lib/rlang", "BugReports": "https://github.com/r-lib/rlang/issues", "Config/build/compilation-database": "true", "Config/testthat/edition": "3", "Config/Needs/website": "dplyr, tidyverse/tidytemplate", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON> [aut], mikef<PERSON> [cph] (Hash implementation based on <PERSON>'s xxhashlite), <PERSON><PERSON> [cph] (Author of the embedded xxHash library), Posit, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "rmarkdown": {"Package": "rmarkdown", "Version": "2.29", "Source": "Repository", "Type": "Package", "Title": "Dynamic Documents for R", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-0645-5666\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0003-4474-2498\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0003-3925-190X\")), person(\"Andrew\", \"Dunning\", role = \"ctb\", comment = c(ORCID = \"0000-0003-0464-5036\")), person(\"Atsushi\", \"Yasumoto\", role = c(\"ctb\", \"cph\"), comment = c(ORCID = \"0000-0002-8335-495X\", cph = \"Number sections Lua filter\")), person(\"Barret\", \"Schloerke\", role = \"ctb\"), person(\"Carson\", \"Sievert\", role = \"ctb\", comment = c(ORCID = \"0000-0002-4958-2844\")),  person(\"Devon\", \"Ryan\", , \"<EMAIL>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-8549-0971\")), person(\"Frederik\", \"Aust\", , \"<EMAIL>\", role = \"ctb\", comment = c(ORCID = \"0000-0003-4900-788X\")), person(\"Jeff\", \"Allen\", , \"<EMAIL>\", role = \"ctb\"),  person(\"JooYoung\", \"Seo\", role = \"ctb\", comment = c(ORCID = \"0000-0002-4064-6012\")), person(\"Malcolm\", \"Barrett\", role = \"ctb\"), person(\"Rob\", \"Hyndman\", , \"<EMAIL>\", role = \"ctb\"), person(\"Romain\", \"Lesur\", role = \"ctb\"), person(\"Roy\", \"Storey\", role = \"ctb\"), person(\"Ruben\", \"Arslan\", , \"<EMAIL>\", role = \"ctb\"), person(\"Sergio\", \"Oller\", role = \"ctb\"), person(given = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(, \"jQuery UI contributors\", role = c(\"ctb\", \"cph\"), comment = \"jQuery UI library; authors listed in inst/rmd/h/jqueryui/AUTHORS.txt\"), person(\"Mark\", \"Otto\", role = \"ctb\", comment = \"Bootstrap library\"), person(\"Jacob\", \"Thornton\", role = \"ctb\", comment = \"Bootstrap library\"), person(, \"Bootstrap contributors\", role = \"ctb\", comment = \"Bootstrap library\"), person(, \"Twitter, Inc\", role = \"cph\", comment = \"Bootstrap library\"), person(\"Alexander\", \"Farkas\", role = c(\"ctb\", \"cph\"), comment = \"html5shiv library\"), person(\"Scott\", \"Jehl\", role = c(\"ctb\", \"cph\"), comment = \"Respond.js library\"), person(\"Ivan\", \"Sagalaev\", role = c(\"ctb\", \"cph\"), comment = \"highlight.js library\"), person(\"Greg\", \"Franko\", role = c(\"ctb\", \"cph\"), comment = \"tocify library\"), person(\"John\", \"MacFarlane\", role = c(\"ctb\", \"cph\"), comment = \"Pandoc templates\"), person(, \"Google, Inc.\", role = c(\"ctb\", \"cph\"), comment = \"ioslides library\"), person(\"Dave\", \"Raggett\", role = \"ctb\", comment = \"slidy library\"), person(, \"W3C\", role = \"cph\", comment = \"slidy library\"), person(\"Dave\", \"Gandy\", role = c(\"ctb\", \"cph\"), comment = \"Font-Awesome\"), person(\"Ben\", \"Sperry\", role = \"ctb\", comment = \"Ionicons\"), person(, \"Drifty\", role = \"cph\", comment = \"Ionicons\"), person(\"Aidan\", \"Lister\", role = c(\"ctb\", \"cph\"), comment = \"jQuery StickyTabs\"), person(\"Benct Philip\", \"Jonsson\", role = c(\"ctb\", \"cph\"), comment = \"pagebreak Lua filter\"), person(\"Albert\", \"Krewinkel\", role = c(\"ctb\", \"cph\"), comment = \"pagebreak Lua filter\") )", "Description": "Convert R Markdown documents into a variety of formats.", "License": "GPL-3", "URL": "https://github.com/rstudio/rmarkdown, https://pkgs.rstudio.com/rmarkdown/", "BugReports": "https://github.com/rstudio/rmarkdown/issues", "Depends": ["R (>= 3.0)"], "Imports": ["bslib (>= *******)", "evaluate (>= 0.13)", "fontawesome (>= 0.5.0)", "htmltools (>= 0.5.1)", "j<PERSON><PERSON><PERSON>", "jsonlite", "knitr (>= 1.43)", "methods", "tinytex (>= 0.31)", "tools", "utils", "xfun (>= 0.36)", "yaml (>= 2.1.19)"], "Suggests": ["digest", "dygraphs", "fs", "rsconnect", "downlit (>= 0.4.0)", "katex (>= 1.4.0)", "sass (>= 0.4.0)", "shiny (>= 1.6.0)", "testthat (>= 3.0.3)", "tibble", "vctrs", "cleanrmd", "withr (>= 2.4.2)", "xml2"], "VignetteBuilder": "knitr", "Config/Needs/website": "rstudio/quillt, pkgdown", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "SystemRequirements": "pandoc (>= 1.14) - http://pandoc.org", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut], <PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0645-5666>), <PERSON> [aut] (<https://orcid.org/0000-0003-4474-2498>), <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut] (<https://orcid.org/0000-0003-3925-190X>), <PERSON> [ctb] (<https://orcid.org/0000-0003-0464-5036>), <PERSON><PERSON><PERSON> [ctb, cph] (<https://orcid.org/0000-0002-8335-495X>, Number sections Lua filter), <PERSON><PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0002-4958-2844>), <PERSON> [ctb] (<https://orcid.org/0000-0002-8549-0971>), <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0003-4900-788X>), <PERSON> [ctb], <PERSON><PERSON><PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-4064-6012>), <PERSON> <PERSON> [ctb], Rob Hyn<PERSON> [ctb], <PERSON>in <PERSON>ur [ctb], <PERSON> <PERSON>y [ctb], Ruben Arslan [ctb], <PERSON> Oller [ctb], Posit Software, P<PERSON> [cph, fnd], jQuery UI contributors [ctb, cph] (jQuery UI library; authors listed in inst/rmd/h/jqueryui/AUTHORS.txt), Mark Otto [ctb] (Bootstrap library), Jacob Thornton [ctb] (Bootstrap library), Bootstrap contributors [ctb] (Bootstrap library), Twitter, Inc [cph] (Bootstrap library), Alexander Farkas [ctb, cph] (html5shiv library), Scott Jehl [ctb, cph] (Respond.js library), Ivan Sagalaev [ctb, cph] (highlight.js library), Greg Franko [ctb, cph] (tocify library), John MacFarlane [ctb, cph] (Pandoc templates), Google, Inc. [ctb, cph] (ioslides library), Dave Raggett [ctb] (slidy library), W3C [cph] (slidy library), Dave Gandy [ctb, cph] (Font-Awesome), Ben Sperry [ctb] (Ionicons), Drifty [cph] (Ionicons), Aidan Lister [ctb, cph] (jQuery StickyTabs), Benct Philip Jonsson [ctb, cph] (pagebreak Lua filter), Albert Krewinkel [ctb, cph] (pagebreak Lua filter)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "robustbase": {"Package": "robustbase", "Version": "0.99-4-1", "Source": "Repository", "VersionNote": "Released 0.99-4 on 2024-08-19, 0.99-3 on 2024-07-01 and 0.99-2 on 2024-01-27 to CRAN", "Date": "2024-09-24", "Title": "Basic Robust Statistics", "Authors@R": "c(person(\"<PERSON>\",\"<PERSON><PERSON><PERSON>\", role=c(\"aut\",\"cre\"), email=\"maech<PERSON>@stat.math.ethz.ch\", comment = c(ORCID = \"0000-0002-8685-9910\")) , person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role=\"ctb\", comment = \"Qn and Sn\") , person(\"<PERSON>\", \"<PERSON>rou<PERSON>\", role=\"ctb\", comment = \"Qn and Sn\") , person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\", comment = \"most robust Cov\") , person(\"<PERSON>\", \"<PERSON><PERSON>tu<PERSON>\", role = \"aut\", email = \"<EMAIL>\", comment = \"nlrob, anova, glmrob\") , person(\"<PERSON><PERSON>\", \"Salibian-Barrera\", role = \"aut\", email = \"<EMAIL>\", comment = \"lmrob orig.\") , person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"ctb\",\"fnd\"), email = \"tobia<PERSON>.verb<PERSON><EMAIL>\", comment = \"mc, adjbox\") , person(\"<PERSON>\", \"<PERSON>ller\", role = \"aut\", email = \"<EMAIL>\", comment = \"mc, lmrob, psi-func.\") , person(c(\"<PERSON>\", \"L. T.\"), \"Conceicao\", role = \"aut\", email = \"<EMAIL>\", comment = \"MM-, tau-, CM-, and MTL- nlrob\") , person(\"Maria\", \"Anna di Palma\", role = \"ctb\", comment = \"initial version of Comedian\") )", "URL": "https://robustbase.R-forge.R-project.org/, https://R-forge.R-project.org/R/?group_id=59, https://R-forge.R-project.org/scm/viewvc.php/pkg/robustbase/?root=robustbase, svn://svn.r-forge.r-project.org/svnroot/robustbase/pkg/robustbase", "BugReports": "https://R-forge.R-project.org/tracker/?atid=302&group_id=59", "Description": "\"Essential\" Robust Statistics. Tools allowing to analyze data with robust methods.  This includes regression methodology including model selections and multivariate statistics where we strive to cover the book \"Robust Statistics, Theory and Methods\" by '<PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON>'; Wiley 2006.", "Depends": ["R (>= 3.5.0)"], "Imports": ["stats", "graphics", "utils", "methods", "DEoptimR"], "Suggests": ["grid", "MASS", "lattice", "boot", "cluster", "Matrix", "robust", "fit.models", "MPV", "xtable", "ggplot2", "GGally", "RColorBrewer", "reshape2", "sfsmisc", "catdata", "doP<PERSON>llel", "foreach", "skewt"], "SuggestsNote": "mostly only because of vignette graphics and simulation", "Enhances": ["robustX", "rrcov", "matrixStats", "quantreg", "Hmisc"], "EnhancesNote": "linked to in man/*.Rd", "LazyData": "yes", "NeedsCompilation": "yes", "License": "GPL (>= 2)", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-8685-9910>), <PERSON> [ctb] (Qn and Sn), <PERSON> [ctb] (Qn and Sn), <PERSON><PERSON> [aut] (most robust Cov), <PERSON> [aut] (nlrob, anova, glmrob), <PERSON><PERSON> [aut] (lmrob orig.), <PERSON> [ctb, fnd] (mc, adjbox), <PERSON> [aut] (mc, lmrob, psi-func.), <PERSON> [aut] (MM-, tau-, CM-, and MTL- nlrob), <PERSON> [ctb] (initial version of Comedian)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "rootSolve": {"Package": "rootSolve", "Version": "1.8.2.4", "Source": "Repository", "Title": "Nonlinear Root Finding, Equilibrium and Steady-State Analysis of Ordinary Differential Equations", "Authors@R": "c(person(\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"),  email = \"<EMAIL>\"), person(\"<PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\",  comment = \"files lsodes.f, sparse.f\"), person(\"S.C.\",\"Eisenstat\", role = \"ctb\",  comment = \"file sparse.f\"), person(\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\", role = \"ctb\",  comment = \"file dlinpk.f\"), person(\"<PERSON>\",\"<PERSON><PERSON>\", role = \"ctb\",  comment = \"file dlinpk.f\"), person(\"Youcef\", \"Saad\", role = \"ctb\",  comment = \"file dsparsk.f\"))", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Author": "<PERSON><PERSON> [aut, cre], <PERSON> [ctb] (files lsodes.f, sparse.f), <PERSON><PERSON><PERSON><PERSON> [ctb] (file sparse.f), <PERSON><PERSON><PERSON> [ctb] (file dlinpk.f), <PERSON> [ctb] (file dlinpk.f), <PERSON><PERSON><PERSON> [ctb] (file dsparsk.f)", "Depends": ["R (>= 2.01)"], "Imports": ["stats", "graphics", "grDevices"], "Description": "Routines to find the root of nonlinear functions, and to perform steady-state and equilibrium analysis of ordinary differential equations (ODE).  Includes routines that: (1) generate gradient and jacobian matrices (full and banded), (2) find roots of non-linear equations by the '<PERSON><PERSON><PERSON><PERSON>' method,  (3) estimate steady-state conditions of a system of (differential) equations in full, banded or sparse form, using the '<PERSON>-<PERSON>' method, or by dynamically running, (4) solve the steady-state conditions for uni-and multicomponent 1-D, 2-D, and 3-D partial differential equations, that have been converted to ordinary differential equations by numerical differencing (using the method-of-lines approach). Includes fortran code.", "License": "GPL (>= 2)", "NeedsCompilation": "yes", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "rprojroot": {"Package": "rprojroot", "Version": "2.0.4", "Source": "Repository", "Title": "Finding Files in Project Subdirectories", "Authors@R": "person(given = \"Kirill\", family = \"M\\u00fcller\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\"))", "Description": "Robust, reliable and flexible paths to files below a project root. The 'root' of a project is defined as a directory that matches a certain criterion, e.g., it contains a certain regular file.", "License": "MIT + file LICENSE", "URL": "https://rprojroot.r-lib.org/, https://github.com/r-lib/rprojroot", "BugReports": "https://github.com/r-lib/rprojroot/issues", "Depends": ["R (>= 3.0.0)"], "Suggests": ["covr", "knitr", "lifecycle", "mockr", "rlang", "rmarkdown", "testthat (>= 3.0.0)", "withr"], "VignetteBuilder": "knitr", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-1416-3412>)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "rsconnect": {"Package": "rsconnect", "Version": "1.4.2", "Source": "Repository", "Type": "Package", "Title": "Deploy Docs, Apps, and APIs to 'Posit Connect', 'shinyapps.io', and 'RPubs'", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>aire\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Programmatic deployment interface for 'RPubs', 'shinyapps.io', and 'Posit Connect'. Supported content types include R Markdown documents, Shiny applications, Plumber APIs, plots, and static web content.", "License": "GPL-2", "URL": "https://rstudio.github.io/rsconnect/, https://github.com/rstudio/rsconnect", "BugReports": "https://github.com/rstudio/rsconnect/issues", "Depends": ["R (>= 3.5.0)"], "Imports": ["cli", "curl", "digest", "jsonlite", "lifecycle", "openssl (>= 2.0.0)", "PKI", "packrat (>= 0.6)", "renv (>= 1.0.0)", "rlang (>= 1.0.0)", "rstudioa<PERSON> (>= 0.5)", "tools", "yaml (>= 2.1.5)", "RcppTOML", "jose", "utils"], "Suggests": ["Biobase", "BiocManager", "foreign", "knitr", "MASS", "plumber (>= 0.3.2)", "quarto", "<PERSON><PERSON><PERSON>", "reticulate", "rmarkdown (>= 1.1)", "shiny", "testthat (>= 3.1.9)", "webfakes", "withr"], "VignetteBuilder": "knitr, rmarkdown", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON> [aut], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "rstatix": {"Package": "rstatix", "Version": "0.7.2", "Source": "Repository", "Type": "Package", "Title": "Pipe-Friendly Framework for Basic Statistical Tests", "Authors@R": "c( person(\"<PERSON><PERSON>uka<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"))", "Description": "Provides a simple and intuitive pipe-friendly framework, coherent with the 'tidyverse' design philosophy,  for performing basic statistical tests, including t-test, Wilcoxon test, ANOVA, Kruskal-Wallis and correlation analyses.  The output of each test is automatically transformed into a tidy data frame to facilitate visualization.  Additional functions are available for reshaping, reordering, manipulating and visualizing correlation matrix.   Functions are also included to facilitate the analysis of factorial experiments, including purely 'within-Ss' designs  (repeated measures), purely 'between-Ss' designs, and mixed 'within-and-between-Ss' designs.  It's also possible to compute several effect size metrics, including \"eta squared\" for ANOVA, \"Cohen's d\" for t-test and  'Cramer V' for the association between categorical variables.  The package contains helper functions for identifying univariate and multivariate outliers, assessing normality and homogeneity of variances.", "License": "GPL-2", "Encoding": "UTF-8", "Depends": ["R (>= 3.3.0)"], "Imports": ["stats", "utils", "tidyr (>= 1.0.0)", "purrr", "broom (>= 0.7.4)", "rlang (>= 0.3.1)", "tibble (>= 2.1.3)", "dplyr (>= 0.7.1)", "magrit<PERSON>", "corrplot", "tidyselect (>= 1.2.0)", "car", "generics (>= 0.0.2)"], "Suggests": ["knitr", "rmarkdown", "ggpubr", "graphics", "emmeans", "coin", "boot", "testthat", "spelling"], "URL": "https://rpkgs.datanovia.com/rstatix/", "BugReports": "https://github.com/kassambara/rstatix/issues", "RoxygenNote": "7.2.3", "Collate": "'utilities.R' 'add_significance.R' 'adjust_pvalue.R' 'factorial_design.R' 'utilities_two_sample_test.R' 'anova_summary.R' 'anova_test.R' 'as_cor_mat.R' 'binom_test.R' 'box_m.R' 'chisq_test.R' 'cochran_qtest.R' 'cohens_d.R' 'cor_as_symbols.R' 'replace_triangle.R' 'pull_triangle.R' 'cor_mark_significant.R' 'cor_mat.R' 'cor_plot.R' 'cor_reorder.R' 'cor_reshape.R' 'cor_select.R' 'cor_test.R' 'counts_to_cases.R' 'cramer_v.R' 'df.R' 'doo.R' 't_test.R' 'dunn_test.R' 'emmeans_test.R' 'eta_squared.R' 'factors.R' 'fisher_test.R' 'freq_table.R' 'friedman_test.R' 'friedman_effsize.R' 'games_howell_test.R' 'get_comparisons.R' 'get_manova_table.R' 'get_mode.R' 'get_pvalue_position.R' 'get_summary_stats.R' 'get_test_label.R' 'kruskal_effesize.R' 'kruskal_test.R' 'levene_test.R' 'mahalanobis_distance.R' 'make_clean_names.R' 'mcnemar_test.R' 'multinom_test.R' 'outliers.R' 'p_value.R' 'prop_test.R' 'prop_trend_test.R' 'reexports.R' 'remove_ns.R' 'sample_n_by.R' 'shapiro_test.R' 'sign_test.R' 'tukey_hsd.R' 'utils-manova.R' 'utils-pipe.R' 'welch_anova_test.R' 'wilcox_effsize.R' 'wilcox_test.R'", "Language": "en-US", "NeedsCompilation": "no", "Author": "Albouka<PERSON> [aut, cre]", "Maintainer": "Alboukadel Kassambara <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "rstudioapi": {"Package": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Version": "0.17.1", "Source": "Repository", "Title": "Safely Access the RStudio API", "Description": "Access the RStudio API (if available) and provide informative error messages when it's not.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\"), person(family = \"RStudio\", role = \"cph\") )", "Maintainer": "<PERSON> <<EMAIL>>", "License": "MIT + file LICENSE", "URL": "https://rstudio.github.io/rstudioapi/, https://github.com/rstudio/rstudioapi", "BugReports": "https://github.com/rstudio/rstudioapi/issues", "RoxygenNote": "7.3.2", "Suggests": ["testthat", "knitr", "rmarkdown", "clipr", "covr"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [cph]", "Repository": "https://packagemanager.posit.co/cran/latest"}, "sass": {"Package": "sass", "Version": "0.4.10", "Source": "Repository", "Type": "Package", "Title": "Syntactically Awesome Style Sheets ('Sass')", "Description": "An 'SCSS' compiler, powered by the 'LibSass' library. With this, R developers can use variables, inheritance, and functions to generate dynamic style sheets. The package uses the 'Sass CSS' extension language, which is stable, powerful, and CSS compatible.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", \"aut\", comment = c(ORCID = \"0000-0003-3925-190X\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", \"aut\", comment = c(ORCID = \"0000-0001-9986-114X\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", c(\"ctb\"), comment = c(ORCID = \"0000-0003-4474-2498\")), person(family = \"RStudio\", role = c(\"cph\", \"fnd\")), person(family = \"Sass Open Source Foundation\", role = c(\"ctb\", \"cph\"), comment = \"LibSass library\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"LibSass library\"), person(\"Mifsud\", \"<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"LibSass library\"), person(\"<PERSON>\", \"Catlin\", role = c(\"ctb\", \"cph\"), comment = \"LibSass library\"), person(\"<PERSON>\", \"Weizenbaum\", role = c(\"ctb\", \"cph\"), comment = \"LibSass library\"), person(\"Chris\", \"Eppstein\", role = c(\"ctb\", \"cph\"), comment = \"LibSass library\"), person(\"Adams\", \"Joseph\", role = c(\"ctb\", \"cph\"), comment = \"json.cpp\"), person(\"Trifunovic\", \"Nemanja\", role = c(\"ctb\", \"cph\"), comment = \"utf8.h\") )", "License": "MIT + file LICENSE", "URL": "https://rstudio.github.io/sass/, https://github.com/rstudio/sass", "BugReports": "https://github.com/rstudio/sass/issues", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "SystemRequirements": "GNU make", "Imports": ["fs (>= 1.2.4)", "rlang (>= 0.4.10)", "htmltools (>= 0.5.1)", "R6", "rapp<PERSON>s"], "Suggests": ["testthat", "knitr", "rmarkdown", "withr", "shiny", "curl"], "VignetteBuilder": "knitr", "Config/testthat/edition": "3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON> [aut] (<https://orcid.org/0000-0003-3925-190X>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0001-9986-114X>), <PERSON> [aut, cre] (<https://orcid.org/0000-0002-4958-2844>), <PERSON> [ctb] (<https://orcid.org/0000-0003-4474-2498>), <PERSON>tu<PERSON> [cph, fnd], Sass Open Source Foundation [ctb, cph] (LibSass library), <PERSON><PERSON><PERSON> [ctb, cph] (LibSass library), <PERSON><PERSON><PERSON> [ctb, cph] (LibSass library), <PERSON> [ctb, cph] (LibSass library), <PERSON> [ctb, cph] (LibSass library), <PERSON> [ctb, cph] (LibSass library), <PERSON> [ctb, cph] (json.cpp), <PERSON><PERSON><PERSON><PERSON> [ctb, cph] (utf8.h)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "scales": {"Package": "scales", "Version": "1.4.0", "Source": "Repository", "Title": "Scale Functions for Visualization", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"cre\", \"aut\"), comment = c(ORCID = \"0000-0002-5147-4711\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"03wc8by49\")) )", "Description": "Graphical scales map data to aesthetics, and provide methods for automatically determining breaks and labels for axes and legends.", "License": "MIT + file LICENSE", "URL": "https://scales.r-lib.org, https://github.com/r-lib/scales", "BugReports": "https://github.com/r-lib/scales/issues", "Depends": ["R (>= 4.1)"], "Imports": ["cli", "farver (>= 2.0.3)", "glue", "labeling", "lifecycle", "R6", "RColorBrewer", "rlang (>= 1.1.0)", "viridisLite"], "Suggests": ["bit64", "covr", "dichromat", "ggplot2", "hms (>= 0.5.0)", "stringi", "testthat (>= 3.0.0)"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/usethis/last-upkeep": "2025-04-23", "Encoding": "UTF-8", "LazyLoad": "yes", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [cre, aut] (<https://orcid.org/0000-0002-5147-4711>), <PERSON> [aut], Posit Software, PBC [cph, fnd] (03wc8by49)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "shiny": {"Package": "shiny", "Version": "1.10.0", "Source": "Repository", "Type": "Package", "Title": "Web Application Framework for R", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1576-2126\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON><PERSON>\", \"<PERSON>aire\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-9986-114X\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(family = \"jQuery Foundation\", role = \"cph\", comment = \"jQuery library and jQuery UI library\"), person(family = \"jQuery contributors\", role = c(\"ctb\", \"cph\"), comment = \"jQuery library; authors listed in inst/www/shared/jquery-AUTHORS.txt\"), person(family = \"jQuery UI contributors\", role = c(\"ctb\", \"cph\"), comment = \"jQuery UI library; authors listed in inst/www/shared/jqueryui/AUTHORS.txt\"), person(\"Mark\", \"Otto\", role = \"ctb\", comment = \"Bootstrap library\"), person(\"Jacob\", \"Thornton\", role = \"ctb\", comment = \"Bootstrap library\"), person(family = \"Bootstrap contributors\", role = \"ctb\", comment = \"Bootstrap library\"), person(family = \"Twitter, Inc\", role = \"cph\", comment = \"Bootstrap library\"), person(\"Prem Nawaz\", \"Khan\", role = \"ctb\", comment = \"Bootstrap accessibility plugin\"), person(\"Victor\", \"Tsaran\", role = \"ctb\", comment = \"Bootstrap accessibility plugin\"), person(\"Dennis\", \"Lembree\", role = \"ctb\", comment = \"Bootstrap accessibility plugin\"), person(\"Srinivasu\", \"Chakravarthula\", role = \"ctb\", comment = \"Bootstrap accessibility plugin\"), person(\"Cathy\", \"O'Connor\", role = \"ctb\", comment = \"Bootstrap accessibility plugin\"), person(family = \"PayPal, Inc\", role = \"cph\", comment = \"Bootstrap accessibility plugin\"), person(\"Stefan\", \"Petre\", role = c(\"ctb\", \"cph\"), comment = \"Bootstrap-datepicker library\"), person(\"Andrew\", \"Rowls\", role = c(\"ctb\", \"cph\"), comment = \"Bootstrap-datepicker library\"), person(\"Brian\", \"Reavis\", role = c(\"ctb\", \"cph\"), comment = \"selectize.js library\"), person(\"Salmen\", \"Bejaoui\", role = c(\"ctb\", \"cph\"), comment = \"selectize-plugin-a11y library\"), person(\"Denis\", \"Ineshin\", role = c(\"ctb\", \"cph\"), comment = \"ion.rangeSlider library\"), person(\"Sami\", \"Samhuri\", role = c(\"ctb\", \"cph\"), comment = \"Javascript strftime library\"), person(family = \"SpryMedia Limited\", role = c(\"ctb\", \"cph\"), comment = \"DataTables library\"), person(\"John\", \"Fraser\", role = c(\"ctb\", \"cph\"), comment = \"showdown.js library\"), person(\"John\", \"Gruber\", role = c(\"ctb\", \"cph\"), comment = \"showdown.js library\"), person(\"Ivan\", \"Sagalaev\", role = c(\"ctb\", \"cph\"), comment = \"highlight.js library\"), person(family = \"R Core Team\", role = c(\"ctb\", \"cph\"), comment = \"tar implementation from R\") )", "Description": "Makes it incredibly easy to build interactive web applications with R. Automatic \"reactive\" binding between inputs and outputs and extensive prebuilt widgets make it possible to build beautiful, responsive, and powerful applications with minimal effort.", "License": "GPL-3 | file LICENSE", "Depends": ["R (>= 3.0.2)", "methods"], "Imports": ["utils", "grDevices", "httpuv (>= 1.5.2)", "mime (>= 0.3)", "jsonlite (>= 0.9.16)", "xtable", "fontawesome (>= 0.4.0)", "htmltools (>= 0.5.4)", "R6 (>= 2.0)", "sourcetools", "later (>= 1.0.0)", "promises (>= 1.3.2)", "tools", "crayon", "rlang (>= 0.4.10)", "fastmap (>= 1.1.1)", "withr", "commonmark (>= 1.7)", "glue (>= 1.3.2)", "bslib (>= 0.6.0)", "cachem (>= 1.1.0)", "lifecycle (>= 0.2.0)"], "Suggests": ["coro (>= 1.1.0)", "datasets", "DT", "Cairo (>= 1.5-5)", "testthat (>= 3.0.0)", "knitr (>= 1.6)", "markdown", "rmarkdown", "ggplot2", "reactlog (>= 1.0.0)", "magrit<PERSON>", "yaml", "future", "dygraphs", "ragg", "showtext", "sass"], "URL": "https://shiny.posit.co/, https://github.com/rstudio/shiny", "BugReports": "https://github.com/rstudio/shiny/issues", "Collate": "'globals.R' 'app-state.R' 'app_template.R' 'bind-cache.R' 'bind-event.R' 'bookmark-state-local.R' 'bookmark-state.R' 'bootstrap-deprecated.R' 'bootstrap-layout.R' 'conditions.R' 'map.R' 'utils.R' 'bootstrap.R' 'busy-indicators-spinners.R' 'busy-indicators.R' 'cache-utils.R' 'deprecated.R' 'devmode.R' 'diagnose.R' 'extended-task.R' 'fileupload.R' 'graph.R' 'reactives.R' 'reactive-domains.R' 'history.R' 'hooks.R' 'html-deps.R' 'image-interact-opts.R' 'image-interact.R' 'imageutils.R' 'input-action.R' 'input-checkbox.R' 'input-checkboxgroup.R' 'input-date.R' 'input-daterange.R' 'input-file.R' 'input-numeric.R' 'input-password.R' 'input-radiobuttons.R' 'input-select.R' 'input-slider.R' 'input-submit.R' 'input-text.R' 'input-textarea.R' 'input-utils.R' 'insert-tab.R' 'insert-ui.R' 'jqueryui.R' 'knitr.R' 'middleware-shiny.R' 'middleware.R' 'timer.R' 'shiny.R' 'mock-session.R' 'modal.R' 'modules.R' 'notifications.R' 'priorityqueue.R' 'progress.R' 'react.R' 'reexports.R' 'render-cached-plot.R' 'render-plot.R' 'render-table.R' 'run-url.R' 'runapp.R' 'serializers.R' 'server-input-handlers.R' 'server-resource-paths.R' 'server.R' 'shiny-options.R' 'shiny-package.R' 'shinyapp.R' 'shinyui.R' 'shinywrappers.R' 'showcase.R' 'snapshot.R' 'staticimports.R' 'tar.R' 'test-export.R' 'test-server.R' 'test.R' 'update-input.R' 'utils-lang.R' 'version_bs_date_picker.R' 'version_ion_range_slider.R' 'version_jquery.R' 'version_jqueryui.R' 'version_selectize.R' 'version_strftime.R' 'viewer.R'", "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "RdMacros": "lifecycle", "Config/testthat/edition": "3", "Config/Needs/check": "shinytest2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-1576-2126>), <PERSON> [aut], <PERSON><PERSON> [aut], <PERSON> [aut] (<https://orcid.org/0000-0002-4958-2844>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0001-9986-114X>), <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], Posit Software, PBC [cph, fnd], jQuery Foundation [cph] (jQuery library and jQuery UI library), jQuery contributors [ctb, cph] (jQuery library; authors listed in inst/www/shared/jquery-AUTHORS.txt), jQuery UI contributors [ctb, cph] (jQuery UI library; authors listed in inst/www/shared/jqueryui/AUTHORS.txt), <PERSON> [ctb] (Bootstrap library), <PERSON> [ctb] (Bootstrap library), Bootstrap contributors [ctb] (Bootstrap library), Twitter, Inc [cph] (Bootstrap library), <PERSON><PERSON> [ctb] (Bootstrap accessibility plugin), <PERSON> [ctb] (Bootstrap accessibility plugin), <PERSON>e [ctb] (Bootstrap accessibility plugin), Srinivasu Chakravarthula [ctb] (Bootstrap accessibility plugin), Cathy O'Connor [ctb] (Bootstrap accessibility plugin), PayPal, Inc [cph] (Bootstrap accessibility plugin), Stefan Petre [ctb, cph] (Bootstrap-datepicker library), Andrew Rowls [ctb, cph] (Bootstrap-datepicker library), Brian Reavis [ctb, cph] (selectize.js library), Salmen Bejaoui [ctb, cph] (selectize-plugin-a11y library), Denis Ineshin [ctb, cph] (ion.rangeSlider library), Sami Samhuri [ctb, cph] (Javascript strftime library), SpryMedia Limited [ctb, cph] (DataTables library), John Fraser [ctb, cph] (showdown.js library), John Gruber [ctb, cph] (showdown.js library), Ivan Sagalaev [ctb, cph] (highlight.js library), R Core Team [ctb, cph] (tar implementation from R)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "shinyMatrix": {"Package": "shinyMatrix", "Version": "0.8.0", "Source": "Repository", "Title": "Shiny Matrix Input Field", "Date": "2024-04-10", "Author": "<PERSON>", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "Implements a custom matrix input field.", "Depends": ["R (>= 3.5)"], "License": "MIT + file LICENSE", "Encoding": "UTF-8", "Imports": ["shiny", "jsonlite"], "Suggests": ["testthat", "covr"], "RoxygenNote": "7.3.1", "URL": "https://inwtlab.github.io/shiny-matrix/", "NeedsCompilation": "no", "Repository": "https://packagemanager.posit.co/cran/latest"}, "shinyWidgets": {"Package": "shinyWidgets", "Version": "0.9.0", "Source": "Repository", "Title": "Custom Inputs Widgets for Shiny", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\", \"cph\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"Methods for mutating vertical tabs & updateMultiInput\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"numericRangeInput function\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"autoNumeric methods\"), person(family = \"JavaScript and CSS libraries authors\", role = c(\"ctb\", \"cph\"), comment = \"All authors are listed in LICENSE.md\") )", "Description": "Collection of custom input controls and user interface components for 'Shiny' applications.  Give your applications a unique and colorful style !", "URL": "https://github.com/dreamRs/shinyWidgets, https://dreamrs.github.io/shinyWidgets/", "BugReports": "https://github.com/dreamRs/shinyWidgets/issues", "License": "GPL-3", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.3.2", "Depends": ["R (>= 3.1.0)"], "Imports": ["bslib", "sass", "shiny (>= 1.6.0)", "htmltools (>= 0.5.1)", "jsonlite", "grDevices", "rlang"], "Suggests": ["testthat", "covr", "ggplot2", "DT", "scales", "shinydashboard", "shinydashboardPlus"], "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre, cph], <PERSON> [aut], <PERSON> [aut], <PERSON> [ctb] (Methods for mutating vertical tabs & updateMultiInput), <PERSON><PERSON> [ctb] (numericRangeInput function), <PERSON> [ctb] (autoNumeric methods), JavaScript and CSS libraries authors [ctb, cph] (All authors are listed in LICENSE.md)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "shinyalert": {"Package": "shinyalert", "Version": "3.1.0", "Source": "Repository", "Title": "<PERSON><PERSON> Pretty <PERSON>up <PERSON>s (Modals) in 'Shiny'", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID=\"0000-0002-5645-3493\", \"R interface\")), person(\"<PERSON>\", \"<PERSON>\", role = c(\"aut\"), comment = \"sweetalert library\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = c(\"ctb\")) )", "Description": "Easily create pretty popup messages (modals) in 'Shiny'. A modal can contain text, images, OK/Cancel buttons, an input to get a response from the user, and many more customizable options.", "URL": "https://github.com/daattali/shinyalert, https://daattali.com/shiny/shinyalert-demo/", "BugReports": "https://github.com/daattali/shinyalert/issues", "Depends": ["R (>= 3.0.2)"], "Imports": ["htmltools (>= 0.3.5)", "shiny (>= 1.0.4)", "uuid"], "Suggests": ["colourpicker", "shinydisconnect"], "License": "MIT + file LICENSE", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-5645-3493>, R interface), <PERSON> [aut] (sweetalert library), <PERSON><PERSON><PERSON> [ctb]", "Maintainer": "<PERSON> <da<PERSON><PERSON>@gmail.com>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "shinybusy": {"Package": "shinybusy", "Version": "0.3.3", "Source": "Repository", "Title": "Busy Indicators and Notifications for 'Shiny' Applications", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Silex Technologies\", comment = \"https://www.silex-ip.com\", role = \"fnd\"))", "Description": "Add indicators (spinner, progress bar, gif) in your 'shiny' applications to show the user that the server is busy. And other tools to let your users know something is happening (send notifications, reports, ...).", "License": "GPL-3", "Encoding": "UTF-8", "Imports": ["htmltools", "shiny", "jsonlite", "htmlwidgets"], "RoxygenNote": "7.3.1", "URL": "https://github.com/dreamRs/shinybusy, https://dreamrs.github.io/shinybusy/", "BugReports": "https://github.com/dreamRs/shinybusy/issues", "Suggests": ["testthat", "covr", "knitr", "rmarkdown"], "VignetteBuilder": "knitr", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut, cre], Silex Technologies [fnd] (https://www.silex-ip.com)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "shinyjs": {"Package": "<PERSON><PERSON><PERSON>", "Version": "2.1.0", "Source": "Repository", "Title": "E<PERSON> Improve the User Experience of Your Shiny Apps in Seconds", "Authors@R": "person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\",  email = \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment= c(ORCID=\"0000-0002-5645-3493\"))", "Description": "Perform common useful JavaScript operations in Shiny apps that will greatly improve your apps without having to know any JavaScript. Examples include: hiding an element, disabling an input, resetting an input back to its original value, delaying code execution by a few seconds, and many more useful functions for both the end user and the developer. 'shinyjs' can also be used to easily call your own custom JavaScript functions from R.", "URL": "https://deanattali.com/shinyjs/", "BugReports": "https://github.com/daattali/shinyjs/issues", "Depends": ["R (>= 3.1.0)"], "Imports": ["digest (>= 0.6.8)", "jsonlite", "shiny (>= 1.0.0)"], "Suggests": ["htmltools (>= 0.2.9)", "knitr (>= 1.7)", "rmarkdown", "shinyAce", "shinydisconnect", "testthat (>= 0.9.1)"], "License": "MIT + file LICENSE", "VignetteBuilder": "knitr", "RoxygenNote": "7.1.1", "Encoding": "UTF-8", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-5645-3493>)", "Maintainer": "<PERSON> <da<PERSON><PERSON>@gmail.com>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "shinythemes": {"Package": "shinythemes", "Version": "1.2.0", "Source": "Repository", "Title": "Themes for <PERSON><PERSON>", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(family = \"RStudio\", role = \"cph\"), person(\"<PERSON>\", \"<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Bootswatch themes\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>ziedzi<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Lato font\"), person(\"<PERSON>\", \"<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"News Cycle font\"), person(family = \"Google Corporation\", role = c(\"ctb\", \"cph\"), comment = \"Open Sans and Roboto fonts\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Raleway font\"), person(family = \"Adobe Systems Incorporated\", role = c(\"ctb\", \"cph\"), comment = \"Source Sans Pro font\"), person(family = \"Canonical Ltd\", role = c(\"ctb\", \"cph\"), comment = \"Ubuntu font\") )", "Description": "Themes for use with Shiny. Includes several Bootstrap themes from <https://bootswatch.com/>, which are packaged for use with Shiny applications.", "Depends": ["R (>= 3.0.0)"], "Imports": ["shiny (>= 0.11)"], "URL": "https://rstudio.github.io/shinythemes/", "License": "GPL-3 | file LICENSE", "RoxygenNote": "7.1.1", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON><PERSON><PERSON> [cph], <PERSON> [ctb, cph] (Bootswatch themes), <PERSON><PERSON><PERSON> [ctb, cph] (Lato font), <PERSON> [ctb, cph] (News Cycle font), Google Corporation [ctb, cph] (Open Sans and Roboto fonts), <PERSON> [ctb, cph] (Raleway font), Adobe Systems Incorporated [ctb, cph] (Source Sans Pro font), Canonical Ltd [ctb, cph] (Ubuntu font)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "shinyvalidate": {"Package": "shinyvalidate", "Version": "0.1.3", "Source": "Repository", "Title": "Input Validation for Shiny Apps", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", c(\"aut\"), comment = c(ORCID = \"0000-0003-3925-190X\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", c(\"aut\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Improves the user experience of Shiny apps by helping to provide feedback when required inputs are missing, or input values are not valid.", "License": "MIT + file LICENSE", "URL": "https://rstudio.github.io/shinyvalidate/, https://github.com/rstudio/shinyvalidate", "BugReports": "https://github.com/rstudio/shinyvalidate/issues", "Encoding": "UTF-8", "Imports": ["shiny (>= 1.6)", "htmltools (>= *******)", "rlang (>= 0.4.10)", "glue (>= 1.4.2)"], "RoxygenNote": "7.2.3", "Suggests": ["testthat", "knitr", "rmarkdown", "covr"], "Config/testthat/edition": "3", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-4958-2844>), <PERSON> [aut] (<https://orcid.org/0000-0003-3925-190X>), <PERSON> [aut], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "sourcetools": {"Package": "sourcetools", "Version": "0.1.7-1", "Source": "Repository", "Type": "Package", "Title": "Tools for Reading, Tokenizing and Parsing R Code", "Author": "<PERSON>", "Maintainer": "<PERSON> <kev<PERSON><PERSON>@gmail.com>", "Description": "Tools for the reading and tokenization of R code. The 'sourcetools' package provides both an R and C++ interface for the tokenization of R code, and helpers for interacting with the tokenized representation of R code.", "License": "MIT + file LICENSE", "Depends": ["R (>= 3.0.2)"], "Suggests": ["testthat"], "RoxygenNote": "5.0.1", "BugReports": "https://github.com/kevinushey/sourcetools/issues", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Repository": "CRAN"}, "stringi": {"Package": "stringi", "Version": "1.8.7", "Source": "Repository", "Date": "2025-03-27", "Title": "Fast and Portable Character String Processing Facilities", "Description": "A collection of character string/text/natural language processing tools for pattern searching (e.g., with 'Java'-like regular expressions or the 'Unicode' collation algorithm), random string generation, case mapping, string transliteration, concatenation, sorting, padding, wrapping, Unicode normalisation, date-time formatting and parsing, and many more. They are fast, consistent, convenient, and - thanks to 'ICU' (International Components for Unicode) - portable across all locales and platforms. Documentation about 'stringi' is provided via its website at <https://stringi.gagolewski.com/> and the paper by <PERSON><PERSON><PERSON><PERSON> (2022, <doi:10.18637/jss.v103.i02>).", "URL": "https://stringi.gagolewski.com/, https://github.com/gagolews/stringi, https://icu.unicode.org/", "BugReports": "https://github.com/gagolews/stringi/issues", "SystemRequirements": "ICU4C (>= 61, optional)", "Type": "Package", "Depends": ["R (>= 3.4)"], "Imports": ["tools", "utils", "stats"], "Biarch": "TRUE", "License": "file LICENSE", "Authors@R": "c(person(given = \"<PERSON><PERSON>\", family = \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\", \"cph\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0637-6028\")), person(given = \"<PERSON><PERSON>\", family = \"Tartanus\", role = \"ctb\"), person(\"Unicode, Inc. and others\", role=\"ctb\", comment = \"ICU4C source code, Unicode Character Database\") )", "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre, cph] (<https://orcid.org/0000-0003-0637-6028>), <PERSON><PERSON> [ctb], Unicode, Inc. and others [ctb] (ICU4C source code, Unicode Character Database)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "License_is_FOSS": "yes", "Repository": "CRAN"}, "stringr": {"Package": "stringr", "Version": "1.5.1", "Source": "Repository", "Title": "Simple, Consistent Wrappers for Common String Operations", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\", \"cph\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A consistent, simple and easy to use set of wrappers around the fantastic 'stringi' package. All function and argument names (and positions) are consistent, all functions deal with \"NA\"'s and zero length vectors in the same way, and the output from one function is easy to feed into the input of another.", "License": "MIT + file LICENSE", "URL": "https://stringr.tidyverse.org, https://github.com/tidyverse/stringr", "BugReports": "https://github.com/tidyverse/stringr/issues", "Depends": ["R (>= 3.6)"], "Imports": ["cli", "glue (>= 1.6.1)", "lifecycle (>= 1.0.3)", "magrit<PERSON>", "rlang (>= 1.0.0)", "stringi (>= 1.5.3)", "vctrs (>= 0.4.0)"], "Suggests": ["covr", "dplyr", "gt", "htmltools", "htmlwidgets", "knitr", "rmarkdown", "testthat (>= 3.0.0)", "tibble"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre, cph], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "survival": {"Package": "survival", "Version": "3.8-3", "Source": "Repository", "Title": "Survival Analysis", "Priority": "recommended", "Date": "2024-12-17", "Depends": ["R (>= 3.5.0)"], "Imports": ["graphics", "Matrix", "methods", "splines", "stats", "utils"], "LazyData": "Yes", "LazyDataCompression": "xz", "ByteCompile": "Yes", "Authors@R": "c(person(c(\"<PERSON>\", \"<PERSON>\"), \"<PERSON><PERSON><PERSON>\", email=\"<EMAIL>\", role=c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=c(\"ctb\", \"trl\"), comment=\"original S->R port and R maintainer until 2009\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"ctb\"))", "Description": "Contains the core survival analysis routines, including definition of Surv objects,  <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (multi-state) curves, Cox models, and parametric accelerated failure time models.", "License": "LGPL (>= 2)", "URL": "https://github.com/therneau/survival", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON> [ctb, trl] (original S->R port and R maintainer until 2009), <PERSON> [ctb], <PERSON><PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "sys": {"Package": "sys", "Version": "3.4.3", "Source": "Repository", "Type": "Package", "Title": "Powerful and Reliable Tools for Running System Commands in R", "Authors@R": "c(person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"),  email = \"jeroeno<PERSON>@gmail.com\", comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"ctb\"))", "Description": "Drop-in replacements for the base system2() function with fine control and consistent behavior across platforms. Supports clean interruption, timeout,  background tasks, and streaming STDIN / STDOUT / STDERR over binary or text  connections. Arguments on Windows automatically get encoded and quoted to work  on different locales.", "License": "MIT + file LICENSE", "URL": "https://jeroen.r-universe.dev/sys", "BugReports": "https://github.com/jeroen/sys/issues", "Encoding": "UTF-8", "RoxygenNote": "7.1.1", "Suggests": ["unix (>= 1.4)", "spelling", "testthat"], "Language": "en-US", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-4035-0289>), <PERSON><PERSON><PERSON> [ctb]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "testthat": {"Package": "testthat", "Version": "3.2.3", "Source": "Repository", "Title": "Unit Testing for R", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(\"R Core team\", role = \"ctb\", comment = \"Implementation of utils::recover()\") )", "Description": "Software testing is important, but, in part because it is frustrating and boring, many of us avoid it. 'testthat' is a testing framework for R that is easy to learn and use, and integrates with your existing 'workflow'.", "License": "MIT + file LICENSE", "URL": "https://testthat.r-lib.org, https://github.com/r-lib/testthat", "BugReports": "https://github.com/r-lib/testthat/issues", "Depends": ["R (>= 3.6.0)"], "Imports": ["brio (>= 1.1.3)", "callr (>= 3.7.3)", "cli (>= 3.6.1)", "desc (>= 1.4.2)", "digest (>= 0.6.33)", "evaluate (>= 1.0.1)", "jsonlite (>= 1.8.7)", "lifecycle (>= 1.0.3)", "magrittr (>= 2.0.3)", "methods", "pkgload (>= *******)", "praise (>= 1.0.0)", "processx (>= 3.8.2)", "ps (>= 1.7.5)", "R6 (>= 2.5.1)", "rlang (>= 1.1.1)", "utils", "waldo (>= 0.6.0)", "withr (>= 3.0.2)"], "Suggests": ["covr", "curl (>= 0.9.5)", "diffviewer (>= 0.1.0)", "knitr", "rmarkdown", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "S7", "shiny", "usethis", "vctrs (>= 0.1.0)", "xml2"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "watcher, parallel*", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd], R Core team [ctb] (Implementation of utils::recover())", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "thematic": {"Package": "thematic", "Version": "0.1.7", "Source": "Repository", "Title": "Unified and Automatic 'Theming' of 'ggplot2', 'lattice', and 'base' R Graphics", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0001-9986-114X\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Theme 'ggplot2', 'lattice', and 'base' graphics based on a few choices, including foreground color, background color, accent color, and font family. Fonts that aren't available on the system, but are available via download on 'Google Fonts', can be automatically downloaded, cached, and registered for use with the 'showtext' and 'ragg' packages.", "License": "MIT + file LICENSE", "URL": "https://rstudio.github.io/thematic/, https://github.com/rstudio/thematic", "BugReports": "https://github.com/rstudio/thematic/issues", "Depends": ["R (>= 3.0.0)"], "Imports": ["farver", "ggplot2 (>= 3.3.0)", "graphics", "grDevices", "grid", "rapp<PERSON>s", "rlang", "rstudioa<PERSON> (>= 0.8)", "scales", "utils"], "Suggests": ["bslib", "Cairo", "curl", "gganimate", "ggiraph", "htmltools", "jsonlite", "knitr", "lattice", "ragg", "rmarkdown", "shiny (>= 1.5.0)", "showtext", "stats", "svglite", "sysfonts", "systemfonts", "testthat", "vdiffr (>= 1.0.0)", "withr"], "Config/testthat/edition": "3", "Config/Needs/check": "shinytest2, callr, sf, ggthemes, patchwork, gridExtra, tinytex, devtools, rversions", "Config/Needs/routine": "ggrepel, r-lib/evaluate", "Config/Needs/website": "<PERSON><PERSON><PERSON>, RColorBrewer, patchwork, apreshill/quillt, r-lib/evaluate", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "Collate": "'auto.R' 'base.R' 'cache.R' 'gfonts.R' 'ggplot.R' 'globals.R' 'hooks.R' 'knitr.R' 'lattice.R' 'onLoad.R' 'thematic-package.R' 'thematic-save-plot.R' 'thematic.R' 'utils.R'", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-4958-2844>), <PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0001-9986-114X>), <PERSON> [aut], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "tibble": {"Package": "tibble", "Version": "3.3.0", "Source": "Repository", "Title": "Simple Data Frames", "Authors@R": "c(person(given = \"Kirill\", family = \"M\\u00fcller\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(given = \"<PERSON>\", family = \"Wickham\", role = \"aut\", email = \"<EMAIL>\"), person(given = \"<PERSON>in\", family = \"<PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"RStudio\", role = c(\"cph\", \"fnd\")))", "Description": "Provides a 'tbl_df' class (the 'tibble') with stricter checking and better formatting than the traditional data frame.", "License": "MIT + file LICENSE", "URL": "https://tibble.tidyverse.org/, https://github.com/tidyverse/tibble", "BugReports": "https://github.com/tidyverse/tibble/issues", "Depends": ["R (>= 3.4.0)"], "Imports": ["cli", "lifecycle (>= 1.0.0)", "magrit<PERSON>", "methods", "pillar (>= 1.8.1)", "pkgconfig", "rlang (>= 1.0.2)", "utils", "vctrs (>= 0.5.0)"], "Suggests": ["bench", "bit64", "blob", "brio", "callr", "DiagrammeR", "dplyr", "evaluate", "formattable", "ggplot2", "here", "hms", "htmltools", "knitr", "lubridate", "nycflights13", "pkgload", "purrr", "rmarkdown", "stringi", "testthat (>= 3.0.2)", "tidyr", "withr"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.3.2.9000", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "vignette-formats, as_tibble, add, invariants", "Config/autostyle/scope": "line_breaks", "Config/autostyle/strict": "true", "Config/autostyle/rmd": "false", "Config/Needs/website": "tidyverse/tidytemplate", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-1416-3412>), <PERSON> [aut], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [cph, fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "tidyr": {"Package": "tidyr", "Version": "1.3.1", "Source": "Repository", "Title": "Tidy Messy Data", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Tools to help to create tidy data, where each column is a variable, each row is an observation, and each cell contains a single value.  'tidyr' contains tools for changing the shape (pivoting) and hierarchy (nesting and 'unnesting') of a dataset, turning deeply nested lists into rectangular data frames ('rectangling'), and extracting values out of string columns. It also includes tools for working with missing values (both implicit and explicit).", "License": "MIT + file LICENSE", "URL": "https://tidyr.tidyverse.org, https://github.com/tidyverse/tidyr", "BugReports": "https://github.com/tidyverse/tidyr/issues", "Depends": ["R (>= 3.6)"], "Imports": ["cli (>= 3.4.1)", "dplyr (>= 1.0.10)", "glue", "lifecycle (>= 1.0.3)", "magrit<PERSON>", "purrr (>= 1.0.1)", "rlang (>= 1.1.1)", "stringr (>= 1.5.0)", "tibble (>= 2.1.1)", "tidyselect (>= 1.2.0)", "utils", "vctrs (>= 0.5.2)"], "Suggests": ["covr", "data.table", "knitr", "readr", "repurrrsive (>= 1.1.0)", "rmarkdown", "testthat (>= 3.0.0)"], "LinkingTo": ["cpp11 (>= 0.4.0)"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.3.0", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON> [aut], <PERSON> [aut], <PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "tidyselect": {"Package": "tidyselect", "Version": "1.2.1", "Source": "Repository", "Title": "Select from a Set of Strings", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A backend for the selecting functions of the 'tidyverse'.  It makes it easy to implement select-like functions in your own packages in a way that is consistent with other 'tidyverse' interfaces for selection.", "License": "MIT + file LICENSE", "URL": "https://tidyselect.r-lib.org, https://github.com/r-lib/tidyselect", "BugReports": "https://github.com/r-lib/tidyselect/issues", "Depends": ["R (>= 3.4)"], "Imports": ["cli (>= 3.3.0)", "glue (>= 1.3.0)", "lifecycle (>= 1.0.3)", "rlang (>= 1.0.4)", "vctrs (>= 0.5.2)", "withr"], "Suggests": ["covr", "crayon", "dplyr", "knitr", "magrit<PERSON>", "rmarkdown", "stringr", "testthat (>= 3.1.1)", "tibble (>= 2.1.3)"], "VignetteBuilder": "knitr", "ByteCompile": "true", "Config/testthat/edition": "3", "Config/Needs/website": "tidyverse/tidytemplate", "Encoding": "UTF-8", "RoxygenNote": "7.3.0.9000", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON> [aut], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "tinytex": {"Package": "tinytex", "Version": "0.57", "Source": "Repository", "Type": "Package", "Title": "Helper Functions to Install and Maintain TeX Live, and Compile LaTeX Documents", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\", \"cph\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\")), person(given = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0003-4474-2498\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-8549-0971\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person() )", "Description": "Helper functions to install and maintain the 'LaTeX' distribution named 'TinyTeX' (<https://yihui.org/tinytex/>), a lightweight, cross-platform, portable, and easy-to-maintain version of 'TeX Live'. This package also contains helper functions to compile 'LaTeX' documents, and install missing 'LaTeX' packages automatically.", "Imports": ["xfun (>= 0.48)"], "Suggests": ["testit", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "License": "MIT + file LICENSE", "URL": "https://github.com/rstudio/tinytex", "BugReports": "https://github.com/rstudio/tinytex/issues", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre, cph] (<https://orcid.org/0000-0003-0645-5666>), Posit Software, PBC [cph, fnd], <PERSON> [ctb] (<https://orcid.org/0000-0003-4474-2498>), <PERSON> [ctb] (<https://orcid.org/0000-0002-8549-0971>), <PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "toastui": {"Package": "<PERSON><PERSON>", "Version": "0.4.0", "Source": "Repository", "Title": "Interactive Tables, Calendars and Charts for the Web", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\", \"cph\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"NHN FE Development Lab\", role = \"cph\", comment = \"tui-grid, tui-calendar, tui-chart libraries\"))", "Description": "Create interactive tables, calendars, charts and markdown WYSIWYG editor with 'TOAST UI' <https://ui.toast.com/> libraries to integrate in 'shiny' applications or 'rmarkdown' 'HTML' documents.", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "LazyData": "true", "Depends": ["R (>= 2.10)"], "Imports": ["utils", "htmlwidgets", "htmltools", "magrit<PERSON>", "phosphoricons", "rlang", "shiny (>= 1.1.0)", "shinyWidgets"], "Suggests": ["apexcharter", "bslib", "knitr", "rmarkdown", "scales", "tinytest"], "VignetteBuilder": "knitr", "RoxygenNote": "7.3.2", "URL": "https://dreamrs.github.io/toastui/", "BugReports": "https://github.com/dreamRs/toastui/issues", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre, cph], <PERSON> [aut], NHN FE Development Lab [cph] (tui-grid, tui-calendar, tui-chart libraries)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "twosamples": {"Package": "twosamples", "Version": "2.0.1", "Source": "Repository", "Type": "Package", "Title": "Fast Permutation Based Two Sample Tests", "Authors@R": "person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-9782-0931\"))", "Description": "Fast randomization based two sample tests.  Testing the hypothesis that two samples come from the same distribution using randomization to create p-values. Included tests are: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and DTS. The default test (two_sample) is based on the DTS test statistic, as it is the most powerful, and thus most useful to most users.  The DTS test statistic builds on the <PERSON><PERSON><PERSON> distance by using a weighting scheme like that of <PERSON><PERSON>. See the companion paper at <arXiv:2007.01360> or <https://codowd.com/public/DTS.pdf> for details of that test statistic, and non-standard uses of the package (parallel for big N, weighted observations, one sample tests, etc). We also include the permutation scheme to make test building simple for others.", "License": "GPL (>= 2)", "Encoding": "UTF-8", "LinkingTo": ["cpp11"], "RoxygenNote": "7.2.3", "URL": "https://twosampletest.com, https://github.com/cdowd/twosamples", "BugReports": "https://github.com/cdowd/twosamples/issues", "Suggests": ["testthat (>= 3.0.0)"], "Config/testthat/edition": "3", "NeedsCompilation": "yes", "Author": "<PERSON>d [aut, cre] (<https://orcid.org/0000-0002-9782-0931>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "tzdb": {"Package": "tzdb", "Version": "0.5.0", "Source": "Repository", "Title": "Time Zone Database Information", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"Hinnant\", role = \"cph\", comment = \"Author of the included date library\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Provides an up-to-date copy of the Internet Assigned Numbers Authority (IANA) Time Zone Database. It is updated periodically to reflect changes made by political bodies to time zone boundaries, UTC offsets, and daylight saving time rules. Additionally, this package provides a C++ interface for working with the 'date' library. 'date' provides comprehensive support for working with dates and date-times, which this package exposes to make it easier for other R packages to utilize. Headers are provided for calendar specific calculations, along with a limited interface for time zone manipulations.", "License": "MIT + file LICENSE", "URL": "https://tzdb.r-lib.org, https://github.com/r-lib/tzdb", "BugReports": "https://github.com/r-lib/tzdb/issues", "Depends": ["R (>= 4.0.0)"], "Suggests": ["covr", "testthat (>= 3.0.0)"], "LinkingTo": ["cpp11 (>= 0.5.2)"], "Biarch": "yes", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON> [cph] (Author of the included date library), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "utf8": {"Package": "utf8", "Version": "1.2.6", "Source": "Repository", "Title": "Unicode Text Processing", "Authors@R": "c(person(given = c(\"<PERSON>\", \"<PERSON><PERSON>\"), family = \"<PERSON>\", role = c(\"aut\", \"cph\")), person(given = \"Kirill\", family = \"M\\u00fcller\", role = \"cre\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(given = \"Unicode, Inc.\", role = c(\"cph\", \"dtc\"), comment = \"Unicode Character Database\"))", "Description": "Process and print 'UTF-8' encoded international text (Unicode). Input, validate, normalize, encode, format, and display.", "License": "Apache License (== 2.0) | file LICENSE", "URL": "https://krlmlr.github.io/utf8/, https://github.com/krlmlr/utf8", "BugReports": "https://github.com/krlmlr/utf8/issues", "Depends": ["R (>= 2.10)"], "Suggests": ["cli", "covr", "knitr", "rlang", "rmarkdown", "testthat (>= 3.0.0)", "withr"], "VignetteBuilder": "knitr, rmarkdown", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2.9000", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cph], <PERSON><PERSON> [cre] (ORCID: <https://orcid.org/0000-0002-1416-3412>), Unicode, Inc. [cph, dtc] (Unicode Character Database)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "uuid": {"Package": "uuid", "Version": "1.2-1", "Source": "Repository", "Title": "Tools for Generating and Handling of UUIDs", "Author": "<PERSON> [aut, cre, cph] (https://urbanek.org, <https://orcid.org/0000-0003-2297-1732>), <PERSON> [aut, cph] (libuuid)", "Maintainer": "<PERSON> <<PERSON>.<PERSON>@r-project.org>", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON>\", role=c(\"aut\",\"cre\",\"cph\"), email=\"<EMAIL>\", comment=c(\"https://urbanek.org\", ORCID=\"0000-0003-2297-1732\")), person(\"<PERSON>\",\"Ts'o\", email=\"<EMAIL>\", role=c(\"aut\",\"cph\"), comment=\"libuuid\"))", "Depends": ["R (>= 2.9.0)"], "Description": "Tools for generating and handling of UUIDs (Universally Unique Identifiers).", "License": "MIT + file LICENSE", "URL": "https://www.rforge.net/uuid", "BugReports": "https://github.com/s-u/uuid/issues", "NeedsCompilation": "yes", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}, "vctrs": {"Package": "vctrs", "Version": "0.6.5", "Source": "Repository", "Title": "Vector Helpers", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"data.table team\", role = \"cph\", comment = \"Radix sort based on data.table's forder() and their contribution to R's order()\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Defines new notions of prototype and size that are used to provide tools for consistent and well-founded type-coercion and size-recycling, and are in turn connected to ideas of type- and size-stability useful for analysing function interfaces.", "License": "MIT + file LICENSE", "URL": "https://vctrs.r-lib.org/, https://github.com/r-lib/vctrs", "BugReports": "https://github.com/r-lib/vctrs/issues", "Depends": ["R (>= 3.5.0)"], "Imports": ["cli (>= 3.4.0)", "glue", "lifecycle (>= 1.0.3)", "rlang (>= 1.1.0)"], "Suggests": ["bit64", "covr", "crayon", "dplyr (>= 0.8.5)", "generics", "knitr", "pillar (>= 1.4.4)", "pkgdown (>= 2.0.1)", "rmarkdown", "testthat (>= 3.0.0)", "tibble (>= 3.1.3)", "waldo (>= 0.2.0)", "withr", "xml2", "zeallot"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "Language": "en-GB", "RoxygenNote": "7.2.3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON> [aut, cre], data.table team [cph] (Radix sort based on data.table's forder() and their contribution to R's order()), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "viridisLite": {"Package": "viridisLite", "Version": "0.4.2", "Source": "Repository", "Type": "Package", "Title": "Colorblind-Friendly Color Maps (Lite Version)", "Date": "2023-05-02", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = c(\"ctb\", \"cph\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"ctb\", \"cph\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"ctb\", \"cph\")), person(\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>here<PERSON>\", email = \"<EMAIL>\", role = c(\"ctb\", \"cph\")) )", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "Color maps designed to improve graph readability for readers with  common forms of color blindness and/or color vision deficiency. The color  maps are also perceptually-uniform, both in regular form and also when  converted to black-and-white for printing. This is the 'lite' version of the  'viridis' package that also contains 'ggplot2' bindings for discrete and  continuous color and fill scales and can be found at  <https://cran.r-project.org/package=viridis>.", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "Depends": ["R (>= 2.10)"], "Suggests": ["hexbin (>= 1.27.0)", "ggplot2 (>= 1.0.1)", "testthat", "covr"], "URL": "https://sjmgarnier.github.io/viridisLite/, https://github.com/sjmgarnier/viridisLite/", "BugReports": "https://github.com/sjmgarnier/viridisLite/issues/", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON><PERSON> [ctb, cph], <PERSON> [ctb, cph], <PERSON> [ctb, cph], <PERSON><PERSON><PERSON><PERSON> [ctb, cph], <PERSON><PERSON><PERSON> [ctb, cph]", "Repository": "CRAN"}, "vroom": {"Package": "vroom", "Version": "1.6.5", "Source": "Repository", "Title": "Read and Write Rectangular Text Data Quickly", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2739-7082\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\", comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-6983-2759\")), person(\"<PERSON>\", \"Bearrows\", role = \"ctb\"), person(\"https://github.com/mandreyel/\", role = \"cph\", comment = \"mio library\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"cph\", comment = \"grisu3 implementation\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"cph\", comment = \"grisu3 implementation\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "The goal of 'vroom' is to read and write data (like 'csv', 'tsv' and 'fwf') quickly. When reading it uses a quick initial indexing step, then reads the values lazily , so only the data you actually use needs to be read.  The writer formats the data in parallel and writes to disk asynchronously from formatting.", "License": "MIT + file LICENSE", "URL": "https://vroom.r-lib.org, https://github.com/tidyverse/vroom", "BugReports": "https://github.com/tidyverse/vroom/issues", "Depends": ["R (>= 3.6)"], "Imports": ["bit64", "cli (>= 3.2.0)", "crayon", "glue", "hms", "lifecycle (>= 1.0.3)", "methods", "rlang (>= 0.4.2)", "stats", "tibble (>= 2.0.0)", "tidyselect", "tzdb (>= 0.1.1)", "vctrs (>= 0.2.0)", "withr"], "Suggests": ["archive", "bench (>= 1.1.0)", "covr", "curl", "dplyr", "forcats", "fs", "ggplot2", "knitr", "patchwork", "prettyunits", "purrr", "rmarkdown", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scales", "spelling", "testthat (>= 2.1.0)", "tidyr", "utils", "waldo", "xml2"], "LinkingTo": ["cpp11 (>= 0.2.0)", "progress (>= 1.2.1)", "tzdb (>= 0.1.1)"], "VignetteBuilder": "knitr", "Config/Needs/website": "nycflights13, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/testthat/parallel": "false", "Copyright": "file COPYRIGHTS", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.2.3.9000", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (<https://orcid.org/0000-0002-2739-7082>), <PERSON> [aut] (<https://orcid.org/0000-0003-4757-117X>), <PERSON> [aut, cre] (<https://orcid.org/0000-0002-6983-2759>), <PERSON> [ctb], https://github.com/mandreyel/ [cph] (mio library), <PERSON><PERSON> [cph] (grisu3 implementation), <PERSON><PERSON><PERSON> [cph] (grisu3 implementation), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "waiter": {"Package": "waiter", "Version": "0.2.5", "Source": "Repository", "Title": "Loading Screen for 'Shiny'", "Date": "2022-01-02", "Authors@R": "c( person(given = \"<PERSON>\", family = \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(given = \"<PERSON><PERSON><PERSON>\", family = \"<PERSON>\", role = \"ctb\", email = '<EMAIL>'), person(given = \"<PERSON>\", family = \"<PERSON><PERSON>\", role = c(\"ctb\"), email = \"victorgra<PERSON><PERSON><PERSON>@gmail.com\", comment = c(ORCID = \"0000-0002-0469-1991\")))", "Description": "Full screen and partial loading screens for 'Shiny' with spinners, progress bars, and notifications.", "License": "MIT + file LICENSE", "URL": "https://waiter.john-coene.com/, https://github.com/JohnCoene/waiter", "BugReports": "https://github.com/JohnCoene/waiter/issues", "Encoding": "UTF-8", "Imports": ["R6", "shiny", "htmltools"], "RoxygenNote": "7.1.2", "Suggests": ["httr", "knitr", "packer", "rmarkdown"], "VignetteBuilder": "knitr", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0002-0469-1991>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "waldo": {"Package": "waldo", "Version": "0.6.1", "Source": "Repository", "Title": "Find Differences Between R Objects", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Compare complex R objects and reveal the key differences. Designed particularly for use in testing packages where being able to quickly isolate key differences makes understanding test failures much easier.", "License": "MIT + file LICENSE", "URL": "https://waldo.r-lib.org, https://github.com/r-lib/waldo", "BugReports": "https://github.com/r-lib/waldo/issues", "Depends": ["R (>= 4.0)"], "Imports": ["cli", "diffobj (>= 0.3.4)", "glue", "methods", "rlang (>= 1.1.0)"], "Suggests": ["bit64", "R6", "S7", "testthat (>= 3.0.0)", "withr", "xml2"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "withr": {"Package": "withr", "Version": "3.0.2", "Source": "Repository", "Title": "Run Code 'With' Temporarily Modified Global State", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"kev<PERSON><PERSON>@gmail.com\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A set of functions to run code 'with' safely and temporarily modified global state. Many of these functions were originally a part of the 'devtools' package, this provides a simple package with limited dependencies to provide access to these functions.", "License": "MIT + file LICENSE", "URL": "https://withr.r-lib.org, https://github.com/r-lib/withr#readme", "BugReports": "https://github.com/r-lib/withr/issues", "Depends": ["R (>= 3.6.0)"], "Imports": ["graphics", "grDevices"], "Suggests": ["callr", "DBI", "knitr", "methods", "rlang", "rmarkdown (>= 2.12)", "RSQLite", "testthat (>= 3.0.0)"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "Collate": "'aaa.R' 'collate.R' 'connection.R' 'db.R' 'defer-exit.R' 'standalone-defer.R' 'defer.R' 'devices.R' 'local_.R' 'with_.R' 'dir.R' 'env.R' 'file.R' 'language.R' 'libpaths.R' 'locale.R' 'makevars.R' 'namespace.R' 'options.R' 'par.R' 'path.R' 'rng.R' 'seed.R' 'wrap.R' 'sink.R' 'tempfile.R' 'timezone.R' 'torture.R' 'utils.R' 'with.R'", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [ctb], <PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "writexl": {"Package": "writexl", "Version": "1.5.4", "Source": "Repository", "Type": "Package", "Title": "Export Data Frames to Excel 'xlsx' Format", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", ,\"jero<PERSON><PERSON>@gmail.com\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON>\", role = \"cph\", comment = \"Author of libxlsxwriter (see AUTHORS and COPYRIGHT files for details)\"))", "Description": "Zero-dependency data frame to xlsx exporter based on 'libxlsxwriter' <https://libxlsxwriter.github.io>. Fast and no Java or Excel required.", "License": "BSD_2_clause + file LICENSE", "Encoding": "UTF-8", "URL": "https://ropensci.r-universe.dev/writexl https://docs.ropensci.org/writexl/", "BugReports": "https://github.com/ropensci/writexl/issues", "RoxygenNote": "7.0.2", "Suggests": ["spelling", "readxl", "nycflights13", "testthat", "bit64"], "Language": "en-US", "SystemRequirements": "zlib", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-4035-0289>), <PERSON> [cph] (Author of libxlsxwriter (see AUTHORS and COPYRIGHT files for details))", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "xfun": {"Package": "xfun", "Version": "0.52", "Source": "Repository", "Type": "Package", "Title": "Supporting Functions for Packages Maintained by '<PERSON><PERSON>'", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\", \"cph\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\", URL = \"https://yihui.org\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"Dai<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-5329-5987\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person() )", "Description": "Miscellaneous functions commonly used in other packages maintained by '<PERSON><PERSON>'.", "Depends": ["R (>= 3.2.0)"], "Imports": ["grDevices", "stats", "tools"], "Suggests": ["testit", "parallel", "codetools", "methods", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tinytex (>= 0.30)", "mime", "litedown (>= 0.4)", "commonmark", "knitr (>= 1.50)", "remotes", "pak", "curl", "xml2", "jsonlite", "magick", "yaml", "qs"], "License": "MIT + file LICENSE", "URL": "https://github.com/yihui/xfun", "BugReports": "https://github.com/yihui/xfun/issues", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "VignetteBuilder": "litedown", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre, cph] (<https://orcid.org/0000-0003-0645-5666>, https://yihui.org), <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-5329-5987>), <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "xplorerr": {"Package": "xplorerr", "Version": "0.2.0", "Source": "Repository", "Type": "Package", "Title": "Tools for Interactive Data Exploration", "Authors@R": "person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0001-9220-9669\"))", "Description": "Tools for interactive data exploration built using 'shiny'. Includes apps for descriptive  statistics, visualizing probability distributions, inferential statistics, linear regression,  logistic regression and RFM analysis.", "Depends": ["R(>= 3.2.4)"], "Imports": ["Rcpp", "shiny", "utils"], "Suggests": ["blorr", "data.table", "descriptr", "DT", "haven", "highcharter", "jsonlite", "magrit<PERSON>", "olsrr", "plotly", "readr", "readxl", "rfm", "shinyBS", "shinycssloaders", "standby", "tools", "vistributions"], "URL": "https://github.com/rsquaredacademy/xplorerr, https://xplorerr.rsquaredacademy.com/", "BugReports": "https://github.com/rsquaredacademy/xplorerr/issues", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.3.2", "LinkingTo": ["Rcpp"], "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0001-9220-9669>)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "https://packagemanager.posit.co/cran/latest"}, "xtable": {"Package": "xtable", "Version": "1.8-4", "Source": "Repository", "Date": "2019-04-08", "Title": "Export Tables to LaTeX or HTML", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON>\", role=c(\"aut\",\"cre\"), email=\"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role=\"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>fa<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"Andronic\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>echer\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>ubri\", role=\"ctb\"), person(\"<PERSON>hi<PERSON>\", \"<PERSON>igler\", role=\"ctb\"), person(\"Robert\", \"Castelo\", role=\"ctb\"), person(\"Seth\", \"Falcon\", role=\"ctb\"), person(\"Stefan\", \"Edwards\", role=\"ctb\"), person(\"Sven\", \"Garbade\", role=\"ctb\"), person(\"Uwe\", \"Ligges\", role=\"ctb\"))", "Maintainer": "<PERSON> <<EMAIL>>", "Imports": ["stats", "utils"], "Suggests": ["knitr", "plm", "zoo", "survival"], "VignetteBuilder": "knitr", "Description": "Coerce data to LaTeX and HTML tables.", "URL": "http://xtable.r-forge.r-project.org/", "Depends": ["R (>= 2.10.0)"], "License": "GPL (>= 2)", "Repository": "CRAN", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut, cre], <PERSON> [aut], <PERSON><PERSON><PERSON> [aut], <PERSON> [aut], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb]"}, "yaml": {"Package": "yaml", "Version": "2.3.10", "Source": "Repository", "Type": "Package", "Title": "Methods to Convert R Data to YAML and Back", "Date": "2024-07-22", "Suggests": ["RUnit"], "Author": "<PERSON> [aut], <PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "License": "BSD_3_clause + file LICENSE", "Description": "Implements the 'libyaml' 'YAML' 1.1 parser and emitter (<https://pyyaml.org/wiki/LibYAML>) for R.", "URL": "https://github.com/vubiostat/r-yaml/", "BugReports": "https://github.com/vubiostat/r-yaml/issues", "NeedsCompilation": "yes", "Repository": "https://packagemanager.posit.co/cran/latest", "Encoding": "UTF-8"}}}