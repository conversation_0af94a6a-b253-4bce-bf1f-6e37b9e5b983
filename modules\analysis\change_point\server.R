ChangePointServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    cpData <- eventReactive(input$cpUserData, {
      handle_file_upload(input$cpUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(cpData(), {
      data <- cpData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'cpSeries', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    cpValidationErrors <- reactive({
      errors <- c()
      data <- cpData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(input$cpSeries)) {
        errors <- c(errors, "Select a time series variable.")
      }
      if (!is.null(input$cpSeries) && !is.numeric(data[[input$cpSeries]])) {
        errors <- c(errors, "Time series variable must be numeric.")
      }
      if (!is.null(input$cpSeries) && length(na.omit(data[[input$cpSeries]])) < 20) {
        errors <- c(errors, "Time series must have at least 20 observations.")
      }
      errors
    })
    
    # Change point analysis reactive
    cpResult <- eventReactive(input$goCP, {
      data <- cpData()
      req(data, input$cpSeries)
      
      # Remove missing values
      clean_data <- na.omit(data[[input$cpSeries]])
      if (length(clean_data) < 20) {
        stop("Insufficient data after removing missing values.")
      }
      
      # Create time series object
      ts_data <- ts(clean_data)
      
      # Perform change point detection
      change_point_detection(ts_data)
    })
    
    # Error handling
    output$cpError <- renderUI({
      errors <- cpValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          cpResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "Change Point Detection Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$cpModelSummary <- renderUI({
      req(cpResult())
      res <- cpResult()
      
      tagList(
        h4("Change Point Detection Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Metric = c("Detection Method", "Observations", "Number of Change Points", "Penalty Type"),
            Value = c(
              "PELT Algorithm",
              length(res$data),
              length(res$change_points),
              "BIC"
            )
          )
        }),
        h4("Change Point Locations"),
        renderTable({
          if (length(res$change_points) > 0) {
            data.frame(
              Change_Point = 1:length(res$change_points),
              Time_Index = res$change_points,
              Time_Value = res$data[res$change_points]
            )
          } else {
            data.frame(Message = "No change points detected")
          }
        }),
        h4("Segment Statistics"),
        renderTable({
          res$segment_stats
        })
      )
    })
    
    output$cpPlot <- renderPlot({
      req(cpResult())
      res <- cpResult()
      
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # Original data with change points
      plot(res$data, main = "Time Series with Change Points", 
           ylab = "Value", xlab = "Time", type = "l")
      if (length(res$change_points) > 0) {
        abline(v = res$change_points, col = "red", lty = 2, lwd = 2)
        points(res$change_points, res$data[res$change_points], 
               col = "red", pch = 19, cex = 1.5)
      }
      
      # Segment means
      if (length(res$change_points) > 0) {
        plot(res$segment_means, main = "Segment Means", 
             ylab = "Mean", xlab = "Segment", type = "b", pch = 19)
      } else {
        plot.new()
        text(0.5, 0.5, "No segments to display", cex = 1.2)
      }
      
      # Residuals
      plot(res$residuals, main = "Residuals", ylab = "Residual", xlab = "Time")
      abline(h = 0, col = "red", lty = 2)
      
      # ACF of residuals
      acf(res$residuals, main = "ACF of Residuals", na.action = na.pass)
      
      par(mfrow = c(1, 1))
    })
    
    output$cpDiagnostics <- renderUI({
      req(cpResult())
      res <- cpResult()
      
      tagList(
        h4("Model Diagnostics"),
        renderTable({
          data.frame(
            Metric = c("Total Sum of Squares", "Residual Sum of Squares", "R-squared", "AIC", "BIC"),
            Value = c(
              round(res$total_ss, 3),
              round(res$residual_ss, 3),
              round(res$r_squared, 3),
              round(res$aic, 3),
              round(res$bic, 3)
            )
          )
        })
      )
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$cpDataSummary <- renderUI({
      req(cpData(), input$cpSeries)
      data <- cpData()
      series_data <- data[[input$cpSeries]]
      
      tagList(
        h4("Time Series Summary"),
        renderTable({
          summary_stats <- summary(series_data)
          data.frame(
            Statistic = names(summary_stats),
            Value = as.numeric(summary_stats)
          )
        }),
        h4("Missing Values"),
        renderTable({
          missing_count <- sum(is.na(series_data))
          total_count <- length(series_data)
          data.frame(
            Metric = c("Total Observations", "Missing Values", "Complete Cases", "Missing Percentage"),
            Value = c(total_count, missing_count, total_count - missing_count, 
                     round(missing_count/total_count * 100, 2))
          )
        })
      )
    })
    
    output$cpAssumptions <- renderUI({
      req(cpResult())
      res <- cpResult()
      
      tagList(
        h4("Model Assumptions Check"),
        renderTable({
          # Normality test
          norm_test <- shapiro.test(res$residuals)
          # Independence test (Ljung-Box)
          lb_test <- Box.test(res$residuals, type = "Ljung-Box")
          
          data.frame(
            Assumption = c("Residual Normality (Shapiro-Wilk)", "Residual Independence (Ljung-Box)"),
            Test_Statistic = c(round(norm_test$statistic, 4), round(lb_test$statistic, 4)),
            P_Value = c(round(norm_test$p.value, 4), round(lb_test$p.value, 4)),
            Decision = c(
              ifelse(norm_test$p.value > 0.05, "Pass", "Fail"),
              ifelse(lb_test$p.value > 0.05, "Pass", "Fail")
            )
          )
        }),
        h4("Residual Analysis"),
        renderTable({
          data.frame(
            Metric = c("Mean", "Standard Deviation", "Skewness", "Kurtosis"),
            Value = c(
              round(mean(res$residuals, na.rm = TRUE), 4),
              round(sd(res$residuals, na.rm = TRUE), 4),
              round(skewness(res$residuals, na.rm = TRUE), 4),
              round(kurtosis(res$residuals, na.rm = TRUE), 4)
            )
          )
        })
      )
    })
    
    output$cpDiagnosticPlots <- renderPlot({
      req(cpResult())
      res <- cpResult()
      
      par(mfrow = c(2, 2))
      
      # Residuals vs Fitted
      plot(res$fitted, res$residuals, main = "Residuals vs Fitted",
           xlab = "Fitted Values", ylab = "Residuals")
      abline(h = 0, col = "red", lty = 2)
      
      # Histogram of residuals
      hist(res$residuals, main = "Histogram of Residuals", 
           xlab = "Residuals", freq = FALSE)
      curve(dnorm(x, mean = mean(res$residuals, na.rm = TRUE), 
                  sd = sd(res$residuals, na.rm = TRUE)), 
            add = TRUE, col = "red")
      
      # Q-Q plot of residuals
      qqnorm(res$residuals, main = "Q-Q Plot of Residuals")
      qqline(res$residuals, col = "red")
      
      # Time series of residuals
      plot(res$residuals, main = "Residuals Over Time", 
           ylab = "Residuals", xlab = "Time")
      abline(h = 0, col = "red", lty = 2)
      
      par(mfrow = c(1, 1))
    })
    
    # Uploaded Data Tab Outputs
    output$cpDataTable <- renderDT({
      req(cpData())
      data <- cpData()
      
      DT::datatable(
        data,
        options = list(
          pageLength = 10,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
        ),
        extensions = 'Buttons',
        filter = 'top',
        rownames = FALSE
      )
    })
    
    output$cpDataInfo <- renderUI({
      req(cpData())
      data <- cpData()
      
      tagList(
        h4("Dataset Information"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "File Name"),
            Value = c(nrow(data), ncol(data), 
                     ifelse(!is.null(input$cpUserData), input$cpUserData$name, "Unknown"))
          )
        }),
        h4("Variable Information"),
        renderTable({
          data.frame(
            Variable = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Unique = sapply(data, function(x) length(unique(x)))
          )
        })
      )
    })
  })
} 