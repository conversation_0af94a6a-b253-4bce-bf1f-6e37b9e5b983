# Social Network Analysis Server
socialNetworksServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns

    # Upload and parse data
    snUploadData <- reactive({
      req(input$snUserData)
      ext <- tools::file_ext(input$snUserData$name)
      if (ext %in% c("csv", "txt")) {
        read.csv(input$snUserData$datapath)
      } else if (ext %in% c("xls", "xlsx")) {
        if (!requireNamespace("readxl", quietly = TRUE)) stop("Install 'readxl' for Excel support.")
        readxl::read_excel(input$snUserData$datapath)
      } else {
        stop("Unsupported file type.")
      }
    })

    # UI for selecting source and target columns
    output$snColSelectors <- renderUI({
      df <- snUploadData()
      req(df)
      tagList(
        selectInput(ns("snSourceCol"), "Source Column", choices = names(df)),
        selectInput(ns("snTargetCol"), "Target Column", choices = names(df))
      )
    })

    # Run Social Network Analysis with error handling
    snResults <- eventReactive(input$goSN, {
      df <- snUploadData()
      req(input$snSourceCol, input$snTargetCol)
      tryCatch({
        socialNetworksResults_func(
          data = df,
          source_col = input$snSourceCol,
          target_col = input$snTargetCol
        )
      }, error = function(e) {
        structure(list(error = TRUE, message = e$message), class = "sn_error")
      })
    })

    # Outputs
    output$snHT <- renderUI({
      results <- snResults()
      if (inherits(results, "sn_error")) {
        div(style = "color: red;", paste("Error:", results$message))
      } else if (is.null(results)) {
        NULL
      } else {
        verbatimTextOutput(ns("snSummary"))
      }
    })
    output$snSummary <- renderPrint({
      results <- snResults()
      if (inherits(results, "sn_error")) return(NULL)
      if (is.null(results)) return(NULL)
      print(results$summary)
    })
    output$snPlot <- renderPlot({
      results <- snResults()
      if (inherits(results, "sn_error")) return(NULL)
      if (is.null(results) || is.null(results$plot)) return(NULL)
      results$plot()
    })
    output$snConclusionOutput <- renderUI({
      results <- snResults()
      if (inherits(results, "sn_error")) return(NULL)
      if (is.null(results)) return(NULL)
      tags$p("Social network analysis complete. See summary and network plot above.")
    })
    output$renderSNData <- renderUI({
      req(snUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("snInitialUploadTable")), style = "width: 75%"),
        br(),
        br(),
        uiOutput(ns("snColSelectors"))
      )
    })
    output$snInitialUploadTable <- DT::renderDT({
      req(snUploadData())
      DT::datatable(snUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(snUploadData())))))
    })
  })
} 