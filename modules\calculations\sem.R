# Structural Equation Modeling (SEM) calculation and output helpers

sem_uploadData_func <- function(semUserData) {
  tryCatch(
    {
      if (is.null(semUserData)) {
        return(NULL)
      }
      
      # Read data based on file type
      if (grepl("\\.csv$", semUserData$name, ignore.case = TRUE)) {
        df <- read.csv(semUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.tsv$", semUserData$name, ignore.case = TRUE)) {
        df <- read.tsv(semUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.xlsx$", semUserData$name, ignore.case = TRUE)) {
        df <- readxl::read_excel(semUserData$datapath)
      } else {
        stop("Unsupported file type. Please upload a CSV, TSV, or XLSX file.")
      }
      
      return(df)
    },
    error = function(e) {
      return(list(error = paste("Error reading data:", e$message)))
    }
  )
}

sem_results_func <- function(data, model_syntax) {
  tryCatch({
    if (is.null(data) || nrow(data) == 0) {
      stop("No data provided for analysis.")
    }
    
    if (is.null(model_syntax) || nchar(model_syntax) == 0) {
      stop("SEM model syntax is required.")
    }
    
    if (!requireNamespace("lavaan", quietly = TRUE)) {
      stop("Package 'lavaan' is required for SEM analysis.")
    }
    
    # Fit the SEM model
    fit <- lavaan::sem(model_syntax, data = data)
    
    list(
      fit = fit,
      summary = summary(fit, fit.measures = TRUE, standardized = TRUE),
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during SEM analysis:", e$message))
  })
}

sem_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  tagList(
    h4("Structural Equation Model"),
    p("See summary and plots for model details.")
  )
}

sem_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  out <- list(
    h4("SEM Summary"),
    renderPrint(results$summary)
  )
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

sem_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  if (requireNamespace("semPlot", quietly = TRUE)) {
    semPlot::semPaths(results$fit, what = "std", layout = "tree", edge.label.cex = 0.8)
  } else {
    plot.new(); title("Package 'semPlot' required for SEM path diagram.")
  }
}
