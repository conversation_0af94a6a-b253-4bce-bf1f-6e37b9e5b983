IRTUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("irtUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectizeInput(ns("irtVars"), "Variables", choices = NULL, multiple = TRUE),
        br(),
        actionButton(ns("goIRT"), label = "Run IRT", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("irtError")),
        tableOutput(ns("irtResults")),
        plotOutput(ns("irtPlot"))
      )
    )
  )
} 