# Proportion Tests UI
ProportionTestSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    radioButtons(ns("propTestType"), "Test Type", choices = c("One-sample Proportion", "Two-sample Proportion", "Binomial Test", "Fisher's Exact Test"), selected = "One-sample Proportion", inline = FALSE),
    fileInput(ns("propTestUserData"), "Upload your Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
    selectizeInput(ns("propTestGroup"), "Group Column (for two-sample/Fisher)", choices = NULL),
    selectizeInput(ns("propTestResponse"), "Response Column (success/failure)", choices = NULL),
    numericInput(ns("propTestSuccesses"), "Number of Successes", value = NA, min = 0),
    numericInput(ns("propTestTrials"), "Number of Trials", value = NA, min = 1),
    radioButtons(ns("propTestSigLvl"), "Significance Level (alpha)", choices = c("10%", "5%", "1%"), selected = "5%", inline = TRUE),
    br(),
    actionButton(ns("goPropTest"), label = "Calculate", class = "act-btn")
  )
}

ProportionTestMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('propTestResults'))
  )
}

ProportionTestUI <- function(id) {
  ns <- NS(id)
  tagList(
    ProportionTestSidebarUI(id),
    ProportionTestMainUI(id)
  )
} 