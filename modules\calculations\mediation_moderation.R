# Mediation/Moderation Analysis calculation and output helpers

mediation_moderation_uploadData_func <- function(mmUserData) {
  ext <- tools::file_ext(mmUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(mmUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(mmUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(mmUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(mmUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

mediation_moderation_results_func <- function(data, iv, dv, mediator = NULL, moderator = NULL, model_type = "mediation") {
  tryCatch({
    
    # Center variables and run analysis
    result <- perform_mediation_moderation_analysis(data, iv, dv, mediator, moderator, model_type)
    
    list(
      results = result,
      data = data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during analysis:", e$message))
  })
}

mediation_moderation_ht_html <- function(results) {
  res <- results$results
  ht_text <- ""
  if (res$type == "mediation") {
    p_val <- res$effects[res$effects$Effect == "Indirect Effect (a*b)", "p_value"]
    ht_text <- paste("The p-value for the indirect effect is", p_val, ".", if(p_val < 0.05) "This is significant." else "This is not significant.")
  } else if (res$type == "moderation") {
    p_val <- res$coefficients[res$coefficients$Variable == "IV × Moderator", "p_value"]
    ht_text <- paste("The p-value for the interaction term is", p_val, ".", if(p_val < 0.05) "This indicates a significant moderation effect." else "This does not indicate a significant moderation effect.")
  } else {
    ht_text <- "See summary for conditional indirect effects."
  }
  
  tagList(
    h4(paste(tools::toTitleCase(res$type), "Analysis")),
    p(ht_text)
  )
}

mediation_moderation_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  res <- results$results
  out <- list()
  if (res$type == "mediation") {
    out <- c(out, h4("Effects Table"), renderTable(res$effects), h4("Path Coefficients"), renderTable(res$path_coefficients))
  } else if (res$type == "moderation") {
    out <- c(out, h4("Coefficients"), renderTable(res$coefficients), h4("Simple Slopes"), renderTable(res$simple_slopes))
  } else {
    out <- c(out, h4("Conditional Indirect Effects"), renderTable(res$conditional_effects))
  }
  # Add fit indices and warnings if available
  if (!is.null(res$model) && !is.null(attr(res$model, "converged"))) {
    out <- c(out, paste("Converged:", attr(res$model, "converged")))
  }
  if (!is.null(res$model) && !is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

mediation_moderation_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  res <- results$results
  if (res$type == "mediation" && !is.null(res$boot_results)) {
    # Plot indirect effect distribution
    hist(res$boot_results$d0.sims, main = "Indirect Effect Distribution", xlab = "Indirect Effect (a*b)")
    abline(v = mean(res$boot_results$d0.sims), col = "red")
  } else if (res$type == "moderation" && !is.null(res$model)) {
    # Plot interaction effect
    library(effects)
    eff <- effects::allEffects(res$model)
    plot(eff, main = "Interaction Effect Plot")
  } else {
    plot.new(); title("No plot available for this model type.")
  }
}


# --- Helper function from original file ---

perform_mediation_moderation_analysis <- function(data, iv, dv, mediator = NULL, moderator = NULL, model_type = "mediation") {
    
    vars_to_check <- c(iv, dv)
    if (!is.null(mediator)) vars_to_check <- c(vars_to_check, mediator)
    if (!is.null(moderator)) vars_to_check <- c(vars_to_check, moderator)
    
    data <- data[complete.cases(data[, vars_to_check, drop = FALSE]), , drop = FALSE]
    
    data$iv_centered <- scale(data[[iv]], center = TRUE, scale = FALSE)
    if (!is.null(mediator)) {
      data$mediator_centered <- scale(data[[mediator]], center = TRUE, scale = FALSE)
    }
    if (!is.null(moderator)) {
      data$moderator_centered <- scale(data[[moderator]], center = TRUE, scale = FALSE)
    }
    
    if (model_type == "mediation" && !is.null(mediator)) {
      if (!requireNamespace("mediation", quietly = TRUE)) {
        stop("Package 'mediation' is required.")
      }
      
      path_a_model <- lm(mediator_centered ~ iv_centered, data = data)
      path_b_model <- lm(as.formula(paste(dv, "~ mediator_centered + iv_centered")), data = data)
      
      boot_results <- mediation::mediate(path_a_model, path_b_model, 
                                        treat = "iv_centered", mediator = "mediator_centered",
                                        boot = TRUE, sims = 1000)
      
      effects_table <- data.frame(
        Effect = c("Direct Effect (c')", "Indirect Effect (a*b)", "Total Effect (c)"),
        Estimate = c(boot_results$d.avg, boot_results$i.avg, boot_results$t.avg),
        Lower_CI = c(boot_results$d.avg.ci[1], boot_results$i.avg.ci[1], boot_results$t.avg.ci[1]),
        Upper_CI = c(boot_results$d.avg.ci[2], boot_results$i.avg.ci[2], boot_results$t.avg.ci[2]),
        p_value = c(boot_results$d.avg.p, boot_results$i.avg.p, boot_results$t.avg.p)
      )
      
      return(list(type = "mediation", effects = effects_table, boot_results = boot_results))
      
    } else if (model_type == "moderation" && !is.null(moderator)) {
      
      data$interaction <- data$iv_centered * data$moderator_centered
      mod_model <- lm(as.formula(paste(dv, "~ iv_centered + moderator_centered + interaction")), data = data)
      mod_summary <- summary(mod_model)
      
      coef_table <- as.data.frame(mod_summary$coefficients)
      names(coef_table) <- c("Estimate", "Std_Error", "t_value", "p_value")
      
      return(list(type = "moderation", coefficients = coef_table, model = mod_model))
      
    } else if (model_type == "moderated_mediation" && !is.null(mediator) && !is.null(moderator)) {
      
      if (!requireNamespace("lavaan", quietly = TRUE)) {
        stop("Package 'lavaan' is required for moderated mediation.")
      }
      
      model_syntax <- '
        # direct effect
        dv ~ c*iv_centered
        # mediator
        mediator_centered ~ a*iv_centered
        dv ~ b*mediator_centered
        # indirect effect (a*b)
        ab := a*b
        # total effect
        total := c + (a*b)
      '
      
      fit <- lavaan::sem(model_syntax, data = data)
      
      return(list(type = "moderated_mediation", fit = fit))
      
    } else {
      stop("Invalid model type or missing variables.")
    }
}