# Stepwise Regression UI
# Variable selection

StepwiseRegressionSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    radioButtons(
      inputId = ns("stepwiseDataMethod"),
      label = "Data Input Method:",
      choices = c("Upload File" = "Upload File"),
      selected = "Upload File"
    ),
    fileInput(
      inputId = ns("stepwiseUserData"),
      label = "Upload Data File:",
      accept = c(".csv", ".txt", ".xlsx", ".xls"),
      buttonLabel = "Browse Files",
      placeholder = "No file selected"
    ),
    selectizeInput(
      inputId = ns("stepwiseResponseVariable"),
      label = "Select Response Variable:",
      choices = NULL,
      options = list(placeholder = "Select response variable...")
    ),
    selectizeInput(
      inputId = ns("stepwisePredictorVariables"),
      label = "Select Predictor Variables:",
      choices = NULL,
      multiple = TRUE,
      options = list(placeholder = "Select predictor variables...")
    ),
    selectInput(
      inputId = ns("stepwiseDirection"),
      label = "Stepwise Direction:",
      choices = c(
        "Forward" = "forward",
        "Backward" = "backward",
        "Both" = "both"
      ),
      selected = "both"
    ),
    numericInput(
      inputId = ns("stepwiseAlphaEntry"),
      label = "Entry Alpha Level:",
      value = 0.05,
      min = 0.001,
      max = 0.5,
      step = 0.001
    ),
    numericInput(
      inputId = ns("stepwiseAlphaRemove"),
      label = "Remove Alpha Level:",
      value = 0.10,
      min = 0.001,
      max = 0.5,
      step = 0.001
    ),
    numericInput(
      inputId = ns("stepwiseConfLevel"),
      label = "Confidence Level:",
      value = 0.95,
      min = 0.5,
      max = 0.99,
      step = 0.01
    ),
    actionButton(
      inputId = ns("goStepwise"),
      label = "Calculate Stepwise Regression",
      class = "btn-primary",
      style = "width: 100%;"
    ),
    br(),
    br(),
    helpText(
      "Stepwise regression automatically selects variables.",
      "Forward: starts with no variables, adds significant ones.",
      "Backward: starts with all variables, removes non-significant ones.",
      "Both: combines forward and backward steps."
    )
  )
}

StepwiseRegressionMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns("stepwiseResults"))
  )
}

StepwiseRegressionUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(StepwiseRegressionSidebarUI(id)),
    mainPanel(StepwiseRegressionMainUI(id))
  )
} 