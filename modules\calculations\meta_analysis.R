# Meta-Analysis calculation and output helpers

meta_analysis_uploadData_func <- function(metaUserData) {
  ext <- tools::file_ext(metaUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(metaUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(metaUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(metaUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(metaUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

meta_analysis_results_func <- function(data, yi, vi, method = "REML") {
  tryCatch({
    if (!requireNamespace("metafor", quietly = TRUE)) {
      stop("Package 'metafor' required.")
    }
    fit <- metafor::rma(yi = data[[yi]], vi = data[[vi]], method = method)
    
    list(
      fit = fit,
      data = data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Meta-Analysis calculation:", e$message))
  })
}

meta_analysis_ht_html <- function(results) {
  fit <- results$fit
  p_val <- fit$pval
  
  tagList(
    h4("Meta-Analysis Results"),
    p(paste("The overall model p-value is", round(p_val, 4), "."))
  )
}

meta_analysis_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  out <- list(
    h4("Model Summary"),
    renderPrint(summary(results$fit))
  )
  if (!is.null(warnings <- warnings())) {
    out <- c(out, h4("Warnings"), renderPrint(warnings))
  }
  tagList(out)
}

meta_analysis_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new(); title("Error: ", results$error); return()
  }
  metafor::forest(results$fit)
  # Funnel plot
  metafor::funnel(results$fit, main = 'Funnel Plot')
}