quantileRegressionSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(
      inputId = ns("qrUserData"),
      label   = strong("Upload your Data (.csv or .xls or .xlsx or .txt)"),
      accept  = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")
    ),
    div(
      id = ns("qrUploadInputs"),
      selectizeInput(
        inputId = ns("qrResponse"),
        label = strong("Response Variable"),
        choices = c(""),
        selected = NULL,
        options = list(placeholder = 'Select a variable', onInitialize = I('function() { this.setValue(""); }'))
      ),
      selectizeInput(
        inputId = ns("qrPredictors"),
        label = strong("Predictor Variables"),
        choices = c(""),
        multiple = TRUE,
        selected = NULL,
        options = list(hideSelected = FALSE, placeholder = 'Select one or more variables', onInitialize = I('function() { this.setValue(""); }'))
      ),
      numericInput(
        inputId = ns("qrQuantile"),
        label = strong("Quantile (0-1)"),
        value = 0.5,
        min = 0.01,
        max = 0.99,
        step = 0.01
      ),
      selectizeInput(
        inputId = ns("qrMethod"),
        label = strong("Method"),
        choices = c("br", "fn", "pfn", "sfn", "conquer"),
        selected = "br",
        options = list(placeholder = 'Select method')
      ),
      checkboxInput(
        inputId = ns("qrIntercept"),
        label = strong("Include Intercept"),
        value = TRUE
      ),
      numericInput(
        inputId = ns("qrConfLevel"),
        label = strong("Confidence Level"),
        value = 0.95,
        min = 0.5,
        max = 0.99,
        step = 0.01
      )
    ),
    br(),
    actionButton(ns("goQuantileRegression"), label = "Calculate", class = "act-btn")
  )
}

quantileRegressionMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('quantileRegressionResults'))
  )
}

quantileRegressionUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(quantileRegressionSidebarUI(id)),
    mainPanel(quantileRegressionMainUI(id))
  )
} 