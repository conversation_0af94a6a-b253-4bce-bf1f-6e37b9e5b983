# Placeholder for Portfolio Optimization Server
portfolioOptimizationServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    # Reactives
    poUploadData <- reactive({
      req(input$poUserData)
      ext <- tools::file_ext(input$poUserData$name)
      if (ext %in% c("csv", "txt")) {
        read.csv(input$poUserData$datapath)
      } else if (ext %in% c("xls", "xlsx")) {
        if (!requireNamespace("readxl", quietly = TRUE)) stop("Install 'readxl' for Excel support.")
        readxl::read_excel(input$poUserData$datapath)
      } else {
        stop("Unsupported file type.")
      }
    })
    poResults <- eventReactive(input$goPO, {
      df <- poUploadData()
      req(input$poReturnCols)
      portfolioOptimizationResults_func(
        data = df,
        return_cols = input$poReturnCols
      )
    })
    # Outputs
    output$poHT <- renderUI({
      results <- poResults()
      if (is.null(results)) return(NULL)
      tagList(
        verbatimTextOutput(ns("poSummary")),
        DT::DTOutput(ns("poWeightsTable"))
      )
    })
    output$poSummary <- renderPrint({
      results <- poResults()
      if (is.null(results)) return(NULL)
      print(results$summary)
    })
    output$poWeightsTable <- DT::renderDT({
      results <- poResults()
      if (is.null(results)) return(NULL)
      DT::datatable(data.frame(Asset = names(results$weights), Weight = results$weights))
    })
    output$poPlot <- renderPlot({
      results <- poResults()
      if (is.null(results) || is.null(results$plot)) return(NULL)
      results$plot
    })
    output$poConclusionOutput <- renderUI({
      results <- poResults()
      if (is.null(results)) return(NULL)
      tags$p("Portfolio optimization complete. See weights, summary, and efficient frontier plot above.")
    })
    output$renderPOData <- renderUI({
      req(poUploadData())
      tagList(
        titlePanel("Data File"),
        br(),
        br(),
        div(DT::DTOutput(ns("poInitialUploadTable")), style = "width: 75%"),
        br(),
        br(),
        uiOutput(ns("poColSelectors"))
      )
    })
    output$poInitialUploadTable <- DT::renderDT({
      req(poUploadData())
      DT::datatable(poUploadData(),
        options = list(pageLength = -1,
                       lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                       columnDefs = list(list(className = 'dt-center', targets = 0:ncol(poUploadData())))))
    })
    output$poColSelectors <- renderUI({
      df <- poUploadData()
      req(df)
      tagList(
        selectInput(ns("poReturnCols"), "Return Columns (Select 2+)", choices = names(df), multiple = TRUE)
      )
    })
  })
} 