CustomPlotUI <- function(id) {
  ns <- NS(id)
  tagList(
    sidebarLayout(
      sidebarPanel(
        fileInput(ns("plotUserData"), "Upload Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
        selectInput(ns("plotType"), "Plot Type", choices = c("Scatterplot", "Boxplot", "Histogram", "Barplot")),
        uiOutput(ns("plotVars")),
        br(),
        actionButton(ns("goPlot"), label = "Create Plot", class = "act-btn")
      ),
      mainPanel(
        uiOutput(ns("plotError")),
        plotOutput(ns("customPlot"))
      )
    )
  )
} 