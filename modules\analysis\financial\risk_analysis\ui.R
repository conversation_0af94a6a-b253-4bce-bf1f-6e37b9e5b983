# Placeholder for Risk Analysis UI
riskAnalysisSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("riskUserData"), "Upload your Data (.csv or .xls or .xlsx or .txt)", accept = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")),
    uiOutput(ns("riskColSelectors")),
    actionButton(ns("goRisk"), label = "Calculate", class = "act-btn")
  )
}

riskAnalysisMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('riskHT')),
    plotOutput(ns('riskPlot'), width = "50%", height = "400px"),
    uiOutput(ns('riskConclusionOutput'))
  )
}

riskAnalysisUI <- function(id) {
  ns <- NS(id)
  tagList(
    riskAnalysisSidebarUI(id),
    riskAnalysisMainUI(id),
    tabsetPanel(
      id = ns("riskTabset"),
      selected = "Analysis",
      tabPanel(
        id    = ns("riskAnalysis"),
        title = "Risk Analysis",
        titlePanel("Risk Analysis"),
        br(),
        uiOutput(ns('riskHT')),
        br(),
        plotOutput(ns('riskPlot'), width = "50%", height = "400px"),
        br(),
        uiOutput(ns('riskConclusionOutput'))
      ),
      tabPanel(
        id    = ns("riskData"),
        title = "Uploaded Data",
        uiOutput(ns("renderRiskData"))
      )
    )
  )
} 