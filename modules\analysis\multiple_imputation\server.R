MultipleImputationServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    miceData <- eventReactive(input$miceUserData, {
      handle_file_upload(input$miceUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(miceData(), {
      data <- miceData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'miceVars', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    miceValidationErrors <- reactive({
      errors <- c()
      data <- miceData()
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      
      # Check for missing data
      missing_count <- sum(is.na(data))
      if (missing_count == 0) {
        errors <- c(errors, "No missing data found. Multiple imputation is not needed.")
      }
      
      # Check number of imputations
      if (!is.null(input$miceM) && (input$miceM < 1 || input$miceM > 50)) {
        errors <- c(errors, "Number of imputations should be between 1 and 50.")
      }
      
      # Check if mice package is available
      if (!requireNamespace("mice", quietly = TRUE)) {
        errors <- c(errors, "Package 'mice' is required for multiple imputation.")
      }
      
      errors
    })
    
    # Multiple imputation analysis reactive
    miceResult <- eventReactive(input$goMICE, {
      data <- miceData()
      req(data)
      
      # Check for missing data
      if (sum(is.na(data)) == 0) {
        stop("No missing data found. Multiple imputation is not needed.")
      }
      
      # Perform multiple imputation
      multiple_imputation(data, m = input$miceM)
    })
    
    # Error handling
    output$miceError <- renderUI({
      errors <- miceValidationErrors()
      if (length(errors) > 0) {
        errorScreenUI(title = "Validation Errors", errors = errors)
      } else {
        tryCatch({ 
          miceResult()
          NULL 
        }, error = function(e) {
          errorScreenUI(title = "Multiple Imputation Error", errors = e$message)
        })
      }
    })
    
    # Analysis Tab Outputs
    output$miceModelSummary <- renderUI({
      req(miceResult())
      res <- miceResult()
      
      tagList(
        h4("Multiple Imputation Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Metric = c("Number of Imputations", "Number of Observations", "Number of Variables", "Missing Data Pattern", "Imputation Method"),
            Value = c(
              input$miceM,
              res$n_observations,
              res$n_variables,
              res$missing_pattern,
              res$imputation_method
            )
          )
        }),
        h4("Imputation Quality Metrics"),
        renderTable({
          data.frame(
            Metric = c("Convergence", "R-hat", "Effective Sample Size", "Monte Carlo Error"),
            Value = c(
              ifelse(!is.null(res$converged), ifelse(res$converged, "Yes", "No"), "N/A"),
              ifelse(!is.null(res$rhat), round(res$rhat, 4), "N/A"),
              ifelse(!is.null(res$effective_sample_size), round(res$effective_sample_size, 2), "N/A"),
              ifelse(!is.null(res$monte_carlo_error), round(res$monte_carlo_error, 4), "N/A")
            )
          )
        })
      )
    })
    
    output$micePlot <- renderPlot({
      req(miceResult())
      res <- miceResult()
      
      # Create comprehensive plot
      par(mfrow = c(2, 2))
      
      # Missing data pattern
      if (requireNamespace("VIM", quietly = TRUE)) {
        VIM::aggr(res$imputed_data, plot = TRUE, main = "Missing Data Pattern")
      } else {
        plot.new()
        title("Missing Data Pattern\n(VIM package required)")
      }
      
      # Imputation diagnostics
      if (!is.null(res$diagnostics)) {
        plot(res$diagnostics, main = "Imputation Diagnostics")
      } else {
        plot.new()
        title("Imputation Diagnostics\n(Not available)")
      }
      
      # Convergence plot
      if (!is.null(res$convergence)) {
        plot(res$convergence, type = "l", main = "Convergence Plot",
             xlab = "Iteration", ylab = "Parameter Value", col = "blue")
      } else {
        plot.new()
        title("Convergence Plot\n(Not available)")
      }
      
      # Density comparison
      if (!is.null(res$density_comparison)) {
        plot(res$density_comparison, main = "Density Comparison",
             xlab = "Value", ylab = "Density")
      } else {
        plot.new()
        title("Density Comparison\n(Not available)")
      }
      
      par(mfrow = c(1, 1))
    })
    
    output$miceDiagnostics <- renderUI({
      req(miceResult())
      res <- miceResult()
      
      tagList(
        h4("Imputation Diagnostics"),
        renderTable({
          data.frame(
            Metric = c("Method", "Convergence", "Number of Iterations", "Computational Time", "Imputation Quality"),
            Value = c(
              res$imputation_method,
              ifelse(!is.null(res$converged), ifelse(res$converged, "Yes", "No"), "N/A"),
              ifelse(!is.null(res$iterations), res$iterations, "N/A"),
              ifelse(!is.null(res$computation_time), paste(round(res$computation_time, 2), "seconds"), "N/A"),
              ifelse(!is.null(res$quality_score), round(res$quality_score, 4), "N/A")
            )
          )
        }),
        h4("Imputed Data Summary"),
        renderTable({
          if (!is.null(res$imputed_summary)) {
            res$imputed_summary
          } else {
            data.frame(
              Variable = "N/A",
              Original_Mean = "N/A",
              Imputed_Mean = "N/A",
              Difference = "N/A",
              stringsAsFactors = FALSE
            )
          }
        })
      )
    })
    
    # Data Summary/Diagnostics Tab Outputs
    output$miceDataSummary <- renderUI({
      req(miceData())
      data <- miceData()
      
      tagList(
        h4("Dataset Summary"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "Complete Cases", "Missing Cases", "Missing Percentage"),
            Value = c(
              nrow(data),
              ncol(data),
              sum(complete.cases(data)),
              sum(!complete.cases(data)),
              paste(round(mean(!complete.cases(data)) * 100, 2), "%")
            )
          )
        }),
        h4("Missing Data by Variable"),
        renderTable({
          missing_summary <- data.frame(
            Variable = names(data),
            Missing_Count = sapply(data, function(x) sum(is.na(x))),
            Missing_Percentage = sapply(data, function(x) round(mean(is.na(x)) * 100, 2)),
            stringsAsFactors = FALSE
          )
          missing_summary[missing_summary$Missing_Count > 0, ]
        })
      )
    })
    
    output$miceAssumptions <- renderUI({
      req(miceResult())
      res <- miceResult()
      
      tagList(
        h4("Multiple Imputation Assumptions Check"),
        renderTable({
          data.frame(
            Assumption = c("Missing at Random (MAR)", "Adequate Sample Size", "Valid Imputation Method", "Convergence"),
            Status = c(
              "Assumed",
              ifelse(res$n_observations >= 10, "Pass", "Fail"),
              "Pass",
              ifelse(!is.null(res$converged) && res$converged, "Pass", "Fail")
            ),
            Description = c(
              "Missing data mechanism is assumed to be MAR",
              "Sufficient observations for stable imputation",
              "Imputation method is appropriate for data type",
              "Imputation algorithm converged successfully"
            )
          )
        }),
        h4("Imputation Guidelines"),
        renderTable({
          data.frame(
            Parameter = c("Number of Imputations", "Convergence Criteria", "Burn-in Period", "Thinning"),
            Recommended_Value = c("5-20", "R-hat < 1.1", "1000-5000", "1-10"),
            Description = c(
              "More imputations for higher missing data percentage",
              "Gelman-Rubin diagnostic for convergence",
              "Initial iterations to reach equilibrium",
              "Save every nth iteration to reduce autocorrelation"
            )
          )
        })
      )
    })
    
    output$miceDiagnosticPlots <- renderPlot({
      req(miceResult())
      res <- miceResult()
      
      par(mfrow = c(2, 2))
      
      # Missing data pattern
      if (requireNamespace("VIM", quietly = TRUE)) {
        VIM::aggr(res$imputed_data, plot = TRUE, main = "Missing Data Pattern")
      } else {
        plot.new()
        title("Missing Data Pattern\n(VIM package required)")
      }
      
      # Imputation diagnostics
      if (!is.null(res$diagnostics)) {
        plot(res$diagnostics, main = "Imputation Diagnostics")
      } else {
        plot.new()
        title("Imputation Diagnostics\n(Not available)")
      }
      
      # Convergence plot
      if (!is.null(res$convergence)) {
        plot(res$convergence, type = "l", main = "Convergence Plot",
             xlab = "Iteration", ylab = "Parameter Value", col = "blue")
      } else {
        plot.new()
        title("Convergence Plot\n(Not available)")
      }
      
      # Density comparison
      if (!is.null(res$density_comparison)) {
        plot(res$density_comparison, main = "Density Comparison",
             xlab = "Value", ylab = "Density")
      } else {
        plot.new()
        title("Density Comparison\n(Not available)")
      }
      
      par(mfrow = c(1, 1))
    })
    
    # Uploaded Data Tab Outputs
    output$miceDataTable <- renderDT({
      req(miceData())
      data <- miceData()
      
      DT::datatable(
        data,
        options = list(
          pageLength = 10,
          scrollX = TRUE,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
        ),
        extensions = 'Buttons',
        filter = 'top',
        rownames = FALSE
      )
    })
    
    output$miceDataInfo <- renderUI({
      req(miceData())
      data <- miceData()
      
      tagList(
        h4("Dataset Information"),
        renderTable({
          data.frame(
            Metric = c("Number of Rows", "Number of Columns", "File Name", "Missing Data Present"),
            Value = c(nrow(data), ncol(data), 
                     ifelse(!is.null(input$miceUserData), input$miceUserData$name, "Unknown"),
                     ifelse(sum(is.na(data)) > 0, "Yes", "No"))
          )
        }),
        h4("Variable Information"),
        renderTable({
          data.frame(
            Variable = names(data),
            Type = sapply(data, class),
            Missing = sapply(data, function(x) sum(is.na(x))),
            Missing_Percent = sapply(data, function(x) round(mean(is.na(x)) * 100, 2)),
            Unique = sapply(data, function(x) length(unique(x)))
          )
        })
      )
    })
    
    # Imputed Data Outputs
    output$miceImputedData <- renderDT({
      req(miceResult())
      res <- miceResult()
      
      if (!is.null(res$imputed_data)) {
        DT::datatable(
          res$imputed_data,
          options = list(
            pageLength = 10,
            scrollX = TRUE,
            dom = 'Bfrtip',
            buttons = c('copy', 'csv', 'excel', 'pdf', 'print')
          ),
          extensions = 'Buttons',
          filter = 'top',
          rownames = FALSE
        )
      } else {
        DT::datatable(
          data.frame(Message = "No imputed data available"),
          options = list(pageLength = 1),
          rownames = FALSE
        )
      }
    })
  })
} 