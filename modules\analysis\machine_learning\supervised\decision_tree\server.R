# Statistical Decision Trees Server
decisionTreesServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns

    # Upload and parse data
    dtUploadData <- reactive({
      req(input$dtUserData)
      ext <- tools::file_ext(input$dtUserData$name)
      if (ext %in% c("csv", "txt")) {
        read.csv(input$dtUserData$datapath)
      } else if (ext %in% c("xls", "xlsx")) {
        if (!requireNamespace("readxl", quietly = TRUE)) stop("Install 'readxl' for Excel support.")
        readxl::read_excel(input$dtUserData$datapath)
      } else {
        stop("Unsupported file type.")
      }
    })

    # Update variable choices when data changes
    observeEvent(dtUploadData(), {
      data <- dtUploadData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectInput(session, "dtTargetCol", choices = names(data))
        updateSelectInput(session, "dtPredictorCols", choices = names(data))
      }
    })

    # Run Decision Tree with error handling
    dtResults <- eventReactive(input$goDecisionTree, {
      df <- dtUploadData()
      req(input$dtTargetCol, input$dtPredictorCols, input$dtTreeType)
      tryCatch({
        decisionTreesResults_func(
          data = df,
          target = input$dtTargetCol,
          predictors = input$dtPredictorCols,
          tree_type = input$dtTreeType
        )
      }, error = function(e) {
        structure(list(error = TRUE, message = e$message), class = "dt_error")
      })
    })

    # Outputs
    output$dtError <- renderText({
      results <- dtResults()
      if (inherits(results, "dt_error")) {
        paste("Error:", results$message)
      } else {
        NULL
      }
    })
    output$dtPlot <- renderPlot({
      results <- dtResults()
      if (inherits(results, "dt_error") || is.null(results) || is.null(results$plot)) return(NULL)
      results$plot()
    })
    output$dtSummary <- renderPrint({
      results <- dtResults()
      if (inherits(results, "dt_error") || is.null(results)) return(NULL)
      print(results$summary)
    })
    output$dtConclusion <- renderText({
      results <- dtResults()
      if (inherits(results, "dt_error") || is.null(results)) return("")
      "Decision tree analysis complete. See plot and summary above."
    })
  })
} 