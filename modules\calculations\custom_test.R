# Custom Test Calculation Helper
calc_custom_test <- function(data, formula, test_type, ...) {
  if (test_type == "t-test") {
    result <- t.test(formula, data = data, ...)
  } else if (test_type == "ANOVA") {
    result <- summary(aov(formula, data = data))
  } else if (test_type == "Linear Regression") {
    result <- summary(lm(formula, data = data))
  } else if (test_type == "Logistic Regression") {
    result <- summary(glm(formula, data = data, family = binomial))
  } else {
    result <- "Custom test type not implemented."
  }
  result
} 