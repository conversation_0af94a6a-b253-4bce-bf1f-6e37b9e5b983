# Time Series Analysis Calculation and Output Helpers

# Required packages
required_packages <- c("forecast", "tseries", "zoo", "xts", "lubridate", "ggplot2", "knitr", "kableExtra")

# Helper function to check and install required packages
.check_packages <- function() {
  for (pkg in required_packages) {
    if (!requireNamespace(pkg, quietly = TRUE)) {
      message("Installing required package: ", pkg)
      install.packages(pkg)
    }
    suppressPackageStartupMessages(library(pkg, character.only = TRUE))
  }
}

# File upload handler
time_series_uploadData_func <- function(uploaded_file, date_col = NULL, value_col = NULL) {
  tryCatch({
    if (is.null(uploaded_file)) {
      return(list(error = "No file uploaded"))
    }
    
    # Read file based on extension
    ext <- tolower(tools::file_ext(uploaded_file$name))
    data <- switch(
      ext,
      csv = readr::read_csv(uploaded_file$datapath, show_col_types = FALSE),
      xls = readxl::read_xls(uploaded_file$datapath),
      xlsx = readxl::read_xlsx(uploaded_file$datapath),
      tsv = readr::read_tsv(uploaded_file$datapath, show_col_types = FALSE),
      txt = readr::read_tsv(uploaded_file$datapath, show_col_types = FALSE),
      {
        return(list(error = paste0("Unsupported file format: ", ext)))
      }
    )
    
    # Convert to data.frame if it's a tibble
    if (inherits(data, "tbl_df")) {
      data <- as.data.frame(data)
    }
    
    # If date and value columns are specified, process them
    if (!is.null(date_col) && !is.null(value_col)) {
      if (!all(c(date_col, value_col) %in% names(data))) {
        return(list(error = "Specified date or value column not found in the data"))
      }
      
      # Convert date column to Date type if it's not already
      if (!inherits(data[[date_col]], c("Date", "POSIXt"))) {
        data[[date_col]] <- as.Date(data[[date_col]])
      }
      
      # Ensure value column is numeric
      if (!is.numeric(data[[value_col]])) {
        data[[value_col]] <- as.numeric(data[[value_col]])
        if (any(is.na(data[[value_col]]))) {
          warning("Non-numeric values found in the value column and converted to NA")
        }
      }
      
      # Order by date
      data <- data[order(data[[date_col]]), ]
      
      # Create time series object
      ts_data <- stats::ts(data[[value_col]], 
                          start = 1, 
                          frequency = 1)
      
      # If we can determine frequency from dates, use it
      if (nrow(data) > 1) {
        time_diff <- as.numeric(diff(data[[date_col]]))
        if (all(time_diff == time_diff[1])) {
          # Regular time series
          if (time_diff[1] == 1) {
            freq <- 1  # Daily
          } else if (time_diff[1] %in% 28:31) {
            freq <- 12  # Monthly
          } else if (time_diff[1] %in% 365:366 || 
                    (time_diff[1] %in% 90:92 && nrow(data) > 12)) {
            freq <- 4  # Quarterly
          } else if (time_diff[1] %in% 365:366) {
            freq <- 1  # Yearly
          } else {
            freq <- 1  # Default to frequency 1 if can't determine
          }
          ts_data <- stats::ts(data[[value_col]], 
                             start = 1, 
                             frequency = freq)
        }
      }
      
      return(list(
        data = data,
        ts_data = ts_data,
        date_col = date_col,
        value_col = value_col,
        frequency = frequency(ts_data)
      ))
    }
    
    return(list(data = data))
    
  }, error = function(e) {
    return(list(error = paste("Error reading uploaded file:", e$message)))
  })
}

# Main time series analysis function
time_series_results_func <- function(ts_data, analysis_type = "decomposition", 
                                   period = NULL, model_type = NULL, ...) {
  tryCatch({
    # Check if required packages are installed
    .check_packages()
    
    # If ts_data is not a ts object, try to convert it
    if (!stats::is.ts(ts_data) && is.numeric(ts_data)) {
      ts_data <- stats::ts(ts_data, frequency = ifelse(is.null(period), 1, period))
    } else if (!stats::is.ts(ts_data)) {
      stop("Input must be a time series object or numeric vector")
    }
    
    # Handle missing values
    if (any(is.na(ts_data))) {
      ts_data <- zoo::na.approx(ts_data)  # Linear interpolation for missing values
    }
    
    result <- list(
      ts_data = ts_data,
      analysis_type = analysis_type,
      frequency = stats::frequency(ts_data),
      summary = summary(ts_data),
      start = stats::start(ts_data),
      end = stats::end(ts_data),
      n = length(ts_data)
    )
    
    # Add analysis based on type
    if (analysis_type == "decomposition") {
      if (stats::frequency(ts_data) > 1) {
        result$decomposition <- stats::decompose(ts_data, type = "additive")
        result$stl_decomposition <- stats::stl(ts_data, s.window = "periodic")
      } else {
        result$decomposition <- list(observed = ts_data, trend = ts_data, random = ts(rep(0, length(ts_data))))
        warning("Decomposition requires frequency > 1, returning original series")
      }
      
    } else if (analysis_type == "stationarity") {
      # Augmented Dickey-Fuller test
      if (requireNamespace("tseries", quietly = TRUE)) {
        result$adf_test <- tseries::adf.test(ts_data, alternative = "stationary")
      }
      
      # KPSS test
      if (requireNamespace("tseries", quietly = TRUE)) {
        result$kpss_test <- tseries::kpss.test(ts_data, null = "Level")
      }
      
      # ACF and PACF
      result$acf <- stats::acf(ts_data, plot = FALSE)
      result$pacf <- stats::pacf(ts_data, plot = FALSE)
      
    } else if (analysis_type == "arima") {
      # Auto ARIMA model
      if (requireNamespace("forecast", quietly = TRUE)) {
        model_type <- if (is.null(model_type)) "auto" else tolower(model_type)
        
        if (model_type == "auto") {
          result$arima_model <- forecast::auto.arima(
            ts_data, 
            seasonal = TRUE,
            stepwise = TRUE,
            approximation = length(ts_data) > 100,
            allowdrift = TRUE,
            allowmean = TRUE
          )
        } else if (model_type == "arima") {
          # Fit ARIMA with specified orders if provided, otherwise auto-select
          if (all(c("p", "d", "q") %in% names(list(...)))) {
            result$arima_model <- stats::arima(
              ts_data, 
              order = c(list(...)$p, list(...)$d, list(...)$q),
              seasonal = if ("P" %in% names(list(...)) && "D" %in% names(list(...)) && "Q" %in% names(list(...))) {
                list(order = c(list(...)$P, list(...)$D, list(...)$Q), period = stats::frequency(ts_data))
              } else if (stats::frequency(ts_data) > 1) {
                list(order = c(0, 1, 1), period = stats::frequency(ts_data))
              } else {
                NULL
              }
            )
          } else {
            result$arima_model <- forecast::auto.arima(
              ts_data, 
              seasonal = stats::frequency(ts_data) > 1
            )
          }
        }
        
        if (!is.null(result$arima_model)) {
          result$arima_accuracy <- forecast::accuracy(result$arima_model)
          result$arima_diagnostics <- list(
            residuals = stats::residuals(result$arima_model),
            ljung_box = stats::Box.test(stats::residuals(result$arima_model), type = "Ljung-Box")
          )
        }
      }
      
    } else if (analysis_type == "forecast") {
      # Generate forecasts
      if (requireNamespace("forecast", quietly = TRUE)) {
        h <- ifelse(is.null(list(...)$h), 
                   ifelse(stats::frequency(ts_data) > 1, 
                         stats::frequency(ts_data) * 2, 10), 
                   list(...)$h)
        
        # Use existing model if provided, otherwise fit auto.arima
        if (!is.null(list(...)$model)) {
          model <- list(...)$model
        } else {
          model <- forecast::auto.arima(ts_data)
        }
        
        result$forecast <- forecast::forecast(model, h = h)
        result$model <- model
      }
      
    } else if (analysis_type == "seasonality") {
      # Check for seasonality
      if (stats::frequency(ts_data) > 1) {
        result$seasonal_strength <- .calculate_seasonal_strength(ts_data)
        result$seasonal_plot <- .create_seasonal_plot(ts_data)
      } else {
        result$seasonal_strength <- 0
        warning("Cannot calculate seasonality for non-seasonal time series (frequency = 1)")
      }
    }
    
    # Add basic statistics
    result$statistics <- list(
      mean = mean(ts_data, na.rm = TRUE),
      sd = sd(ts_data, na.rm = TRUE),
      min = min(ts_data, na.rm = TRUE),
      max = max(ts_data, na.rm = TRUE),
      median = median(ts_data, na.rm = TRUE),
      iqr = IQR(ts_data, na.rm = TRUE),
      na_count = sum(is.na(ts_data))
    )
    
    result$is_stationary <- .is_stationary(ts_data)
    
    class(result) <- c("time_series_analysis", class(result))
    return(result)
    
  }, error = function(e) {
    return(list(error = paste("Error in time series analysis:", e$message)))
  })
}

# Helper function to check stationarity
.is_stationary <- function(x) {
  if (!requireNamespace("tseries", quietly = TRUE)) {
    warning("tseries package required for stationarity tests")
    return(NA)
  }
  
  # ADF test
  adf <- try(tseries::adf.test(na.omit(as.numeric(x))), silent = TRUE)
  if (inherits(adf, "try-error")) {
    adf_p <- NA
  } else {
    adf_p <- adf$p.value
  }
  
  # KPSS test
  kpss <- try(tseries::kpss.test(na.omit(as.numeric(x)), null = "Level"), silent = TRUE)
  if (inherits(kpss, "try-error")) {
    kpss_p <- NA
  } else {
    kpss_p <- kpss$p.value
  }
  
  # Decision based on tests
  if (!is.na(adf_p) && !is.na(kpss_p)) {
    return(adf_p < 0.05 && kpss_p > 0.05)  # ADF: reject non-stationary; KPSS: don't reject stationary
  } else if (!is.na(adf_p)) {
    return(adf_p < 0.05)
  } else if (!is.na(kpss_p)) {
    return(kpss_p > 0.05)
  } else {
    return(NA)
  }
}

# Calculate seasonal strength
.calculate_seasonal_strength <- function(x) {
  if (!stats::is.ts(x) || stats::frequency(x) <= 1) {
    return(0)
  }
  
  # Decompose the time series
  x_decomp <- stats::decompose(stats::ts(na.omit(as.numeric(x)), frequency = stats::frequency(x)))
  
  # Calculate seasonal strength
  var_remainder <- stats::var(x_decomp$random, na.rm = TRUE)
  var_seasonal <- stats::var(x_decomp$seasonal, na.rm = TRUE)
  var_total <- stats::var(x_decomp$trend + x_decomp$seasonal + x_decomp$random, na.rm = TRUE)
  
  # Prevent division by zero
  if (var_total == 0) return(0)
  
  # Calculate strength (0 to 1)
  strength <- max(0, min(1, var_seasonal / (var_seasonal + var_remainder)))
  return(strength)
}

# Create seasonal plot
.create_seasonal_plot <- function(x) {
  if (!requireNamespace("ggplot2", quietly = TRUE) || !stats::is.ts(x) || stats::frequency(x) <= 1) {
    return(NULL)
  }
  
  # Convert to data frame for plotting
  df <- data.frame(
    time = time(x),
    value = as.numeric(x),
    season = cycle(x)
  )
  
  # Create seasonal plot
  p <- ggplot2::ggplot(df, ggplot2::aes(x = .data$season, y = .data$value, group = .data$season)) +
    ggplot2::geom_boxplot() +
    ggplot2::labs(
      title = "Seasonal Plot",
      x = "Seasonal Period",
      y = "Value"
    ) +
    ggplot2::theme_minimal()
  
  return(p)
}

# HTML output functions
time_series_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(htmltools::div(
      class = "alert alert-danger",
      htmltools::h4("Error"),
      htmltools::p(results$error)
    ))
  }
  
  htmltools::div(
    class = "time-series-header",
    htmltools::h4("Time Series Analysis"),
    htmltools::p(paste("Analysis type:", results$analysis_type)),
    htmltools::p(paste("Frequency:", results$frequency, "(observations per unit time)")),
    htmltools::p(paste("Observations:", results$n, "from", 
                      paste(results$start, collapse = " "), "to", 
                      paste(results$end, collapse = " ")))
  )
}

time_series_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(htmltools::div(
      class = "alert alert-danger",
      htmltools::h4("Error"),
      htmltools::p(results$error)
    ))
  }
  
  # Create summary statistics table
  stats_df <- data.frame(
    Statistic = c(
      "Mean", "Standard Deviation", "Minimum", "Maximum", 
      "Median", "IQR", "Missing Values"
    ),
    Value = c(
      sprintf("%.4f", results$statistics$mean),
      sprintf("%.4f", results$statistics$sd),
      sprintf("%.4f", results$statistics$min),
      sprintf("%.4f", results$statistics$max),
      sprintf("%.4f", results$statistics$median),
      sprintf("%.4f", results$statistics$iqr),
      results$statistics$na_count
    )
  )
  
  # Convert to kable for nice formatting
  stats_table <- knitr::kable(stats_df, format = "html", align = c("l", "r")) %>%
    kableExtra::kable_styling(bootstrap_options = c("striped", "hover"))
  
  # Add stationarity information if available
  stationarity_text <- if (!is.null(results$is_stationary)) {
    if (is.na(results$is_stationary)) {
      "Stationarity: Could not determine (check if tseries package is installed)"
    } else if (results$is_stationary) {
      "Stationarity: Time series appears to be stationary"
    } else {
      "Stationarity: Time series appears to be non-stationary"
    }
  } else {
    ""
  }
  
  # Combine all elements
  htmltools::tagList(
    htmltools::h4("Summary Statistics"),
    htmltools::HTML(stats_table),
    htmltools::p(stationarity_text, style = "margin-top: 15px;")
  )
}

time_series_plot <- function(results) {
  if (!is.null(results$error)) {
    plot.new()
    title(main = "Error", sub = results$error)
    return()
  }
  
  # Set up plot layout
  old_par <- par(no.readonly = TRUE)
  on.exit(par(old_par))
  
  if (results$analysis_type == "decomposition") {
    # Decomposition plot
    if (!is.null(results$decomposition)) {
      plot(results$decomposition, col = "blue", 
           main = "Time Series Decomposition")
    }
    
  } else if (results$analysis_type == "stationarity") {
    # ACF and PACF plots
    par(mfrow = c(2, 1))
    acf(results$ts_data, main = "Autocorrelation Function (ACF)")
    pacf(results$ts_data, main = "Partial Autocorrelation Function (PACF)")
    
  } else if (results$analysis_type == "arima" && !is.null(results$arima_model)) {
    # ARIMA model diagnostics
    if (requireNamespace("forecast", quietly = TRUE)) {
      forecast::tsdisplay(stats::residuals(results$arima_model), 
                         main = "ARIMA Model Residuals")
    } else {
      plot(stats::residuals(results$arima_model), 
           type = "l", 
           main = "ARIMA Model Residuals",
           ylab = "Residuals")
      abline(h = 0, col = "red")
    }
    
  } else if (results$analysis_type == "forecast" && !is.null(results$forecast)) {
    # Forecast plot
    if (requireNamespace("forecast", quietly = TRUE)) {
      plot(results$forecast, 
           main = paste("Forecast", 
                       ifelse(!is.null(results$model$method), 
                              paste("(", results$model$method, ")"), "")),
           xlab = "Time",
           ylab = "Value")
    }
    
  } else if (results$analysis_type == "seasonality" && !is.null(results$seasonal_plot)) {
    # Seasonal plot
    if (requireNamespace("ggplot2", quietly = TRUE)) {
      print(results$seasonal_plot)
    }
    
  } else {
    # Default time series plot
    plot(results$ts_data, 
         main = "Time Series Plot",
         xlab = "Time",
         ylab = "Value",
         col = "blue",
         lwd = 2)
    grid()
    
    # Add trend line if available
    if (!is.null(results$decomposition$trend)) {
      lines(results$decomposition$trend, col = "red", lwd = 2)
      legend("topleft", 
             legend = c("Original", "Trend"),
             col = c("blue", "red"), 
             lty = 1, 
             lwd = 2,
             bty = "n")
    }
  }
}

# Print method for time_series_analysis objects
print.time_series_analysis <- function(x, ...) {
  cat("Time Series Analysis\n")
  cat("-------------------\n")
  cat("Type:", x$analysis_type, "\n")
  cat("Observations:", x$n, "\n")
  cat("Frequency:", x$frequency, "\n")
  cat("Start:", x$start[1], "(", x$start[2], ")\n", sep = "")
  cat("End:", x$end[1], "(", x$end[2], ")\n", sep = "")
  
  if (!is.null(x$statistics)) {
    cat("\nSummary Statistics:\n")
    print(x$statistics)
  }
  
  if (!is.null(x$is_stationary)) {
    cat("\nStationarity:", 
        if (is.na(x$is_stationary)) "Could not determine" 
        else if (x$is_stationary) "Stationary" 
        else "Non-stationary", 
        "\n")
  }
  
  if (!is.null(x$arima_model)) {
    cat("\nARIMA Model:")
    print(x$arima_model)
  }
  
  if (!is.null(x$forecast)) {
    cat("\nForecast:")
    print(x$forecast)
  }
  
  invisible(x)
}