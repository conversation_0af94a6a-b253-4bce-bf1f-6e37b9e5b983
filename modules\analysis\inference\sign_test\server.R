# Sign Test Server
# Non-parametric alternative to one-sample t-test

source("modules/calculations/sign_test.R")

SignTestServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # File upload reactive
    signTestUploadData <- eventReactive(input$signTestUserData, {
      handle_file_upload(input$signTestUserData)
    })
    
    # Update variable choices when data is uploaded
    observeEvent(signTestUploadData(), {
      data <- signTestUploadData()
      if (!is.null(data) && is.data.frame(data)) {
        updateSelectizeInput(session, 'signTestVariable', choices = names(data), server = TRUE)
      }
    })
    
    # Validation errors reactive
    signTestValidationErrors <- reactive({
      errors <- c()
      
      if (input$signTestDataMethod == "Manual Entry") {
        # Manual entry validation
        if (is.null(input$signTestData) || input$signTestData == "") {
          errors <- c(errors, "Data values are required for manual entry.")
        } else {
          data_vals <- createNumLst(input$signTestData)
          if (length(data_vals) < 2) {
            errors <- c(errors, "At least 2 observations are required.")
          }
          if (length(unique(data_vals)) == 1) {
            errors <- c(errors, "Data must have some variation (not all the same value).")
          }
        }
      } else {
        # File upload validation
        data <- signTestUploadData()
        if (is.null(data) || !is.data.frame(data)) {
          errors <- c(errors, "No data uploaded or file could not be read.")
          return(errors)
        }
        if (is.null(input$signTestVariable) || input$signTestVariable == "") {
          errors <- c(errors, "Please select a variable.")
        } else {
          var_data <- data[[input$signTestVariable]]
          if (!is.numeric(var_data)) {
            errors <- c(errors, "Selected variable must be numeric.")
          }
          if (length(na.omit(var_data)) < 2) {
            errors <- c(errors, "At least 2 non-missing observations are required.")
          }
          if (length(unique(na.omit(var_data))) == 1) {
            errors <- c(errors, "Variable must have some variation (not all the same value).")
          }
        }
      }
      
      # Validate hypothesized median
      if (is.null(input$signTestMedian) || is.na(input$signTestMedian)) {
        errors <- c(errors, "Hypothesized median is required.")
      }
      
      errors
    })
    
    # Sign test analysis reactive
    signTestResult <- reactive({
      req(input$signTestDataMethod)
      
      if (input$signTestDataMethod == "Manual Entry") {
        data_vals <- createNumLst(input$signTestData)
      } else {
        data <- signTestUploadData()
        req(data, input$signTestVariable)
        data_vals <- na.omit(data[[input$signTestVariable]])
      }
      
      hypothesized_median <- input$signTestMedian
      alternative <- input$signTestAlternative
      conf_level <- input$signTestConfLevel
      
      # Perform sign test
      result <- perform_sign_test(data_vals, hypothesized_median, alternative, conf_level)
      
      result
    })
    
    # Show results or error screen when Calculate is pressed
    observeEvent(input$goSignTest, {
      output$signTestResults <- renderUI({
        errors <- signTestValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Sign Test", errors = errors)
        } else {
          tagList(
            tabsetPanel(
              id = ns("signTestTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("signTestAnalysis"),
                title = "Analysis",
                titlePanel("Sign Test Results"),
                br(),
                uiOutput(ns('signTestSummary')),
                br(),
                h4("Test Details"),
                uiOutput(ns('signTestDetails')),
                br(),
                h4("Assumptions and Interpretation"),
                uiOutput(ns('signTestAssumptions'))
              ),
              tabPanel(
                id = ns("signTestDataSummary"),
                title = "Data Summary",
                h4("Descriptive Statistics"),
                tableOutput(ns('signTestDescriptive')),
                br(),
                h4("Data Distribution"),
                plotOutput(ns('signTestHistogram'), height = "400px"),
                br(),
                h4("Signs Analysis"),
                tableOutput(ns('signTestSigns'))
              ),
              tabPanel(
                id = ns("signTestUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('signTestViewUpload'))
              )
            )
          )
        }
      })
    })
    
    # Analysis Tab Outputs
    output$signTestSummary <- renderUI({
      req(signTestResult())
      result <- signTestResult()
      
      tagList(
        h4("Sign Test Summary"),
        withMathJax(),
        renderTable({
          data.frame(
            Statistic = c("Sample Size", "Hypothesized Median", "Alternative Hypothesis", 
                         "Test Statistic (S)", "P-value", "Significance Level", "Decision"),
            Value = c(
              result$n,
              result$hypothesized_median,
              result$alternative,
              result$test_statistic,
              ifelse(result$p_value < 0.0001, "< 0.0001", round(result$p_value, 4)),
              paste0((1 - result$conf_level) * 100, "%"),
              ifelse(result$p_value < (1 - result$conf_level), "Reject H₀", "Fail to reject H₀")
            )
          )
        }),
        br(),
        p(strong("Conclusion:"), 
          ifelse(result$p_value < (1 - result$conf_level),
                 "There is sufficient evidence to reject the null hypothesis that the median equals the hypothesized value.",
                 "There is insufficient evidence to reject the null hypothesis that the median equals the hypothesized value."))
      )
    })
    
    output$signTestDetails <- renderUI({
      req(signTestResult())
      result <- signTestResult()
      
      tagList(
        h5("Hypotheses:"),
        p("H₀: Median = ", result$hypothesized_median),
        p("Hₐ: Median ", 
          ifelse(result$alternative == "two.sided", "≠ ", 
                 ifelse(result$alternative == "greater", "> ", "< ")), 
          result$hypothesized_median),
        br(),
        h5("Test Statistic:"),
        p("S = number of positive differences = ", result$test_statistic),
        p("Under H₀, S ~ Binomial(n = ", result$n, ", p = 0.5)"),
        br(),
        h5("P-value:"),
        p("P-value = ", ifelse(result$p_value < 0.0001, "< 0.0001", round(result$p_value, 4))),
        br(),
        h5("Confidence Interval:"),
        p("(", round(result$confidence_interval[1], 4), ", ", round(result$confidence_interval[2], 4), ")")
      )
    })
    
    output$signTestAssumptions <- renderUI({
      req(signTestResult())
      result <- signTestResult()
      
      tagList(
        h5("Key Assumptions:"),
        p("1. Data are independent and identically distributed"),
        p("2. Data are continuous (or at least ordinal)"),
        p("3. The distribution is symmetric around the median (for two-sided tests)"),
        br(),
        h5("Advantages:"),
        p("• Robust to outliers"),
        p("• No assumption of normality"),
        p("• Works with ordinal data"),
        br(),
        h5("Disadvantages:"),
        p("• Less powerful than t-test when data are normal"),
        p("• Ignores magnitude of differences"),
        p("• Requires symmetric distribution for two-sided tests"),
        br(),
        h5("When to Use:"),
        p("• Small sample sizes"),
        p("• Non-normal data"),
        p("• Outliers present"),
        p("• Ordinal data")
      )
    })
    
    # Data Summary Tab Outputs
    output$signTestDescriptive <- renderTable({
      req(signTestResult())
      result <- signTestResult()
      
      data.frame(
        Statistic = c("Sample Size", "Mean", "Median", "Standard Deviation", 
                     "Minimum", "Maximum", "Range", "IQR"),
        Value = c(
          result$n,
          round(result$descriptive$mean, 4),
          round(result$descriptive$median, 4),
          round(result$descriptive$sd, 4),
          round(result$descriptive$min, 4),
          round(result$descriptive$max, 4),
          round(result$descriptive$range, 4),
          round(result$descriptive$iqr, 4)
        )
      )
    }, digits = 4)
    
    output$signTestHistogram <- renderPlot({
      req(signTestResult())
      result <- signTestResult()
      
      hist(result$data, main = "Histogram of Data", xlab = "Values", 
           col = "#4F81BD", border = "white", breaks = "Sturges")
      abline(v = result$hypothesized_median, col = "red", lwd = 2, lty = 2)
      legend("topright", legend = paste("Hypothesized Median =", result$hypothesized_median), 
             col = "red", lwd = 2, lty = 2)
    })
    
    output$signTestSigns <- renderTable({
      req(signTestResult())
      result <- signTestResult()
      
      data.frame(
        Category = c("Positive Differences", "Negative Differences", "Zero Differences", "Total"),
        Count = c(
          result$positive_count,
          result$negative_count,
          result$zero_count,
          result$n
        ),
        Percentage = c(
          round(result$positive_count / result$n * 100, 2),
          round(result$negative_count / result$n * 100, 2),
          round(result$zero_count / result$n * 100, 2),
          100
        )
      )
    }, digits = 2)
    
    # Uploaded Data Tab Output
    output$signTestViewUpload <- DT::renderDT({
      req(signTestUploadData())
      DT::datatable(signTestUploadData(), 
                   options = list(pageLength = 25, autoWidth = TRUE, scrollX = TRUE), 
                   rownames = FALSE)
    })
  })
} 