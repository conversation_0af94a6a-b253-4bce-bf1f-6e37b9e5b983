# Jarque-Bera Test calculation and output helpers

jarque_bera_uploadData_func <- function(jbUserData) {
  ext <- tools::file_ext(jbUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(jbUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(jbUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(jbUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(jbUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

jarque_bera_results_func <- function(data, var) {
  tryCatch({
    if (!requireNamespace("tseries", quietly = TRUE)) {
      stop("Package 'tseries' needed for Jarque-Bera test.")
    }
    
    x <- data[[var]]
    x <- x[!is.na(x)]
    
    if (length(x) < 3) {
      stop("At least 3 observations are required.")
    }
    
    test_result <- tseries::jarque.bera.test(x)
    
    list(
      test = test_result,
      data = x,
      var = var,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Jarque-Bera test calculation:", e$message))
  })
}

jarque_bera_ht_html <- function(results, sigLvl) {
  test <- results$test
  p_value <- test$p.value
  
  conclusion <- if (p_value < sigLvl) "does not follow a normal distribution" else "follows a normal distribution"
  
  withMathJax(tagList(
    h4("Jarque-Bera Test for Normality"),
    p(sprintf("The data likely %s.", conclusion)),
    p(sprintf("Test Statistic (JB): %.4f", test$statistic)),
    p(sprintf("P-value: %.4f", p_value))
  ))
}

jarque_bera_summary_html <- function(results) {
  desc_stats <- data.frame(
    Variable = results$var,
    N = length(results$data),
    Mean = mean(results$data),
    SD = sd(results$data),
    Skewness = moments::skewness(results$data),
    Kurtosis = moments::kurtosis(results$data)
  )
  
  tagList(
    h4("Test Details"),
    renderPrint(results$test),
    h4("Descriptive Statistics"),
    renderTable(desc_stats, digits = 4)
  )
}

jarque_bera_plot <- function(results) {
  plot_data <- data.frame(
    Value = results$data
  )
  
  ggplot(plot_data, aes(x = Value)) +
    geom_histogram(aes(y = ..density..), bins = 15, alpha = 0.7, fill = "lightblue", color = "black") +
    stat_function(fun = dnorm, args = list(mean = mean(results$data), sd = sd(results$data)), 
                   color = "red", size = 1) +
    labs(title = "Histogram with Normal Curve Overlay",
         x = "Value",
         y = "Density") +
    theme_minimal()
}