# Risk Analysis calculation and output helpers

risk_analysis_uploadData_func <- function(raUserData, variable) {
  tryCatch(
    {
      if (is.null(raUserData) || is.null(variable)) {
        return(NULL)
      }
      
      # Read data based on file type
      if (grepl("\\.csv$", raUserData$name, ignore.case = TRUE)) {
        df <- read.csv(raUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.tsv$", raUserData$name, ignore.case = TRUE)) {
        df <- read.tsv(raUserData$datapath, stringsAsFactors = FALSE)
      } else if (grepl("\\.xlsx$", raUserData$name, ignore.case = TRUE)) {
        df <- readxl::read_excel(raUserData$datapath)
      } else {
        stop("Unsupported file type. Please upload a CSV, TSV, or XLSX file.")
      }
      
      if (!variable %in% names(df)) {
        stop(paste("Variable '", variable, "' not found in the uploaded file.", sep = ""))
      }
      
      # Return the selected variable as a numeric vector
      return(as.numeric(df[[variable]]))
    },
    error = function(e) {
      # Return a list with an error message
      return(list(error = paste("Error reading data:", e$message)))
    }
  )
}

risk_analysis_results_func <- function(data, conf_level = 0.95) {
  tryCatch({
    if (is.null(data) || length(data) == 0) {
      stop("No data provided for analysis.")
    }
    
    if (!requireNamespace("actuar", quietly = TRUE)) {
      stop("Package 'actuar' is required for risk analysis.")
    }
    
    VaR <- actuar::VaR(data, p = conf_level)
    CVaR <- actuar::CTE(data, p = conf_level)
    
    list(
      VaR = VaR,
      CVaR = CVaR,
      summary = summary(data),
      data = data,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Risk Analysis:", e$message))
  })
}

risk_analysis_ht_html <- function(results) {
  if (!is.null(results$error)) {
    return(tagList(h4("Error"), p(results$error)))
  }
  tagList(
    h4("Risk Analysis"),
    p(paste("Value at Risk (VaR):", round(results$VaR, 4))),
    p(paste("Conditional Value at Risk (CVaR):", round(results$CVaR, 4)))
  )
}

risk_analysis_summary_html <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  renderPrint(results$summary)
}

risk_analysis_plot <- function(results) {
  if (!is.null(results$error)) {
    return(NULL)
  }
  
  if (!requireNamespace("ggplot2", quietly = TRUE)) {
    stop("Package 'ggplot2' is required for plotting.")
  }
  
  plot_data <- data.frame(value = results$data)
  
  ggplot(plot_data, aes(x = value)) +
    geom_histogram(fill = "orange", color = "black", bins = 30) +
    geom_vline(xintercept = results$VaR, color = "red", linetype = "dashed") +
    geom_vline(xintercept = results$CVaR, color = "blue", linetype = "dashed") +
    labs(title = "Risk Analysis: VaR and CVaR", x = "Value", y = "Count") +
    theme_minimal()
}