# McNemar's Test UI
McNemarTestSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("mcnemarUserData"), "Upload your Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
    selectizeInput(ns("mcnemarBefore"), "Before (Binary)", choices = NULL),
    selectizeInput(ns("mcnemarAfter"), "After (Binary)", choices = NULL),
    radioButtons(ns("mcnemarSigLvl"), "Significance Level (alpha)", choices = c("10%", "5%", "1%"), selected = "5%", inline = TRUE),
    br(),
    actionButton(ns("goMcNemar"), label = "Calculate", class = "act-btn")
  )
}

McNemarTestMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('mcnemarResults'))
  )
}

McNemarTestUI <- function(id) {
  ns <- NS(id)
  tagList(
    McNemarTestSidebarUI(id),
    McNemarTestMainUI(id)
  )
} 