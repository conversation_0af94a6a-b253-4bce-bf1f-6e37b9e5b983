# Placeholder for Automated Reporting UI
automatedReportsSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    fileInput(ns("reportUserData"), "Upload your Data (.csv or .xls or .xlsx or .txt)", accept = c("text/csv", "text/comma-separated-values", "text/plain", ".csv", ".xls", ".xlsx")),
    textInput(ns("reportTitle"), "Report Title", value = "CougarStats Report"),
    actionButton(ns("goReport"), label = "Generate Report", class = "act-btn")
  )
}

automatedReportsMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('reportContent')),
    uiOutput(ns('reportConclusion'))
  )
}

automatedReportsUI <- function(id) {
  ns <- NS(id)
  tagList(
    automatedReportsSidebarUI(id),
    automatedReportsMainUI(id),
    tabsetPanel(
      id = ns("reportTabset"),
      selected = "Report",
      tabPanel(
        id    = ns("reportPanel"),
        title = "Report",
        titlePanel("Automated Report"),
        br(),
        uiOutput(ns('reportContent')),
        br(),
        uiOutput(ns('reportConclusion'))
      )
    )
  )
} 