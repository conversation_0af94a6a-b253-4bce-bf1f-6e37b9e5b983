# Nonlinear Regression calculation and output helpers

nonlinear_regression_uploadData_func <- function(nlrUserData) {
  ext <- tools::file_ext(nlrUserData$name)
  ext <- tolower(ext)
  if (ext == "csv") {
    readr::read_csv(nlrUserData$datapath, show_col_types = FALSE)
  } else if (ext == "xls") {
    readxl::read_xls(nlrUserData$datapath)
  } else if (ext == "xlsx") {
    readxl::read_xlsx(nlrUserData$datapath)
  } else if (ext == "txt") {
    readr::read_tsv(nlrUserData$datapath, show_col_types = FALSE)
  } else {
    validate("Improper file format.")
  }
}

nonlinear_regression_results_func <- function(data, response, predictors, formula_str, start_values) {
  tryCatch({
    
    # Parse start values
    start_list <- list()
    if (!is.null(start_values) && start_values != "") {
      start_pairs <- strsplit(start_values, ",")[[1]]
      for (pair in start_pairs) {
        pair <- trimws(pair)
        if (grepl("=", pair)) {
          parts <- strsplit(pair, "=")[[1]]
          param_name <- trimws(parts[1])
          param_value <- as.numeric(trimws(parts[2]))
          start_list[[param_name]] <- param_value
        }
      }
    }
    
    formula_obj <- as.formula(formula_str)
    
    fit <- nls(formula_obj, data = data, start = start_list)
    
    list(
      fit = fit,
      data = data,
      response = response,
      predictors = predictors,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Nonlinear Regression calculation:", e$message))
  })
}

nonlinear_regression_ht_html <- function(results) {
  tagList(
    h4("Nonlinear Regression Model"),
    p("See summary table for model coefficients and diagnostics.")
  )
}

nonlinear_regression_summary_html <- function(results) {
  tagList(
    h4("Coefficients"),
    renderTable(broom::tidy(results$fit)),
    h4("Model Fit"),
    renderTable(data.frame(
      AIC = AIC(results$fit),
      BIC = BIC(results$fit)
    ))
  )
}

nonlinear_regression_plot <- function(results) {
  plot_data <- data.frame(
    x = results$data[[results$predictors[1]]],
    y = results$data[[results$response]],
    fitted = fitted(results$fit)
  )
  
  ggplot(plot_data, aes(x = x, y = y)) +
    geom_point(alpha = 0.5) +
    geom_line(aes(y = fitted), color = "blue") +
    labs(title = "Nonlinear Regression Curve",
         x = results$predictors[1],
         y = results$response) +
    theme_minimal()
}