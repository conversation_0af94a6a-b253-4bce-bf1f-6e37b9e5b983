# Friedman Test calculation and output helpers

friedman_uploadData_func <- function(friedmanUserData) {
  ext <- tools::file_ext(friedmanUserData$name)
  ext <- tolower(ext)
  switch(ext,
         csv = readr::read_csv(friedmanUserData$datapath, show_col_types = FALSE),
         xls = readxl::read_xls(friedmanUserData$datapath),
         xlsx = readxl::read_xlsx(friedmanUserData$datapath),
         txt = readr::read_tsv(friedmanUserData$datapath, show_col_types = FALSE),
         validate("Improper file format.")
  )
}

friedman_results_func <- function(data, response_var, block_var, treatment_var) {
  tryCatch({
    friedmanData <- na.omit(data)
    
    formula <- as.formula(paste0("`", response_var, "` ~ `", treatment_var, "` | `", block_var, "`"))
    
    # Perform Friedman test
    friedman_test <- friedman.test(formula, data = friedmanData)
    
    # Descriptive Statistics
    desc_stats <- friedmanData %>%
      dplyr::group_by_at(treatment_var) %>%
      dplyr::summarise(
        N = n(),
        Mean = mean(.data[[response_var]]),
        SD = sd(.data[[response_var]]),
        Median = median(.data[[response_var]]),
        .groups = 'drop'
      )
      
    list(
      data = friedmanData,
      friedman_test = friedman_test,
      desc_stats = desc_stats,
      error = NULL
    )
  }, error = function(e) {
    list(error = paste("An error occurred during Friedman test calculation:", e$message))
  })
}

friedman_ht_html <- function(results, sigLvl) {
  friedman_test <- results$friedman_test
  p_value <- friedman_test$p.value
  
  conclusion <- if (p_value < sigLvl) {
    "Reject H0. There is a significant difference among the treatment medians."
  } else {
    "Do not reject H0. There is no significant difference among the treatment medians."
  }
  
  withMathJax(tagList(
    h4("Friedman Rank Sum Test"),
    p("$H_0$: The medians of the treatment groups are equal."),
    p("$H_A$: At least one treatment median is different."),
    p(sprintf("Test Statistic (Friedman's chi-squared): %.4f", friedman_test$statistic)),
    p(sprintf("Degrees of Freedom: %d", friedman_test$parameter)),
    p(sprintf("P-value: %.4f", p_value)),
    p(strong("Conclusion: "), conclusion)
  ))
}

friedman_summary_html <- function(results) {
  tagList(
    h4("Descriptive Statistics by Treatment Group"),
    renderTable(results$desc_stats, digits = 4)
  )
}

friedman_plot <- function(data, response_var, treatment_var) {
  ggplot(data, aes(x = as.factor(.data[[treatment_var]]), y = .data[[response_var]], fill = as.factor(.data[[treatment_var]]))) +
    geom_boxplot(alpha = 0.7) +
    labs(title = "Boxplot of Response by Treatment",
         x = treatment_var,
         y = response_var) +
    theme_minimal() +
    theme(legend.position = "none")
}