# Placeholder for Stratified Kaplan-<PERSON><PERSON> calculation helpers if needed in future 

stratified_km <- function(data, time_var, event_var, strata_var) {
  if (!requireNamespace("survival", quietly = TRUE)) stop("Package 'survival' required.")
  form <- as.formula(paste0("survival::Surv(", time_var, ", ", event_var, ") ~ ", strata_var))
  fit <- survival::survfit(form, data = data)
  plot_fun <- function() { plot(fit, col = 1:length(unique(data[[strata_var]])), lwd = 2, main = "Stratified KM Curves"); legend("topright", legend = unique(data[[strata_var]]), col = 1:length(unique(data[[strata_var]])), lwd = 2) }
  list(
    fit = fit,
    summary = summary(fit),
    plot = plot_fun
  )
} 