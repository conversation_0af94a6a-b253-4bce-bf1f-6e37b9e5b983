source("modules/calculations/mann_whitney.R")

mannWhitneyServer <- function(id) {
  moduleServer(id, function(input, output, session) {
    ns <- session$ns
    
    # --- Reactives ---
    mwUploadData <- eventReactive(input$mwUserData, {
      handle_file_upload(input$mwUserData)
    })
    
    mwValidationErrors <- reactive({
      errors <- c()
      data <- mwUploadData()
      numvar <- input$mwNumeric
      groupvar <- input$mwGroup
      
      if (is.null(data) || !is.data.frame(data)) {
        errors <- c(errors, "No data uploaded or file could not be read.")
        return(errors)
      }
      if (is.null(numvar) || !(numvar %in% names(data))) {
        errors <- c(errors, "Please select a valid numeric variable.")
      } else if (!is.numeric(data[[numvar]])) {
        errors <- c(errors, sprintf("Column '%s' must be numeric.", numvar))
      }
      if (is.null(groupvar) || !(groupvar %in% names(data))) {
        errors <- c(errors, "Please select a valid group variable.")
      } else if (length(unique(data[[groupvar]])) != 2) {
        errors <- c(errors, "Group variable must have exactly two unique values.")
      }
      errors
    })
    
    mwResults <- eventReactive(input$goInference, {
      # Ensure validation passes before calculating
      if (length(mwValidationErrors()) > 0) {
        return(NULL)
      }
      mann_whitney_results_func(
        data = mwUploadData(),
        num_var = input$mwNumeric,
        group_var = input$mwGroup,
        conf_level = input$mwConfLevel,
        alternative = input$mwAlternative
      )
    })
    
    # --- Observers ---
    observeEvent(mwUploadData(), {
      data <- mwUploadData()
      updateSelectizeInput(session, 'mwNumeric', choices = names(data), selected = NULL, server = TRUE)
      updateSelectizeInput(session, 'mwGroup', choices = names(data), selected = NULL, server = TRUE)
      
      # Show data preview
      output$mannWhitneyResults <- renderUI({
        if (!is.null(data) && is.data.frame(data)) {
          tagList(
            h4('Preview of Uploaded Data'),
            DT::DTOutput(ns('mwPreviewTable'))
          )
        }
      })
      output$mwPreviewTable <- DT::renderDT({
        head(data, 20)
      })
    })
    
    observeEvent(input$goInference, {
      output$mannWhitneyResults <- renderUI({
        errors <- mwValidationErrors()
        if (length(errors) > 0) {
          errorScreenUI(title = "Validation Error(s) in Mann-Whitney Test", errors = errors)
        } else {
          results <- mwResults()
          req(results)
          
          tagList(
            tabsetPanel(
              id = ns("mwTabset"),
              selected = "Analysis",
              tabPanel(
                id = ns("mwAnalysis"),
                title = "Analysis",
                titlePanel("Mann-Whitney U Test Results"),
                br(),
                mann_whitney_ht_html(results, input$mwConfLevel)
              ),
              tabPanel(
                id = ns("mwDataSummary"),
                title = "Data Summary",
                mann_whitney_summary_tables_html(results)
              ),
              tabPanel(
                id = ns("mwUploadedData"),
                title = "Uploaded Data",
                h4("Raw Data"),
                DT::DTOutput(ns('mwDataTable'))
              )
            )
          )
        }
      })
      
      output$mwDataTable <- DT::renderDT({
        req(mwUploadData())
        DT::datatable(mwUploadData(),
          options = list(pageLength = 25,
                         lengthMenu = list(c(25, 50, 100, -1), c("25", "50", "100", "all")),
                         columnDefs = list(list(className = 'dt-center', targets = 0:ncol(mwUploadData())))))
      })
    })
  })
}