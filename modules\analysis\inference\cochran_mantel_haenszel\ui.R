# Cochran-Mantel-Haenszel Test UI
# Stratified contingency tables

CochranMantelHaenszelSidebarUI <- function(id) {
  ns <- NS(id)
  
  tagList(
    h4("Cochran-Mantel-Haenszel Test"),
    p("Tests for association between two categorical variables while controlling for a third stratification variable."),
    
    # Data Input
    radioButtons(
      inputId = ns("cmhDataMethod"),
      label = "Data Input Method:",
      choices = c("Upload File" = "Upload File", "Manual Entry" = "Manual Entry"),
      selected = "Upload File"
    ),
    
    # File Upload
    conditionalPanel(
      condition = "input.cmhDataMethod == 'Upload File'",
      ns = ns,
      fileInput(
        inputId = ns("cmhUserData"),
        label = "Upload Data File:",
        accept = c(".csv", ".txt", ".xlsx", ".xls"),
        buttonLabel = "Browse Files",
        placeholder = "No file selected"
      ),
      selectizeInput(
        inputId = ns("cmhVariable1"),
        label = "Select First Variable:",
        choices = NULL,
        options = list(placeholder = "Select first variable...")
      ),
      selectizeInput(
        inputId = ns("cmhVariable2"),
        label = "Select Second Variable:",
        choices = NULL,
        options = list(placeholder = "Select second variable...")
      ),
      selectizeInput(
        inputId = ns("cmhStratumVariable"),
        label = "Select Stratification Variable:",
        choices = NULL,
        options = list(placeholder = "Select stratification variable...")
      )
    ),
    
    # Manual Entry
    conditionalPanel(
      condition = "input.cmhDataMethod == 'Manual Entry'",
      ns = ns,
      textAreaInput(
        inputId = ns("cmhManualData"),
        label = "Enter Contingency Table Data (comma-separated):",
        placeholder = "Stratum,Var1,Var2,Count\n1,A,X,10\n1,A,Y,5\n1,B,X,8\n1,B,Y,12\n2,A,X,15\n2,A,Y,7\n2,B,X,6\n2,B,Y,9",
        rows = 10
      )
    ),
    
    # Analysis Options
    h5("Analysis Options"),
    
    checkboxInput(
      inputId = ns("cmhOddsRatio"),
      label = "Calculate Odds Ratios",
      value = TRUE
    ),
    
    checkboxInput(
      inputId = ns("cmhHomogeneityTest"),
      label = "Test Homogeneity of Odds Ratios",
      value = TRUE
    ),
    
    checkboxInput(
      inputId = ns("cmhEffectSize"),
      label = "Calculate Effect Sizes",
      value = TRUE
    ),
    
    numericInput(
      inputId = ns("cmhConfLevel"),
      label = "Confidence Level:",
      value = 0.95,
      min = 0.5,
      max = 0.99,
      step = 0.01
    ),
    
    actionButton(
      inputId = ns("goCmh"),
      label = "Perform Cochran-Mantel-Haenszel Test",
      class = "btn-primary",
      style = "width: 100%;"
    ),
    
    br(),
    br(),
    
    # Help text
    helpText(
      "CMH test analyzes association between two categorical variables.",
      "Stratification variable controls for confounding effects.",
      "Tests for homogeneity across strata.",
      "Provides odds ratios and confidence intervals."
    )
  )
}

CochranMantelHaenszelMainUI <- function(id) {
  ns <- NS(id)
  
  tagList(
    uiOutput(ns("cmhResults"))
  )
} 