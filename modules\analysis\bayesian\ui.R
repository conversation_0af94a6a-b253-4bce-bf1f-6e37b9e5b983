# Bayesian Tests UI
BayesianTestSidebarUI <- function(id) {
  ns <- NS(id)
  tagList(
    h5("Test Configuration"),
    selectInput(ns("bayesTestType"), "Test Type", 
                choices = c("Bayesian t-test" = "Bayesian t-test", 
                           "Bayesian ANOVA" = "Bayesian ANOVA",
                           "Bayesian Correlation" = "Bayesian Correlation",
                           "Bayesian Proportion Test" = "Bayesian Proportion Test",
                           "Bayesian Regression" = "Bayesian Regression"), 
                selected = "Bayesian t-test"),
    
    fileInput(ns("bayesUserData"), "Upload your Data (.csv, .xls, .xlsx, .txt)", accept = c(".csv", ".xls", ".xlsx", ".txt")),
    
    # Variable selection based on test type
    conditionalPanel(
      condition = sprintf("input['%s'] == 'Bayesian t-test' || input['%s'] == 'Bayesian ANOVA'", ns("bayesTestType"), ns("bayesTestType")),
      selectizeInput(ns("bayesGroup"), "Group Column", choices = NULL),
      selectizeInput(ns("bayesResponse"), "Response Column", choices = NULL)
    ),
    
    conditionalPanel(
      condition = sprintf("input['%s'] == 'Bayesian Correlation'", ns("bayesTestType")),
      selectizeInput(ns("bayesVariables"), "Select Variables", choices = NULL, multiple = TRUE),
      helpText("Select at least two variables for correlation analysis")
    ),
    
    conditionalPanel(
      condition = sprintf("input['%s'] == 'Bayesian Regression'", ns("bayesTestType")),
      selectizeInput(ns("bayesResponse"), "Response Column", choices = NULL),
      selectizeInput(ns("bayesPredictors"), "Predictor Columns", choices = NULL, multiple = TRUE)
    ),
    
    conditionalPanel(
      condition = sprintf("input['%s'] == 'Bayesian Proportion Test'", ns("bayesTestType")),
      selectizeInput(ns("bayesGroup"), "Group Column", choices = NULL),
      selectizeInput(ns("bayesResponse"), "Response Column", choices = NULL)
    ),
    
    hr(),
    h5("Prior Specification"),
    selectInput(ns("bayesPrior"), "Prior Type", 
                choices = c("Default/Non-informative" = "Default", 
                           "Weakly Informative" = "Weak",
                           "Custom" = "Custom")),
    conditionalPanel(
      condition = sprintf("input['%s'] == 'Custom'", ns("bayesPrior")),
      textInput(ns("bayesPriorParams"), "Custom Prior Parameters", value = ""),
      helpText("Enter prior parameters in format: mean, sd (e.g., '0, 1')")
    ),
    
    hr(),
    h5("MCMC Parameters"),
    numericInput(ns("bayesChains"), "Number of Chains", value = 4, min = 1, max = 8, step = 1),
    numericInput(ns("bayesIterations"), "Number of Iterations", value = 2000, min = 500, max = 10000, step = 500),
    numericInput(ns("bayesWarmup"), "Warmup Period", value = 1000, min = 100, max = 5000, step = 100),
    
    hr(),
    h5("Analysis Options"),
    checkboxInput(ns("bayesShowDiagnostics"), "Show MCMC Diagnostics", value = TRUE),
    checkboxInput(ns("bayesShowModelComparison"), "Show Model Comparison", value = FALSE),
    checkboxInput(ns("bayesShowPosterior"), "Show Posterior Distributions", value = TRUE),
    
    br(),
    actionButton(ns("goBayesian"), label = "Run Bayesian Analysis", class = "act-btn")
  )
}

BayesianTestMainUI <- function(id) {
  ns <- NS(id)
  tagList(
    uiOutput(ns('bayesianResults'))
  )
}

BayesianTestUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(
      BayesianTestSidebarUI(id)
    ),
    mainPanel(
      BayesianTestMainUI(id)
    )
  )
} 