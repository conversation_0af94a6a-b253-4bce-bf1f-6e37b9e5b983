SpatialAnalysisUI <- function(id) {
  ns <- NS(id)
  sidebarLayout(
    sidebarPanel(
      fileInput(ns("data_file"), "Upload Data (CSV)", accept = ".csv"),
      fileInput(ns("shapefile"), "Upload Shapefile (ZIP)", accept = ".zip"),
      selectInput(ns("analysis_type"), "Analysis Type",
                 choices = c("Spatial Autocorrelation" = "autocorrelation",
                           "Spatial Regression" = "regression",
                           "Point Pattern Analysis" = "point_pattern",
                           "Spatial Clustering" = "clustering",
                           "Hot Spot Analysis" = "hotspot",
                           "Spatial Interpolation" = "interpolation",
                           "Buffer Analysis" = "buffer",
                           "Spatial Overlay" = "overlay")),
      selectInput(ns("variable"), "Variable of Interest", choices = NULL),
      conditionalPanel(
        condition = paste0("input['", ns("analysis_type"), "'] == 'regression'"),
        selectInput(ns("dependent_var"), "Dependent Variable", choices = NULL),
        selectInput(ns("independent_vars"), "Independent Variables", choices = NULL, multiple = TRUE)
      ),
      selectInput(ns("x_coord"), "X Coordinate Column", choices = NULL),
      selectInput(ns("y_coord"), "Y Coordinate Column", choices = NULL),
      selectInput(ns("crs"), "Coordinate Reference System",
                 choices = c("WGS84 (EPSG:4326)" = "EPSG:4326",
                           "UTM Zone 10N (EPSG:32610)" = "EPSG:32610",
                           "UTM Zone 11N (EPSG:32611)" = "EPSG:32611",
                           "NAD83 (EPSG:4269)" = "EPSG:4269")),
      # Add all other parameter inputs as needed (see original for details)
      actionButton(ns("run_analysis"), "Run Spatial Analysis", class = "btn-primary"),
      downloadButton(ns("download_results"), "Download Results"),
      downloadButton(ns("download_map"), "Download Map")
    ),
    mainPanel(
      leafletOutput(ns("spatial_map"), height = "500px"),
      tableOutput(ns("autocorr_table")),
      plotOutput(ns("moran_scatter")),
      plotOutput(ns("lisa_map")),
      plotOutput(ns("autocorr_plot")),
      tableOutput(ns("regression_summary")),
      plotOutput(ns("regression_diagnostics")),
      plotOutput(ns("spatial_residuals")),
      tableOutput(ns("regression_coefficients")),
      tableOutput(ns("point_pattern_table")),
      plotOutput(ns("ripley_plot")),
      plotOutput(ns("nearest_neighbor_plot")),
      plotOutput(ns("quadrant_plot")),
      tableOutput(ns("clustering_summary")),
      plotOutput(ns("cluster_map")),
      plotOutput(ns("cluster_diagnostics")),
      tableOutput(ns("cluster_centers")),
      tableOutput(ns("hotspot_table")),
      plotOutput(ns("hotspot_map")),
      plotOutput(ns("hotspot_density")),
      tableOutput(ns("interpolation_table")),
      plotOutput(ns("interpolation_map")),
      plotOutput(ns("buffer_map")),
      tableOutput(ns("buffer_table")),
      tableOutput(ns("overlay_table"))
    )
  )
} 